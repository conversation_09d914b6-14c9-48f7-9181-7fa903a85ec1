# 风控名单管理系统分析 - 总结

## 系统架构总结

风控名单管理系统主要由两个模块组成：

1. **car-risk-manage**: 主要负责风控名单的管理功能，包括添加、删除、查询等操作，主要面向后台管理人员。
2. **car-risk-process**: 主要负责风控名单的处理和应用功能，包括黑名单检查、风险评估等，主要面向业务处理流程。

这两个模块都使用了 `riskCustomerManageMapper.insert` 和 `riskCustomerManageMapper.update` 方法来操作风控名单数据。

## riskCustomerManageMapper.insert 调用分析

### car-risk-manage 模块中的调用

1. **RiskCustomerService.add**: 添加风控名单时，如果名单不存在，则调用 `riskCustomerManageMapper.insert` 方法插入新记录。
2. **HcCustomerService.add**: 添加汇川司机风控名单时，如果名单不存在，则调用 `hcCustomerMapper.insert` 方法插入新记录。

### car-risk-process 模块中的调用

1. **BlackDriverService.userShield**: 用户拉黑司机时，如果拉黑记录不存在，则调用 `riskCustomerManageMapper.insert` 方法插入新记录。
2. **BlackListService.blackDriver**: 用户拉黑司机时，如果拉黑记录不存在，则调用 `riskCustomerManageMapper.insert` 方法插入新记录。
3. **RiskCustomerService.addRiskCustomer**: 规则动作拉黑时，调用 `riskCustomerManageMapper.insert` 方法插入新记录。
4. **RiskCustomerService.addRiskCustomerOne**: 规则动作一对一拉黑时，调用 `riskCustomerManageMapper.insert` 方法插入新记录。
5. **DisposeCenterService.actionBlack**: 处理中心全部拉黑时，调用 `riskCustomerManageMapper.insert` 方法插入新记录。
6. **DisposeCenterService.actionAllBlack**: 处理中心一对一拉黑时，调用 `riskCustomerManageMapper.insert` 方法插入新记录。

## riskCustomerManageMapper.update 调用分析

### car-risk-manage 模块中的调用

1. **RiskCustomerService.add**: 添加风控名单时，如果名单已存在，则调用 `riskCustomerManageMapper.updateById` 方法更新记录。
2. **RiskCustomerService.delete**: 删除风控名单时，调用 `riskCustomerManageMapper.updateById` 方法更新记录状态为无效。
3. **HcCustomerService.add**: 添加汇川司机风控名单时，如果名单已存在，则调用 `hcCustomerMapper.updateById` 方法更新记录。
4. **HcCustomerService.delete**: 删除汇川司机风控名单时，调用 `hcCustomerMapper.updateById` 方法更新记录状态为无效。

### car-risk-process 模块中的调用

1. **BlackDriverService.userShield**: 用户拉黑司机时，如果拉黑记录已存在，则调用 `riskCustomerManageMapper.updateById` 方法更新记录。
2. **BlackDriverService.userRemove**: 用户取消拉黑司机时，调用 `riskCustomerManageMapper.updateById` 方法更新记录状态为无效。
3. **BlackListService.blackDriver**: 用户拉黑司机时，如果拉黑记录已存在，则调用 `riskCustomerManageMapper.updateById` 方法更新记录。
4. **BlackListService.removeDriver**: 用户取消拉黑司机时，调用 `riskCustomerManageMapper.updateById` 方法更新记录状态为无效。
5. **RiskCustomerService.invalid**: 风控名单定时失效时，调用 `riskCustomerManageMapper.updateById` 方法更新记录状态为失效。
6. **RiskCustomerService.initCustomer**: 名单初始化失效时间时，调用 `riskCustomerManageMapper.updateById` 方法更新记录的失效时间。

## 主要业务流程总结

1. **添加风控名单**:
   - 校验参数
   - 检查名单是否已存在
   - 如果已存在，更新名单信息
   - 如果不存在，创建新名单
   - 记录操作日志

2. **删除风控名单**:
   - 查询名单信息
   - 更新名单状态为无效
   - 记录操作日志

3. **用户拉黑司机**:
   - 查询是否已存在拉黑记录
   - 如果已存在，更新记录
   - 如果不存在，创建新记录
   - 记录操作日志

4. **用户取消拉黑司机**:
   - 查询拉黑记录
   - 更新记录状态为无效
   - 记录操作日志

5. **风控名单定时失效**:
   - 查询需要失效的风控名单
   - 遍历每条记录，更新状态为失效

6. **规则动作拉黑**:
   - 创建新的风控名单记录
   - 记录操作日志

## 系统优化建议

1. **代码重复问题**:
   - 在 `car-risk-manage` 和 `car-risk-process` 模块中存在大量重复的代码，特别是在处理风控名单的添加、删除等操作时。建议将这些共同的逻辑抽取到一个公共模块中，以减少代码重复。

2. **事务管理**:
   - 在添加风控名单和记录操作日志时，应该使用事务来确保数据的一致性。如果插入风控名单成功但插入操作日志失败，应该回滚整个事务。

3. **异常处理**:
   - 在代码中存在一些异常处理不完善的地方，建议增加更详细的异常处理和日志记录，以便于问题排查。

4. **参数校验**:
   - 在接收用户输入时，应该进行更严格的参数校验，以防止恶意输入和数据异常。

5. **性能优化**:
   - 在查询风控名单时，可以考虑使用缓存来提高查询性能，特别是对于频繁查询的数据。
   - 在批量操作时，可以考虑使用批量插入或更新来提高性能。

6. **日志记录**:
   - 在关键操作点增加更详细的日志记录，以便于问题排查和审计。

7. **接口设计**:
   - 考虑使用 RESTful API 设计风格，使接口更加规范和易于理解。
   - 考虑使用 API 版本控制，以便于接口的升级和维护。

8. **安全性**:
   - 增加接口的权限控制，确保只有授权用户才能访问敏感操作。
   - 对敏感数据进行加密处理，保护用户隐私。

## 结论

风控名单管理系统是一个复杂的系统，涉及到多个模块和多种业务场景。通过对 `riskCustomerManageMapper.insert` 和 `riskCustomerManageMapper.update` 方法的调用分析，我们可以看到系统的主要业务流程和数据操作方式。

系统的设计基本合理，但存在一些代码重复和优化空间。通过实施上述优化建议，可以提高系统的可维护性、性能和安全性。
