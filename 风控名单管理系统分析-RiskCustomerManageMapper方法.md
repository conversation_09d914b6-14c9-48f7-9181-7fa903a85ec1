# 风控名单管理系统分析 - RiskCustomerManageMapper 方法

本文档分析了 `RiskCustomerManageMapper` 接口中的所有方法及其功能。

## BaseMapper 继承的方法

`RiskCustomerManageMapper` 接口继承自 MyBatis-Plus 的 `BaseMapper` 接口，因此拥有以下基本方法：

### 1. insert 方法

```java
int insert(T entity);
```

**功能**：插入一条记录。

**参数**：
- `entity`: 要插入的实体对象。

**返回值**：插入成功的记录数。

### 2. updateById 方法

```java
int updateById(@Param("et") T entity);
```

**功能**：根据 ID 更新记录。

**参数**：
- `entity`: 要更新的实体对象，必须包含 ID。

**返回值**：更新成功的记录数。

### 3. selectById 方法

```java
T selectById(Serializable id);
```

**功能**：根据 ID 查询记录。

**参数**：
- `id`: 记录的 ID。

**返回值**：查询到的实体对象。

### 4. selectOne 方法

```java
T selectOne(@Param("ew") Wrapper<T> queryWrapper);
```

**功能**：根据条件查询一条记录。

**参数**：
- `queryWrapper`: 查询条件。

**返回值**：查询到的实体对象。

### 5. selectList 方法

```java
List<T> selectList(@Param("ew") Wrapper<T> queryWrapper);
```

**功能**：根据条件查询多条记录。

**参数**：
- `queryWrapper`: 查询条件。

**返回值**：查询到的实体对象列表。

### 6. deleteById 方法

```java
int deleteById(Serializable id);
```

**功能**：根据 ID 删除记录。

**参数**：
- `id`: 记录的 ID。

**返回值**：删除成功的记录数。

### 7. delete 方法

```java
int delete(@Param("ew") Wrapper<T> queryWrapper);
```

**功能**：根据条件删除记录。

**参数**：
- `queryWrapper`: 删除条件。

**返回值**：删除成功的记录数。

## 自定义方法

除了继承自 `BaseMapper` 的方法外，`RiskCustomerManageMapper` 接口还定义了以下自定义方法：

### 1. getListTotal 方法

```java
long getListTotal(Object query);
```

**功能**：获取符合条件的记录总数。

**参数**：
- `query`: 查询条件。

**返回值**：记录总数。

### 2. getList 方法

```java
List<RiskCustomerManage> getList(Object query);
```

**功能**：获取符合条件的记录列表。

**参数**：
- `query`: 查询条件。

**返回值**：符合条件的记录列表。

### 3. getListExport 方法

```java
List<RiskCustomerManage> getListExport(Object query);
```

**功能**：获取符合条件的记录列表，用于导出。

**参数**：
- `query`: 查询条件。

**返回值**：符合条件的记录列表。

### 4. getByTypeAndValueAndRiskType 方法

```java
RiskCustomerManage getByTypeAndValueAndRiskType(
    @Param("customerType") Integer customerType,
    @Param("customerValue") String customerValue,
    @Param("riskType") Integer riskType,
    @Param("time") Date time
);
```

**功能**：根据客户类型、客户值、风险类型和时间查询记录。

**参数**：
- `customerType`: 客户类型。
- `customerValue`: 客户值。
- `riskType`: 风险类型。
- `time`: 时间。

**返回值**：符合条件的记录。

### 5. getByTypeAndValueAndRiskTypeAndUser 方法

```java
RiskCustomerManage getByTypeAndValueAndRiskTypeAndUser(
    @Param("customerType") Integer customerType,
    @Param("customerValue") String customerValue,
    @Param("riskType") Integer riskType,
    @Param("bindUser") String bindUser,
    @Param("time") Date time
);
```

**功能**：根据客户类型、客户值、风险类型、绑定用户和时间查询记录。

**参数**：
- `customerType`: 客户类型。
- `customerValue`: 客户值。
- `riskType`: 风险类型。
- `bindUser`: 绑定用户。
- `time`: 时间。

**返回值**：符合条件的记录。

### 6. clearInvalid 方法

```java
int clearInvalid();
```

**功能**：清除无效的风控名单。

**返回值**：清除成功的记录数。

### 7. clearInvalidById 方法

```java
int clearInvalidById(@Param("id") Long id);
```

**功能**：根据 ID 清除无效的风控名单。

**参数**：
- `id`: 记录的 ID。

**返回值**：清除成功的记录数。

### 8. getDupList 方法

```java
List<RiskCustomerManage> getDupList();
```

**功能**：获取重复的风控名单。

**返回值**：重复的风控名单列表。

### 9. getDup1v1List 方法

```java
List<RiskCustomerManage> getDup1v1List();
```

**功能**：获取重复的一对一风控名单。

**返回值**：重复的一对一风控名单列表。

### 10. getByCondition 方法

```java
List<RiskCustomerManage> getByCondition(Object query);
```

**功能**：根据条件查询记录。

**参数**：
- `query`: 查询条件。

**返回值**：符合条件的记录列表。

### 11. selectInvalidData 方法 (car-risk-process 模块)

```java
@Select("select "
        + "  * "
        + "from "
        + "  risk_customer_manage "
        + "where "
        + "  status = 1 "
        + "  and ( "
        + "    ( "
        + "      ttl != -1 "
        + "      and ttl != 0 "
        + "      and NOW() >= DATE_ADD(create_time, INTERVAL ttl day) "
        + "    ) "
        + "    or ( "
        + "      ttl = 0 "
        + "      and NOW() >= invalid_time "
        + "    ) "
        + "  )")
List<RiskCustomerManage> selectInvalidData();
```

**功能**：查询需要失效的风控名单。

**返回值**：需要失效的风控名单列表。

### 12. getListByValue 方法 (car-risk-process 模块)

```java
List<RiskCustomerManage> getListByValue(
    @Param("params") FilterParams params,
    @Param("invalidTime") Date dateTime
);
```

**功能**：根据值查询风控名单。

**参数**：
- `params`: 过滤参数。
- `dateTime`: 失效时间。

**返回值**：符合条件的风控名单列表。

### 13. getListByValueByGroup 方法 (car-risk-process 模块)

```java
List<RiskCustomerManage> getListByValueByGroup(
    @Param("params") CommonCustomerParam params,
    @Param("invalidTime") Date dateTime
);
```

**功能**：按组查询风控名单。

**参数**：
- `params`: 通用客户参数。
- `dateTime`: 失效时间。

**返回值**：符合条件的风控名单列表。

### 14. getValidCount 方法 (car-risk-process 模块)

```java
long getValidCount(
    @Param("customerType") int customerType,
    @Param("customerValue") String customerValue,
    @Param("riskType") int riskType
);
```

**功能**：获取有效的风控名单数量。

**参数**：
- `customerType`: 客户类型。
- `customerValue`: 客户值。
- `riskType`: 风险类型。

**返回值**：有效的风控名单数量。

### 15. queryAllValidRiskRecord 方法 (car-risk-process 模块)

```java
List<RiskCustomerManage> queryAllValidRiskRecord(
    @Param("offset") int offset,
    @Param("limit") int limit
);
```

**功能**：查询所有有效的风控记录。

**参数**：
- `offset`: 偏移量。
- `limit`: 限制数量。

**返回值**：有效的风控记录列表。
