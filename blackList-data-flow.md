# 黑名单功能数据流向分析

## 1. 数据流向概述

黑名单功能的数据流向主要包括以下几个方面：

1. **数据添加流向**：用户或客服将司机添加到黑名单中
2. **数据查询流向**：用户查询黑名单列表或检查司机是否在黑名单中
3. **数据移除流向**：用户将司机从黑名单中移除
4. **数据同步流向**：外部系统与本系统之间的黑名单数据同步

## 2. 数据添加流向

### 2.1 用户拉黑司机流程

```mermaid
graph TD
    A[用户端] -->|拉黑司机| B[BlackListApiController]
    B -->|blackDriver| C[BlackListService]
    C -->|查询是否已存在| D[RiskCustomerManageMapper]
    D -->|返回查询结果| C
    C -->|创建新记录| E[RiskCustomerManage]
    C -->|插入记录| D
    D -->|写入数据库| F[risk_customer_manage表]
```

### 2.2 客服拉黑司机流程

```mermaid
graph TD
    A[客服端] -->|拉黑司机| B[BlackListController]
    B -->|blackDriverFromManage| C[BlackListService]
    C -->|获取分布式锁| D[RedissonClient]
    C -->|查询是否已存在| E[RiskCustomerManageMapper]
    E -->|返回查询结果| C
    C -->|创建新记录| F[RiskCustomerManage]
    C -->|插入记录| E
    E -->|写入数据库| G[risk_customer_manage表]
    C -->|释放分布式锁| D
```

### 2.3 外部系统同步黑名单流程

```mermaid
graph TD
    A[外部系统] -->|同步黑名单| B[BlackListApiController]
    B -->|syncDriverBlack| C[BlackListService]
    C -->|查询是否已存在| D[RiskCustomerManageMapper]
    D -->|返回查询结果| C
    C -->|创建新记录或更新记录| E[RiskCustomerManage]
    C -->|插入或更新记录| D
    D -->|写入数据库| F[risk_customer_manage表]
```

## 3. 数据查询流向

### 3.1 查询用户拉黑司机列表流程

```mermaid
graph TD
    A[用户端] -->|查询黑名单| B[BlackListApiController]
    B -->|listDriver| C[BlackListService]
    C -->|查询黑名单记录| D[RiskCustomerManageMapper]
    D -->|执行SQL查询| E[risk_customer_manage表]
    E -->|返回查询结果| D
    D -->|返回查询结果| C
    C -->|过滤和转换数据| F[RiskCustomerManageListDTO]
    C -->|返回结果| B
    B -->|返回响应| A
```

### 3.2 检查司机是否被拉黑流程

```mermaid
graph TD
    A[用户端] -->|检查是否被拉黑| B[BlackListApiController]
    B -->|checkDriverIn| C[BlackListService]
    C -->|查询黑名单记录| D[RiskCustomerManageMapper]
    D -->|执行SQL查询| E[risk_customer_manage表]
    E -->|返回查询结果| D
    D -->|返回查询结果| C
    C -->|判断是否存在| F[Boolean]
    C -->|返回结果| B
    B -->|返回响应| A
```

### 3.3 批量查询车牌黑名单流程

```mermaid
graph TD
    A[用户端] -->|批量查询黑名单| B[BlackListApiController]
    B -->|batchQueryDriverBlack| C[BlackListService]
    C -->|查询黑名单记录| D[RiskCustomerManageMapper]
    D -->|执行SQL查询| E[risk_customer_manage表]
    E -->|返回查询结果| D
    D -->|返回查询结果| C
    C -->|筛选和转换数据| F[List<CarBlackInfo>]
    C -->|返回结果| B
    B -->|返回响应| A
```

## 4. 数据移除流向

### 4.1 用户取消拉黑司机流程

```mermaid
graph TD
    A[用户端] -->|取消拉黑| B[BlackListApiController]
    B -->|removeDriver| C[BlackListService]
    C -->|查询黑名单记录| D[RiskCustomerManageMapper]
    D -->|执行SQL查询| E[risk_customer_manage表]
    E -->|返回查询结果| D
    D -->|返回查询结果| C
    C -->|更新状态为失效| F[RiskCustomerManage]
    C -->|更新记录| D
    D -->|写入数据库| E
    C -->|返回结果| B
    B -->|返回响应| A
```

## 5. 数据表关系

```mermaid
erDiagram
    risk_customer_manage ||--o{ risk_customer_record : "记录操作"
    
    risk_customer_manage {
        bigint id PK
        int customer_type
        string customer_value
        int risk_type
        int status
        int ttl
        datetime create_time
        datetime update_time
        datetime invalid_time
        string bind_user
        string bind_order
    }
    
    risk_customer_record {
        bigint id PK
        bigint customer_id FK
        int operate_type
        string create_user
        string operate_user
        int customer_type
        string remark
        datetime create_time
        string customer_value
    }
```

## 6. `/blackList/driver/list` 接口数据流向

### 6.1 请求数据流向

```mermaid
graph TD
    A[客户端] -->|POST请求| B[BlackListApiController]
    B -->|请求参数| C[DriverBlackListRequest]
    C -->|passengerCellphone| D[BlackListService.listDriver]
```

请求参数示例：

```json
{
  "traceId": "123456789",
  "passengerCellphone": "13800138000"
}
```

### 6.2 数据库查询流向

```mermaid
graph TD
    A[BlackListService.listDriver] -->|构建查询条件| B[QueryWrapper]
    B -->|bind_user=passengerCellphone| C[RiskCustomerManageMapper.selectList]
    C -->|执行SQL查询| D[risk_customer_manage表]
    D -->|返回查询结果| E[List<RiskCustomerManage>]
    E -->|过滤无效数据| F[过滤后的List<RiskCustomerManage>]
    F -->|转换为DTO| G[List<RiskCustomerManageListDTO>]
```

SQL 查询语句：

```sql
SELECT * FROM risk_customer_manage
WHERE bind_user = #{passengerCellphone}
  AND invalid_time > NOW()
```

### 6.3 响应数据流向

```mermaid
graph TD
    A[List<RiskCustomerManageListDTO>] -->|设置为响应数据| B[UiResult]
    B -->|转换为响应包装对象| C[UiResultWrapper]
    C -->|JSON序列化| D[JSON响应]
    D -->|HTTP响应| E[客户端]
```

响应数据示例：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "customerValue": "粤B12345",
      "invalidTime": "2024-12-31 23:59:59",
      "driverName": "张三",
      "orderId": "YC123456789",
      "shieldTime": "2023-12-31 12:00:00",
      "driverCardNo": "粤B12345",
      "startAddress": "广州市天河区",
      "endAddress": "广州市白云区",
      "useTime": "2023-12-31 10:00:00"
    },
    {
      "customerValue": "粤A67890",
      "invalidTime": "2024-12-31 23:59:59",
      "driverName": "李四",
      "orderId": "YC987654321",
      "shieldTime": "2023-12-30 12:00:00",
      "driverCardNo": "粤A67890",
      "startAddress": "广州市越秀区",
      "endAddress": "广州市海珠区",
      "useTime": "2023-12-30 10:00:00"
    }
  ],
  "success": true
}
```

## 7. 数据来源与去向汇总

### 7.1 数据来源

| 数据来源 | 接口路径 | 控制器 | 服务方法 | 描述 |
| --- | --- | --- | --- | --- |
| 用户拉黑司机 | `/blackList/driver/black` | BlackListApiController | blackDriver | 用户将司机添加到黑名单中 |
| 客服拉黑司机 | `/manage/blackList/driver/black` | BlackListController | blackDriverFromManage | 客服将司机添加到黑名单中 |
| 外部系统同步 | `/blackList/sync` | BlackListApiController | syncDriverBlack | 外部系统同步黑名单数据 |
| 系统自动拉黑 | 定时任务 | RiskDriverTask | - | 系统根据规则自动将司机拉入黑名单 |

### 7.2 数据去向

| 数据去向 | 接口路径 | 控制器 | 服务方法 | 描述 |
| --- | --- | --- | --- | --- |
| 查询黑名单列表 | `/blackList/driver/list` | BlackListApiController | listDriver | 查询用户拉黑的司机列表 |
| 检查是否被拉黑 | `/blackList/driver/checkIn` | BlackListApiController | checkDriverIn | 检查司机是否被拉黑 |
| 批量查询黑名单 | `/blackList/batchQueryDriverBlack` | BlackListApiController | batchQueryDriverBlack | 批量查询车牌黑名单 |
| 取消拉黑 | `/blackList/driver/remove` | BlackListApiController | removeDriver | 用户取消拉黑司机 |

## 8. 总结

黑名单功能的数据流向主要包括数据添加、数据查询、数据移除和数据同步四个方面。其中，`/blackList/driver/list` 接口是数据查询流向的一部分，用于查询用户拉黑的司机列表。

数据流向的核心是 `risk_customer_manage` 表，该表存储了所有的黑名单数据。黑名单数据可以通过多种方式添加，包括用户拉黑、客服拉黑、外部系统同步和系统自动拉黑。黑名单数据可以通过用户取消拉黑或系统自动过期来移除。

整个数据流向清晰明了，各个环节职责分明，确保了黑名单功能的正常运行。
