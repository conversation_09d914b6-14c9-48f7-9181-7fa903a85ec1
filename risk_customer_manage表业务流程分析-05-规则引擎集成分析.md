# risk_customer_manage表业务流程分析 - 05 规则引擎集成分析

## 1. 规则引擎架构概述

risk_customer_manage表在整个风控系统中作为规则引擎的重要数据源，通过多个服务类与规则引擎进行集成，实现实时风控检查和自动化风控处理。

### 1.1 规则引擎集成架构

```mermaid
graph TB
    A[业务请求] --> B[规则引擎入口]
    B --> C[BlackListService]
    B --> D[CustomerHandler]
    B --> E[RiskEngine]
    
    C --> F[risk_customer_manage表]
    D --> F
    E --> F
    
    F --> G[风控决策结果]
    G --> H[业务系统响应]
    
    I[定时任务] --> J[RiskCustomerService]
    J --> F
    
    K[外部系统] --> L[同步接口]
    L --> F
```

### 1.2 核心集成组件

- **BlackListService** - 黑名单处理服务
- **CustomerHandler** - 客户风控处理链
- **RiskEngine** - 风控规则引擎
- **FilterContext** - 风控过滤上下文
- **RiskAnalysisEngine** - 风控分析引擎

## 2. BlackListService规则引擎集成

### 2.1 服务概述

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/BlackListService.java" mode="EXCERPT">
````java
@Service
@Slf4j
public class BlackListService {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private RedissonClient redissonClient;
    
    // 司机黑名单类型定义
    private List<Integer> driverBlackTypes = Stream.of(
        black_list.getCode(),           // 黑名单
        ban_receive_list.getCode(),     // 禁止接单
        ban_ync_receive_list.getCode()  // 禁止云南昌接单
    ).collect(Collectors.toList());
}
````
</augment_code_snippet>

### 2.2 司机黑名单检查

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/BlackListService.java" mode="EXCERPT">
````java
public UiResult checkDriverBlack(DriverBlackRequest request) {
    try {
        // 1. 参数校验
        validateDriverBlackRequest(request);
        
        // 2. 查询司机黑名单
        List<RiskCustomerManage> blackList = queryDriverBlackList(request.getDriverCardNo());
        
        // 3. 检查黑名单命中
        if (!CollectionUtils.isEmpty(blackList)) {
            return buildBlackListResult(blackList);
        }
        
        // 4. 检查一对一名单
        List<RiskCustomerManage> oneToOneList = queryOneToOneList(request);
        if (!CollectionUtils.isEmpty(oneToOneList)) {
            return buildOneToOneResult(oneToOneList);
        }
        
        // 5. 未命中任何风控规则
        return UiResult.ok();
        
    } catch (Exception e) {
        log.error("司机黑名单检查异常：{}", JsonUtils.json(request), e);
        return UiResult.fail(-1, "风控检查异常");
    }
}

private List<RiskCustomerManage> queryDriverBlackList(String driverCardNo) {
    return riskCustomerManageMapper.selectList(
        new QueryWrapper<RiskCustomerManage>()
            .eq("customer_value", driverCardNo)
            .eq("customer_type", RiskCustomerCustomerTypeEnum.driver_card_no.getCode())
            .in("risk_type", driverBlackTypes)
            .eq("status", RiskCustomerStatusEnum.valid.getCode())
            .gt("invalid_time", new Date())
    );
}
````
</augment_code_snippet>

### 2.3 批量司机黑名单查询

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/BlackListService.java" mode="EXCERPT">
````java
public List<BlackInfoManageDTO> batchQueryCarBlackInfo(BatchCarBlackInfoQueryRequest request) {
    List<BlackInfoManageDTO> result = new ArrayList<>();
    
    if (CollectionUtils.isEmpty(request.getCarBlackInfos())) {
        return result;
    }
    
    // 1. 批量查询车牌号
    List<String> plateNumbers = request.getCarBlackInfos().stream()
        .map(CarBlackInfo::getPlateNumber)
        .distinct()
        .collect(Collectors.toList());
    
    // 2. 查询风控名单
    List<RiskCustomerManage> blackList = riskCustomerManageMapper.selectList(
        new QueryWrapper<RiskCustomerManage>()
            .in("customer_value", plateNumbers)
            .eq("customer_type", RiskCustomerCustomerTypeEnum.driver_card_no.getCode())
            .in("risk_type", driverBlackTypes)
            .eq("status", RiskCustomerStatusEnum.valid.getCode())
            .gt("invalid_time", new Date())
    );
    
    // 3. 构建结果映射
    Map<String, List<RiskCustomerManage>> blackListMap = blackList.stream()
        .collect(Collectors.groupingBy(RiskCustomerManage::getCustomerValue));
    
    // 4. 构建返回结果
    for (CarBlackInfo carInfo : request.getCarBlackInfos()) {
        BlackInfoManageDTO dto = new BlackInfoManageDTO();
        dto.setPlateNumber(carInfo.getPlateNumber());
        dto.setPassengerPhone(carInfo.getPassengerPhone());
        
        List<RiskCustomerManage> carBlackList = blackListMap.get(carInfo.getPlateNumber());
        if (!CollectionUtils.isEmpty(carBlackList)) {
            dto.setIsBlack(true);
            dto.setBlackReason(buildBlackReason(carBlackList));
            dto.setBlackType(getBlackType(carBlackList));
        } else {
            dto.setIsBlack(false);
        }
        
        result.add(dto);
    }
    
    return result;
}
````
</augment_code_snippet>

### 2.4 司机黑名单自动拉黑

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/BlackListService.java" mode="EXCERPT">
````java
public UiResult addDriverToBlackList(DriverBlackFromManageRequest request) {
    String lockKey = "driver_black_" + request.getDriverCardNo();
    RLock lock = redissonClient.getLock(lockKey);
    
    try {
        if (lock.tryLock(10, TimeUnit.SECONDS)) {
            // 1. 检查是否已在黑名单
            long existCount = riskCustomerManageMapper.getValidCount(
                RiskCustomerCustomerTypeEnum.driver_card_no.getCode(),
                request.getDriverCardNo(),
                RiskCustomerRiskTypeEnum.black_list.getCode()
            );
            
            if (existCount > 0) {
                return UiResult.fail(-1, "司机已在黑名单中");
            }
            
            // 2. 构建黑名单记录
            RiskCustomerManage blackRecord = buildBlackRecord(request);
            
            // 3. 插入黑名单
            riskCustomerManageMapper.insert(blackRecord);
            
            // 4. 记录操作日志
            recordBlackOperation(blackRecord, request);
            
            // 5. 更新缓存
            updateBlackListCache(blackRecord);
            
            return UiResult.ok();
        } else {
            return UiResult.fail(-1, "系统繁忙，请稍后重试");
        }
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        return UiResult.fail(-1, "操作中断");
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
````
</augment_code_snippet>

## 3. CustomerHandler责任链模式

### 3.1 责任链处理器

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/chain/CustomerHandler.java" mode="EXCERPT">
````java
@Component
public class CustomerHandler {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    public RiskResult handleCustomerRisk(FilterParams params) {
        // 1. 查询风控名单
        List<RiskCustomerManage> riskList = riskCustomerManageMapper.getListByValue(params, new Date());
        
        if (CollectionUtils.isEmpty(riskList)) {
            return RiskResult.pass();
        }
        
        // 2. 按风险类型分组处理
        Map<Integer, List<RiskCustomerManage>> riskMap = riskList.stream()
            .collect(Collectors.groupingBy(RiskCustomerManage::getRiskType));
        
        // 3. 白名单优先级最高
        if (riskMap.containsKey(RiskCustomerRiskTypeEnum.white_list.getCode())) {
            return RiskResult.pass("命中白名单");
        }
        
        // 4. 检查黑名单
        if (riskMap.containsKey(RiskCustomerRiskTypeEnum.black_list.getCode())) {
            List<RiskCustomerManage> blackList = riskMap.get(RiskCustomerRiskTypeEnum.black_list.getCode());
            return RiskResult.reject("命中黑名单", buildRiskDetail(blackList));
        }
        
        // 5. 检查一对一名单
        if (riskMap.containsKey(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())) {
            return handleOneToOneList(riskMap.get(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()), params);
        }
        
        // 6. 检查其他风控类型
        return handleOtherRiskTypes(riskMap, params);
    }
}
````
</augment_code_snippet>

### 3.2 一对一名单处理逻辑

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/chain/CustomerHandler.java" mode="EXCERPT">
````java
private RiskResult handleOneToOneList(List<RiskCustomerManage> oneToOneList, FilterParams params) {
    for (RiskCustomerManage record : oneToOneList) {
        // 1. 检查绑定用户匹配
        if (StringUtils.isNotBlank(record.getBindUser()) 
            && record.getBindUser().equals(params.getPassengerCellphone())) {
            
            // 2. 检查车牌号匹配
            if (record.getCustomerValue().equals(params.getDriverCardNo())) {
                return RiskResult.reject("命中一对一禁止名单", 
                    String.format("车牌号%s与乘客%s禁止匹配", 
                        record.getCustomerValue(), 
                        DesensitizedUtil.mobilePhone(record.getBindUser())));
            }
        }
    }
    
    return RiskResult.pass();
}
````
</augment_code_snippet>

## 4. RiskEngine风控引擎

### 4.1 风控引擎核心逻辑

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/groovy/RiskEngineImpl.java" mode="EXCERPT">
````java
@Service
public class RiskEngineImpl implements RiskEngine {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private CustomerHandler customerHandler;
    
    @Override
    public RiskResult executeRisk(FilterParams params) {
        try {
            // 1. 参数预处理
            preprocessParams(params);
            
            // 2. 执行客户风控检查
            RiskResult customerResult = customerHandler.handleCustomerRisk(params);
            if (!customerResult.isPass()) {
                return customerResult;
            }
            
            // 3. 执行规则引擎检查
            RiskResult ruleResult = executeRuleEngine(params);
            if (!ruleResult.isPass()) {
                return ruleResult;
            }
            
            // 4. 执行策略检查
            RiskResult strategyResult = executeStrategyCheck(params);
            
            return strategyResult;
            
        } catch (Exception e) {
            log.error("风控引擎执行异常：{}", JsonUtils.json(params), e);
            return RiskResult.error("风控检查异常");
        }
    }
}
````
</augment_code_snippet>

### 4.2 多维度风控查询

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/groovy/RiskEngineImpl.java" mode="EXCERPT">
````java
private RiskResult executeCustomerCheck(CommonCustomerParam params) {
    // 1. 构建查询参数
    Date currentTime = new Date();
    
    // 2. 执行多维度查询
    List<RiskCustomerManage> riskList = riskCustomerManageMapper.getListByValueByGroup(params, currentTime);
    
    if (CollectionUtils.isEmpty(riskList)) {
        return RiskResult.pass();
    }
    
    // 3. 风控结果分析
    RiskAnalysisResult analysisResult = analyzeRiskList(riskList, params);
    
    // 4. 构建风控决策
    if (analysisResult.hasWhiteList()) {
        return RiskResult.pass("命中白名单");
    }
    
    if (analysisResult.hasBlackList()) {
        return RiskResult.reject("命中黑名单", analysisResult.getBlackReason());
    }
    
    if (analysisResult.hasOneToOneRestriction()) {
        return RiskResult.reject("命中一对一限制", analysisResult.getOneToOneReason());
    }
    
    return RiskResult.pass();
}
````
</augment_code_snippet>

## 5. FilterContext上下文处理

### 5.1 风控过滤上下文

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/context/FilterSceneContext.java" mode="EXCERPT">
````java
@Component
public class FilterSceneContext {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    public FilterResult filterByScene(FilterParams params, String sceneCode) {
        // 1. 根据场景获取风控配置
        SceneConfig sceneConfig = getSceneConfig(sceneCode);
        
        // 2. 执行风控名单检查
        List<RiskCustomerManage> riskList = queryRiskByScene(params, sceneConfig);
        
        // 3. 应用场景规则
        FilterResult result = applySceneRules(riskList, params, sceneConfig);
        
        // 4. 记录风控日志
        recordFilterLog(params, sceneCode, result);
        
        return result;
    }
    
    private List<RiskCustomerManage> queryRiskByScene(FilterParams params, SceneConfig sceneConfig) {
        QueryWrapper<RiskCustomerManage> queryWrapper = new QueryWrapper<>();
        
        // 1. 基础查询条件
        queryWrapper.eq("status", RiskCustomerStatusEnum.valid.getCode())
                   .gt("invalid_time", new Date());
        
        // 2. 根据场景配置添加查询条件
        if (sceneConfig.isCheckDriverCard() && StringUtils.isNotBlank(params.getDriverCardNo())) {
            queryWrapper.or().eq("customer_value", params.getDriverCardNo());
        }
        
        if (sceneConfig.isCheckUserPhone() && StringUtils.isNotBlank(params.getUserPhone())) {
            queryWrapper.or().eq("customer_value", params.getUserPhone());
        }
        
        if (sceneConfig.isCheckMemberId() && StringUtils.isNotBlank(params.getMemberId())) {
            queryWrapper.or().eq("customer_value", params.getMemberId());
        }
        
        // 3. 风险类型过滤
        if (!CollectionUtils.isEmpty(sceneConfig.getRiskTypes())) {
            queryWrapper.in("risk_type", sceneConfig.getRiskTypes());
        }
        
        return riskCustomerManageMapper.selectList(queryWrapper);
    }
}
````
</augment_code_snippet>

### 5.2 SFC场景特殊处理

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/context/FilterSfcContext.java" mode="EXCERPT">
````java
@Component
public class FilterSfcContext {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    public SfcFilterResult filterSfcOrder(SfcFilterParams params) {
        // 1. 查询顺风车相关风控名单
        List<RiskCustomerManage> riskList = querySfcRiskList(params);
        
        // 2. 特殊规则处理
        SfcFilterResult result = new SfcFilterResult();
        
        for (RiskCustomerManage risk : riskList) {
            switch (risk.getRiskType()) {
                case 1: // 黑名单
                    result.addBlackRisk(risk);
                    break;
                case 2: // 白名单
                    result.addWhiteRisk(risk);
                    break;
                case 7: // 一对一名单
                    if (matchOneToOneRule(risk, params)) {
                        result.addOneToOneRisk(risk);
                    }
                    break;
                case 8: // 顺风车特殊名单
                    result.addSfcSpecialRisk(risk);
                    break;
            }
        }
        
        // 3. 应用顺风车业务规则
        return applySfcBusinessRules(result, params);
    }
}
````
</augment_code_snippet>

## 6. 缓存集成优化

### 6.1 风控名单缓存策略

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/cache/RiskCustomerCacheService.java" mode="EXCERPT">
````java
@Service
public class RiskCustomerCacheService {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    public List<RiskCustomerManage> getCachedRiskList(String customerValue, Integer customerType) {
        String cacheKey = buildCacheKey(customerValue, customerType);
        
        // 1. 尝试从缓存获取
        RBucket<List<RiskCustomerManage>> bucket = redissonClient.getBucket(cacheKey);
        List<RiskCustomerManage> cachedList = bucket.get();
        
        if (cachedList != null) {
            return cachedList;
        }
        
        // 2. 缓存未命中，查询数据库
        List<RiskCustomerManage> dbList = riskCustomerManageMapper.selectList(
            new QueryWrapper<RiskCustomerManage>()
                .eq("customer_value", customerValue)
                .eq("customer_type", customerType)
                .eq("status", RiskCustomerStatusEnum.valid.getCode())
                .gt("invalid_time", new Date())
        );
        
        // 3. 写入缓存
        bucket.set(dbList, 30, TimeUnit.MINUTES);
        
        return dbList;
    }
    
    public void invalidateCache(RiskCustomerManage entity) {
        // 1. 删除相关缓存
        String cacheKey = buildCacheKey(entity.getCustomerValue(), entity.getCustomerType());
        redissonClient.getBucket(cacheKey).delete();
        
        // 2. 删除模糊匹配缓存
        String pattern = "risk_customer:*:" + entity.getCustomerValue() + ":*";
        redissonClient.getKeys().deleteByPattern(pattern);
    }
}
````
</augment_code_snippet>

### 6.2 热点数据预加载

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/cache/RiskCustomerCacheService.java" mode="EXCERPT">
````java
@Scheduled(fixedRate = 300000) // 每5分钟执行一次
public void preloadHotData() {
    try {
        // 1. 查询热点车牌号
        List<String> hotPlateNumbers = getHotPlateNumbers();
        
        // 2. 预加载到缓存
        for (String plateNumber : hotPlateNumbers) {
            List<RiskCustomerManage> riskList = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                    .eq("customer_value", plateNumber)
                    .eq("customer_type", RiskCustomerCustomerTypeEnum.driver_card_no.getCode())
                    .eq("status", RiskCustomerStatusEnum.valid.getCode())
                    .gt("invalid_time", new Date())
            );
            
            String cacheKey = buildCacheKey(plateNumber, RiskCustomerCustomerTypeEnum.driver_card_no.getCode());
            redissonClient.getBucket(cacheKey).set(riskList, 1, TimeUnit.HOURS);
        }
        
        log.info("热点数据预加载完成，加载数量：{}", hotPlateNumbers.size());
        
    } catch (Exception e) {
        log.error("热点数据预加载异常", e);
    }
}
````
</augment_code_snippet>

## 7. 规则引擎性能优化

### 7.1 批量查询优化

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/optimization/BatchRiskQueryService.java" mode="EXCERPT">
````java
@Service
public class BatchRiskQueryService {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    public Map<String, List<RiskCustomerManage>> batchQueryRisk(List<String> customerValues, Integer customerType) {
        if (CollectionUtils.isEmpty(customerValues)) {
            return new HashMap<>();
        }
        
        // 1. 批量查询
        List<RiskCustomerManage> allRiskList = riskCustomerManageMapper.selectList(
            new QueryWrapper<RiskCustomerManage>()
                .in("customer_value", customerValues)
                .eq("customer_type", customerType)
                .eq("status", RiskCustomerStatusEnum.valid.getCode())
                .gt("invalid_time", new Date())
        );
        
        // 2. 按客户值分组
        return allRiskList.stream()
            .collect(Collectors.groupingBy(RiskCustomerManage::getCustomerValue));
    }
}
````
</augment_code_snippet>

### 7.2 异步风控处理

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/async/AsyncRiskService.java" mode="EXCERPT">
````java
@Service
public class AsyncRiskService {
    @Async("riskExecutor")
    public CompletableFuture<RiskResult> asyncRiskCheck(FilterParams params) {
        try {
            // 1. 执行风控检查
            RiskResult result = executeRiskCheck(params);
            
            // 2. 记录风控日志
            recordRiskLog(params, result);
            
            return CompletableFuture.completedFuture(result);
            
        } catch (Exception e) {
            log.error("异步风控检查异常：{}", JsonUtils.json(params), e);
            return CompletableFuture.completedFuture(RiskResult.error("风控检查异常"));
        }
    }
}
````
</augment_code_snippet>

这个规则引擎集成分析展示了risk_customer_manage表如何与整个风控系统的规则引擎进行深度集成，实现实时风控检查、自动化处理和性能优化。
