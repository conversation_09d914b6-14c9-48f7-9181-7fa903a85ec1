# `/blackList/driver/list` 接口业务处理流程分析

## 1. 接口概述

`/blackList/driver/list` 是一个用于查询用户拉黑司机列表的接口，属于黑名单管理功能的一部分。该接口允许用户根据乘客手机号查询已被该乘客拉黑的司机列表。

### 1.1 接口基本信息

- **接口路径**：`/blackList/driver/list`
- **请求方式**：POST
- **所在控制器**：`BlackListApiController`
- **对应服务**：`BlackListService.listDriver()`
- **功能描述**：查询用户拉黑司机列表

### 1.2 请求参数

请求参数封装在 `DriverBlackListRequest` 类中：

```java
@Data
public class DriverBlackListRequest {
    private String traceId;
    //乘车人手机号
    private String passengerCellphone;
}
```

### 1.3 响应数据

响应数据为 `RiskCustomerManageListDTO` 对象列表，包含以下字段：

```java
@Data
public class RiskCustomerManageListDTO {
    private String customerValue;      // 客户值（车牌号）
    private String invalidTime;        // 有效期
    private String driverName;         // 司机名称
    private String orderId;            // 订单ID
    private String shieldTime;         // 拉黑时间
    private String driverCardNo;       // 司机车牌号
    private String startAddress;       // 上车点
    private String endAddress;         // 下车点
    private String useTime;            // 用车时间
}
```

## 2. 业务处理流程

### 2.1 流程图

```mermaid
graph TD
    A[接收请求] --> B{检查乘客手机号}
    B -->|为空| C[返回空结果]
    B -->|不为空| D[查询数据库]
    D --> E[查询RiskCustomerManage表]
    E --> F[过滤有效数据]
    F --> G[转换为DTO对象]
    G --> H[返回结果]
```

### 2.2 详细流程

1. **接收请求**：`BlackListApiController.listDriver()` 方法接收 `DriverBlackListRequest` 请求对象
2. **参数校验**：检查 `passengerCellphone` 是否为空
3. **数据查询**：
   - 如果手机号为空，直接返回空结果
   - 如果手机号不为空，查询 `risk_customer_manage` 表中与该手机号绑定且未过期的记录
4. **数据过滤**：过滤掉没有绑定订单或没有起始地址的记录
5. **数据转换**：将 `RiskCustomerManage` 对象转换为 `RiskCustomerManageListDTO` 对象
6. **返回结果**：将 DTO 对象列表作为响应返回

## 3. 核心代码分析

### 3.1 控制器层

`BlackListApiController.listDriver()` 方法负责接收请求并调用服务层方法：

```java
@RequestMapping("/driver/list")
public UiResultWrapper listDriver(@RequestBody DriverBlackListRequest request) {
    LoggerUtils.initLogMap("listDriver", "", request.getTraceId(), request.getPassengerCellphone());
    LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
    try {
        UiResult uiResult = service.listDriver(request);
        LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
        return UiResultWrapper.convert(uiResult);
    } catch (Exception e) {
        LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

### 3.2 服务层

`BlackListService.listDriver()` 方法负责业务逻辑处理：

```java
public UiResult listDriver(DriverBlackListRequest request) {
    if (StringUtils.isBlank(request.getPassengerCellphone())) {
        return UiResult.ok();
    }

    UiResult result = UiResult.ok();

    List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
            .eq("bind_user", request.getPassengerCellphone())
            .gt("invalid_time", new Date())
    );

    List<RiskCustomerManageListDTO> dtoList = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(manageList)) {
        for (RiskCustomerManage manage : manageList) {
            if (StringUtils.isBlank(manage.getBindOrder()) || StringUtils.isBlank(manage.getStartAddress())) {
                continue;
            }
            RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
            dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
            dto.setDriverName(manage.getDriverName());
            dto.setCustomerValue(manage.getCustomerValue());
            dto.setOrderId(manage.getBindOrder());
            dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
            dto.setStartAddress(manage.getStartAddress());
            dto.setEndAddress(manage.getEndAddress());
            dto.setUseTime(manage.getUseTime());
            dto.setDriverCardNo(manage.getCustomerValue());
            dtoList.add(dto);
        }
    }
    result.setData(dtoList);
    return result;
}
```

## 4. 数据表结构

### 4.1 risk_customer_manage 表

该接口主要使用 `risk_customer_manage` 表存储黑名单数据。表结构如下：

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| id | Long | 主键ID |
| customer_type | Integer | 客户类型（6表示司机/车牌号） |
| customer_value | String | 客户值（车牌号） |
| risk_type | Integer | 风险类型（1-黑名单，7-一对一名单等） |
| status | Integer | 状态（1-有效，2-失效，3-已删除） |
| ttl | Integer | 有效期限（天数，-1表示永久） |
| create_time | Date | 创建时间 |
| update_time | Date | 更新时间 |
| invalid_time | Date | 失效时间 |
| option_type | Integer | 操作类型（1-系统操作，2-人工操作，3-客户操作） |
| create_user | String | 创建人 |
| option_name | String | 操作人 |
| risk_remark | String | 风险备注（拉黑原因） |
| bind_user | String | 绑定用户（乘客手机号） |
| bind_order | String | 绑定订单 |
| supplier_name | String | 供应商名称 |
| start_address | String | 上车地址 |
| end_address | String | 下车地址 |
| use_time | String | 用车时间 |
| driver_name | String | 司机名称 |
| driver_id | String | 司机ID |
| member_id | String | 会员ID |

### 4.2 相关枚举类型

#### 4.2.1 RiskCustomerCustomerTypeEnum（客户类型枚举）

```java
public enum RiskCustomerCustomerTypeEnum {
    user_id(1, "用户/ID"),
    user_phone(2, "用户/手机号"),
    user_device_id(3, "用户/设备号"),
    user_unionid(4, "用户/unionid"),
    user_pay_account(5, "用户/支付账号"),
    car_number(6, "司机/车牌号"),
    hc_member_id(7,"司机/司机id"),
    hc_phone(8,"司机/手机号"),
    hc_id_card(9,"司机/身份证号"),
    user_cert_no(10, "用户/证件号");
}
```

#### 4.2.2 RiskCustomerRiskTypeEnum（风险类型枚举）

```java
public enum RiskCustomerRiskTypeEnum {
    black_list(1, "黑名单", RiskCustomerRiskTipTypeEnum.black),
    white_list(2, "白名单", RiskCustomerRiskTipTypeEnum.white),
    ban_coupon_list(3, "禁止领券名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_reward_list(4, "禁止奖励名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_send_list(5, "禁止派单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_receive_list(6, "禁止接单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_one_to_one_list(7, "一对一名单", RiskCustomerRiskTipTypeEnum.one2one),
    ban_register_list(8, "禁止认证名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_ync_receive_list(9, "禁止网约车接单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_bus_create_order(10, "禁止汽车票下单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_credit_auth_list(11, "禁止信用授权名单", RiskCustomerRiskTipTypeEnum.forbid),
    tx_black_list(21,"腾讯黑名单", RiskCustomerRiskTipTypeEnum.black),
    tx_ban_one_to_one_list(22, "腾讯一对一名单", RiskCustomerRiskTipTypeEnum.one2one);
}
```

#### 4.2.3 RiskCustomerStatusEnum（状态枚举）

```java
public enum RiskCustomerStatusEnum {
    valid(1, "有效"),
    invalid(2, "失效"),
    del_flag(3, "标记为已删除");
}
```

#### 4.2.4 RiskCustomerOptionTypeEnum（操作类型枚举）

```java
public enum RiskCustomerOptionTypeEnum {
    system(1, "系统操作"),
    user(2, "人工操作"),
    customer(3, "客户操作");
}
```

## 5. 数据来源与流向

### 5.1 数据来源

`risk_customer_manage` 表中的数据主要来源于以下几个入口：

1. **用户拉黑司机**：通过 `/blackList/driver/black` 接口，用户可以将司机拉入黑名单
   - 对应方法：`BlackListService.blackDriver()`
   - 请求参数：`DriverBlackRequest`

2. **客服拉黑司机**：通过 `/manage/blackList/driver/black` 接口，客服可以将司机拉入黑名单
   - 对应方法：`BlackListService.blackDriverFromManage()`
   - 请求参数：`DriverBlackFromManageRequest`

3. **外部系统同步**：通过 `/blackList/sync` 接口，外部系统（如腾讯出行）可以同步黑名单数据
   - 对应方法：`BlackListService.syncDriverBlack()`
   - 请求参数：`BlackSyncRequest`

4. **系统自动拉黑**：系统根据规则自动将司机拉入黑名单
   - 例如：`RiskDriverTask` 中的定时任务

### 5.2 数据流向

1. **查询黑名单**：
   - `/blackList/driver/list`：查询用户拉黑的司机列表
   - `/blackList/driver/checkIn`：检查司机是否被拉黑
   - `/blackList/batchQueryDriverBlack`：批量查询车牌黑名单

2. **移除黑名单**：
   - `/blackList/driver/remove`：用户取消拉黑司机
   - 对应方法：`BlackListService.removeDriver()`

3. **外部系统调用**：
   - `TrafficClient.driverShielding()`：调用外部系统拉黑司机
   - `TrafficClient.isBlackDriver()`：判断司机是否在黑名单中

## 6. 完整调用链

### 6.1 查询黑名单流程

```mermaid
sequenceDiagram
    Client->>+BlackListApiController: POST /blackList/driver/list
    BlackListApiController->>+BlackListService: listDriver(request)
    BlackListService->>+RiskCustomerManageMapper: selectList(QueryWrapper)
    RiskCustomerManageMapper-->>-BlackListService: List<RiskCustomerManage>
    BlackListService->>BlackListService: 转换为DTO对象
    BlackListService-->>-BlackListApiController: UiResult<List<RiskCustomerManageListDTO>>
    BlackListApiController-->>-Client: UiResultWrapper
```

### 6.2 拉黑司机流程

```mermaid
sequenceDiagram
    Client->>+BlackListApiController: POST /blackList/driver/black
    BlackListApiController->>+BlackListService: blackDriver(request)
    BlackListService->>+RiskCustomerManageMapper: selectList(QueryWrapper)
    RiskCustomerManageMapper-->>-BlackListService: List<RiskCustomerManage>
    BlackListService->>BlackListService: 检查是否已存在
    BlackListService->>+RiskCustomerManageMapper: insert/update
    RiskCustomerManageMapper-->>-BlackListService: 结果
    BlackListService-->>-BlackListApiController: UiResult
    BlackListApiController-->>-Client: UiResultWrapper
```

## 7. 总结

`/blackList/driver/list` 接口是黑名单管理功能的一部分，用于查询用户拉黑的司机列表。该接口通过查询 `risk_customer_manage` 表中与指定乘客手机号绑定且未过期的记录，返回已被拉黑的司机信息。

黑名单数据可以通过多种方式添加，包括用户拉黑、客服拉黑、外部系统同步和系统自动拉黑。黑名单数据可以通过用户取消拉黑或系统自动过期来移除。

该功能是风控系统的重要组成部分，用于保护用户安全和提升用户体验。
