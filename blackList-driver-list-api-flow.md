# `/blackList/driver/list` 接口调用流程详解

## 1. 接口调用流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/driver/list
    Controller->>Controller: 记录请求日志
    Controller->>Service: listDriver(request)
    
    Service->>Service: 检查乘客手机号是否为空
    alt 手机号为空
        Service-->>Controller: 返回空结果
    else 手机号不为空
        Service->>Mapper: selectList(QueryWrapper)
        Mapper->>DB: 执行SQL查询
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: List<RiskCustomerManage>
        
        Service->>Service: 过滤无效数据
        Service->>Service: 转换为DTO对象
        Service-->>Controller: UiResult<List<RiskCustomerManageListDTO>>
    end
    
    Controller->>Controller: 记录响应日志
    Controller-->>Client: UiResultWrapper
```

## 2. 接口调用步骤详解

### 2.1 客户端发起请求

客户端向服务器发送 POST 请求，请求路径为 `/blackList/driver/list`，请求体为 JSON 格式的 `DriverBlackListRequest` 对象：

```json
{
  "traceId": "123456789",
  "passengerCellphone": "13800138000"
}
```

### 2.2 控制器接收请求

`BlackListApiController` 的 `listDriver` 方法接收请求：

```java
@RequestMapping("/driver/list")
public UiResultWrapper listDriver(@RequestBody DriverBlackListRequest request) {
    LoggerUtils.initLogMap("listDriver", "", request.getTraceId(), request.getPassengerCellphone());
    LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
    try {
        UiResult uiResult = service.listDriver(request);
        LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
        return UiResultWrapper.convert(uiResult);
    } catch (Exception e) {
        LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

控制器首先记录请求日志，然后调用 `BlackListService` 的 `listDriver` 方法处理请求。

### 2.3 服务层处理请求

`BlackListService` 的 `listDriver` 方法处理请求：

```java
public UiResult listDriver(DriverBlackListRequest request) {
    if (StringUtils.isBlank(request.getPassengerCellphone())) {
        return UiResult.ok();
    }

    UiResult result = UiResult.ok();

    List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
            .eq("bind_user", request.getPassengerCellphone())
            .gt("invalid_time", new Date())
    );

    List<RiskCustomerManageListDTO> dtoList = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(manageList)) {
        for (RiskCustomerManage manage : manageList) {
            if (StringUtils.isBlank(manage.getBindOrder()) || StringUtils.isBlank(manage.getStartAddress())) {
                continue;
            }
            RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
            dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
            dto.setDriverName(manage.getDriverName());
            dto.setCustomerValue(manage.getCustomerValue());
            dto.setOrderId(manage.getBindOrder());
            dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
            dto.setStartAddress(manage.getStartAddress());
            dto.setEndAddress(manage.getEndAddress());
            dto.setUseTime(manage.getUseTime());
            dto.setDriverCardNo(manage.getCustomerValue());
            dtoList.add(dto);
        }
    }
    result.setData(dtoList);
    return result;
}
```

服务层首先检查乘客手机号是否为空，如果为空则直接返回空结果。如果不为空，则调用 `RiskCustomerManageMapper` 的 `selectList` 方法查询数据库。

### 2.4 数据访问层查询数据库

`RiskCustomerManageMapper` 的 `selectList` 方法查询数据库：

```java
List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
        .eq("bind_user", request.getPassengerCellphone())
        .gt("invalid_time", new Date())
);
```

这里使用 MyBatis-Plus 的 `QueryWrapper` 构建查询条件，查询 `bind_user` 等于乘客手机号且 `invalid_time` 大于当前时间的记录。

对应的 SQL 语句为：

```sql
SELECT * FROM risk_customer_manage
WHERE bind_user = #{passengerCellphone}
  AND invalid_time > NOW()
```

### 2.5 服务层处理查询结果

服务层接收到查询结果后，进行过滤和转换：

```java
List<RiskCustomerManageListDTO> dtoList = new ArrayList<>();
if (CollectionUtils.isNotEmpty(manageList)) {
    for (RiskCustomerManage manage : manageList) {
        if (StringUtils.isBlank(manage.getBindOrder()) || StringUtils.isBlank(manage.getStartAddress())) {
            continue;
        }
        RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
        dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
        dto.setDriverName(manage.getDriverName());
        dto.setCustomerValue(manage.getCustomerValue());
        dto.setOrderId(manage.getBindOrder());
        dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
        dto.setStartAddress(manage.getStartAddress());
        dto.setEndAddress(manage.getEndAddress());
        dto.setUseTime(manage.getUseTime());
        dto.setDriverCardNo(manage.getCustomerValue());
        dtoList.add(dto);
    }
}
```

首先过滤掉没有绑定订单或没有起始地址的记录，然后将 `RiskCustomerManage` 对象转换为 `RiskCustomerManageListDTO` 对象。

### 2.6 控制器返回响应

控制器接收到服务层的处理结果后，记录响应日志，然后将结果包装为 `UiResultWrapper` 对象返回给客户端：

```java
UiResult uiResult = service.listDriver(request);
LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
return UiResultWrapper.convert(uiResult);
```

### 2.7 客户端接收响应

客户端接收到服务器的响应，响应体为 JSON 格式的 `UiResultWrapper` 对象：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "customerValue": "粤B12345",
      "invalidTime": "2024-12-31 23:59:59",
      "driverName": "张三",
      "orderId": "YC123456789",
      "shieldTime": "2023-12-31 12:00:00",
      "driverCardNo": "粤B12345",
      "startAddress": "广州市天河区",
      "endAddress": "广州市白云区",
      "useTime": "2023-12-31 10:00:00"
    },
    {
      "customerValue": "粤A67890",
      "invalidTime": "2024-12-31 23:59:59",
      "driverName": "李四",
      "orderId": "YC987654321",
      "shieldTime": "2023-12-30 12:00:00",
      "driverCardNo": "粤A67890",
      "startAddress": "广州市越秀区",
      "endAddress": "广州市海珠区",
      "useTime": "2023-12-30 10:00:00"
    }
  ],
  "success": true
}
```

## 3. 关键代码分析

### 3.1 请求参数校验

```java
if (StringUtils.isBlank(request.getPassengerCellphone())) {
    return UiResult.ok();
}
```

这段代码检查乘客手机号是否为空，如果为空则直接返回空结果。这是一个简单的参数校验，确保查询条件有效。

### 3.2 数据库查询

```java
List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
        .eq("bind_user", request.getPassengerCellphone())
        .gt("invalid_time", new Date())
);
```

这段代码使用 MyBatis-Plus 的 `QueryWrapper` 构建查询条件，查询 `bind_user` 等于乘客手机号且 `invalid_time` 大于当前时间的记录。这确保只查询与指定乘客相关且未过期的黑名单记录。

### 3.3 数据过滤

```java
if (StringUtils.isBlank(manage.getBindOrder()) || StringUtils.isBlank(manage.getStartAddress())) {
    continue;
}
```

这段代码过滤掉没有绑定订单或没有起始地址的记录。这确保返回的黑名单记录包含完整的订单信息。

### 3.4 数据转换

```java
RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
dto.setDriverName(manage.getDriverName());
dto.setCustomerValue(manage.getCustomerValue());
dto.setOrderId(manage.getBindOrder());
dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
dto.setStartAddress(manage.getStartAddress());
dto.setEndAddress(manage.getEndAddress());
dto.setUseTime(manage.getUseTime());
dto.setDriverCardNo(manage.getCustomerValue());
dtoList.add(dto);
```

这段代码将 `RiskCustomerManage` 对象转换为 `RiskCustomerManageListDTO` 对象。这是一个典型的数据转换过程，将数据库实体对象转换为前端需要的数据传输对象。

### 3.5 异常处理

```java
try {
    UiResult uiResult = service.listDriver(request);
    LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
    return UiResultWrapper.convert(uiResult);
} catch (Exception e) {
    LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
    return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
} finally {
    LoggerUtils.removeAll();
}
```

这段代码处理可能发生的异常，记录错误日志，并返回错误响应。这确保即使发生异常，接口也能返回友好的错误信息。

## 4. 总结

`/blackList/driver/list` 接口的调用流程包括以下步骤：

1. 客户端发起请求
2. 控制器接收请求并记录日志
3. 服务层处理请求，包括参数校验、数据库查询、数据过滤和转换
4. 数据访问层查询数据库
5. 服务层处理查询结果
6. 控制器返回响应并记录日志
7. 客户端接收响应

整个流程遵循标准的三层架构（控制器层、服务层、数据访问层），代码结构清晰，职责分明，易于维护和扩展。
