# 风控名单管理系统分析 - car-risk-process 模块 (第2部分)

## Service 层

### 1. BlackDriverService

`BlackDriverService` 是司机黑名单屏蔽的服务类，实现了用户拉黑司机、取消拉黑等业务逻辑。

```java
@Service
@Slf4j
public class BlackDriverService {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    @Resource
    private CarOrderService carOrderService;

    /**
     * 用户拉黑司机
     * */
    public UiResult userShield(DriverBlackParam param) {
        UiResult uiResult = UiResult.ok();
        List<RiskCustomerManage> manages = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user", param.getPassengerCellphone())
                        .eq("customer_value", param.getDriverCardNo())
                        .gt("invalid_time", new Date())
        );
        manages = manages.stream().sorted(Comparator.comparing(RiskCustomerManage::getInvalidTime).reversed()).collect(Collectors.toList());
        RiskCustomerManage manage = CollectionUtils.isEmpty(manages) ? null : manages.get(0);
        
        //有效期内存在，则更新过期时间
        if (manage != null) {
            manage.setInvalidTime(DateUtil.addMonth(new Date(), 12));
            manage.setUpdateTime(new Date());
            manage.setBindOrder(param.getOrderId());
            this.riskCustomerManageMapper.updateById(manage);
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(manage.getId());
            record.setOperateType(1);//修改
            record.setCreateUser(manage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
            record.setRemark(param.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(manage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return uiResult;
        }
        //先看下当前有没有拉黑的
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
        riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
        riskCustomerManage.setCustomerValue(param.getDriverCardNo());
        riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
        riskCustomerManage.setTtl(RiskCustomerTtlEnum.one_year.getCode());
        riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.user_client.getCode());
        riskCustomerManage.setOptionName("用户");
        riskCustomerManage.setCreateUser("用户");
        riskCustomerManage.setRiskRemark(param.getRemark());
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setInvalidTime(DateUtil.addMonth(new Date(), 12));
        riskCustomerManage.setBindUser(param.getPassengerCellphone());//手机号
        riskCustomerManage.setBindOrder(param.getOrderId());
        riskCustomerManage.setDriverName(param.getDriverName());
        riskCustomerManage.setStartAddress(param.getStartAddress());
        riskCustomerManage.setEndAddress(param.getEndAddress());
        riskCustomerManage.setUseTime(param.getUseTime());
        //顺风车的去看下有没有driverId
        if (StringUtils.isNotBlank(param.getOrderId())) {
            // 获取订单信息
            // ...
        }
        this.riskCustomerManageMapper.insert(riskCustomerManage);
        RiskCustomerRecord record = new RiskCustomerRecord();
        record.setCustomerId(riskCustomerManage.getId());
        record.setOperateType(1);//新增
        record.setCreateUser(riskCustomerManage.getCreateUser());
        record.setOperateUser("用户");
        record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
        record.setRemark(param.getRemark());
        record.setCreateTime(new Date());
        record.setCustomerValue(riskCustomerManage.getCustomerValue());
        riskCustomerRecordMapper.insert(record);
        return uiResult;
    }

    /**
     * 用户移除1v1黑名单司机
     * */
    public UiResult userRemove(DriverBlackParam param) {
        UiResult result = UiResult.ok();
        RiskCustomerManage riskCustomerManage = this.riskCustomerManageMapper.selectOne(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user", param.getPassengerCellphone())
                        .eq("customer_value", param.getDriverCardNo())
                        .gt("invalid_time", new Date())
        );
        if (riskCustomerManage != null) {
            riskCustomerManage.setInvalidTime(new Date());
            riskCustomerManage.setUpdateTime(new Date());
            this.riskCustomerManageMapper.updateById(riskCustomerManage);
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(riskCustomerManage.getId());
            record.setOperateType(3);//删除
            record.setCreateUser(riskCustomerManage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
            record.setRemark(param.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(riskCustomerManage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
        }
        return result;
    }

    /**
     * 用户司机黑名单列表
     * */
    public UiResult getList(DriverBlackParam param) {
        // 获取黑名单列表
        // ...
    }

    public UiResult orderBlack(DriverBlackParam param) {
        // 检查订单是否在黑名单中
        // ...
    }
}
```

### 2. BlackListService

`BlackListService` 是黑名单管理的服务类，实现了用户拉黑司机、取消拉黑等业务逻辑。

```java
@Service
@Slf4j
public class BlackListService {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private HcCustomerMapper hcCustomerMapper;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private RedissonClient redissonClient;

    private List<Integer> driverBlackTypes = Stream.of(black_list.getCode(), ban_receive_list.getCode(), ban_ync_receive_list.getCode()).collect(Collectors.toList());

    /**
     * 用户拉黑司机
     * riskType：1v1
     */
    public UiResult blackDriver(DriverBlackRequest request) throws BizException {
        UiResult uiResult = UiResult.ok();
        CheckUtil.checkNotBlank(request.getPassengerCellphone(), "乘车人手机号不可为空");
        CheckUtil.checkNotBlank(request.getDriverCardNo(), "车牌号不可为空");

        // 查看当前司机车牌是否已存在（车牌、客户类型、名单类型、数据有效期）
        List<RiskCustomerManage> manages = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user", request.getPassengerCellphone())
                        .eq("customer_value", request.getDriverCardNo())
                        .eq("risk_type", ban_one_to_one_list.getCode()) // 客户类型
                        .eq("customer_type", RiskCustomerCustomerTypeEnum.car_number.getCode())// 名单类型
                        .eq("status", 1) // 有效名单
                        .gt("invalid_time", new Date())
        );

        manages = manages.stream().sorted(Comparator.comparing(RiskCustomerManage::getInvalidTime).reversed()).collect(Collectors.toList());
        RiskCustomerManage manage = CollectionUtils.isEmpty(manages) ? null : manages.get(0);

        //有效期内存在，则更新过期时间
        if (manage != null) {
            manage.setInvalidTime(DateUtil.addMonth(new Date(), 12));
            manage.setUpdateTime(new Date());
            manage.setBindOrder(request.getOrderId());
            this.riskCustomerManageMapper.updateById(manage);
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(manage.getId());
            record.setOperateType(1);//修改
            record.setCreateUser(manage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
            record.setRemark(request.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(manage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return uiResult;
        }

        //先看下当前有没有拉黑的
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(ban_one_to_one_list.getCode());
        riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
        riskCustomerManage.setCustomerValue(request.getDriverCardNo());
        riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
        riskCustomerManage.setTtl(RiskCustomerTtlEnum.one_year.getCode());
        riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.user_client.getCode());
        riskCustomerManage.setOptionName("用户");
        riskCustomerManage.setCreateUser("用户");
        riskCustomerManage.setRiskRemark(request.getRemark());
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setInvalidTime(DateUtil.addMonth(new Date(), 12));
        riskCustomerManage.setBindUser(request.getPassengerCellphone());//手机号
        riskCustomerManage.setBindOrder(request.getOrderId());
        riskCustomerManage.setDriverName(request.getDriverName());
        riskCustomerManage.setStartAddress(request.getStartAddress());
        riskCustomerManage.setEndAddress(request.getEndAddress());
        riskCustomerManage.setUseTime(request.getUseTime());

        // 获取订单信息
        // ...

        this.riskCustomerManageMapper.insert(riskCustomerManage);
        RiskCustomerRecord record = new RiskCustomerRecord();
        record.setCustomerId(riskCustomerManage.getId());
        record.setOperateType(1);//新增
        record.setCreateUser(riskCustomerManage.getCreateUser());
        record.setOperateUser("用户");
        record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
        record.setRemark(request.getRemark());
        record.setCreateTime(new Date());
        record.setCustomerValue(riskCustomerManage.getCustomerValue());
        riskCustomerRecordMapper.insert(record);
        return uiResult;
    }

    /**
     * 用户取消拉黑司机
     * riskType：1v1
     */
    public UiResult removeDriver(DriverRemoveRequest request) {
        // 取消拉黑司机
        // ...
    }

    /**
     * 司机是否被拉黑过
     * riskType：1v1
     */
    public UiResult checkDriverIn(DriverCheckInRequest request) {
        // 检查司机是否被拉黑
        // ...
    }

    /**
     * 客服拉黑司机
     * riskType：1v1、全部
     */
    public UiResult blackDriverFromManage(DriverBlackFromManageRequest request) {
        // 客服拉黑司机
        // ...
    }

    // 其他方法...
}
```
