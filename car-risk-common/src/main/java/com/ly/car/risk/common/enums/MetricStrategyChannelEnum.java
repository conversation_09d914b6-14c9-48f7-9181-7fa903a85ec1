package com.ly.car.risk.common.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MetricStrategyChannelEnum {
    
    TC_LITTLE("852", "同程小程序"),
    RIDE("1424", "乘车呗"),
    ANDROID("434", "app（安卓）"),
    IOS("433", "app（ios）"),
    ALIPAY("10088", "支付宝小程序"),
    WX_LITTLE("502", "微信小程序"),
    TXTRAVEL("1421", "腾讯出行小程序(已废弃)"),
    TX_TRAVEL("1343", "腾讯出行"),
    BAIDU("1051", "百度小程序"),
    TRANSPORT_WORLD("10119", "客运天下小程序"),
    MADA_IOS("10178", "马达APP-ios"),
    MADA_ANDROID("10177", "马达APP-android"),
    ALI_INDEPENDENT_LITTLE("10190", "支付宝独立小程序"),
    WX_INDEPENDENT_LITTLE("8522", "微信独立小程序"),
    RIDE_LITTLE("1443", "乘车呗小程序"),
    TOUCH("432", "touch"),
    DT_DISTRIBUTION("789", "大唐商旅分销"),
    TC_DISTRIBUTION("883", "同程小分销"),
    PC("1", "pc"),
    ALL("all", "全部");;
    
    public final String code;
    public final String desc;
    
    public static String getDescByCode(String code) {
        MetricStrategyChannelEnum[] values = MetricStrategyChannelEnum.values();
        for (MetricStrategyChannelEnum e : values) {
            if (Objects.equals(e.code, code)) {
                return e.desc;
            }
        }
        return "";
    }
}
