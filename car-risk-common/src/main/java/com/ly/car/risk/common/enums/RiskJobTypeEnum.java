package com.ly.car.risk.common.enums;

import java.util.*;

import lombok.Getter;

@Getter
public enum RiskJobTypeEnum {

    SUPPLIER_COUPON("当日单个供应商核销金额较大", RiskAlertApproveSceneEnum.MARKETING),

    SC_DAILY_COUNT("当日单供应商核销张数较大", RiskAlertApproveSceneEnum.MARKETING),

    SC_DAILY_BATCH_COUNT("当日单供应商核销单张优惠券张数较大", RiskAlertApproveSceneEnum.MARKETING),

    DRIVER_COUPON("当日单个司机核销金额较大", RiskAlertApproveSceneEnum.MARKETING),

    USER_COUPON("近一小时单用户核销金额较大", RiskAlertApproveSceneEnum.MARKETING),

    UC_DAILY_BATCH_COUNT("当日单用户核销张数较大", RiskAlertApproveSceneEnum.MARKETING),

    DC_DAILY_COUNT("当日单个司机核销张数较大", RiskAlertApproveSceneEnum.MARKETING),

    DC_DAILY_BATCH_COUNT("当日单个司机核销单张优惠券张数较大", RiskAlertApproveSceneEnum.MARKETING),

    DO_3DAY_CANCEL_COUNT("近3天司机存在套取消费的订单数较多", RiskAlertApproveSceneEnum.MARKETING),

    USER_DRIVER_COUPON("近一小时单个用户固定司机核销订单较多", RiskAlertApproveSceneEnum.MARKETING),
    
    DRIVER_PUNISH_ORDER("当日单司机产生中风险追款金额", RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND),
    
    DRIVER_PUNISH_MORE_ORDER("当日单司机产生高风险追款金额", RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND),
    
    MT_DAILY_WITHDRAW_LARGE("萌艇当日单司机提现金额较大", RiskAlertApproveSceneEnum.MT),
    
    DRIVER_INFO_DAILY_COUNT("昨日司机认证校验次数超过阈值", RiskAlertApproveSceneEnum.CALL_STATISTICS),

    DRIVER_INFO_MONTH_COUNT("当月司机认证校验次数超过阈值", RiskAlertApproveSceneEnum.CALL_STATISTICS),

    DAILY_TENCENT_ASR_COUNT("昨日腾讯语音转文本调用小时数超过阈值",RiskAlertApproveSceneEnum.CALL_STATISTICS),

    MONTH_TENCENT_ASR_COUNT("当月腾讯语音转文本调用小时数超过阈值",RiskAlertApproveSceneEnum.CALL_STATISTICS),

    DAILY_IVR_COUNT("昨日ivr外呼调用次数超过阈值",RiskAlertApproveSceneEnum.CALL_STATISTICS),

    MONTH_IVR_COUNT("当月ivr外呼调用次数超过阈值",RiskAlertApproveSceneEnum.CALL_STATISTICS),

    USER_VIEW_SENSITIVE_DAILY("当日该员工频繁查看敏感信息", RiskAlertApproveSceneEnum.SENSITIVE_ACTION),

    USER_VIEW_SENSITIVE_HOUR("员工短时间高频查看敏感信息", RiskAlertApproveSceneEnum.SENSITIVE_ACTION),

    ;


    private String desc;

    private RiskAlertApproveSceneEnum category;

    private static Map<RiskAlertApproveSceneEnum, List<RiskJobTypeEnum>> jobMap = new HashMap<>();

    RiskJobTypeEnum(String desc, RiskAlertApproveSceneEnum category) {
        this.desc = desc;
        this.category = category;
    }

    static {
        for (RiskJobTypeEnum riskJob : values()) {
            List<RiskJobTypeEnum> jobList;
            if (jobMap.containsKey(riskJob.category)) {
                jobList = jobMap.get(riskJob.category);
                jobList.add(riskJob);
            } else {
                jobList = new ArrayList<>();
                jobList.add(riskJob);
                jobMap.put(riskJob.category, jobList);
            }
        }
    }

    public static List<RiskJobTypeEnum> findMktJob() {
        List<RiskJobTypeEnum> list = new ArrayList<>();
        for (RiskJobTypeEnum typeEnum : values()) {
            if (typeEnum.category.equals(RiskAlertApproveSceneEnum.MARKETING)) {
                list.add(typeEnum);
            }
        }
        return list;
    }
    
    public static String getDescByCode(String code) {
        RiskJobTypeEnum[] values = RiskJobTypeEnum.values();
        for (RiskJobTypeEnum e : values) {
            if (Objects.equals(e.name(), code)) {
                return e.desc;
            }
        }
        return "";
    }

    public static RiskJobTypeEnum findByName(String name) {
        for (RiskJobTypeEnum e : values()) {
            if (name.equals(e.name())) {
                return e;
            }
        }
        return null;
    }

    public static List<RiskJobTypeEnum> findJobByApproveType(RiskAlertApproveSceneEnum sceneEnum) {
        if (null == sceneEnum) {
            return new ArrayList<>();
        }
        return jobMap.get(sceneEnum);
    }
}
