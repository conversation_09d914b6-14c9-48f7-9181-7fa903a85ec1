package com.ly.car.risk.common.enums;

import com.ly.car.risk.common.dto.CommonOpt;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

@Getter
public enum VoiceProductTypeEnum {
    
    VIRTUAL_PHONE("VIRTUAL_PHONE", "虚拟通话"),
    
    // 暂时使用，后续拆成 IM、VIRTUAL_PHONE_SENSITIVE_WORDS、WOMEN_SENSITIVE_TIME
    OUTER_CALL("OUTER_CALL", "外呼(暂时使用)"),
    
    IM("IM", "IM"),
    
    VIRTUAL_PHONE_SENSITIVE_WORDS("VIRTUAL_PHONE_SENSITIVE_WORDS", "虚拟通话敏感词"),
    
    WOMEN_SENSITIVE_TIME("WOMEN_SENSITIVE_TIME", "女性敏感时间"),
    ;
    
    private final String code;
    
    private final String desc;
    
    VoiceProductTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(String code) {
        VoiceProductTypeEnum[] values = VoiceProductTypeEnum.values();
        for (VoiceProductTypeEnum e : values) {
            if (Objects.equals(e.code, code)) {
                return e.desc;
            }
        }
        return "";
    }
    
    public static List<CommonOpt> getAllEnum() {
        List<CommonOpt> list = new ArrayList<>();
        for (VoiceProductTypeEnum data : values()) {
            CommonOpt opt = new CommonOpt();
            opt.setLabel(data.desc);
            opt.setValue(data.code);
            list.add(opt);
        }
        return list;
    }
}
