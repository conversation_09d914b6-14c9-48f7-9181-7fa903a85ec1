package com.ly.car.risk.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MetricStrategyRiskTypeEnum {
    
    PERSONAL_SAFE("1-1", "人身安全"),
    ABNORMAL_TRACK("1-2", "轨迹异常"),
    DRIVER_PROTECTION("1-3", "司机保护"),
    PASSENGER_PROTECTION("1-4", "乘客保护"),
    BRUSH_ORDER("2-1", "恶意下单"),
    PRICE_ABNORMAL("2-2", "询价异常"),
    COUPON("2-3", "薅券"),
    SUBSIDY("2-4", "套补贴"),
    QUICK_ORDER("2-5", "快速划单"),
    FEE_OBJECTION("2-6", "费用异议"),
    MALICIOUS_REFUND("2-7", "恶意退款"),
    MALICIOUS_COMPENSATION("2-8", "恶意赔付"),
    ACCOUNT_ABNORMAL("2-9", "账户异常"),
    UNKNOWN("2-10", "未知错误")
    
    ;
    
    public final String code;
    public final String desc;
}
