package com.ly.car.risk.common.enums;

import com.ly.car.risk.common.dto.CommonOpt;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderProductLineEnum {
    
    SFC("SFC", "顺风车"),
    YNC("YNC", "网约车"),

    
    ;
    
    public final String code;
    public final String desc;
    
    public static String getDescByCode(String code) {
        OrderProductLineEnum[] values = OrderProductLineEnum.values();
        for (OrderProductLineEnum e : values) {
            if (Objects.equals(e.code, code)) {
                return e.desc;
            }
        }
        return "";
    }
    
    public static List<CommonOpt> getAllEnum() {
        List<CommonOpt> list = new ArrayList<>();
        for (OrderProductLineEnum data : values()) {
            CommonOpt opt = new CommonOpt();
            opt.setLabel(data.desc);
            opt.setValue(data.code);
            list.add(opt);
        }
        return list;
    }
}
