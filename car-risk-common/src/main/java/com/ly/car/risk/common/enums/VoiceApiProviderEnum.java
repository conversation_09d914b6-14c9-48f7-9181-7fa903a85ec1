package com.ly.car.risk.common.enums;

import com.ly.car.risk.common.dto.CommonOpt;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

@Getter
public enum VoiceApiProviderEnum {
    
    TX_VOICE("TX_VOICE", "腾讯语音转文本"),
    
    IVR_CALL("IVR_CALL", "IVR自动外呼"),
    
    ;
    
    private final String code;
    
    private final String desc;
    
    VoiceApiProviderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(String code) {
        VoiceApiProviderEnum[] values = VoiceApiProviderEnum.values();
        for (VoiceApiProviderEnum e : values) {
            if (Objects.equals(e.code, code)) {
                return e.desc;
            }
        }
        return "";
    }
    
    public static List<CommonOpt> getAllEnum() {
        List<CommonOpt> list = new ArrayList<>();
        for (VoiceApiProviderEnum data : values()) {
            CommonOpt opt = new CommonOpt();
            opt.setLabel(data.desc);
            opt.setValue(data.code);
            list.add(opt);
        }
        return list;
    }
}
