package com.ly.car.risk.common.enums;

/**
 * ApproveStatusEnum
 * 审批状态
 */
public enum ApproveStatusEnum {

    /**
     * 未知
     */
    UNKNOWN(-999, "未知"),

    /**
     * 无需审批
     */
    NO_APPROVAL(0, "无需审批"),

    /**
     * 待送审
     */
    WAITING_SEND(1, "待送审"),

    /**
     * 审批中
     */
    APPROVING(2, "审批中"),

    /**
     * 审批成功
     */
    APPROVED(3, "审批成功"),

    /**
     * 审批驳回
     */
    REJECT(4, "审批失败");

    /**
     * 枚举编码
     */
    private final int code;

    /**
     * 枚举描述
     */
    private final String desc;

    /**
     * 私有构造函数
     */
    ApproveStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取枚举类型
     *
     * @param code 枚举码
     * @return 枚举类型
     */
    public static ApproveStatusEnum getEnumByCode(int code) {
        for (ApproveStatusEnum e : ApproveStatusEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return UNKNOWN;
    }

    /**
     * 获取枚举编码
     *
     * @return 枚举编码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取枚举描述
     *
     * @return 枚举描述
     */
    public String getDesc() {
        return desc;
    }
}