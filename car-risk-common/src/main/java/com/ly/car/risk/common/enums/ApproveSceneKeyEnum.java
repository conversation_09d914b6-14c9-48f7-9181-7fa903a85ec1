package com.ly.car.risk.common.enums;

/**
 * ApproveStatusEnum
 * 审批场景key
 */
public enum ApproveSceneKeyEnum {

    /**
     * 用车风控策略
     */
    CAR_RISK_METRIC_STRATEGY("carRisk/metricStrategy", "用车风控策略"),
    ;


    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String desc;

    /**
     * 私有构造函数
     */
    ApproveSceneKeyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    /**
     * 获取枚举编码
     *
     * @return 枚举编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取枚举描述
     *
     * @return 枚举描述
     */
    public String getDesc() {
        return desc;
    }
}