package com.ly.car.risk.process.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 风控检查流水线，负责组织和执行处理器
 */
@Service
@Slf4j
public class RiskCheckPipeline {
    
    private final List<RiskCheckProcessor> processors;
    
    public RiskCheckPipeline(List<RiskCheckProcessor> processors) {
        this.processors = processors.stream()
            .sorted(Comparator.comparing(RiskCheckProcessor::getOrder))
            .collect(Collectors.toList());
        
        log.info("风控检查流水线初始化完成，处理器列表: {}", 
            processors.stream()
                .map(p -> p.getClass().getSimpleName() + "(" + p.getOrder() + ")")
                .collect(Collectors.joining(", ")));
    }
    
    /**
     * 执行风控检查流水线
     * 
     * @param context 风控检查上下文
     */
    public void process(RiskCheckContext context) {
        for (RiskCheckProcessor processor : processors) {
            long startTime = System.currentTimeMillis();
            String processorName = processor.getClass().getSimpleName();
            
            try {
                processor.process(context);
            } finally {
                long costTime = System.currentTimeMillis() - startTime;
                log.info("处理器[{}]执行完成，耗时: {}ms", processorName, costTime);
            }
            
            if (!context.shouldContinue()) {
                log.info("处理器[{}]中断流水线执行，结果: {}", processorName, context.getResult().getRiskMsg());
                break;
            }
        }
    }
}
