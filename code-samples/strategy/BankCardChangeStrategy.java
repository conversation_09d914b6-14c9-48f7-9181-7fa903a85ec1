package com.ly.car.risk.process.strategy.specialscene.impl;

import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.service.rule.mtGroup.MtDriverBankCardService;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.strategy.specialscene.SpecialSceneStrategy;
import com.ly.car.risk.process.utils.StrategyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 银行卡变更特殊场景策略
 */
@Component
@Slf4j
public class BankCardChangeStrategy implements SpecialSceneStrategy {
    
    private final MtDriverBankCardService mtDriverBankCardService;
    
    public BankCardChangeStrategy(MtDriverBankCardService mtDriverBankCardService) {
        this.mtDriverBankCardService = mtDriverBankCardService;
    }
    
    @Override
    public RiskSceneResult check(UnifyCheckRequest request) {
        // 银行卡校验
        RiskSceneResult bankCardCheckResult = mtDriverBankCardService.bankCardCheck(request);
        if (bankCardCheckResult != null && bankCardCheckResult.isRiskFlag()) {
            log.info("银行卡变更校验不通过: {}", bankCardCheckResult.getRiskMsg());
            return bankCardCheckResult;
        }
        
        log.info("银行卡变更校验通过");
        return null;
    }
    
    @Override
    public boolean supports(UnifyCheckRequest request) {
        // 只支持萌艇业务线的银行卡变更场景
        boolean isBankCardChange = StringUtils.equals(request.getScene(), StrategySceneEnum.BANKCARD_CHANGE.getScene());
        boolean isMT = StringUtils.equals(request.getProductLine(), ProductLineEnum.MT.getCode());
        
        // 必须包含必要的参数
        boolean hasRequiredParams = StringUtils.isNotBlank(StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NAME))
                && StringUtils.isNotBlank(StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO))
                && StringUtils.isNotBlank(StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID))
                && StringUtils.isNotBlank(StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.BANK_CARD_NO));
        
        return isBankCardChange && isMT && hasRequiredParams;
    }
}
