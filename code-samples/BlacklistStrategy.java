package com.ly.car.risk.process.strategy.blacklist;

import com.ly.car.risk.process.controller.request.UnifyCheckRequest;

/**
 * 黑名单策略接口
 */
public interface BlacklistStrategy {
    
    /**
     * 检查是否命中黑名单
     * 
     * @param request 统一风控检查请求
     * @return 黑名单检查结果
     */
    BlacklistCheckResult check(UnifyCheckRequest request);
    
    /**
     * 是否支持该请求
     * 
     * @param request 统一风控检查请求
     * @return 是否支持
     */
    boolean supports(UnifyCheckRequest request);
}
