package com.ly.car.risk.process.strategy.blacklist;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 黑名单检查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlacklistCheckResult {
    
    /**
     * 是否命中黑名单
     */
    private boolean hit;
    
    /**
     * 命中的规则类型
     */
    private String ruleType;
    
    /**
     * 命中的客户值
     */
    private String customerValue;
    
    /**
     * 命中的客户类型
     */
    private Integer customerType;
    
    /**
     * 提示信息
     */
    private String message;
    
    /**
     * 创建命中结果
     */
    public static BlacklistCheckResult hit(String ruleType, String customerValue, Integer customerType, String message) {
        return BlacklistCheckResult.builder()
            .hit(true)
            .ruleType(ruleType)
            .customerValue(customerValue)
            .customerType(customerType)
            .message(message)
            .build();
    }
    
    /**
     * 创建未命中结果
     */
    public static BlacklistCheckResult notHit() {
        return BlacklistCheckResult.builder()
            .hit(false)
            .build();
    }
}
