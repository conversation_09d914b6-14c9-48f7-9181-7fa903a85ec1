package com.ly.car.risk.process.processor;

import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.core.AbstractRiskCheckProcessor;
import com.ly.car.risk.process.core.RiskCheckContext;
import com.ly.car.risk.process.strategy.blacklist.BlacklistCheckResult;
import com.ly.car.risk.process.strategy.blacklist.BlacklistStrategy;
import com.ly.car.risk.process.strategy.blacklist.BlacklistStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 黑名单检查处理器
 */
@Component
@Slf4j
public class BlacklistCheckProcessor extends AbstractRiskCheckProcessor {
    
    private final BlacklistStrategyFactory strategyFactory;
    
    public BlacklistCheckProcessor(BlacklistStrategyFactory strategyFactory) {
        this.strategyFactory = strategyFactory;
    }
    
    @Override
    protected void doProcess(RiskCheckContext context) {
        UnifyCheckRequest request = context.getRequest();
        
        // 获取黑名单策略
        BlacklistStrategy strategy = strategyFactory.getStrategy(request);
        
        log.info("使用黑名单策略: {}", strategy.getClass().getSimpleName());
        
        // 执行黑名单检查
        BlacklistCheckResult result = strategy.check(request);
        
        // 处理结果
        if (result.isHit()) {
            log.info("命中黑名单: {}", result);
            
            context.setResult(true, result.getMessage());
            context.setAttribute("hitType", 1);
            context.setAttribute("hitRule", result.getRuleType());
            context.setAttribute("customerValue", result.getCustomerValue());
            context.setAttribute("customerType", result.getCustomerType());
        } else {
            log.info("未命中黑名单");
        }
    }
    
    @Override
    public int getOrder() {
        return 200;
    }
    
    @Override
    public boolean supports(RiskCheckContext context) {
        return true;
    }
}
