public List<Integer> findDisappeareNums(int[] nums) {
    List<Integer> list = new ArrayList<>();
    //将对应元素放到它对应的index上,eg:数字 x 放到索引 x-1 的位置,然后遍历数组，某个位置的数不等于该index+1,则该索引对应的数缺失
    for (int i = 0; i < nums.length; i++) {
        //获取当前数字应该在的索引位置
        int index = Math.abs(nums[i]) - 1;
        if (nums[index] > 0){
            //标记已出现的数,将已出现的数置为负数，下次扫描时不用再处理，最终index位置不为负数的数就是缺失的
            nums[index] = -nums[index];
        }
    }
    for (int i = 0; i < nums.length; i++) {
        if (nums[i] > 0){
            list.add(i+1);
        }
    }
    return list;
}

public int findMissingNumberInSortedArray(int[] nums, int k) {
    /*
    1.先找规律 对于每个位置i，可计算出nums[i](含)之前有多少个数字缺失
      计算当前缺失元素个数；
      missing(i) = nums[i]-(i+1)
    eg:
        nums[0]=2,本应该是1，但实际是2，说明前面缺了2-(0+1)=1个数字
        nums[3]=7,本应该是4，但实际是7，说明前面缺了7-(3+1)=3个数字
    如果missing(mid) < k，说明要找的数在mid的右边，反之在左边
     */
    int left = 0;
    int right = nums.length - 1;
    while (left <= right) {
        int mid = left + (right - left) / 2;
        int missing = nums[mid] - (mid + 1);
        if (missing < k) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    //最终结果为：k + left 的位置
    return left + k;


}