# risk_customer_manage表业务流程分析 - 04 Controller层接口详细分析

## 1. Controller层架构概述

risk_customer_manage表相关的Controller层分布在两个模块中，各自承担不同的职责：

- **car-risk-manage模块**：提供管理后台的HTTP接口
- **car-risk-process模块**：提供业务处理的HTTP接口和定时任务接口

## 2. car-risk-manage模块Controller分析

### 2.1 RiskCustomerController接口概览

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RestController
@RequestMapping("/riskListManage/riskCustomer")
public class RiskCustomerController {
    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;
}
````
</augment_code_snippet>

### 2.2 核心接口详细分析

#### 2.2.1 分页查询接口

**接口定义：**
```
POST /riskListManage/riskCustomer/getList
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/getList")
public Pagination<RiskCustomerManageDto> getList(@RequestBody RiskCustomerListParams query) {
    return riskCustomerService.getListPage(query);
}
````
</augment_code_snippet>

**请求参数（RiskCustomerListParams）：**
- `status` - 状态筛选（1-有效，2-失效，3-已删除）
- `customerType` - 客户类型筛选
- `customerValue` - 客户值精确查询
- `riskType` - 风险类型筛选
- `ttl` - 有效期筛选
- `optionType` - 操作类型筛选
- `optionName` - 操作人筛选
- `startTime/endTime` - 创建时间范围
- `bindUser` - 绑定用户筛选
- `supplierName` - 供应商名称筛选
- `page/size` - 分页参数

**响应数据（RiskCustomerManageDto）：**
- 基础字段：id、customerType、customerValue、riskType等
- 显示字段：枚举值转中文描述
- 脱敏字段：手机号、身份证号脱敏处理
- 状态字段：disabled（是否禁用）、delRemark（删除备注）

#### 2.2.2 新增风控名单接口

**接口定义：**
```
POST /riskListManage/riskCustomer/add
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/add")
public Boolean add(@RequestBody RiskCustomerAddParams params) {
    return riskCustomerService.add(params);
}
````
</augment_code_snippet>

**请求参数（RiskCustomerAddParams）：**
- `customerType` - 客户类型（必填）
- `customerValue` - 客户值（必填）
- `riskType` - 风险类型（必填）
- `ttl` - 有效期（必填）
- `riskRemark` - 风险备注
- `bindUser` - 绑定用户（一对一名单必填）
- `memberId` - 会员ID
- `supplierName` - 供应商名称
- `invalidTime` - 自定义失效时间（ttl=0时必填）

#### 2.2.3 删除风控名单接口

**接口定义：**
```
POST /riskListManage/riskCustomer/delete
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/delete")
public Boolean delete(@RequestBody BaseEntityParams req, UserInfo userInfo) {
    return riskCustomerService.delete(req.getId(), userInfo, req.getDelRemark());
}
````
</augment_code_snippet>

**请求参数（BaseEntityParams）：**
- `id` - 记录ID（必填）
- `delRemark` - 删除备注

**用户信息（UserInfo）：**
- 通过拦截器自动注入当前登录用户信息
- 包含用户名、工号等身份信息

#### 2.2.4 Excel批量导入接口

**接口定义：**
```
POST /riskListManage/riskCustomer/import
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/import")
public Integer excelImport(MultipartFile file, UserInfo userInfo){
    return riskCustomerService.excelImport(file, userInfo);
}
````
</augment_code_snippet>

**请求参数：**
- `file` - Excel文件（MultipartFile类型）
- `userInfo` - 当前用户信息（自动注入）

**返回值：**
- 成功导入的记录数量

#### 2.2.5 Excel批量删除接口

**接口定义：**
```
POST /riskListManage/riskCustomer/deleteImport
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/deleteImport")
public Integer excelDeleteImport(MultipartFile file, UserInfo userInfo){
    return riskCustomerService.excelDeleteImport(file, userInfo);
}
````
</augment_code_snippet>

#### 2.2.6 数据导出接口

**接口定义：**
```
POST /riskListManage/riskCustomer/export
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/export")
public String exportList(@RequestBody RiskCustomerListParams query) {
    // 1. 查询导出数据
    List<RiskCustomerManageDto> exportData = riskCustomerService.exportList(query);
    
    // 2. 生成Excel文件
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    String fileName = "风控名单列表_" + System.currentTimeMillis() + ".xlsx";
    EasyExcel.write(byteArrayOutputStream, RiskCustomerManageDto.class)
        .registerConverter(new BillDateConverter())
        .sheet("sheet1")
        .doWrite(exportData);
    
    // 3. 上传到文件服务器
    String upload = FileUploadCephUtil.upload(fileName, byteArrayOutputStream);
    
    // 4. 记录敏感操作
    addSensitiveAction(upload);
    
    return upload; // 返回文件下载链接
}
````
</augment_code_snippet>

#### 2.2.7 模板下载接口

**导入模板下载：**
```
GET /riskListManage/riskCustomer/exportTemplate
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("exportTemplate")
public void downloadTemplate(HttpServletResponse response){
    riskCustomerService.downloadTemplate(response);
}
````
</augment_code_snippet>

**删除模板下载：**
```
GET /riskListManage/riskCustomer/exportDeleteTemplate
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("exportDeleteTemplate")
public void downloadDeleteTemplate(HttpServletResponse response){
    riskCustomerService.downloadDeleteTemplate(response);
}
````
</augment_code_snippet>

#### 2.2.8 辅助查询接口

**获取风险类型枚举：**
```
GET /riskListManage/riskCustomer/getRiskType
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("getRiskType")
public List<RiskTypeDTO> getRiskType(){
    return RiskCustomerRiskTypeEnum.getRiskType();
}
````
</augment_code_snippet>

**获取拉黑类型枚举：**
```
GET /riskListManage/riskCustomer/getBlackType
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("getBlackType")
public List<BlackTypeDTO> getBlackType(){
    return BlackTypeEnum.getBlackType();
}
````
</augment_code_snippet>

**查询名单详情：**
```
POST /riskListManage/riskCustomer/detail
```

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("detail")
public List<RiskCustomerRecordDTO> detail(@RequestBody BaseEntityParams params){
    return this.riskCustomerService.detail(params.getId());
}
````
</augment_code_snippet>

### 2.3 敏感操作记录

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
private void addSensitiveAction(String upload) {
    RiskSensitiveActionAddReq record = new RiskSensitiveActionAddReq();
    record.setModule(SensitiveModuleEnum.sensitive_words_list.name());
    record.setAction(SensitiveActionEnum.export.name());
    record.setKey(SensitiveKeyEnum.file.name());
    record.setValue(upload);
    riskSensitiveActionService.add(record);
}
````
</augment_code_snippet>

## 3. car-risk-process模块Controller分析

### 3.1 RiskCustomerController接口概览

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RestController
@RequestMapping("/risk/customer")
public class RiskCustomerController {
    @Resource
    private RiskCustomerService riskCustomerService;
}
````
</augment_code_snippet>

### 3.2 定时任务接口

#### 3.2.1 风控名单定时失效

**接口定义：**
```
POST /risk/customer/invalid
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/invalid")
public String invalid(){
    return riskCustomerService.invalid();
}
````
</augment_code_snippet>

**功能说明：**
- 查询所有需要失效的风控名单
- 批量更新状态为失效
- 通常由定时任务调用

#### 3.2.2 名单初始化失效时间

**接口定义：**
```
POST /risk/customer/initCustomer
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("/initCustomer")
public String initCustomer(){
    return this.riskCustomerService.initCustomer();
}
````
</augment_code_snippet>

**功能说明：**
- 初始化历史数据的失效时间
- 用于数据迁移和修复

### 3.3 数据同步接口

#### 3.3.1 司机黑名单同步

**接口定义：**
```
POST /risk/customer/syncBlackDriver
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("/syncBlackDriver")
public UiResult syncBlackDriver(@RequestBody DriverSyncParams params){
    this.riskCustomerService.syncDriver(params);
    return UiResult.ok();
}
````
</augment_code_snippet>

**请求参数（DriverSyncParams）：**
- `driverCardNo` - 司机车牌号（必填）
- `ttl` - 有效期天数
- `invalidTime` - 失效时间
- `optionType` - 操作类型
- `optionName` - 操作人
- `riskRemark` - 风险备注
- `flag` - 操作标记（update表示立即失效）
- `orderId` - 关联订单号
- `supplierName` - 供应商名称
- `memberId` - 会员ID

**ERP系统同步接口：**
```
POST /risk/customer/syncBlackDriverByErp
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@RequestMapping("/syncBlackDriverByErp")
public UiResult syncBlackDriverByErp(@RequestBody DriverSyncParams params){
    this.riskCustomerService.syncDriver(params);
    return UiResult.ok();
}
````
</augment_code_snippet>

## 4. 风控能力查询接口

### 4.1 CustomerAbilityController

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/ability/CustomerAbilityController.java" mode="EXCERPT">
````java
@RestController
@RequestMapping("customer")
public class CustomerAbilityController {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private HcCustomerMapper hcCustomerMapper;
}
````
</augment_code_snippet>

#### 4.1.1 一对一名单查询

**接口定义：**
```
POST /customer/query
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/ability/CustomerAbilityController.java" mode="EXCERPT">
````java
@RequestMapping("query")
public UiResult queryList(@RequestBody CustomerAbilityParam param){
    List<RiskCustomerManage> customerManageList = this.riskCustomerManageMapper.selectList(
        new QueryWrapper<RiskCustomerManage>()
            .eq("bind_user", param.getPassengerPhone())
            .gt("invalid_time", new Date())
    );
    
    List<CustomerAbilityDTO> dtoList = new ArrayList<>();
    if(param.getType() == 1){
        dtoList = customerManageList.stream()
                .filter(data->data.getRiskType() == 7) // 一对一名单
                .map(data->{
                    CustomerAbilityDTO dto = new CustomerAbilityDTO();
                    dto.setPlateNumber(data.getCustomerValue());
                    dto.setType(data.getRiskType());
                    return dto;
                })
                .collect(Collectors.toList());
    }
    return UiResult.ok(dtoList);
}
````
</augment_code_snippet>

#### 4.1.2 全量风控查询

**接口定义：**
```
POST /customer/queryAll
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/ability/CustomerAbilityController.java" mode="EXCERPT">
````java
@RequestMapping("queryAll")
public UiResult queryAllList(@RequestBody CustomerAbilityParam param){
    // 1. 查询用户端风控名单
    List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(
        new QueryWrapper<RiskCustomerManage>()
            .eq("customer_value", param.getPlateNumber())
            .gt("invalid_time", new Date())
    );

    // 2. 检查白名单
    List<RiskCustomerManage> whiteTcCustomerList = customerManageList.stream()
            .filter(data->data.getRiskType()==2) // 白名单
            .collect(Collectors.toList());

    // 3. 构建返回结果
    List<CustomerAbilityDTO> dtoList = new ArrayList<>();
    dtoList.addAll(customerManageList.stream()
            .filter(data->data.getRiskType() == 1 || // 黑名单
                   (data.getRiskType() == 7 && data.getBindUser().equals(param.getPassengerPhone()))) // 一对一
            .map(this::convertToDTO)
            .collect(Collectors.toList()));

    // 4. 查询司机端名单
    List<HcCustomer> driverCustomerList = hcCustomerMapper.selectList(
        new QueryWrapper<HcCustomer>()
            .eq("driver_card_no", param.getPlateNumber())
            .gt("invalid_time", new Date())
    );
    
    // 5. 白名单优先级处理
    if(CollectionUtils.isNotEmpty(whiteCustomerList) && CollectionUtils.isNotEmpty(whiteTcCustomerList)){
        return UiResult.ok(new ArrayList<>()); // 白名单优先，返回空结果
    }
    
    // 6. 添加司机端黑名单
    dtoList.addAll(processDriverBlackList(driverCustomerList));

    return UiResult.ok(dtoList);
}
````
</augment_code_snippet>

### 4.2 MarketingRiskController

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/ability/MarketingRiskController.java" mode="EXCERPT">
````java
@RestController
@RequestMapping("marketingRisk")
public class MarketingRiskController {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
}
````
</augment_code_snippet>

#### 4.2.1 营销风控查询

**接口定义：**
```
POST /marketingRisk/query
```

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/ability/MarketingRiskController.java" mode="EXCERPT">
````java
@RequestMapping("query")
public UiResult marketingRiskQuery(@RequestBody CustomerAbilityParam param){
    RiskResultNewDTO riskResultNewDTO = new RiskResultNewDTO();
    
    // 1. 多维度查询风控名单
    List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(
        new QueryWrapper<RiskCustomerManage>()
            .gt("invalid_time", new Date())
            .and(qw-> qw.eq(StringUtils.isNotBlank(param.getPassengerPhone()), "customer_value", param.getPassengerPhone())
                    .or().eq(StringUtils.isNotBlank(param.getMemberId()), "customer_value", param.getMemberId())
                    .or().eq(StringUtils.isNotBlank(param.getUnionId()), "customer_value", param.getUnionId())
            ));

    if(CollectionUtils.isEmpty(customerManageList)){
        return UiResult.ok(riskResultNewDTO); // 未命中风控
    }
    
    // 2. 检查白名单
    RiskCustomerManage riskCustomerManage = customerManageList.stream()
        .filter(data->data.getRiskType()==2) // 白名单
        .findFirst().orElse(null);
        
    if(riskCustomerManage == null){
        riskResultNewDTO.setCode(1);
        riskResultNewDTO.setMessage("命中黑名单");
    }
    
    return UiResult.ok(riskResultNewDTO);
}
````
</augment_code_snippet>

## 5. 接口安全和权限控制

### 5.1 用户身份验证

```java
@RestController
public class RiskCustomerController {
    
    @PostMapping("/add")
    public Boolean add(@RequestBody RiskCustomerAddParams params, UserInfo userInfo) {
        // userInfo通过拦截器自动注入
        // 包含用户身份、权限等信息
        return riskCustomerService.add(params, userInfo);
    }
}
```

### 5.2 接口权限控制

```java
@PreAuthorize("hasRole('RISK_ADMIN')")
@PostMapping("/delete")
public Boolean delete(@RequestBody BaseEntityParams req, UserInfo userInfo) {
    return riskCustomerService.delete(req.getId(), userInfo, req.getDelRemark());
}
```

### 5.3 参数校验

```java
@PostMapping("/add")
public Boolean add(@Valid @RequestBody RiskCustomerAddParams params) {
    // @Valid注解自动触发参数校验
    return riskCustomerService.add(params);
}
```

## 6. 接口监控和日志

### 6.1 接口调用日志

```java
@RestController
@Slf4j
public class RiskCustomerController {
    
    @PostMapping("/add")
    public Boolean add(@RequestBody RiskCustomerAddParams params) {
        log.info("添加风控名单请求：{}", JsonUtils.json(params));
        
        Boolean result = riskCustomerService.add(params);
        
        log.info("添加风控名单结果：{}", result);
        return result;
    }
}
```

### 6.2 接口性能监控

```java
@RestController
public class RiskCustomerController {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @PostMapping("/getList")
    @Timed(name = "risk_customer_query", description = "风控名单查询耗时")
    public Pagination<RiskCustomerManageDto> getList(@RequestBody RiskCustomerListParams query) {
        return riskCustomerService.getListPage(query);
    }
}
```

## 7. 异常处理

### 7.1 全局异常处理

```java
@ControllerAdvice
public class RiskCustomerExceptionHandler {
    
    @ExceptionHandler(CodeException.class)
    public UiResult handleCodeException(CodeException e) {
        log.warn("业务异常：{}", e.getMessage());
        return UiResult.fail(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public UiResult handleException(Exception e) {
        log.error("系统异常", e);
        return UiResult.fail(-1, "系统异常，请稍后重试");
    }
}
```

### 7.2 接口级异常处理

```java
@PostMapping("/import")
public Integer excelImport(MultipartFile file, UserInfo userInfo) {
    try {
        return riskCustomerService.excelImport(file, userInfo);
    } catch (Exception e) {
        log.error("Excel导入异常", e);
        throw new CodeException(-1, "文件导入失败：" + e.getMessage());
    }
}
```

这个Controller层分析涵盖了所有与risk_customer_manage表相关的HTTP接口，包括管理端和业务端的完整接口定义、参数说明、业务逻辑和安全控制。
