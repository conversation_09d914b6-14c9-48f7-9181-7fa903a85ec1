package com.ly.car.risk.manage.integration.approve.model;

import lombok.Data;

import java.util.Map;

/**
 * MktApproveApplyReqVO
 *
 * <AUTHOR>
 * @version Id : MktApproveApplyReqVO, v 1.0  2024/5/16 15:27,hansheng.zhang Exp $
 */
@Data
public class MktApproveApplyReqVO {
    /**
     * 工单编号(重新送审时必传)
     */
    private String approveNo;

    /**
     * 业务场景key
     */
    private String sceneKey;

    /**
     * 业务编号（营销活动对应promotionCode）
     */
    private String businessNo;

    /**
     * 业务名称（营销活动对应活动名称）
     */
    private String businessName;

    /**
     * 申请人工号（7位）
     */
    private String applyUserNo;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 详情页地址
     */
    private String detailUrl;

    /**
     * 渲染数据，必传
     */
    private MktApproveRenderDataVO renderData;

    /**
     * 原始数据，非必传
     */
    private Map<String, Object> originData;

    /**
     * 列表页展示数据，独立审批页必传
     */
    private Map<String, MktApproveRowDataItemVO> rowData;
}