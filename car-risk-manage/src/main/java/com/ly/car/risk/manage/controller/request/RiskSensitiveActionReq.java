package com.ly.car.risk.manage.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.car.risk.manage.bean.Pageable;
import java.util.Date;
import lombok.Data;

@Data
public class RiskSensitiveActionReq extends Pageable {
    
    /**
     * 菜单模块
     */
    private String module;
    
    /**
     * 操作行为
     */
    private String action;
    
    /**
     * 敏感字段
     */
    private String key;
    
    /**
     * 源编号
     */
    private String sourceCode;
    
    /**
     * 时间区间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /**
     * 操作人
     */
    private String createUserFuzzy;
    
    /**
     * 数据源code
     */
    private String sourceCodeFuzzy;
}
