package com.ly.car.risk.manage.controller;

import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.RiskEngineRuleDetailDTO;
import com.ly.car.risk.manage.controller.params.RiskEngineRuleParams;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.service.RiskEngineRuleService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

@RequestMapping("/riskRulesConfig/regularManage")
@RestController
public class RiskEngineRuleController {

    @Resource
    private RiskEngineRuleService riskEngineRuleService;

    @RequestMapping("add")
    public String add(@RequestBody RiskEngineRuleParams params, HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        this.riskEngineRuleService.add(params,userInfo);
        return "success";
    }

    @RequestMapping("delete")
    public String delete(@RequestBody RiskEngineRuleParams params, HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        this.riskEngineRuleService.delete(params,userInfo);
        return "success";
    }

    @RequestMapping("update")
    public String update(@RequestBody RiskEngineRuleParams params,HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        this.riskEngineRuleService.update(params,userInfo);
        return "success";
    }

    @RequestMapping("getList")
    public Pagination getList(@RequestBody RiskEngineRuleParams params){
        return this.riskEngineRuleService.getList(params);
    }

    @RequestMapping("getAllList")
    public List getAllList(){
        return this.riskEngineRuleService.getAllList();
    }

    @RequestMapping("detail")
    public RiskEngineRuleDetailDTO detail(@RequestBody RiskEngineRuleParams params){
        return this.riskEngineRuleService.detail(params.getId());
    }
}
