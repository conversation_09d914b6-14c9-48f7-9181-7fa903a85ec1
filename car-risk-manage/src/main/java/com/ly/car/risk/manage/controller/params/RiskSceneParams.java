package com.ly.car.risk.manage.controller.params;

import com.ly.car.risk.manage.bean.Pageable;
import lombok.Data;

@Data
public class RiskSceneParams extends Pageable {

    //0-主场景 1-子场景
    private Integer sceneType;
    //所属场景，当sceneType=1时有值
    private String guid;
    //场景名称
    private String sceneName;
    //场景说明
    private String description;


    //查询参数
    //场景名称主场景
    private String parentScene;
    //策略名称
    private String categoryName;
    //操作时间
    private String startTime;
    private String endTime;
    //操作人
    private String updateUser;

    private Long id;





}
