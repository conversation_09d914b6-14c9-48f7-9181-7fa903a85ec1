package com.ly.car.risk.manage.service;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.RiskHitDTO;
import com.ly.car.risk.manage.controller.dto.RiskSensitiveActionDTO;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionAddReq;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionReq;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.manage.repo.risk.mapper.DriverCheckMapper;
import com.ly.car.risk.manage.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.manage.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.manage.repo.risk.mapper.RiskHitMapper;
import com.ly.car.risk.manage.repo.risk.mapper.RiskSensitiveActionMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.DistributionRiskManage;
import com.ly.car.risk.manage.repo.risk.mapper.entity.DriverCheck;
import com.ly.car.risk.manage.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskSensitiveAction;
import com.ly.sof.utils.common.UUID;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RiskSensitiveActionService {
    @Resource
    private HttpSession                  httpSession;
    @Resource
    private RiskSensitiveActionMapper    riskSensitiveActionMapper;
    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private RiskHitMapper                riskHitMapper;
    @Resource
    private DriverCheckMapper            driverCheckMapper;
    @Resource
    private RiskCustomerManageMapper     riskCustomerManageMapper;
    @Resource
    private HcCustomerMapper             hcCustomerMapper;
    
    private void fillProp(RiskSensitiveActionDTO dto) {
        dto.setModule(SensitiveModuleEnum.getDescByCode(dto.getModule()));
        dto.setAction(SensitiveActionEnum.getDescByCode(dto.getAction()));
        dto.setKey(SensitiveKeyEnum.getDescByCode(dto.getKey()));
    }
    
    public Pagination getList(RiskSensitiveActionReq req) {
        
        long total = riskSensitiveActionMapper.getListTotal(req);
        List<RiskSensitiveAction> list = riskSensitiveActionMapper.getList(req);
        
        return new Pagination<>(format(list), total, req);
    }
    
    private List<RiskSensitiveActionDTO> format(List<RiskSensitiveAction> list) {
        return list.parallelStream().map(action -> {
            RiskSensitiveActionDTO dto = new RiskSensitiveActionDTO();
            BeanUtils.copyProperties(action, dto);
            dto.setKey(action.getSensitiveKey());
            fillProp(dto);
            return dto;
        }).collect(Collectors.toList());
    }
    
    public List<RiskSensitiveActionDTO> export(RiskSensitiveActionReq req) {
        List<RiskSensitiveAction> list = riskSensitiveActionMapper.getAllList(req);
        
        return format(list);
    }
    
    public UiResult add(RiskSensitiveActionAddReq req) {
        UserInfo userInfo = (UserInfo) httpSession.getAttribute(SESSION_USER_INFO);
        if (null == userInfo) {
            return UiResult.ok();
        }
        RiskSensitiveAction record = new RiskSensitiveAction();
        record.setCode(UUID.generateTimeBasedUUID());
        record.setModule(req.getModule());
        record.setAction(req.getAction());
        record.setSensitiveKey(req.getKey());
        record.setValue(StringUtils.defaultString(req.getValue()));
        record.setSourceCode(StringUtils.defaultString(req.getSourceCode()));
        record.setCreateTime(new Date());
        record.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
        record.setUpdateTime(new Date());
        record.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        riskSensitiveActionMapper.insert(record);
        return UiResult.ok();
    }
    
    public void add(RiskSensitiveActionReq req, String value) {
        UserInfo userInfo = (UserInfo) httpSession.getAttribute(SESSION_USER_INFO);
        if (null == userInfo) {
            return;
        }
        RiskSensitiveAction record = new RiskSensitiveAction();
        record.setCode(UUID.generateTimeBasedUUID());
        record.setModule(req.getModule());
        record.setAction(req.getAction());
        record.setSensitiveKey(req.getKey());
        record.setValue(StringUtils.defaultString(value));
        record.setSourceCode(StringUtils.defaultString(req.getSourceCode()));
        record.setCreateTime(new Date());
        record.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
        record.setUpdateTime(new Date());
        record.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        riskSensitiveActionMapper.insert(record);
    }
    
    public String getSensitiveValue(RiskSensitiveActionReq req) {
        String sensitiveValue = getValue(req);
        
        if (StringUtils.isNotBlank(sensitiveValue)) {
            add(req, sensitiveValue);
        }
        
        return sensitiveValue;
    }
    
    public String getValue(RiskSensitiveActionReq req) {
        if (StringUtils.isBlank(req.getSourceCode()) || StringUtils.isBlank(req.getModule()) || StringUtils.isBlank(req.getAction()) || StringUtils.isBlank(req.getKey())) {
            return StringUtils.EMPTY;
        }
        
        SensitiveModuleEnum moduleEnum = SensitiveModuleEnum.getEnumByCode(req.getModule());
        
        if (null == moduleEnum) {
            return StringUtils.EMPTY;
        }
        
        switch (moduleEnum) {
            
            case risk_control_list:
                DistributionRiskManage distributionRiskManage = distributionRiskManageMapper.selectById(req.getSourceCode());
                if (null == distributionRiskManage) {
                    return StringUtils.EMPTY;
                }
                if (Objects.equals(req.getKey(), SensitiveKeyEnum.mobile.name())) {
                    return distributionRiskManage.getUserPhone();
                }
                return StringUtils.EMPTY;
            
            case hit_risk_manage_list:
                RiskHitDTO riskHitDTO = riskHitMapper.getById(req.getSourceCode());
                if (null == riskHitDTO) {
                    return StringUtils.EMPTY;
                }
                if (Objects.equals(req.getKey(), SensitiveKeyEnum.mobile.name())) {
                    return riskHitDTO.getPassengerCellphone();
                } else if (Objects.equals(req.getKey(), SensitiveKeyEnum.hit_value.name())) {
                    return riskHitDTO.getCustomerValue();
                }
                return StringUtils.EMPTY;
            case driver_check_list:
                DriverCheck driverCheck = driverCheckMapper.selectById(req.getSourceCode());
                if (null == driverCheck) {
                    return StringUtils.EMPTY;
                }
                if (Objects.equals(req.getKey(), SensitiveKeyEnum.mobile.name())) {
                    return driverCheck.getMobile();
                } else if (Objects.equals(req.getKey(), SensitiveKeyEnum.cert.name())) {
                    return driverCheck.getIdCard();
                } else if (Objects.equals(req.getKey(), SensitiveKeyEnum.bank_card.name())) {
                    return driverCheck.getBankCardNo();
                }
                return StringUtils.EMPTY;
            case risk_customer_list:
                RiskCustomerManage riskCustomerManage = riskCustomerManageMapper.selectById(req.getSourceCode());
                if (null == riskCustomerManage) {
                    return StringUtils.EMPTY;
                }
                if (Objects.equals(req.getKey(), SensitiveKeyEnum.hit_value.name())) {
                    return riskCustomerManage.getCustomerValue();
                } else if (Objects.equals(req.getKey(), SensitiveKeyEnum.mobile.name())) {
                    return riskCustomerManage.getBindUser();
                }
                return StringUtils.EMPTY;
            case hc_customer_list:
                HcCustomer hcCustomer = hcCustomerMapper.selectById(req.getSourceCode());
                if (null == hcCustomer) {
                    return StringUtils.EMPTY;
                }
                if (Objects.equals(req.getKey(), SensitiveKeyEnum.cert.name())) {
                    return hcCustomer.getIdCardNo();
                }
                if (Objects.equals(req.getKey(), SensitiveKeyEnum.mobile.name())) {
                    return hcCustomer.getDriverPhone();
                }
                return StringUtils.EMPTY;
        }
        
        return StringUtils.EMPTY;
    }
}
