package com.ly.car.risk.manage.controller.dto;

import java.util.List;

import com.ly.car.risk.common.enums.ApproveStatusEnum;
import lombok.Data;

@Data
public class MetricStrategyDTO {
    
    private Long    id;
    /**
     * 0-测试 1-上线运行 2-下线
     */
    private Integer status;
    /**
     * 策略编号
     */
    private String  strategyNo;
    /**
     * 策略名称
     */
    private String  name;

    /**
     * 审批状态
     *  {@link ApproveStatusEnum}
     */
    private Integer approveStatus;

    /**
     * 审批编号
     */
    private String approveNo;

    private Integer linkRuleCount;
    private Integer linkSceneCount;
    private String  updateTime;
    private String  updateUser;
    
    /**
     * 策略描述
     */
    private String       description;
    /**
     * 业务线：YNC-网约车 SFC-顺风车
     */
    private List<String> productLines;
    
    private List<String> channels;
    
    /**
     * 策略返回文案
     */
    private String strategyWord;
    
    /**
     * 管控时间 单位天
     */
    private Integer controlTime;
    
    /**
     * 管控对象 0-司机 1-用户
     */
    private Integer controlType;
    
    /**
     * 命中字段
     */
    private String hitField;
    
    /**
     * 命中动作0:加全局黑 1-加1v1
     */
    private Integer hitAction;
    
    /**
     * 处置动作 0-禁止 1-增强校验 2-通过
     */
    private Integer disposeAction;
    /**
     * 表达式
     */
    private String  expression;
    
    private Integer cityId;
    
    /**
     * 执行脚本
     */
    private String script;
    
    private List<MetriclinkObject> linkSceneList;
    
    private List<Long> linkSceneIds;
    
    private List<MetriclinkObject> linkRuleList;
    
    private Integer strategyType;
    
    private Integer level;
    
    private String riskType;
    
    private Integer noticeType;
    
    private List<String> supplierCodes;
}
