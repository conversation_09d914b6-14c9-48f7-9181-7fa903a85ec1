package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 风控指标-规则指标关联
 * metric_rule_field_relation
 */
@Data
public class MetricRuleFieldRelation extends Model<MetricRuleFieldRelation> {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 特征指标
     */
    private Long leftFieldId;

    /**
     * 运算符
     */
    private String operator;

    /**
     * 0-常量
     */
    private Integer rightType;

    /**
     * 数字或者任意字符串
     */
    private String rightValue;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}