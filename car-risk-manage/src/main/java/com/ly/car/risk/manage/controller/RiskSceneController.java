package com.ly.car.risk.manage.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.RiskSceneAllListDTO;
import com.ly.car.risk.manage.controller.params.RiskSceneParams;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskScene;
import com.ly.car.risk.manage.service.RiskSceneService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

@RequestMapping("/riskRulesConfig/sceneManage")
@RestController
public class RiskSceneController {

    @Resource
    private RiskSceneService riskSceneService;

    @RequestMapping("add")
    public String add(@RequestBody RiskSceneParams riskSceneParams, HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        this.riskSceneService.add(riskSceneParams,userInfo);
        return "success";
    }

    @RequestMapping("getList")
    public Pagination getList(@RequestBody RiskSceneParams params){
        return this.riskSceneService.getList(params);
    }

    @RequestMapping("getParentList")
    public List<RiskScene> getParentList(){
        return this.riskSceneService.getParentScene();
    }

    @RequestMapping("delete")
    public String delete(@RequestBody RiskSceneParams params){
        return this.riskSceneService.delete(params);
    }

//    @RequestMapping("update")
//    public String update(@RequestBody RiskSceneParams riskSceneParams,HttpServletRequest request){
//        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
//    }

    @RequestMapping("detail")
    public RiskScene detail(@RequestBody RiskSceneParams params){
        return this.riskSceneService.detail(params.getId());
    }

}
