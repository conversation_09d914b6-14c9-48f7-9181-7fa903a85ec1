package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 风控计费调用统计表
 * risk_charge_call
 */
@Data
public class RiskChargeCall extends Model<RiskChargeCall> {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 产品类型 0:身份证 1:风险评分 2:驾驶证 3:行驶证 4:银行卡
     */
    private Integer type;
    
    /**
     * 服务商 TIANCHUANG:天创 TCSHUKE:同程数科
     */
    private String apiProvider;
    
    /**
     * 调用费用（元）
     */
    private BigDecimal charge;
    
    /**
     * 调用时间
     */
    private Date callTime;
    
    private String productLine;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改时间
     */
    private Date updateTime;
    
    /**
     * 额外字段
     */
    private String ext;
    
    private static final long serialVersionUID = 1L;
    
    @Data
    public static class Ext {
        private String req;
        private String res;
        private String msg;
    }
}