<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.riskmetrics.mapper.CarMtDriverPunishRecordMapper">

    <select id="getTotal" resultType="long">
        select count(1) from
        (
        SELECT
        count(car_num) as punishOrderCount,
        DATE_FORMAT(gmt_pubish, '%Y-%m-%d') as punishDate
        FROM
        `car_mt_driver_punish_record`
        where car_num != ''
        <include refid="getCondition"/>
        group by car_num,punishDate
        <include refid="havingCondition"/>
        ) tmp;
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.service.dto.DriverPunishOrderDto">
        SELECT
        count(car_num) as punishOrderCount,
        car_num as carNum,
        DATE_FORMAT(gmt_pubish, '%Y-%m-%d') as punishDate
        FROM
        `car_mt_driver_punish_record`
        where car_num != ''
        <include refid="getCondition"/>
        group by car_num, punishDate
        <include refid="havingCondition"/>
        order by punishDate desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.manage.service.dto.DriverPunishOrderDto">
        SELECT
        count(car_num) as punishOrderCount,
        car_num as carNum,
        DATE_FORMAT(gmt_pubish, '%Y-%m-%d') as punishDate
        FROM
        `car_mt_driver_punish_record`
        where car_num != ''
        <include refid="getCondition"/>
        group by car_num, punishDate
        <include refid="havingCondition"/>
        order by punishDate desc
    </select>

    <sql id="getCondition">
        <if test="carNum != null and carNum != '' ">
            and `car_num` = #{carNum}
        </if>
        <if test="startTime != null and startTime != '' ">
            and `gmt_pubish` >= #{startTime}
        </if>
        <if test="endTime != null and endTime != '' ">
            and #{endTime} >= `gmt_pubish`
        </if>
    </sql>

    <sql id="havingCondition">
        having 1 = 1
        <if test="punishOrderCount != null and punishOrderCount != '' ">
            and punishOrderCount = #{punishOrderCount}
        </if>
        <if test="level != null">
            <if test="level == 0">
                and punishOrderCount = 1
            </if>
            <if test="level == 1">
                and punishOrderCount >= 2 and 5 > punishOrderCount
            </if>
            <if test="level == 2">
                and punishOrderCount >= 5
            </if>
        </if>
    </sql>
</mapper>