package com.ly.car.risk.manage.repo.hitchorder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 乘客行程订单地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2023/02/15 14:48
 */
@Getter
@Setter
@TableName("passenger_order_address")
public class PassengerOrderAddress extends Model<PassengerOrderAddress> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 乘客订单id
     */
    @TableField("passenger_order_id")
    private String passengerOrderId;

    /**
     * 出发城市id
     */
    @TableField("start_city_id")
    private Integer startCityId;

    /**
     * 出发城市名称
     */
    @TableField("start_city_name")
    private String startCityName;

    /**
     * 出发城市行政编码
     */
    @TableField("start_ad_code")
    private String startAdCode;

    /**
     * 出发短地址
     */
    @TableField("start_address")
    private String startAddress;

    /**
     * 出发长地址
     */
    @TableField("start_address_detail")
    private String startAddressDetail;

    /**
     * 出发纬度
     */
    @TableField("start_lat")
    private BigDecimal startLat;

    /**
     * 出发经度
     */
    @TableField("start_lng")
    private BigDecimal startLng;

    /**
     * 目的地城市id
     */
    @TableField("end_city_id")
    private Integer endCityId;

    /**
     * 目的地城市名称
     */
    @TableField("end_city_name")
    private String endCityName;

    /**
     * 目的地城市行政编码
     */
    @TableField("end_ad_code")
    private String endAdCode;

    /**
     * 目的地短地址
     */
    @TableField("end_address")
    private String endAddress;

    /**
     * 目的地长地址
     */
    @TableField("end_address_detail")
    private String endAddressDetail;

    /**
     * 目的地纬度
     */
    @TableField("end_lat")
    private BigDecimal endLat;

    /**
     * 目的地经度
     */
    @TableField("end_lng")
    private BigDecimal endLng;

    /**
     * 机场三字码
     */
    @TableField("landmark_no")
    private String landmarkNo;

    /**
     * 预估总里程（公里）
     */
    @TableField("estimate_kilo")
    private BigDecimal estimateKilo;

    /**
     * 预估总分钟
     */
    @TableField("estimate_minute")
    private Integer estimateMinute;

    /**
     * 实际总里程（公里）
     */
    @TableField("actual_kilo")
    private BigDecimal actualKilo;

    /**
     * 实际总分钟
     */
    @TableField("actual_minute")
    private Integer actualMinute;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最新修改时间
     */
    @TableField("update_time")
    private Date updateTime;


}
