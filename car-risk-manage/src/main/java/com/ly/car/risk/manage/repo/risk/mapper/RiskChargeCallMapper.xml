<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.RiskChargeCallMapper">

    <select id="total" resultType="long">
        select count(*) from risk_charge_call
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.RiskChargeCall">
        select * from risk_charge_call
        <include refid="getCondition"/>
        order by call_time desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.RiskChargeCall">
        select *
        from risk_charge_call
        <include refid="getCondition"/>
        order by call_time desc
    </select>

    <select id="getSum" resultType="com.ly.car.risk.manage.controller.dto.RiskChargeCallSummaryDTO">
        select
        COUNT(*) AS callCount,
        ifnull(SUM(charge), 0) AS chargeSum,
        COUNT(CASE WHEN charge != 0 THEN 1 END) AS chargeCount
        from risk_charge_call
        <include refid="getCondition"/>
    </select>

    <sql id="getCondition">
        where 1=1
        <if test="productLine != null">
            and product_line = #{productLine}
        </if>
        <if test="callStartTime != null">
            and call_time >= #{callStartTime}
        </if>
        <if test="callEndTime != null">
            and #{callEndTime} >= call_time
        </if>
        <if test="apiProvider != null and apiProvider != ''">
            and api_provider = #{apiProvider}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="forCharge != null and forCharge == 0">
            and charge = 0
        </if>
        <if test="forCharge != null and forCharge == 1">
            and charge > 0
        </if>
    </sql>
</mapper>