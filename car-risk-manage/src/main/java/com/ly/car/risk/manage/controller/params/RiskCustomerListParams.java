package com.ly.car.risk.manage.controller.params;

import com.ly.car.risk.manage.bean.Pageable;
import lombok.Data;

@Data
public class RiskCustomerListParams extends Pageable {
    /**
     * 风控的客户名单类型 1-用户ID 2-用户手机号 3-用户设备号 4-用户unionID  5-支付账号 6-司机车牌号
     */
    private Integer customerType;
    /**
     * 风控的客户名单内容
     */
    private String customerValue;
    /**
     * 名单类型 1-黑名单 2-白名单  3-禁止领卷名单 4-禁止奖励名单 5-禁止派单名单 6=禁止接单名单
     */
    private Integer riskType;
    /**
     * 名单状态 1-有效 2-失效
     */
    private Integer status;
    /**
     * 有效期限 1-1天 7-7天  30-一个月 365-一年 -1-永久
     */
    private Integer ttl;
    /**
     * 操作人
     */
    private String optionName;
    /**
     * 操作类型 1-系统操作 2-人工操作
     */
    private Integer optionType;
    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 失效时间
     * */
    private String invalidTime;

    /**
     * 拉黑类型
     * */
    private Integer blackType;
    
    /**
     * 绑定号码
     */
    private String bindUser;
    
    /**
     * 会员ID
     */
    private String memberId;
    
    /**
     * 所属供应商
     */
    private String supplierName;
    
    private String bindOrder;
}
