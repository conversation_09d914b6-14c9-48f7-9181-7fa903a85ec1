package com.ly.car.risk.manage.controller;

import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.RiskChargeVoiceSummaryDTO;
import com.ly.car.risk.manage.controller.request.RiskChargeVoiceListReq;
import com.ly.car.risk.manage.service.RiskChargeVoiceService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("riskChargeVoice")
public class RiskChargeVoiceController {
    
    @Resource
    private RiskChargeVoiceService riskChargeVoiceService;
    
    @RequestMapping("getList")
    public Pagination getList(@RequestBody RiskChargeVoiceListReq req) {
        return riskChargeVoiceService.getList(req);
    }
    
    @RequestMapping("getSum")
    public RiskChargeVoiceSummaryDTO getSum(@RequestBody RiskChargeVoiceListReq req) {
        return riskChargeVoiceService.getSum(req);
    }
    
    @RequestMapping("/export")
    public String export(@RequestBody RiskChargeVoiceListReq req, HttpServletResponse httpServletResponse) {
        return riskChargeVoiceService.exportData(req, httpServletResponse);
    }
    
}
