package com.ly.car.risk.manage.service;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.constant.PlatformOrderConstants;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.manage.repo.risk.mapper.entity.DistributionRiskManage;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.constants.*;
import com.ly.car.risk.manage.controller.params.DistributionInfoRiskParams;
import com.ly.car.risk.manage.controller.params.RiskOrderAddReq;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.manage.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.manage.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.manage.service.dto.DistributionRiskManageDto;
import com.ly.car.sharding.order.entity.*;
import com.ly.car.sharding.order.mapper.*;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DistributionRiskService {

    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private OrderSupplierBillMapper orderSupplierBillMapper;
    @Resource
    private OrderExpandMapper orderExpandMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private RiskOrderManageService riskOrderManageService;


    public Pagination<DistributionRiskManageDto> getListPage(DistributionInfoRiskParams queryParams) {

        if(StringUtils.isNotBlank(queryParams.getChildScene())){
            queryParams.setChildSceneInteger(Integer.valueOf(queryParams.getChildScene()));
        }
        if(StringUtils.isNotBlank(queryParams.getSupplierCode())){
            if (queryParams.getSupplierCode().contains("-")) {
                queryParams.setSupplierCode(queryParams.getSupplierCode().split("-")[0]);
            } else {
                queryParams.setSupplierCode(queryParams.getSupplierCode().split("_")[0]);
            }
        }
        long total = distributionRiskManageMapper.getListTotal(queryParams);
        List<DistributionRiskManage> list = distributionRiskManageMapper.getList(queryParams);
        List<DistributionRiskManageDto> manageList =list.stream().map(DistributionRiskManage->{
            DistributionRiskManageDto dto = new DistributionRiskManageDto();
            BeanUtils.copyProperties(DistributionRiskManage, dto);
            dto.setChildScene(DistributionRiskManage.getMainScene()+"-"+DistributionRiskManage.getChildScene());
            dto.setChildSceneStr(ChildSceneEnum.getMsgByCode(dto.getChildScene()));
            dto.setMainSceneStr(MainSceneEnum.getMsgByCode(dto.getMainScene()));
            dto.setRiskLevelStr(RiskLevelEnum.getMsgByCode(dto.getRiskLevel()));
            dto.setUserId(DistributionRiskManage.getMemberId());
            dto.setUserPhone(DesensitizedUtil.mobilePhone(DistributionRiskManage.getUserPhone()));
            if(DistributionRiskManage.getIsCustomer() == 1){
                String msg;
                if(StringUtils.isNumeric(dto.getRuleNoList())){
                    msg = RiskCustomerRiskTypeEnum.getMsgByCode(Integer.valueOf(dto.getRuleNoList()));
                } else {
                    msg = "黑名单";
                }
                dto.setRuleNoList(msg);
            }
            return dto;
        }).collect(Collectors.toList());
        return new Pagination<>(manageList, total, queryParams);
    }


    public List<DistributionRiskManageDto> exportList(DistributionInfoRiskParams query) {
        if(StringUtils.isNotBlank(query.getChildScene())){
            query.setChildSceneInteger(Integer.valueOf(query.getChildScene()));
        }
        if(StringUtils.isNotBlank(query.getSupplierCode())){
            if (query.getSupplierCode().contains("-")) {
                query.setSupplierCode(query.getSupplierCode().split("-")[0]);
            } else {
                query.setSupplierCode(query.getSupplierCode().split("_")[0]);
            }
        }
        List<DistributionRiskManage> list = distributionRiskManageMapper.getListExport(query);
        List<DistributionRiskManageDto> manageList =list.stream().map(DistributionRiskManage->{
            DistributionRiskManageDto dto = new DistributionRiskManageDto();
            BeanUtils.copyProperties(DistributionRiskManage, dto);
            dto.setChildScene(DistributionRiskManage.getMainScene()+"-"+DistributionRiskManage.getChildScene());
            dto.setChildSceneStr(ChildSceneEnum.getMsgByCode(dto.getChildScene()));
            dto.setMainSceneStr(MainSceneEnum.getMsgByCode(dto.getMainScene()));
            dto.setRiskLevelStr(RiskLevelEnum.getMsgByCode(dto.getRiskLevel()));
            dto.setIsCheatingStr((dto.getIsCheating().equals(1)?"是":"否"));
            dto.setHitValue(DistributionRiskManage.getHitValue());
            dto.setPhone(DesensitizedUtil.mobilePhone(DistributionRiskManage.getPhone()));
            if(DistributionRiskManage.getIsCustomer() == 1){
                String msg;
                if(StringUtils.isNumeric(dto.getRuleNoList())){
                    msg = RiskCustomerRiskTypeEnum.getMsgByCode(Integer.valueOf(dto.getRuleNoList()));
                } else {
                    msg = "黑名单";
                }
                dto.setRuleNoList(msg);
            }
            return dto;
        }).collect(Collectors.toList());
        return manageList;
    }

    public List<DistributionRiskDto> detail(DistributionInfoRiskParams req) {


        DistributionRiskManage distributionRiskManage = distributionRiskManageMapper.selectById(req.getId());
        log.info("[][][][]当前查询命中信息{}",JsonUtils.json(distributionRiskManage));
//        if (StringUtils.isBlank(distributionRiskManage.getLinkOrder())) {
//            return new ArrayList<>();
//        }

        List<String> orderList = new ArrayList<>();
        if(StringUtils.isBlank(distributionRiskManage.getLinkOrder())){
            orderList.add(distributionRiskManage.getOrderId());
        } else {
            List<String> orderLists = Arrays.asList(distributionRiskManage.getLinkOrder().split(","));
            orderList = new ArrayList<>(orderLists);
            orderList.add(distributionRiskManage.getOrderId());
        }
        log.info("[][][][]当前查询命中关联订单{}",JsonUtils.json(orderList));
        //查询供应商单据
        List<OrderSupplierBill> orderSupplierBillList = orderSupplierBillMapper.findByOrderIds(orderList);
        Map<String,OrderSupplierBill> orderSupplierBillMap = orderSupplierBillList.stream().collect(Collectors.toMap(OrderSupplierBill::getOrderId,v->v,(old,cur)->old));

        if(!(distributionRiskManage.getMainScene() == MainSceneEnum.OFF_LINE.getCode()
                && (MainSceneEnum.OFF_LINE.getCode()+"-"+distributionRiskManage.getChildScene()).equals(ChildSceneEnum.OFF_LINE_RAKE_ACK.getCode()))){
            log.info("[][][][]当前查询命中关联订单11{}",JsonUtils.json(orderList));

            //查地址信息
            List<OrderAddress> orderAddressList = orderAddressMapper.findByOrderIds(orderList);
            Map<String,OrderAddress> orderAddressMap = orderAddressList.stream().collect(Collectors.toMap(OrderAddress::getOrderId,v->v,(old,cur)->old));

            //查询当前关联订单命中的规则
            List<DistributionRiskManage> distributionRiskManageList = distributionRiskManageMapper.selectList(
                    new QueryWrapper<DistributionRiskManage>().in("order_id",orderList)
            );
            Map<String,DistributionRiskManage> distributionRiskManageMap = distributionRiskManageList.stream().collect(Collectors.toMap(DistributionRiskManage::getOrderId,v->v,(old,cur)->old));
            List<SfcOrder> sfcOrderList = sfcOrderMapper.selectList(new QueryWrapper<SfcOrder>().in("order_id",orderList));
            List<SfcSupplierOrder> sfcSupplierOrderList = sfcSupplierOrderMapper.selectList(new QueryWrapper<SfcSupplierOrder>().in("order_id",orderList));
            List<OrderAddress> OrderAddressList = orderAddressMapper.findByOrderIds(orderList);
            List<DistributionRiskDto> listByOrderIds = new ArrayList<>();
            if(distributionRiskManage.getOrderId().startsWith("SFC")){
                for(String str : orderList){
                    DistributionRiskDto dto = new DistributionRiskDto();
                    dto.setOrderId(str);
                    SfcOrder sfcOrder = sfcOrderList.stream().filter(a -> a.getOrderId().equals(str)).findFirst().orElse(null);
                    SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderList.stream().filter(a->a.getOrderId().equals(str) && a.getSupplierOrderId().equals(sfcOrder.getSupplierOrderId())).findFirst().orElse(null);
                    OrderAddress orderAddress = orderAddressList.stream().filter(a -> a.getOrderId().equals(str)).findFirst().orElse(null);
                    if(sfcOrder != null && sfcSupplierOrder != null){
                        dto.setDecisionTime(sfcSupplierOrder.getAcceptTime());
                        dto.setFinishTime(sfcOrder.getFinishTime());
                        dto.setStatus(sfcOrder.getStatus());
                        dto.setTotalAmount(fenZhuanYuan(sfcOrder.getTotalAmount()));
                        dto.setDiscountAmount(fenZhuanYuan(sfcOrder.getDiscountAmount()));
                        dto.setStatusStr(PlatformOrderConstants.Status.getName(sfcOrder.getStatus()).name);
                        dto.setPhone(sfcOrder.getPassengerCellphone());
                        dto.setActualKilo(orderAddress.getEstimateKilo());

//                        dto.setDriverCardNo(sfcSupplierOrder.getDeviceId());
                        dto.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
                        dto.setPhone(sfcOrder.getPassengerCellphone());

                        dto.setRuleNoList(distributionRiskManageMap.get(str).getRuleNoList());
                        dto.setPhone(distributionRiskManageMap.get(str).getPhone());
                        dto.setIsCheating(distributionRiskManageMap.get(str).getIsCheating());
                        dto.setId(distributionRiskManageMap.get(str).getId());
                        dto.setStartAddress(orderAddressMap.get(str).getStartAddressDetail());
                        dto.setEndAddress(orderAddressMap.get(str).getEndAddressDetail());
                        dto.setEndCityName(orderAddressMap.get(str).getEndCityName());
                        dto.setHitValue(distributionRiskManageMap.get(str).getHitValue());
                        if(distributionRiskManageMap.get(str).getIsCustomer() == 1){
                            String msg;
                            if(StringUtils.isNumeric(dto.getRuleNoList())){
                                msg = RiskCustomerRiskTypeEnum.getMsgByCode(Integer.valueOf(dto.getRuleNoList()));
                            } else {
                                msg = "黑名单";
                            }
                            dto.setRuleNoList(msg);
                        }
                        listByOrderIds.add(dto);
                    }
                }
            } else {
                //查订单信息
                List<OrderInfo> orderInfoList = orderInfoMapper.findByOrderIds(orderList);
                //设备id
                List<OrderExpand> orderExpandList = orderExpandMapper.findByOrderIds(orderList);
                Map<String,OrderExpand> orderExpandMap = orderExpandList.stream().collect(Collectors.toMap(OrderExpand::getOrderId,v->v,(old,cur)->old));
                //司机信息
                List<OrderDriver> orderDriverList = orderDriverMapper.findByOrderIds(orderList);
                Map<String,OrderDriver> orderDriverMap = orderDriverList.stream().collect(Collectors.toMap(OrderDriver::getOrderId,v->v,(old,cur)->old));
                Map<String,OrderInfo> orderInfoMap = orderInfoList.stream().collect(Collectors.toMap(OrderInfo::getOrderId,v->v,(old,cur)->old));
                for(String str : orderList){
                    DistributionRiskDto dto = new DistributionRiskDto();
                    dto.setOrderId(str);
                    if(orderInfoMap.get(str) != null){
                        dto.setDecisionTime(orderInfoMap.get(str).getDecisionTime());
                        dto.setFinishTime(orderInfoMap.get(str).getFinishTime());
                        dto.setSupplierName(orderInfoMap.get(str).getSupplierCode());
                        dto.setTotalAmount(orderInfoMap.get(str).getTotalAmount());
                        dto.setStatus(orderInfoMap.get(str).getStatus());
                        dto.setStatusStr(PlatformOrderConstants.Status.getName(orderInfoMap.get(str).getStatus()).name);
                    }

                    if(orderAddressMap.get(str) != null){
                        dto.setStartAddress(orderAddressMap.get(str).getStartAddressDetail());
                        dto.setEndAddress(orderAddressMap.get(str).getEndAddressDetail());
                        dto.setEndCityName(orderAddressMap.get(str).getEndCityName());
                    }
                    if(orderSupplierBillMap.get(str) != null){
                        dto.setActualKilo(orderSupplierBillMap.get(str).getDistance());
                    }
                    if(orderExpandMap.get(str) != null){
                        dto.setDriverCardNo(orderExpandMap.get(str).getDeviceId());
                    }
                    if(orderDriverMap.get(str) != null){
                        dto.setDriverCardNo(orderDriverMap.get(str).getPlateNumber());
                    }
                    if(distributionRiskManageMap.get(str) != null){
                        dto.setRuleNoList(distributionRiskManageMap.get(str).getRuleNoList());
                        dto.setPhone(distributionRiskManageMap.get(str).getPhone());
                        dto.setIsCheating(distributionRiskManageMap.get(str).getIsCheating());
                        dto.setId(distributionRiskManageMap.get(str).getId());
                    }
                    if(StringUtils.isBlank(distributionRiskManageMap.get(str).getPhone())){
                        dto.setPhone(orderInfoMap.get(str).getPassengerCellphone());
                    }
                    listByOrderIds.add(dto);
                }
            }

            return listByOrderIds;
        }
        //下面是司推乘
        List<DistributionRiskDto> listByOrderIds = distributionRiskManageMapper.getListByOrderIds(orderList);
        log.info("[][][][]当前顺风车关联订单查询总数{}",listByOrderIds.size());
        List<OrderInfo> OrderList = orderInfoMapper.findByOrderIds(orderList);
        List<OrderAddress> OrderAddressList = orderAddressMapper.findByOrderIds(orderList);
        listByOrderIds.forEach(e -> {
            log.info("[][][][]当前顺风车关联订单查询{}",JsonUtils.json(e));
            OrderAddress orderAddress = OrderAddressList.stream().filter(a -> a.getOrderId().equals(e.getOrderId())).findFirst().orElse(null);
            e.setIsNewOrderStr(e.getIsNewOrder()==1?"是":"否");
            OrderInfo orderInfo = OrderList.stream().filter(a -> a.getOrderId().equals(e.getOrderId())).findFirst().orElse(null);
            if (orderInfo != null) {
                e.setDecisionTime(orderInfo.getDecisionTime());
                e.setFinishTime(orderInfo.getFinishTime());
                e.setStatus(orderInfo.getStatus());
                e.setTotalAmount(orderInfo.getTotalAmount());
                e.setDiscountAmount(orderInfo.getDiscountAmount());
                e.setStatusStr(PlatformOrderConstants.Status.getName(orderInfo.getStatus()).name);
                e.setPhone(orderInfo.getPassengerCellphone());
            }
            if(orderSupplierBillMap.get(e.getOrderId()) != null){
                e.setActualKilo(orderSupplierBillMap.get(e.getOrderId()).getDistance());
            }
            if (orderAddress != null) {
                e.setStartAddress(orderAddress.getStartAddress());
                e.setEndAddress(orderAddress.getEndAddress());
                e.setEndCityName(orderAddress.getEndCityName());
            }
        });
        //排序
        listByOrderIds= listByOrderIds.stream().sorted(Comparator.comparing(DistributionRiskDto::getDecisionTime).reversed()).collect(Collectors.toList());
        return listByOrderIds;

    }

    public Boolean updateCheating(DistributionInfoRiskParams req, UserInfo userInfo) {
        DistributionRiskManage distributionRiskManage = distributionRiskManageMapper.selectById(req.getId());
        distributionRiskManage.setIsCheating(req.getIsCheating());
        distributionRiskManage.setUpdateTime(new Date());
        distributionRiskManageMapper.updateById(distributionRiskManage);
        //同步到风险订单
        if(req.getIsCheating() == 1){
            RiskOrderAddReq addReq = new RiskOrderAddReq();
            addReq.setOrderId(distributionRiskManage.getOrderId());
            addReq.setRuleNo(distributionRiskManage.getRuleNoList());
            addReq.setIsRisk(1);
            addReq.setRiskType(RiskTypeEnum.BRUSH_ORDER_EX.getCode());
            riskOrderManageService.addRiskOrder(addReq,userInfo);
        }
        return true;
    }

    public BigDecimal fenZhuanYuan(Integer amount){
        return new BigDecimal(amount).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public Map<String,DistributionRiskManage> getGroupManage(List<String> orderIds){
        Map<String,DistributionRiskManage> returnMap = new HashMap<>();
        List<DistributionRiskManage> manageList = distributionRiskManageMapper.selectList(
                new QueryWrapper<DistributionRiskManage>().in("order_id",orderIds)
        );
        Map<String,List<DistributionRiskManage>> manageMap = manageList.stream().collect(Collectors.groupingBy(DistributionRiskManage::getOrderId));
        for(Map.Entry<String,List<DistributionRiskManage>> entry : manageMap.entrySet()){
            List<String> ruleNoList = entry.getValue().stream().map(DistributionRiskManage::getRuleNoList).collect(Collectors.toList());
            Set<String> strSet = new HashSet<>();
            for(String str : ruleNoList){
                strSet.addAll(Arrays.asList(str.split(",")));
            }
            DistributionRiskManage distributionRiskManage = new DistributionRiskManage();
            distributionRiskManage.setOrderId(entry.getKey());
            distributionRiskManage.setRuleNoList(StringUtils.join(strSet,","));
            returnMap.put(entry.getKey(),distributionRiskManage);
        }
        return returnMap;
    }

}
