package com.ly.car.risk.manage.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.ly.car.risk.common.enums.MetricStrategyChannelEnum;
import com.ly.car.risk.common.enums.OrderProductLineEnum;
import com.ly.car.risk.common.enums.VoiceApiProviderEnum;
import com.ly.car.risk.common.enums.VoiceProductTypeEnum;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.constants.ChannelEnum;
import com.ly.car.risk.manage.controller.dto.RiskChargeCallDTO;
import com.ly.car.risk.manage.controller.dto.RiskChargeVoiceDTO;
import com.ly.car.risk.manage.controller.dto.RiskChargeVoiceSummaryDTO;
import com.ly.car.risk.manage.controller.request.RiskChargeVoiceListReq;
import com.ly.car.risk.manage.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper;
import com.ly.car.risk.manage.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskChargeCall.Ext;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class RiskChargeVoiceService {
    
    @Resource
    private RiskChargeVoiceMapper riskChargeVoiceMapper;
    
    public Pagination getList(RiskChargeVoiceListReq params) {
        long total = riskChargeVoiceMapper.getTotal(params);
        List<RiskChargeVoice> list = riskChargeVoiceMapper.queryList(params);
        return new Pagination<>(format(list), total, params);
    }
    
    private List<RiskChargeVoiceDTO> format(List<RiskChargeVoice> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.parallelStream().map(e -> {
            RiskChargeVoiceDTO dto = new RiskChargeVoiceDTO();
            dto.setId(e.getId());
            dto.setCallTime(e.getCreateTime());
            dto.setOrderNo(e.getOrderNo());
            dto.setProductLine(OrderProductLineEnum.getDescByCode(e.getProductLine()));
            dto.setApiProvider(VoiceApiProviderEnum.getDescByCode(e.getApiProvider()));
            dto.setProductType(VoiceProductTypeEnum.getDescByCode(e.getProductType()));
            dto.setVoiceDuring(e.getVoiceDuring());
            dto.setSource(MetricStrategyChannelEnum.getDescByCode(e.getSource()));
            if (StringUtils.isNotBlank(e.getExt())) {
                Ext ext = JSON.parseObject(e.getExt(), Ext.class);
                dto.setReq(ext.getReq());
            }
            dto.setCharge(e.getCharge());
            return dto;
        }).collect(Collectors.toList());
    }
    
    public String exportData(RiskChargeVoiceListReq request, HttpServletResponse response) {
        List<RiskChargeVoice> list = riskChargeVoiceMapper.queryAllList(request);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String fileName = "外部语音服务调用统计_" + System.currentTimeMillis() + ".xlsx";
        EasyExcel.write(byteArrayOutputStream, RiskChargeVoiceDTO.class).sheet("sheet1").doWrite(format(list));
        return FileUploadCephUtil.upload(fileName, byteArrayOutputStream);
    }
    
    public RiskChargeVoiceSummaryDTO getSum(RiskChargeVoiceListReq req) {
        return riskChargeVoiceMapper.getSum(req);
    }
}
