package com.ly.car.risk.manage.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.car.risk.manage.bean.Pageable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class RiskAlertListReq extends Pageable {
    
    /**
     * 预警编号
     */
    private String code;
    
    /**
     * 处理状态 0:未处理 1:无风险 2:跟进中
     */
    private Integer handleResult;
    
    /**
     * 预警级别 1:关注 2:高风险
     */
    private Integer level;
    
    /**
     * 预警时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime alertStartTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime alertEndTime;
    
    /**
     * 预警主体 USER:用户 DRIVER:司机 SUPPLIER:供应商
     */
    private String target;
    
    /**
     * 处理人
     */
    private String handleUser;
    
    private String targetValue;
    
    private String alertScene;
}
