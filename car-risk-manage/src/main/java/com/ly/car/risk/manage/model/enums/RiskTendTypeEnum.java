package com.ly.car.risk.manage.model.enums;

import lombok.Getter;

import java.util.List;

@Getter
public enum RiskTendTypeEnum {

    RISK_USER("风险用户","#73c0de"),

    RISK_DRIVER("风险司机数","#73c0de"),

    ONLINE_ORDER("实时策略订单数","#73c0de"),

    OFFLINE_ORDER("实时离线订单数","#73c0de"),

    RISK_MONEY("挽留金额","#73c0de"),


    ;

    private String desc;

    private String color;

    RiskTendTypeEnum(String desc, String color) {
        this.desc = desc;
        this.color = color;
    }
}
