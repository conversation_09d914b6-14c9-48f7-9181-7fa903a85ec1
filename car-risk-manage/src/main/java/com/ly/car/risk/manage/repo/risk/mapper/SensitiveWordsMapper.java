package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.SensitiveWords;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface SensitiveWordsMapper extends BaseMapper<SensitiveWords> {

    @Update("<script>" +
            "   update sensitive_words set hit_count = hit_count + 1 where id = #{id}" +
            "</script>")
    void incrHitNum(@Param("id") Long id);


}
