package com.ly.car.risk.manage.controller.params;

import com.ly.car.risk.manage.oauth.bean.UserInfo;
import lombok.Data;


@Data
public class RiskOrderAddReq extends UserInfo {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 分销商订单编号
     */
    private String distributeOrderId;

    /**
     * 渠道编号
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 风险类型
     */
    private Integer riskType;

    /**
     * 风险名称
     */
    private String riskTypeName;

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 0-初始化 1-是 2-否
     */
    private Integer isRisk;

    private String operateUser;

    private String remark;


    private Integer judgeType;

}
