package com.ly.car.risk.manage.controller.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ExcelIgnoreUnannotated
public class RiskChargeCallDTO {
    
    @ExcelProperty(value = "id")
    private Long id;
    
    @ExcelProperty(value = "调用时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;
    
    @ExcelProperty(value = "服务商")
    private String apiProvider;
    
    @ExcelProperty(value = "业务线")
    private String productLine;
    
    @ExcelProperty(value = "产品类型")
    private String type;
    
    @ExcelProperty(value = "调用入参")
    private String req;
    
    @ExcelProperty(value = "结果")
    private String msg;
    
    @ExcelProperty(value = "调用费用")
    private BigDecimal charge;
    
    @ExcelProperty(value = "是否收费")
    private String isCharge;
    
}
