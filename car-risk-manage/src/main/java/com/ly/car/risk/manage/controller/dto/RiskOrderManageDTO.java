package com.ly.car.risk.manage.controller.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ColumnWidth(25)
public class RiskOrderManageDTO {

    @ExcelProperty(value = "订单编号",index = 0)
    private String orderId;

    /**
     * 分销商订单编号
     */
    @ExcelProperty(value = "分销订单号",index = 1)
    private String distributeOrderId;

    /**
     * 渠道编号
     */
    private String channelId;

    /**
     * 渠道名称
     */
    @ExcelProperty(value = "渠道名称",index = 2)
    private String channelName;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编号",index = 3)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称",index = 4)
    private String supplierName;

    /**
     * 供应商订单号
     */
    @ExcelProperty(value = "供应商订单号",index = 5)
    private String supplierOrderId;

    /**
     * 风险名称
     */
    @ExcelProperty(value = "风险名称",index = 6)
    private String riskTypeName;

    /**
     * 规则编号
     */
    @ExcelProperty(value = "规则名称",index = 7)
    private String ruleNo;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间",index = 8)
    private Date operateTime;


    /**
     * 城市名称
     */
    @ExcelProperty(value = "城市名称",index = 9)
    private String cityName;

    /**
     * 0-初始化 1-是 2-否
     */
//    @ExcelProperty(value = "风险单",index = 10)
    private Integer isRisk;

//    @ExcelProperty(value = "操作人",index = 11)
    private String operateUser;

    @ExcelProperty(value = "业务线",index = 10)
    private String productLine;

    @ExcelProperty(value = "完单时间",index = 11)
    private Date finishTime;
    @ExcelProperty(value = "订单金额",index = 12)
    private BigDecimal totalAmount;
    @ExcelProperty(value = "优惠券名称",index = 13)
    private String couponName;
    @ExcelProperty(value = "优惠券批次号",index = 14)
    private String couponBatchNo;
    @ExcelProperty(value = "优惠券金额",index = 15)
    private BigDecimal couponAmount;
    @ExcelProperty(value = "实际公里",index = 16)
    private BigDecimal actualKilo;
    @ExcelProperty(value = "附加费",index = 17)
    private BigDecimal surcharge;
    @ExcelProperty(value = "实际时长",index = 18)
    private Integer actualDuration;
    @ExcelProperty(value = "司机编号",index = 19)
    private String driverCardNo;
//    @ExcelProperty(value = "子供应商",index = 21)
    private String supplierCodeFull;
    @ExcelProperty(value = "处罚结果",index = 20)
    private String judgeResult;
    @ExcelProperty(value = "申诉理由",index = 21)
    private String appealText;

}
