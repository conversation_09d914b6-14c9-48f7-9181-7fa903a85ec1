package com.ly.car.risk.manage.controller.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class RiskSensitiveActionDTO {
    
    @ExcelProperty("编号")
    private Long id;
    
    @ExcelProperty("操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 菜单模块-SensitiveModuleEnum
     */
    @ExcelProperty("菜单模块")
    private String module;
    
    /**
     * 敏感行为-SensitiveActionEnum
     */
    @ExcelProperty("敏感行为")
    private String action;
    
    /**
     * 敏感字段-SensitiveKeyEnum
     */
    @ExcelProperty("敏感字段")
    private String key;
    
    /**
     * 敏感值所属数据行主键/code
     */
    @ExcelProperty("源编号")
    private String sourceCode;
    
    /**
     * 创建人
     */
    @ExcelProperty("操作人")
    private String createUser;
    
}
