package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 敏感信息操作管理
 * risk_sensitive_action
 */
@Data
public class RiskSensitiveAction implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 记录编号
     */
    private String code;
    
    /**
     * 菜单模块-SensitiveModuleEnum
     */
    private String module;
    
    /**
     * 敏感行为-SensitiveActionEnum
     */
    private String action;
    
    /**
     * 敏感字段-SensitiveKeyEnum
     */
    private String sensitiveKey;
    
    /**
     * 敏感值(证件、手机、下载链接等)
     */
    private String value;
    
    /**
     * 敏感值所属数据行主键/code
     */
    private String sourceCode;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 操作人
     */
    private String updateUser;
    
    private static final long serialVersionUID = 1L;
}