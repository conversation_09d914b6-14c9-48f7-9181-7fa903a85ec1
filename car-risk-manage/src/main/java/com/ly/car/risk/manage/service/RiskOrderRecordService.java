package com.ly.car.risk.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskOrderRecord;
import com.ly.car.risk.manage.repo.risk.mapper.RiskOrderRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class RiskOrderRecordService {

    @Resource
    private RiskOrderRecordMapper riskOrderRecordMapper;

    public void insertRecord(String orderId,String ruleNo,Integer isRisk,String operateUser,String remark){
        RiskOrderRecord record = new RiskOrderRecord();
        record.setCreateTime(new Date());
        record.setOperateTime(new Date());
        record.setOrderId(orderId);
        record.setRuleNo(ruleNo);
        record.setIsRisk(isRisk);
        record.setOperateUser(operateUser);
        record.setRemark(remark);
        riskOrderRecordMapper.insert(record);
    }

    public List<RiskOrderRecord> getList(String orderId){
        List<RiskOrderRecord> recordList = new RiskOrderRecord().selectList(
                new QueryWrapper<RiskOrderRecord>().eq("order_id",orderId)
        );
        return recordList;
    }
}
