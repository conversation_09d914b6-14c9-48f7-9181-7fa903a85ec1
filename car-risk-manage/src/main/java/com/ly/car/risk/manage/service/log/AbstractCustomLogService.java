package com.ly.car.risk.manage.service.log;


import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.utils.OperatorLogUtils;

/**
 * <AUTHOR>
 * @version Id: AbstractCustomLogService, v 0.1 2018/10/29 下午2:00 lingfenglee Exp $
 */
public abstract class AbstractCustomLogService {

    /**
     * 获取前后内容差异日志
     * @param oldValue 修改前的内容
     * @param newValue 修改后的内容
     * @return 修改内容
     */
    public String getDesc(Object oldValue, Object newValue) throws BizException {
        return getLogValue(oldValue) + OperatorLogUtils.ARROWS + getLogValue(newValue);
    }

    /**
     * 获取转换后的值，如: 1 -> 有效
     * 子类需要重写该方法，获取自己转换后的值
     * @param value 转换前的值
     * @return 转换后的值
     */
    public String getLogValue(Object value) throws BizException {
        return null;
    }

}
