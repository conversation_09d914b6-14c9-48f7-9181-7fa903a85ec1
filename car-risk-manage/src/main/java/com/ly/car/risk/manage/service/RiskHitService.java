package com.ly.car.risk.manage.service;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.excel.EasyExcel;
import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.manage.bean.BillDateConverter;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.manage.controller.dto.RiskHitDTO;
import com.ly.car.risk.manage.controller.dto.RiskHitLinkDTO;
import com.ly.car.risk.manage.controller.params.RiskHitLinkResponse;
import com.ly.car.risk.manage.controller.params.RiskHitRequestParams;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionAddReq;
import com.ly.car.risk.manage.repo.risk.mapper.RiskHitMapper;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class RiskHitService {

    @Resource
    private RiskHitMapper riskHitMapper;
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;

    public Pagination getList(RiskHitRequestParams params) {
        long total = this.riskHitMapper.total(params);
        List<RiskHitDTO> list = this.riskHitMapper.queryList(params);
        format(list);
        return new Pagination<>(list,total,params);
    }
    
    private static void format(List<RiskHitDTO> list) {
        list.parallelStream().forEach(dto -> {
            
            if (Objects.equals(dto.getCustomerType(), RiskCustomerCustomerTypeEnum.user_phone.getCode())) {
                
                dto.setCustomerValue(DesensitizedUtil.mobilePhone(dto.getCustomerValue()));
                
            } else if (Objects.equals(dto.getCustomerType(), RiskCustomerCustomerTypeEnum.user_cert_no.getCode())) {
                
                dto.setCustomerValue(DesensitizedUtil.idCardNum(dto.getCustomerValue(), 4, 4));
                
            } else if (Objects.equals(dto.getCustomerType(), 0)) {
                
                if (StringUtils.isNotBlank(dto.getCustomerValue())
                        && !Validator.hasChinese(dto.getCustomerValue())
                        && StringUtils.isNotBlank(dto.getCustomerValue())
                        && dto.getCustomerValue().length() > 4) {
                    
                    int length = (dto.getCustomerValue().length() - 4) / 2;
                    dto.setCustomerValue(DesensitizedUtil.idCardNum(dto.getCustomerValue(), dto.getCustomerValue().length() % 2 == 0 ? length : length + 1, length));
                }
            }
            
            dto.setPassengerCellphone(DesensitizedUtil.mobilePhone(dto.getPassengerCellphone()));
            dto.setCustomerTypeName(RiskCustomerCustomerTypeEnum.getMsgByCode(dto.getCustomerType()));
        });
    }
    
    public List<RiskHitLinkResponse> getDetailList(String requestId) {
        List<RiskHitLinkDTO> detailByRequestId = riskHitMapper.getDetailByRequestId(requestId);
        return detailByRequestId.stream().map(t -> {
            RiskHitLinkResponse response = new RiskHitLinkResponse();
            BeanUtils.copyProperties(t, response);
            return response;
        }).collect(Collectors.toList());
    }

    public String exportData(RiskHitRequestParams request, HttpServletResponse response) {
        List<RiskHitDTO> detailByRequestId = riskHitMapper.queryAllList(request);
        if (CollectionUtils.isEmpty(detailByRequestId)) {
            return "nothing to export";
        }
        format(detailByRequestId);
        
        String fileName = "命中风控信息文件_" + System.currentTimeMillis() + ".xlsx";
        
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, RiskHitDTO.class)
                .registerConverter(new BillDateConverter())
                .sheet("sheet1")
                .doWrite(detailByRequestId);
        String upload = FileUploadCephUtil.upload(fileName, byteArrayOutputStream);
        addSensitiveAction(upload);
        return upload;
    }
    
    private void addSensitiveAction(String upload) {
        RiskSensitiveActionAddReq record = new RiskSensitiveActionAddReq();
        record.setModule(SensitiveModuleEnum.hit_risk_manage_list.name());
        record.setAction(SensitiveActionEnum.export.name());
        record.setKey(SensitiveKeyEnum.file.name());
        record.setValue(upload);
        riskSensitiveActionService.add(record);
    }
    
    public static String encodeFileName(String fileName) {
        String name = fileName;
        try{
            name = URLEncoder.encode(name,"utf-8");
        } catch(UnsupportedEncodingException e){
            log.error("URL Encoder编码异常",e);
        }
        return name;
    }
}
