package com.ly.car.risk.manage.utils;

import com.amazonaws.AmazonClientException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.*;
import com.ly.car.risk.manage.bean.FileUpCephResult;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Ceph 对象存储(云存储) 工具类
 * http://wiki.17usoft.com/pages/viewpage.action?pageId=15040396
 */
@Slf4j
public class FileUploadCephUtil {

    private static String ACCESS_KEY = "DEPRF1J48VRT0FA2KAEP";
    private static String ACCESS_SECRET_KEY = "OABYuMDxNoBL951101dm9YWjbkrjQCAPy9DDYkeJ";
    private static String END_POINT = "http://tcstore1.17usoft.com";
    private static String BUCKET = "balance_excel";

    private static AmazonS3Client s3client = null;
    private static AmazonS3Client getInstance(){
        if(s3client == null){
            AWSCredentials credentials = new BasicAWSCredentials(ACCESS_KEY, ACCESS_SECRET_KEY);
            ClientConfiguration clientCfg = new ClientConfiguration();
            clientCfg.setProtocol(Protocol.HTTP);
            s3client = new AmazonS3Client(credentials, clientCfg);
            s3client.setEndpoint(END_POINT);
            s3client.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
        }
        return s3client;
    }

    /**
     * 根据 MultipartFile 上传到 对象存储
     * @param fileName 下载文件名
     * @param multipartFile 文件
     * @return 文件url,为空则上传失败
     */
    public static String upload(String fileName,MultipartFile multipartFile){
        try {
            InputStream input = new ByteArrayInputStream(multipartFile.getBytes());
            return putObjByStream( fileName, input).getUrl();
        }catch (Exception e){
            log.error("[][][][][] Ceph上传 uploadByMultipartFile异常", e);
        }
        return null;
    }

    /**
     * 根据 MultipartFile 上传到 对象存储
     * @param fileName 下载文件名
     * @param file 文件
     * @return 文件url,为空则上传失败
     */
    public static String upload(String fileName,File file){
        try {
            InputStream input = new FileInputStream(file);
            return putObjByStream( fileName, input).getUrl();
        }catch (Exception e){
            log.error("[][][][][] Ceph上传 uploadByMultipartFile异常", e);
        }
        return null;
    }

    /**
     * 根据 InputStream 上传到 对象存储
     * @param fileName 下载文件名
     * @param input 文件流
     * @return 文件url,为空则上传失败
     */
    public static String upload(String fileName, InputStream input){
        return putObjByStream(fileName,input).getUrl();
    }

    /**
     * 根据 byteArrayOutputStream 上传到 对象存储
     * @param fileName 下载文件名
     * @return 文件url,为空则上传失败
     */
    public static String upload(String fileName, ByteArrayOutputStream byteArrayOutputStream){
        FileUpCephResult fileUpCephResult = uploadAndGetDeatil(fileName, byteArrayOutputStream);
        if( fileUpCephResult==null ){
            return "";
        }
        return fileUpCephResult.getUrl();

    }

    /**
     * 根据 byteArrayOutputStream 上传到 对象存储
     * @param fileName 下载文件名
     * @return 文件url,为空则上传失败
     */
    public static FileUpCephResult uploadAndGetDeatil(String fileName, InputStream input){
        return putObjByStream(fileName,input);
    }

    /**
     * 根据 byteArrayOutputStream 上传到 对象存储
     * @param fileName 下载文件名
     * @return 文件url,为空则上传失败
     */
    public static FileUpCephResult uploadAndGetDeatil(String fileName, ByteArrayOutputStream byteArrayOutputStream){
        try {
            InputStream inputStream =  new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            return putObjByStream(fileName, inputStream);
        }catch (Exception e){
            log.error("[][][][][] Ceph上传 uploadBybyteArrayOutputStream异常",e);
        }finally {
            try {
                byteArrayOutputStream.flush();
                byteArrayOutputStream.close();
            }catch (Exception e2){
                log.error("[][][][][] Ceph上传 uploadBybyteArrayOutputStream close异常",e2);
            }
        }
        return null;
    }


    /**
     * 预生成url, 供第三方上传object到这个url使用
     */
    private static URL getPresignedURL(String key) {

        try {
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(BUCKET, key);
            Date expirationDate = new SimpleDateFormat("yyyy-MM-dd").parse("2029-12-31");
            request.setExpiration(expirationDate);
            return s3client.generatePresignedUrl(request);

        } catch (Exception e) {
            throw new RuntimeException("[][][][][] Ceph上传 getPresignedURL error " + e.getMessage());
        }
    }

    /**
     * 上传stream
     * @param fileName
     * @param input
     * @return
     * @throws IOException
     */
    private static FileUpCephResult putObjByStream(String fileName, InputStream input){
        try{
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(input.available());
            String key = getKey(fileName);

            int len = 0;
            //6m传一次
            byte[] buffer = new byte[6 * 1024 * 1024];

            // 创建一个列表保存所有分传的 PartETag, 在分段完成后会用到
            List<PartETag> partETags = new ArrayList<PartETag>();
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(BUCKET, key);
            InitiateMultipartUploadResult initResponse = getInstance().initiateMultipartUpload(initRequest);
            int i = 1;
            while ((len = input.read(buffer)) != -1) {
                // 分段片
                UploadPartRequest uploadRequest = new UploadPartRequest()
                        .withBucketName(BUCKET)
                        .withKey(key)
                        .withUploadId(initResponse.getUploadId())
                        .withPartNumber(i)
                        .withInputStream(new ByteArrayInputStream(buffer))
                        .withPartSize(len);

                // 转换
                UploadPartResult uploadResult = getInstance().uploadPart(uploadRequest);
                partETags.add(uploadResult.getPartETag());

                i++;
            }

            // 开始上传.
            CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(BUCKET, key,
                    initResponse.getUploadId(), partETags);
            CompleteMultipartUploadResult completeMultipartUploadResult = getInstance().completeMultipartUpload(compRequest);
            FileUpCephResult fileUpCephResult = new FileUpCephResult();
            fileUpCephResult.setMd5(completeMultipartUploadResult.getETag());
            fileUpCephResult.setKey(key);
            fileUpCephResult.setFileName(fileName);
            //获取url
            fileUpCephResult.setUrl(getPresignedURL(key).toString());
            return fileUpCephResult;
        }catch(AmazonClientException clientException){
            log.error("[][][][][] Ceph上传 AmazonClientException异常!", clientException);
        } catch (IOException e) {
            log.error("[][][][][] Ceph上传 IOException!", e);
        }catch (Exception e){
            log.error("[][][][][] Ceph上传 Exception!", e);
        }finally {
            try {
                input.close();
            }catch (Exception e){
                log.error("[][][][][] Ceph上传 close Exception!", e);
            }
        }
        return null;
    }

    /**
     * 获取key(文件目录),防止同一名称文件上传覆盖
     * @param fileName
     * @return
     */
    private static String getKey( String fileName ){
        return new SimpleDateFormat("yyyyMMdd").format(new Date())+"/"+System.currentTimeMillis()+"/"+fileName;
    }

    public static void main(String[] args) {
        try {
            InputStream inputStream = new FileInputStream(new File("D:\\xlstest2\\20220704.xlsx"));
            FileUpCephResult fileUpCephResult = putObjWithPublicRead("666test23.xlsx", inputStream);
            System.out.println( JsonUtils.json(fileUpCephResult) );
        }catch (Exception e){

        }
    }

    /**
     * 上传到公网
     * @param fileName
     * @param byteArrayOutputStream
     * @return
     */
    public static FileUpCephResult putObjWithPublicRead( String fileName, ByteArrayOutputStream byteArrayOutputStream){
        try {
            InputStream input = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            return putObjWithPublicRead(fileName, input);
        }catch (Exception e){
            log.error("[][][][][] Ceph公网上传异常e!", e);
        }finally {
            try {
                if(byteArrayOutputStream!=null){
                    byteArrayOutputStream.flush();
                    byteArrayOutputStream.close();
                }
            }catch (Exception e){
                log.error("[][][][][] Ceph公网上传流关闭异常!b", e);
            }
        }
        return null;
    }

    /**
     * 上传到公网
     * @param fileName
     * @param input
     * @return
     */
    public static FileUpCephResult putObjWithPublicRead( String fileName, InputStream input){
        try{

            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(input.available());
            String key = getKey(fileName);
            PutObjectRequest request = new PutObjectRequest(BUCKET, key, input, meta);
            request.setCannedAcl(CannedAccessControlList.PublicRead);
            PutObjectResult putObjectResult = getInstance().putObject(request);
            FileUpCephResult fileUpCephResult = new FileUpCephResult();
            fileUpCephResult.setFileName(fileName);
            fileUpCephResult.setMd5(putObjectResult.getETag());
            fileUpCephResult.setKey(key);
            String url = getURLWithPublicRead(BUCKET, key).toString();
            //公网地址需替换
            fileUpCephResult.setUrl(url.replace("tcstore1.17usoft.com","file.40017.cn"));
            return fileUpCephResult;
        }catch(AmazonClientException | IOException clientException){
            log.error("[][][][][] Ceph公网上传异常!", clientException);
        }finally {
            try {
                if(input!=null){
                    input.close();
                }
            }catch (Exception e){
                log.error("[][][][][] Ceph公网上传流关闭异常!", e);
            }

        }
        return null;
    }

    public static URL getURLWithPublicRead(String bucket, String key) {
        return getInstance().getUrl(bucket, key);
    }

}
