package com.ly.car.risk.manage.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.car.risk.entity.DistributionOfflineRisk;
import com.ly.car.risk.manage.repo.risk.mapper.entity.DistributionRiskManage;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.manage.repo.order.mapper.DistributionInfoMapper;
import com.ly.car.risk.manage.repo.risk.mapper.DistributionOfflineRiskMapper;
import com.ly.car.risk.manage.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.manage.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.manage.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskOrderManage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("data")
public class DataController {

    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private DistributionOfflineRiskMapper distributionOfflineRiskMapper;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;

    @RequestMapping("riskManage")
    public String remove(@RequestBody DistributionRiskManage manage){
        DistributionRiskManage distributionRiskManage = distributionRiskManageMapper.selectById(manage.getId());
        if(distributionRiskManage != null && StringUtils.isNotBlank(distributionRiskManage.getOrderId())){
            if(manage.getMainScene().equals(distributionRiskManage.getMainScene())
                    && manage.getChildScene().equals(distributionRiskManage.getChildScene())){

            } else {
                manage.setId(null);
                distributionRiskManageMapper.insert(manage);
            }
        } else {
            manage.setId(null);
            distributionRiskManageMapper.insert(manage);
        }
        return "success";
    }

    @RequestMapping("distributionRisk")
    public String distributionRiskRemove(@RequestBody DistributionOfflineRisk risk){
        DistributionOfflineRisk already = distributionOfflineRiskMapper.selectOne(
                new QueryWrapper<DistributionOfflineRisk>().eq("order_id",risk.getOrderId())
        );
        if(already == null){
            risk.setId(null);
            distributionOfflineRiskMapper.insert(risk);
        }
        return "success";
    }

    @RequestMapping("customerManage")
    public String customerManageRemove(@RequestBody RiskCustomerManage riskCustomerManage){
        RiskCustomerManage manage = riskCustomerManageMapper.selectById(riskCustomerManage.getId());
        if(manage == null){
            riskCustomerManageMapper.insert(riskCustomerManage);
        }
        return "success";
    }

    @RequestMapping("deleteRiskOrder")
    public String deleteRiskOrder(){
        riskOrderManageMapper.delete(
                new UpdateWrapper<RiskOrderManage>().gt("id",0)
        );
        return "success";
    }

    @RequestMapping("deleRiskManage")
    public String deleRiskManage(){
        distributionRiskManageMapper.delete(
                new UpdateWrapper<DistributionRiskManage>().gt("id",0)
        );
        return "success";
    }

    @RequestMapping("deleteManageByRule")
    public String deleteManageByRule(){
        distributionRiskManageMapper.delete(new UpdateWrapper<DistributionRiskManage>()
                .eq("main_scene",3).eq("child_scene",5));
        return "success";
    }

    @RequestMapping("deleteManageBy015")
    public String deleteManageBy015(){
        distributionRiskManageMapper.delete(new UpdateWrapper<DistributionRiskManage>()
                .eq("main_scene",3).eq("child_scene",5)
                .eq("rule_no_list","015")
        );
        riskOrderManageMapper.delete(new UpdateWrapper<RiskOrderManage>()
                .eq("rule_no","015").likeRight("order_id","SFC")
        );
        return "success";
    }
}
