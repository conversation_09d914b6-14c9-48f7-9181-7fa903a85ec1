package com.ly.car.risk.manage.handle.dashboard;

import com.ly.car.risk.manage.model.enums.RiskDistributionTypeEnum;
import com.ly.car.risk.manage.model.resp.dashboard.RiskDistributionData;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;

/**
 * Description of RiskDistributionHandler
 *
 * <AUTHOR>
 * @date 2024/12/10
 * @desc
 */
public interface RiskDistributionHandler {

    RiskDistributionTypeEnum support();

    RiskDistributionData riskDistribution(List<Pair<Date, Date>> linkRelativeRatioDates);

}