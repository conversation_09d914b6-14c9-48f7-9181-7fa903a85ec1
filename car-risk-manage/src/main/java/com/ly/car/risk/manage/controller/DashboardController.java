package com.ly.car.risk.manage.controller;

import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.model.enums.OverviewTypeEnum;
import com.ly.car.risk.manage.model.req.DashboardOfflineStrategyReq;
import com.ly.car.risk.manage.model.req.DashboardRealTimeStrategyReq;
import com.ly.car.risk.manage.model.req.DashboardRiskDistributionReq;
import com.ly.car.risk.manage.model.req.DashboardRiskPeakReq;
import com.ly.car.risk.manage.model.req.DashboardRiskTendReq;
import com.ly.car.risk.manage.model.resp.DashboardOfflineStrategyResp;
import com.ly.car.risk.manage.model.resp.DashboardRealTimeStrategyResp;
import com.ly.car.risk.manage.model.resp.DashboardRiskDistributionResp;
import com.ly.car.risk.manage.model.resp.DashboardRiskPeakResp;
import com.ly.car.risk.manage.model.resp.DashboardRiskTendResp;
import com.ly.car.risk.manage.service.dashboard.DashboardService;
import com.ly.car.risk.manage.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description of DashboardController
 *
 * <AUTHOR>
 * @date 2024/12/9
 * @desc
 */
@RestController
@RequestMapping("dashboard")
@Slf4j
public class DashboardController {

    @Resource
    private DashboardService dashboardService;

    @RequestMapping(value = "/riskTend",method = RequestMethod.POST)
    public DashboardRiskTendResp riskTend(@RequestBody DashboardRiskTendReq req) throws BizException {
        try {
            OverviewTypeEnum overviewType = OverviewTypeEnum.of(req.getType());
            if(null == overviewType){
                throw new BizException(-1,"");
            }
            DashboardRiskTendResp resp = dashboardService.riskTend(overviewType);
            return resp;
        } catch (BizException e) {
            LoggerUtils.error(log,"riskTend error",e);
            throw e;
        }
    }

    @RequestMapping(value = "/riskPeak",method = RequestMethod.POST)
    public DashboardRiskPeakResp riskPeak(@RequestBody DashboardRiskPeakReq req) throws BizException {
        try {
            OverviewTypeEnum overviewType = OverviewTypeEnum.of(req.getType());
            if(null == overviewType){
                throw new BizException(-1,"");
            }
            DashboardRiskPeakResp resp = dashboardService.riskPeak(overviewType);
            return resp;
        } catch (BizException e) {
            LoggerUtils.error(log,"riskTend error",e);
            throw e;
        }
    }

    @RequestMapping(value = "/riskDistribution",method = RequestMethod.POST)
    public DashboardRiskDistributionResp riskDistribution(@RequestBody DashboardRiskDistributionReq req) throws BizException {
        try {
            OverviewTypeEnum overviewType = OverviewTypeEnum.of(req.getType());
            if(null == overviewType){
                throw new BizException(-1,"");
            }
            DashboardRiskDistributionResp resp = dashboardService.riskDistribution(overviewType);
            return resp;
        } catch (BizException e) {
            LoggerUtils.error(log,"riskTend error",e);
            throw e;
        }
    }

    @RequestMapping(value = "/offlineStrategyInfo",method = RequestMethod.POST)
    public DashboardOfflineStrategyResp offlineStrategyInfo(@RequestBody DashboardOfflineStrategyReq req){
        try {
            DashboardOfflineStrategyResp resp = dashboardService.offlineStrategyInfo(req);
            return resp;
        } catch (Exception e) {
            LoggerUtils.error(log,"riskTend error",e);
            throw e;
        }
    }
    
    @RequestMapping(value = "/realTimeStrategyInfo",method = RequestMethod.POST)
    public DashboardRealTimeStrategyResp realTimeStrategyInfo(@RequestBody DashboardRealTimeStrategyReq req){
        try {
            DashboardRealTimeStrategyResp resp = dashboardService.realTimeStrategyInfo(req);
            return resp;
        } catch (Exception e) {
            LoggerUtils.error(log,"realTimeStrategyInfo error",e);
            throw e;
        }
    }
}
