package com.ly.car.risk.manage.controller;

import com.ly.car.order.entity.SupplierInfo;
import com.ly.car.risk.common.enums.ApiProviderEnum;
import com.ly.car.risk.common.enums.ChargeCallTypeEnum;
import com.ly.car.risk.common.enums.OrderProductLineEnum;
import com.ly.car.risk.common.enums.VoiceApiProviderEnum;
import com.ly.car.risk.common.enums.VoiceProductTypeEnum;
import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.manage.api.TrafficApi;
import com.ly.car.risk.manage.constants.*;
import com.ly.car.risk.common.dto.CommonOpt;
import com.ly.car.risk.manage.controller.dto.CommonReturnDTO;
import com.ly.car.risk.manage.controller.dto.NameValue;
import com.ly.car.risk.manage.repo.order.mapper.SupplierInfoMapper;
import com.ly.car.risk.manage.service.BasicCityService;
import com.ly.car.risk.manage.service.dto.CityDTO;
import com.ly.car.risk.manage.service.dto.CommonCityDTO;
import com.ly.car.traffic.entity.BasicDataCity;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequestMapping("/common")
@RestController
@Slf4j
public class CommonController {

    @Resource
    private TrafficApi trafficApi;
    @Resource
    private SupplierInfoMapper supplierInfoMapper;
    @Resource
    private BasicCityService basicCityService;

    /**
     * 查询城市
     * */
    @RequestMapping("queryCity")
    public List<CityDTO> queryCity(){
        List<CityDTO> cityDTOList = new ArrayList<>();
        List<BasicDataCity> list = basicCityService.all();
        CityDTO cityAll = new CityDTO();
        cityAll.setCityId("0000");
        cityAll.setName("全国");
        cityDTOList.add(cityAll);
        if (CollectionUtils.isNotEmpty(list)) {
            cityDTOList.addAll(list.parallelStream().map(item -> {
                CityDTO cityDTO = new CityDTO();
                cityDTO.setCityId(item.getTcId() + "");
                cityDTO.setName(item.getNameShort());
                return cityDTO;
            }).collect(Collectors.toList()));
        }
        return cityDTOList;
    }
    
    @RequestMapping("queryCommonCity")
    public List<CommonCityDTO> queryCommonCity(){
        List<CommonCityDTO> cityDTOList = new ArrayList<>();
        List<BasicDataCity> list = basicCityService.all();
        CommonCityDTO cityAll = new CommonCityDTO();
        cityAll.setCityId(0L);
        cityAll.setName("全国");
        cityDTOList.add(cityAll);
        if (CollectionUtils.isNotEmpty(list)) {
            cityDTOList.addAll(list.parallelStream().map(item -> {
                CommonCityDTO cityDTO = new CommonCityDTO();
                cityDTO.setCityId(item.getTcId());
                cityDTO.setName(item.getNameShort());
                return cityDTO;
            }).collect(Collectors.toList()));
        }
        return cityDTOList;
    }

    /**
     * 查询供应商
     * */
    @RequestMapping("querySupplier")
    public List<SupplierInfo> querySupplier(){
        List<SupplierInfo> supplierInfos = supplierInfoMapper.queryAllSupplier();
        supplierInfos = supplierInfos.parallelStream().filter(e->!e.getCode().contains("_")).collect(Collectors.toList());
        return supplierInfos;
    }

    @RequestMapping("querySupplierAll")
    public List<SupplierInfo> querySupplierAll(){
        List<SupplierInfo> supplierInfos = supplierInfoMapper.queryAllSupplier();
        return supplierInfos;
    }

    /**
     * 查询渠道
     * */
    @RequestMapping("queryChannel")
    public Map<String,String> queryChannel(){
        return ChannelEnum.getAllEnum();
    }

    /**
     *  查询风险场景
     * */
    @RequestMapping("queryScene")
    public List<CommonReturnDTO> queryMainScene(){
        return MainSceneEnum.getAllEnum();
    }

//    /**
//     * 查询风险子场景
//     * */
//    @RequestMapping("queryChildScene")
//    public Map<Integer,String> queryChildScene(@RequestBody Map<String,Integer> codeMap){
//        return ChildSceneEnum.getAllEnum(codeMap.get("code"));
//    }

    @RequestMapping("queryChannelTypes")
    public List<NameValue> queryChannelTypes(){
        return ChannelTypeEnums.getAll();
    }

    @RequestMapping("queryOrderStatus")
    public List<CommonReturnDTO> queryOrderStatus(){
        return OrderStatusEnum.getAllEnum();
    }

    @RequestMapping("queryRiskType")
    public List<CommonReturnDTO> queryRiskType(){
        return NewRiskOrderTypeEnum.getAllEnum();
    }

    @RequestMapping("queryRuleNo")
    public List<NameValue> queryRuleNo(){
        return RuleNoEnum.getAllEnum();
    }
    
    @RequestMapping("enum")
    public List<CommonOpt> queryCommonEnum(@RequestParam("name") String name){
        if(StringUtils.isBlank(name)){
            return Collections.emptyList();
        }
        switch (name){
            case "ApiProviderEnum":
                return ApiProviderEnum.getAllEnum();
            case "ChargeCallTypeEnum":
                return ChargeCallTypeEnum.getAllEnum();
            case "VoiceProductTypeEnum":
                return VoiceProductTypeEnum.getAllEnum();
            case "VoiceApiProviderEnum":
                return VoiceApiProviderEnum.getAllEnum();
            case "OrderProductLineEnum":
                return OrderProductLineEnum.getAllEnum();
            case "SensitiveModuleEnum":
                return SensitiveModuleEnum.getAllEnum();
            case "SensitiveActionEnum":
                return SensitiveActionEnum.getAllEnum();
            case "SensitiveKeyEnum":
                return SensitiveKeyEnum.getAllEnum();
        }
        return Collections.emptyList();
    }
}
