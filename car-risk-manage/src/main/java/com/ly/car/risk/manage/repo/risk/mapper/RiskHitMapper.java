package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.manage.controller.dto.RiskHitDTO;
import com.ly.car.risk.manage.controller.dto.RiskHitLinkDTO;
import com.ly.car.risk.manage.model.resp.dashboard.DistributionItem;
import com.ly.car.risk.manage.model.resp.dashboard.PeakItem;
import com.ly.car.risk.manage.model.resp.dashboard.StrategyHitData;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface RiskHitMapper extends BaseMapper<RiskHitDTO> {

    long total(Object query);

    List<RiskHitDTO> queryList(Object query);

    List<RiskHitDTO> queryAllList(Object query);
    
    RiskHitDTO getById(@Param("id") Object id);

    List<RiskHitLinkDTO> getDetailByRequestId(@Param("requestId") String requestId);

    List<PeakItem> findTopCity(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("top") int top);

    List<PeakItem> findTopScene(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("top") int top);

    List<PeakItem> findTopBusiness(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("top") int top);

    List<DistributionItem> findTopRules(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("top") int top);

    List<DistributionItem> findRulePeriodData(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("rules") List<String> rules);

    List<RiskHitDTO> queryRiskDriverRecords(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("env") String env);

    List<RiskHitDTO> queryRiskOnlineOrders(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("env") String env);

    List<RiskHitDTO> queryRiskUserRecords(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("env") String env);
    
    Integer findRiskDriverCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("env") String env, @Param("strategyNo") String strategyNo, @Param("productLineList") List<String> productLineList);
    
    Integer findRiskUserCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("env") String env, @Param("strategyNo") String strategyNo, @Param("productLineList") List<String> productLineList);
    
    List<StrategyHitData> queryStrategyDistribution(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("env") String env, @Param("top") Integer top, @Param("strategyNo") String strategyNo, @Param("productLineList") List<String> productLineList);
}
