package com.ly.car.risk.manage.repo.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.OrderWxCard;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OrderWxCardMapper extends BaseMapper<OrderWxCard> {

    @Select("<script>" +
            "   select * from order_wx_card where order_id = #{orderId}}" +
            "</script>")
    List<OrderWxCard> getListByOrderId(@Param("orderId")String orderId);
}
