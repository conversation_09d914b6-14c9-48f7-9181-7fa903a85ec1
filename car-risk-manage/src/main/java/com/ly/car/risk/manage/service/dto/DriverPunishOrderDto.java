package com.ly.car.risk.manage.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class DriverPunishOrderDto {
    
    @ExcelProperty(value = "司机车牌")
    private String carNum;
    
    @ExcelProperty(value = "创建时间")
    private String punishDate;
    
    @ExcelProperty(value = "追款订单数")
    private Integer punishOrderCount;
    
    @ExcelProperty(value = "预警级别")
    private String levelName;
}

