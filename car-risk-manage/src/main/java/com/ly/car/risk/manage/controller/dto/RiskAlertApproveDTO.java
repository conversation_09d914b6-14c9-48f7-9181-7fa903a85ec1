package com.ly.car.risk.manage.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

@Data
public class RiskAlertApproveDTO {
    /**
     * 主键id
     */
    private Long id;
    
    /**
     * 预警编号
     */
    private String code;
    
    /**
     * 预警级别 1:关注 2:高风险
     */
    private Integer level;
    
    /**
     * 预警主体 USER:用户 DRIVER:司机 SUPPLIER:供应商
     */
    private String target;
    
    /**
     * 预警值
     */
    private String targetValue;
    
    /**
     * 预警场景 MARKETING:营销
     */
    private String alertScene;
    
    /**
     * 预警策略
     */
    private String alertStrategy;
    private String alertStrategyName;
    
    /**
     * 预警内容
     */
    private String alertContent;
    
    /**
     * 预警时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alertTime;
    
    /**
     * 处理状态 0:未处理 1:无风险 2:跟进中
     */
    private Integer handleResult;
    
    /**
     * 处理时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;
    
    /**
     * 处理人
     */
    private String handleUser;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 操作人
     */
    private String updateUser;
    
    private String supplierName;
    
}
