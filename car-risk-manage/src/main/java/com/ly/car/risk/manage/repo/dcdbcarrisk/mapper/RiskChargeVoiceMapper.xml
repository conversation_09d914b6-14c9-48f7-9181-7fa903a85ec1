<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper">

    <select id="getTotal" resultType="long">
        select count(*) from risk_charge_voice
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice">
        select * from risk_charge_voice
        <include refid="getCondition"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.manage.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice">
        select *
        from risk_charge_voice
        <include refid="getCondition"/>
        order by id desc
    </select>

    <sql id="getCondition">
        where 1 = 1
        <if test="callStartTime != null">
            and create_time >= #{callStartTime}
        </if>
        <if test="callEndTime != null">
            and #{callEndTime} >= create_time
        </if>
        <if test="orderNo != null and orderNo != '' ">
            and `order_no` = #{orderNo}
        </if>
        <if test="productType != null and productType != '' ">
            and `product_type` = #{productType}
        </if>
        <if test="apiProvider != null and apiProvider != '' ">
            and `api_provider` = #{apiProvider}
        </if>
        <if test="productLine != null and productLine != '' ">
            and `product_line` = #{productLine}
        </if>

    </sql>

    <select id="getSum" resultType="com.ly.car.risk.manage.controller.dto.RiskChargeVoiceSummaryDTO">
        select
        COUNT(*) AS callCount,
        ifnull(SUM(charge), 0) AS chargeSum,
        COUNT(CASE WHEN charge != 0 THEN 1 END) AS chargeCount
        from risk_charge_voice
        <include refid="getCondition"/>
    </select>

</mapper>