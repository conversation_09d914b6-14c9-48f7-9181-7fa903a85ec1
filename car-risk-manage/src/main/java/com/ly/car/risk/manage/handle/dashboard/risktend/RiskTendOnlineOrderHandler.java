package com.ly.car.risk.manage.handle.dashboard.risktend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.manage.controller.dto.RiskHitDTO;
import com.ly.car.risk.manage.handle.dashboard.RiskTendHandler;
import com.ly.car.risk.manage.model.enums.RiskTendTypeEnum;
import com.ly.car.risk.manage.model.resp.dashboard.RiskTendData;
import com.ly.car.risk.manage.model.resp.dashboard.RiskTendSimpleData;
import com.ly.car.risk.manage.repo.risk.mapper.RiskHitMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Description of RiskTendOnlineOrderHandler
 *
 * <AUTHOR>
 * @date 2024/12/9
 * @desc
 */
@Service
public class RiskTendOnlineOrderHandler implements RiskTendHandler {

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private RiskHitMapper riskHitMapper;

    @Override
    public RiskTendTypeEnum support() {
        return RiskTendTypeEnum.ONLINE_ORDER;
    }

    @Override
    public RiskTendData riskTend( Pair<List<String>, List<Date>> datePair) {
        List<Date> dateList = datePair.getValue();

        RiskTendData riskTendData = new RiskTendData();
        riskTendData.setCode(support().name());
        riskTendData.setName(support().getDesc());
        riskTendData.setDateList(datePair.getKey());
        List<Number> detail = new ArrayList<>();

        Date startTime = dateList.get(0);
        Date endTime = dateList.get(dateList.size() - 1);
        String queryEnv = env.equalsIgnoreCase("product") ? "PROD" : "STAGE";
        List<RiskHitDTO> riskOnlineOrderRecords = riskHitMapper.queryRiskOnlineOrders(startTime, endTime,queryEnv);
        for (int i = 0; i < dateList.size() - 1; i++) {
            Date point1 = dateList.get(i);
            Date point2 = dateList.get(i + 1);
            long count = riskOnlineOrderRecords.stream().filter(p -> p.getCreateTime().after(point1) && !p.getCreateTime().after(point2))
                    .map(p -> p.getOrderId()).distinct().count();
            detail.add(count);
        }
        RiskTendSimpleData simpleData = new RiskTendSimpleData(support());
        simpleData.setDetail(detail);

        riskTendData.setSimpleData(Collections.singletonList(simpleData));
        return riskTendData;
    }
}