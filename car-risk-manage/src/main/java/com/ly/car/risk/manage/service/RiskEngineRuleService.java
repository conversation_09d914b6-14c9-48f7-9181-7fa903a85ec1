package com.ly.car.risk.manage.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskRule;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.RiskEngineRuleDTO;
import com.ly.car.risk.manage.controller.dto.RiskEngineRuleDetailDTO;
import com.ly.car.risk.manage.controller.params.RiskEngineRuleParams;
import com.ly.car.risk.manage.controller.params.RuleParam;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.RiskEngineRuleMapper;
import com.ly.car.risk.manage.repo.risk.mapper.RiskRuleRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.CategoryRelation;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskEngineRule;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskRuleRelation;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskEngineRuleService {

    @Resource
    private RiskEngineRuleMapper riskEngineRuleMapper;
    @Resource
    private CategoryRelationService categoryRelationService;
    @Resource
    private RiskRuleRelationMapper riskRuleRelationMapper;

    public void add(RiskEngineRuleParams params, UserInfo userInfo){
        RiskEngineRule rule = new RiskEngineRule();
        rule.setRuleName(params.getRuleName());
        rule.setRuleNo(params.getRuleNo());
        rule.setRuleDesc(params.getRuleDesc());
        rule.setScript(this.parseScript(params.getExpression(), params.getRuleJson()));//要解析规则
        rule.setRuleJson(JsonUtils.json(params.getRuleJson()));
        rule.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
        rule.setCreateTime(new Date());
        rule.setExpression(params.getExpression());
        rule.setRiskLevel(0);
        rule.setUpdateTime(new Date());
        rule.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        this.riskEngineRuleMapper.insert(rule);

        List<RiskRuleRelation> relationList = convertRelation(params.getRuleJson(), String.valueOf(rule.getId()));
        for(RiskRuleRelation entity : relationList){
            this.riskRuleRelationMapper.insert(entity);
        }
    }

    public void delete(RiskEngineRuleParams params,UserInfo userInfo){
        RiskEngineRule rule = new RiskEngineRule();
        rule.setId(params.getId());
        rule.setIsDeleted(1);
        rule.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        rule.setUpdateTime(new Date());
        this.riskEngineRuleMapper.deleteById(rule);
    }

    public void update(RiskEngineRuleParams params,UserInfo userInfo){
        RiskEngineRule rule = new RiskEngineRule();
        rule.setRuleName(params.getRuleName());
        rule.setRuleNo(params.getRuleNo());
        rule.setRuleDesc(params.getRuleDesc());
        rule.setScript(this.parseScript(params.getExpression(), params.getRuleJson()));//要解析规则
        rule.setRuleJson(JsonUtils.json(params.getRuleJson()));
        rule.setExpression(params.getExpression());
        rule.setRiskLevel(0);
        rule.setUpdateTime(new Date());
        rule.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        rule.setId(params.getId());
        this.riskEngineRuleMapper.updateById(rule);
        //具体的规则可能有新增，有删减
        dealRelation(params.getRuleJson(), String.valueOf(rule.getId()));

    }

    public void dealRelation(List<RuleParam> params,String ruleNo){
        List<RiskRuleRelation> relationList = this.riskRuleRelationMapper.selectList(new QueryWrapper<RiskRuleRelation>()
                .eq("rule_no",ruleNo)
        );
        //这个是新传过来的
        List<RiskRuleRelation> convertList = convertRelation(params,ruleNo);
        for(RiskRuleRelation relation : relationList){
            RiskRuleRelation ruleRelation = convertList.stream().filter(data -> data.getLeftField().equals(relation.getLeftField())).findFirst().orElse(null);
            if(ruleRelation != null){
                //有了就更新
                ruleRelation.setId(relation.getId());
                this.riskRuleRelationMapper.updateById(ruleRelation);
            } else {
                this.riskRuleRelationMapper.deleteById(relation);
            }
        }
        for(RiskRuleRelation relation : convertList){
            RiskRuleRelation ruleRelation = relationList.stream().filter(data -> data.getLeftField().equals(relation.getLeftField())).findFirst().orElse(null);
            if(ruleRelation == null){
                this.riskRuleRelationMapper.insert(relation);
            }
        }
    }

    public Pagination getList(RiskEngineRuleParams params){
        long total = this.riskEngineRuleMapper.total(params);
        List<RiskEngineRule> list = this.riskEngineRuleMapper.queryList(params);
        List<RiskEngineRuleDTO> dtoList = new ArrayList<>();
        for(RiskEngineRule rule : list){
            RiskEngineRuleDTO dto = new RiskEngineRuleDTO();
            dto.setId(rule.getId());
            dto.setRuleNo(rule.getRuleNo());
            dto.setRuleName(rule.getRuleName());

            List<CategoryRelation> relationList = this.categoryRelationService.queryByRuleSceneNo(rule.getRuleNo(),0);
            List<String> strategyNos = relationList.stream().map(CategoryRelation::getStrategyNo).distinct().collect(Collectors.toList());
            dto.setLinkCategory(strategyNos.size());
            //场景是要通过策略编号查询所有的场景
            List<CategoryRelation> relationList1 = this.categoryRelationService.queryByStrategyNos(strategyNos, 1);
            dto.setLinkScene(relationList1.size());
            dto.setIsDeleted(String.valueOf(rule.getIsDeleted()));
            dto.setUpdateTime(DateUtil.date2String(rule.getUpdateTime()));
            dto.setUpdateUser(rule.getUpdateUser());
            dto.setRuleDesc(rule.getRuleDesc());
            dto.setCreateTime(DateUtil.date2String(rule.getCreateTime()));
            dto.setCreateUser(rule.getCreateUser());
            dto.setExpression(rule.getExpression());
            dto.setRuleJson(JSONArray.parseArray(rule.getRuleJson(), RuleParam.class));
            for(RuleParam param : dto.getRuleJson()){
                if(param.getOperator().equals("==")){
                    param.setOperator("=");
                }
            }
            dtoList.add(dto);
        }
        return new Pagination<>(dtoList,total,params);
    }

    public List<RiskEngineRuleDTO> getAllList(){
        List<RiskEngineRule> list = this.riskEngineRuleMapper.queryAllList();
        List<RiskEngineRuleDTO> dtoList = new ArrayList<>();
        for(RiskEngineRule rule : list){
            RiskEngineRuleDTO dto = new RiskEngineRuleDTO();
            dto.setId(rule.getId());
            dto.setRuleNo(rule.getRuleNo());
            dto.setRuleName(rule.getRuleName());
            dto.setLinkScene(rule.getLinkScene());
            dto.setLinkCategory(rule.getLinkCategory());
            dto.setIsDeleted(rule.getIsDeleted()==0?"使用":"未使用");
            dto.setUpdateTime(DateUtil.date2String(rule.getUpdateTime()));
            dto.setUpdateUser(rule.getUpdateUser());
            dto.setRuleDesc(rule.getRuleDesc());
            dto.setCreateTime(DateUtil.date2String(rule.getCreateTime()));
            dto.setCreateUser(rule.getCreateUser());
            dto.setExpression(rule.getExpression());
            dto.setRuleJson(JSONArray.parseArray(rule.getRuleJson(), RuleParam.class));
            dtoList.add(dto);
        }
        return dtoList;
    }

    public RiskEngineRuleDetailDTO detail(Long id){
        RiskEngineRule rule = this.riskEngineRuleMapper.selectById(id);
        List<RiskRuleRelation> relationList = this.riskRuleRelationMapper.selectList(new QueryWrapper<RiskRuleRelation>()
                .eq("rule_no",rule.getRuleNo())
        );
        RiskEngineRuleDetailDTO dto = new RiskEngineRuleDetailDTO();
        dto.setRuleName(rule.getRuleName());
        dto.setRuleNo(rule.getRuleNo());
        dto.setRuleDesc(rule.getRuleDesc());
        dto.setExpression(rule.getExpression());
        dto.setId(rule.getId());
        dto.setCreateTime(DateUtil.date2String(rule.getCreateTime()));
        dto.setCreateUser(rule.getCreateUser());
        dto.setUpdateTime(DateUtil.date2String(rule.getUpdateTime()));
        dto.setUpdateUser(rule.getUpdateUser());
        List<RuleParam> paramList = new ArrayList<>();
        for(RiskRuleRelation relation : relationList){
            RuleParam ruleParam = new RuleParam();
            ruleParam.setSort(relation.getSort());
            ruleParam.setFileName(relation.getLeftField());
            ruleParam.setOperator(relation.getOperator());
            ruleParam.setRightType(relation.getRightType());
            ruleParam.setValue(relation.getRightValue());
            paramList.add(ruleParam);
        }
        dto.setRuleJson(paramList);
        return dto;
    }

    public List<RiskRuleRelation> convertRelation(List<RuleParam> ruleJson,String ruleNo){
        List<RiskRuleRelation> relationList = new ArrayList<>();
        for(RuleParam param : ruleJson){
            RiskRuleRelation relation = new RiskRuleRelation();
            relation.setRuleNo(ruleNo);
            relation.setLeftField(param.getFileName());
            relation.setOperator(param.getOperator());
            relation.setRightType(param.getRightType());
            relation.setRightValue((String) param.getValue());
            relation.setSort(param.getSort());
            relation.setCreateTime(new Date());
            relationList.add(relation);
        }
        return relationList;
    }



    //有表达式，就直接用就好了，将1，2这些替换为data.xxx
    public String parseScript(String expression,List<RuleParam> ruleJson){
        List<String> operatorList = new ArrayList<>();
        operatorList.add("(");
        operatorList.add(")");
        operatorList.add("|");
        operatorList.add("&");
        List<Integer> sortList = ruleJson.stream().map(RuleParam::getSort).collect(Collectors.toList());
        Map<Integer,RuleParam> ruleMap = ruleJson.stream().collect(Collectors.toMap(RuleParam::getSort,v->v,(old,cur)->old));
        expression = expression.replace("|","||").replace("&","&&");
        //对当前表达式进行数字分割
        String[] strings = expression.split("");
        Pattern pattern = Pattern.compile("[0-9]");
        String matchNum = "";
        List<String> pipeiList = new ArrayList<>();
        int i = 0;
        for(String str : strings){
            i = i+1;
            Matcher isNum = pattern.matcher(str);
            if(isNum.matches()){
                //当前为数字就看看下面是不是数字，先记一下
                if(StringUtils.isBlank(matchNum)){
                    matchNum = str;
                } else {
                    matchNum = matchNum + str;
                }
                if(i == strings.length){
                    pipeiList.add(matchNum);
                }
            } else {
                if(StringUtils.isNotBlank(matchNum)){
                    pipeiList.add(matchNum);
                    matchNum = "";
                }
                pipeiList.add(str);
            }
        }
        String expressionAfter = "";
        for(String str : pipeiList){
            if(StringUtils.isNumeric(str)){
                RuleParam ruleParam = ruleMap.get(Integer.valueOf(str));
                if(ruleParam.getRightType() == 1){
                    ruleParam.setValue("data."+ruleParam.getValue());
                }
                if(ruleParam.getOperator().equals("=")){
                    ruleParam.setOperator("==");
                }
                expressionAfter = expressionAfter + "data."+ruleParam.getFileName()+ruleParam.getOperator()+ruleParam.getValue();
            } else {
                expressionAfter = expressionAfter + str;
            }
        }
        String classStr = "class checkRule{ " +
                "   public boolean check(def data) { " +
                "if("+
                        expressionAfter +
                "){ " +
                " return true ; " +
                "} else { " +
                " return false ; " +
                " }" +
                "}" +
                "}";
                ;
        return classStr;
    }

    public static void main(String[] args) {
        String expression = "1&2";
        List<RuleParam> list = new ArrayList<>();
        RuleParam param1 = new RuleParam();
        param1.setSort(1);
        param1.setFileName("orderCount");
        param1.setOperator(">");
        param1.setRightType(0);
        param1.setValue(5);

        RuleParam param2 = new RuleParam();
        param2.setSort(2);
        param2.setFileName("loginCount");
        param2.setOperator(">");
        param2.setRightType(0);
        param2.setValue(3);
        list.add(param1);
        list.add(param2);
//        String s = parseScript(expression, list);
//        System.out.println(s);


    }


}
