package com.ly.car.risk.manage.oauth.service;

import com.ly.car.risk.manage.oauth.bean.UserInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IOauthService {

    /**
     * 向SSO站点申请访问令牌
     *
     * @param response
     * @return
     */
    String applyForTokenFromSSO(HttpServletRequest request, HttpServletResponse response);

    /**
     * 检查认证服务器回传过来的state是否与客户端传过去的一致，防止CSRF攻击
     *
     * @param request
     * @param response
     * @param state
     * @return
     */
    Boolean checkState(HttpServletRequest request, HttpServletResponse response, String state);


    /**
     * 根据token获取用户信息
     *
     * @param accessToken
     * @return
     */
    UserInfo getUserInfoByToken(String accessToken);
}

