package com.ly.car.risk.manage.repo.order.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.SfcOrder;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-11-08
 */
public interface SfcOrderMapper extends BaseMapper<SfcOrder> {

    @Select("select * from sfc_order where order_id = #{orderId} limit 1")
    SfcOrder queryByOrderId(String orderId);

}
