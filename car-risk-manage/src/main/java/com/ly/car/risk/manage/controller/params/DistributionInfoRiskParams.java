package com.ly.car.risk.manage.controller.params;

import com.ly.car.risk.manage.bean.Pageable;
import lombok.Data;

import java.util.List;

@Data
public class DistributionInfoRiskParams extends Pageable {


    private Long id;
    private Integer isCheating;
    private Integer cityId;


    /**
     * 风险主场景
     */
    private String riskMainScenario;

    /**
     * 风险子场景
     */
    private String riskChildScenario;



    /**
     * 订单id
     */
    private String orderId;

    private String mainScene;
    private Integer riskLevel;
    private String childScene;
    private String cityName;

    private Integer childSceneInteger;

    /**
     * 订单司机车牌号
     * */
    private String driverCardNo;
    private String supplierCode;
    //分销员名称
    private String distributeName;
    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String userPhone;
    private List<String> ruleNoList;

    /**
     * 名单类风控添加
     * */
    private Integer customerType;

    /**
     * 业务线
     * */
    private String productLine;

    private String distributorName;

    private String hitValue;

    private String userId;
    
    private Integer strategyType;
}
