package com.ly.car.risk.manage.constants;

import org.apache.commons.lang3.StringUtils;

/**
 * 操作日志操作类型
 */
public enum RiskCustomerRecordOptionTypeEnum {

    add(1, "新增"),
    modify(2, "修改"),
    del(3, "删除"),
    ;
    private Integer code;
    private String msg;

    RiskCustomerRecordOptionTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskCustomerRecordOptionTypeEnum enumItem : RiskCustomerRecordOptionTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getCodeByMsg(String msg) {
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getMsg().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
}
