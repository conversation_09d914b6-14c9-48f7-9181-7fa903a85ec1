package com.ly.car.risk.manage.repo.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.SupplierInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SupplierInfoMapper extends BaseMapper<SupplierInfo> {

    @Select("<script>" +
            "   select * from supplier_info" +
            "</script>")
    List<SupplierInfo> queryAllSupplier();
}
