package com.ly.car.risk.manage.oauth.service;

import com.ly.car.exception.SilentException;
import com.ly.car.http.HttpLogUtils;
import com.ly.car.http.exception.HttpIOException;
import com.ly.car.log.PrintLog;
import com.ly.car.risk.manage.oauth.bean.GetMenuListRequest;
import com.ly.car.risk.manage.oauth.bean.GetMenuListResponse;
import com.ly.car.risk.manage.oauth.bean.MenuDTO;
import com.ly.car.risk.manage.oauth.bean.PermissionProperties;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.message.BasicHeader;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限服务
 */
@Slf4j
@Service
public class PermissionService {
    @Resource
    private PermissionProperties permissionProperties;

    /**
     * 从统一权限获取菜单信息
     */
    @PrintLog
    @SilentException
    @Retryable(value = {HttpIOException.class, IOException.class})
    public List<MenuDTO> getMenu(long userId) {
        GetMenuListRequest getMenuListRequest = new GetMenuListRequest();
        getMenuListRequest.setAct(new GetMenuListRequest.Act(permissionProperties.getAppKey(), permissionProperties.getAppCode()));
        getMenuListRequest.setUserId(userId);
        String requestBody = JsonUtils.json(getMenuListRequest);
        String responseBody = HttpLogUtils.post(permissionProperties.getGetMenuUrl(),
                requestBody, new BasicHeader("Labrador-Token", permissionProperties.getLabradorToken()));
        GetMenuListResponse getMenuListResponse = JsonUtils.json(responseBody, GetMenuListResponse.class);
        MenuDTO root = new MenuDTO();
        forEach(root, getMenuListResponse.getData().getMenus());
        return root.getChildren();
    }

    private void forEach(MenuDTO parent, List<GetMenuListResponse.Menu> menus) {
        if (CollectionUtils.isNotEmpty(menus)) {
            List<MenuDTO> menuList = menus.stream().map(menuData -> {
                MenuDTO menuDTO = new MenuDTO();
                menuDTO.setName(menuData.getTitle());
                menuDTO.setPath(menuData.getAddress());
                menuDTO.setRemark(menuData.getRemark());
                forEach(menuDTO, menuData.getChildMenus());
                return menuDTO;
            }).collect(Collectors.toList());
            parent.setChildren(menuList);
        }
    }

}
