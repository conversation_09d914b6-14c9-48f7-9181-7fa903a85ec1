package com.ly.car.risk.manage.controller.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* 
* @TableName risk_hit_link
*/
@Data
public class RiskHitLinkDTO {

    /**
    * 主键id
    */
    private Long id;
    /**
    * 命中订单号
    */
    private String parentOrderId;
    /**
    * 订单号
    */
    private String orderId;
    /**
    * 供应商编号
    */
    private String supplierCode;
    /**
    * 来源id
    */
    private String refId;
    /**
    * 渠道名称
    */
    private String refName;
    /**
    * 供应商名称
    */
    private String supplierName;
    /**
    * 乘车人手机号
    */
    private String passengerCellphone;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 接单时间
    */
    private Date acceptTime;
    /**
    * 出发地
    */
    private String startAddress;
    /**
    * 目的地
    */
    private String endAddress;
    /**
    * 命中规则
    */
    private String ruleNo;
    /**
    * 完单时间
    */
    private Date finishTime;
    /**
    * 实际距离
    */
    private BigDecimal actualDistance;
    /**
    * 实际分钟
    */
    private Integer actualMinute;
    /**
    * 城市id
    */
    private Integer cityId;
    /**
    * 城市名称
    */
    private String cityName;
    /**
    * 优惠券号
    */
    private String couponNo;
    /**
    * 优惠券金额
    */
    private BigDecimal couponAmount;
    /**
    * 命中时间
    */
    private Date hitTime;
    /**
    * 修改时间
    */
    private Date updateTime;
    /**
    * 0-非作弊 1-作弊
    */
    private Integer isCheating;
    /**
    * 订单金额
    */
    private BigDecimal totalAmount;
    /**
    * 唯一请求标识
    */
    private String requestId;
    /**
    * 用户id
    */
    private String memberId;
    /**
    * 用户手机号
    */
    private String userPhone;
    /**
    * 司机车牌号
    */
    private String driverCardNo;

}
