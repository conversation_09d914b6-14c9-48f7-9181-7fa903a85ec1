<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.RiskHitMapper">

    <resultMap id="baseMap" type="com.ly.car.risk.manage.controller.dto.RiskHitDTO">
        <result column="customer_value" property="customerValue"/>
        <result column="order_id" property="orderId"/>
        <result column="member_id" property="memberId"/>
        <result column="union_id" property="unionId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="peakItemMap" type="com.ly.car.risk.manage.model.resp.dashboard.PeakItem">
        <result column="name" property="name"/>
        <result column="percent" property="value"/>
    </resultMap>

    <resultMap id="distributionItemMap" type="com.ly.car.risk.manage.model.resp.dashboard.DistributionItem">
        <result column="rule" property="rule"/>
        <result column="match_count" property="matchCount"/>
        <result column="current_percent" property="currentPercent"/>
        <result column="last_percent" property="lastPercent"/>
    </resultMap>

    <select id="total" resultType="long">
        select count(*) from risk_hit
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.controller.dto.RiskHitDTO">
        select * from risk_hit
        <include refid="getCondition"/>
        order by update_time desc
        limit #{offset},#{size}
    </select>
    <select id="getDetailByRequestId" resultType="com.ly.car.risk.manage.controller.dto.RiskHitLinkDTO">
        select *
        from risk_hit_link
        where request_id = #{requestId}
    </select>
    <select id="queryAllList" resultType="com.ly.car.risk.manage.controller.dto.RiskHitDTO">
        select * from risk_hit
        <include refid="getCondition"/>
        order by update_time desc
    </select>

    <sql id="getCondition">
        where 1=1
        <if test="idType != null">
            and customer_type = #{idType}
        </if>
        <if test="cityId != null">
            and city_id = #{cityId}
        </if>
        <if test="mainSceneNo != null and mainSceneNo != '' ">
            and main_scene_no = #{mainSceneNo}
        </if>
        <if test="childSceneNo != null and childSceneNo != '' ">
            and child_scene_no = #{childSceneNo}
        </if>
        <if test="mainScene != null and mainScene != '' ">
            and main_scene = #{mainScene}
        </if>
        <if test="childScene != null and childScene != '' ">
            and child_scene = #{childScene}
        </if>
        <if test="riskLevel != null">
            and risk_level = #{riskLevel}
        </if>
        <if test="orderId != null and orderId != ''">
            and order_id = #{orderId}
        </if>
        <if test="ruleNo != null and ruleNo != ''">
            and hit_rule like CONCAT('%',#{ruleNo},'%')
        </if>
        <if test="startTime != null">
            and create_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="strategyNo != null">
            and hit_strategy = #{strategyNo}
        </if>
        <if test="disposeAction != null and disposeAction != ''">
            and dispose_action = #{disposeAction}
        </if>
        <if test="idValue != null and idValue != '' ">
            and (passenger_cellphone = #{idValue}
            or member_id = #{idValue}
            or driver_card_no = #{idValue}
            or union_id = #{idValue}
            )
        </if>
    </sql>

    <select id="findTopCity" resultMap="peakItemMap">
        SELECT top_cities.city_name as name,
               (city_count / total_count) * 100 AS percent
        FROM (SELECT city_name,
                     COUNT(*) AS city_count
              FROM risk_hit
              WHERE hit_type = 0
                and create_time BETWEEN #{startDate} AND #{endDate}
              GROUP BY city_id
              ORDER BY city_count DESC
                  LIMIT #{top}) AS top_cities,
             (SELECT COUNT(*) AS total_count
              FROM risk_hit
              WHERE hit_type = 0
                and create_time BETWEEN #{startDate} AND #{endDate}) AS total
        ORDER BY top_cities.city_count DESC
    </select>

    <select id="findTopScene" resultMap="peakItemMap">
        SELECT top_scenes.child_scene_name as name,
               (scene_count / total_count) * 100 AS percent
        FROM (SELECT child_scene_name,
                     COUNT(*) AS scene_count
              FROM risk_hit
              WHERE hit_type = 0
                and create_time BETWEEN #{startDate} AND #{endDate}
              GROUP BY child_scene_name
              ORDER BY scene_count DESC
                  LIMIT #{top}) AS top_scenes,
             (SELECT COUNT(*) AS total_count
              FROM risk_hit
              WHERE hit_type = 0
                and create_time BETWEEN #{startDate} AND #{endDate}) AS total
        ORDER BY top_scenes.scene_count DESC
    </select>

    <select id="findTopBusiness" resultMap="peakItemMap">
        SELECT top_bizs.bizName as name,
               (biz_count / total_count) * 100 AS percent
        FROM (SELECT case SUBSTRING(order_id,1,3) when 'YCW' then '专车' when 'YNC' then '专车'
                                                  when 'YCS' then '顺风车' when 'SFC' then '顺风车' when 'YCX' then '上门接送' when 'GNC' then '国际接送机' else SUBSTRING(order_id,1,3) end as bizName,
                     COUNT(*) AS biz_count
              FROM risk_hit
              WHERE create_time BETWEEN #{startDate} AND #{endDate}
                and order_id != ''
              GROUP BY case SUBSTRING(order_id,1,3) when 'YCW' then '专车' when 'YNC' then '专车'
                  when 'YCS' then '顺风车' when 'SFC' then '顺风车' when 'YCX' then '上门接送' when 'GNC' then '国际接送机' else SUBSTRING(order_id,1,3) end
              ORDER BY biz_count DESC
                  LIMIT #{top}) AS top_bizs,
             (SELECT COUNT(*) AS total_count
              FROM risk_hit
              WHERE create_time BETWEEN #{startDate} AND #{endDate} and order_id != '') AS total
        ORDER BY top_bizs.biz_count DESC
    </select>

    <select id="findTopRules" resultMap="distributionItemMap">
        SELECT  top_rules.hit_rule   as rule,
                rule_count as match_count,
                (rule_count / total_count) * 100 AS current_percent
        FROM (SELECT hit_rule,
                     COUNT(*) AS rule_count
              FROM risk_hit
              WHERE create_time BETWEEN #{startDate} AND #{endDate}
              GROUP BY hit_rule
              ORDER BY rule_count DESC
                  LIMIT #{top}) AS top_rules,
             (SELECT COUNT(*) AS total_count
              FROM risk_hit
              WHERE create_time BETWEEN #{startDate} AND #{endDate}) AS total
        ORDER BY top_rules.rule_count DESC
    </select>

    <select id="findRulePeriodData" resultMap="distributionItemMap">
        SELECT  top_rules.hit_rule   as rule,
                rule_count as match_count,
                (rule_count / total_count) * 100 AS last_percent
        FROM (SELECT hit_rule,
                     COUNT(*) AS rule_count
              FROM risk_hit
              WHERE create_time BETWEEN #{startDate} AND #{endDate}
                and hit_rule in
                <foreach collection="rules" item="rule" index="index" open="(" close=")" separator=",">
                    #{rule}
                </foreach>
              GROUP BY hit_rule) AS top_rules,
             (SELECT COUNT(*) AS total_count
              FROM risk_hit
              WHERE create_time BETWEEN #{startDate} AND #{endDate}) AS total
    </select>

    <select id="queryRiskDriverRecords" resultMap="baseMap">
        select customer_value, create_time
        from risk_hit
        where create_time between #{startTime} and #{endTime}
          and env = #{env}
          and customer_value != '' and driver_card_no != ''
    </select>

    <select id="queryRiskOnlineOrders" resultMap="baseMap">
        select order_id, create_time
        from risk_hit
        where create_time between #{startTime} and #{endTime}
          and hit_type = 0
          and env = #{env}
          and order_id != ''
    </select>

    <select id="queryRiskUserRecords" resultMap="baseMap">
        select member_id, union_id,create_time
        from risk_hit
        where create_time between #{startTime} and #{endTime}
          and hit_type = 0
          and env = #{env}
          and customer_value = ''
    </select>

    <select id="findRiskDriverCount" resultType="java.lang.Integer">
        select
        count(distinct driver_card_no)
        from risk_hit
        where hit_type = 0
        and env = #{env}
        and control_target = '0'
        and driver_card_no != ''
        and hit_strategy != ''
        and order_id != ''
        <if test="strategyNo!=null and strategyNo !=''">
            and hit_strategy = #{strategyNo}
        </if>
        <if test="productLineList != null and productLineList.size > 0">
            and product_line in
            <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime!=null and startTime !=''">
            and create_time > #{startTime}
        </if>
        <if test="endTime!=null and endTime !=''">
            and create_time &lt; #{endTime}
        </if>
    </select>

    <select id="findRiskUserCount" resultType="java.lang.Integer">
        select count(1)
        from (
        select
        distinct (
        case
        when hit_field = 'phone' then (case when passenger_cellphone = '' THEN user_phone  else passenger_cellphone end)
        when hit_field = 'userId' then (case when member_id = '' THEN union_id  else member_id end)
        else '' end
        ) as value
        from risk_hit
        where hit_type = 0
        and hit_strategy != ''
        and order_id != ''
        and env = #{env}
        and control_target = '1'
        <if test="strategyNo!=null and strategyNo !=''">
            and hit_strategy = #{strategyNo}
        </if>
        <if test="productLineList != null and productLineList.size > 0">
            and product_line in
            <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime!=null and startTime !=''">
            and create_time > #{startTime}
        </if>
        <if test="endTime!=null and endTime !=''">
            and create_time &lt; #{endTime}
        </if>
             ) t
        where value != '' and value is not null
    </select>

    <select id="queryStrategyDistribution" resultType="com.ly.car.risk.manage.model.resp.dashboard.StrategyHitData">
        SELECT
            count(DISTINCT order_id) as riskOrderCount,
            hit_strategy as strategyNo,
            COUNT(distinct(
                case
                    when hit_field = 'phone' then (case when passenger_cellphone = '' THEN user_phone  else passenger_cellphone end)
                    when hit_field = 'userId' then (case when member_id = '' THEN union_id  else member_id end)
                    else ''
                    end)) as userCount,
            count(distinct driver_card_no) as driverCount,
            control_target as controlTarget,
            count(DISTINCT order_id)/totalCount as hitRate
        FROM
            risk_hit,
            (
            select
            count(DISTINCT order_id) as totalCount
            FROM
            risk_hit
            where
            hit_type = 0
            and order_id != ''
            and env = #{env}
            and control_target != ''
            <if test="strategyNo!=null and strategyNo !=''">
                and hit_strategy = #{strategyNo}
            </if>
            <if test="productLineList != null and productLineList.size > 0">
                and product_line in
                <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime!=null and startTime !=''">
                and create_time > #{startTime}
            </if>
            <if test="endTime!=null and endTime !=''">
                and create_time &lt; #{endTime}
            </if>
            and hit_strategy != '') as c
        where
            hit_type = 0
          and env = #{env}
          and hit_strategy != ''
          and order_id != ''
          and control_target!=''
            <if test="strategyNo!=null and strategyNo !=''">
                and hit_strategy = #{strategyNo}
            </if>
            <if test="productLineList != null and productLineList.size > 0">
                and product_line in
                <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime!=null and startTime !=''">
                and create_time > #{startTime}
            </if>
            <if test="endTime!=null and endTime !=''">
                and create_time &lt; #{endTime}
            </if>
        group BY
            hit_strategy
        order by riskOrderCount desc limit #{top}
    </select>

    <select id="getById" resultType="com.ly.car.risk.manage.controller.dto.RiskHitDTO">
        select * from risk_hit
        WHERE id = #{id} limit 1
    </select>
</mapper>