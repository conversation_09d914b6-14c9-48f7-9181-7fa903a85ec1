package com.ly.car.risk.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.MetricFieldDTO;
import com.ly.car.risk.manage.controller.request.MetricFieldAddReq;
import com.ly.car.risk.manage.controller.request.MetricFieldDeleteReq;
import com.ly.car.risk.manage.controller.request.MetricFieldListReq;
import com.ly.car.risk.manage.controller.request.MetricFieldUpdateReq;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.MetricFieldMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricRuleFieldRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricField;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricRuleFieldRelation;
import com.ly.dal.util.DateUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MetricFieldService {
    
    @Resource
    private MetricFieldMapper             metricFieldMapper;
    @Resource
    private MetricRuleFieldRelationMapper metricRuleFieldRelationMapper;
    
    public void add(MetricFieldAddReq req, UserInfo userInfo) {
        MetricField field = new MetricField();
        field.setName(req.getName());
        field.setFieldNo(req.getFieldNo());
        field.setDescription(req.getDescription());
        field.setScript(req.getScript());
        field.setCategory(req.getCategory());
        field.setType(req.getType());
        field.setTarget(req.getTarget());
        field.setBasedCurrent(req.getBasedCurrent());
        field.setCreateTime(new Date());
        field.setUpdateTime(new Date());
        field.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
        field.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        metricFieldMapper.insert(field);
    }
    
    public void delete(MetricFieldDeleteReq req, UserInfo userInfo) {
        metricFieldMapper.deleteById(req.getId());
        metricRuleFieldRelationMapper.delete(new QueryWrapper<MetricRuleFieldRelation>().eq("left_field_id", req.getId()));
    }
    
    public void update(MetricFieldUpdateReq req, UserInfo userInfo) {
        MetricField field = metricFieldMapper.selectById(req.getId());
        field.setName(req.getName());
        field.setFieldNo(req.getFieldNo());
        field.setDescription(req.getDescription());
        field.setScript(req.getScript());
        field.setCategory(req.getCategory());
        field.setType(req.getType());
        field.setTarget(req.getTarget());
        field.setBasedCurrent(req.getBasedCurrent());
        field.setUpdateTime(new Date());
        field.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        metricFieldMapper.updateById(field);
    }
    
    public Pagination getList(MetricFieldListReq req) {
        long total = metricFieldMapper.getTotal(req);
        List<MetricField> list = metricFieldMapper.queryList(req);
        List<MetricFieldDTO> dtoList = new ArrayList<>();
        for (MetricField field : list) {
            dtoList.add(parse(field));
        }
        return new Pagination<>(dtoList, total, req);
    }
    
    private MetricFieldDTO parse(MetricField field) {
        MetricFieldDTO dto = new MetricFieldDTO();
        dto.setId(field.getId());
        dto.setFieldNo(field.getFieldNo());
        dto.setName(field.getName());
        dto.setDescription(field.getDescription());
        dto.setCategory(field.getCategory());
        dto.setScript(field.getScript());
        dto.setCategory(field.getCategory());
        dto.setType(field.getType());
        dto.setTarget(field.getTarget());
        dto.setBasedCurrent(field.getBasedCurrent());
        dto.setUpdateTime(DateUtil.date2String(field.getUpdateTime()));
        dto.setUpdateUser(field.getUpdateUser());
        dto.setCreateTime(DateUtil.date2String(field.getCreateTime()));
        dto.setCreateUser(field.getCreateUser());
        
        Long linkRuleCount = metricRuleFieldRelationMapper.selectCount(new QueryWrapper<MetricRuleFieldRelation>().eq("left_field_id", field.getId()));
        dto.setLinkRuleCount(linkRuleCount);
        return dto;
    }
    
    public List<MetricFieldDTO> getAllList() {
        List<MetricField> list = metricFieldMapper.queryAllList();
        List<MetricFieldDTO> dtoList = new ArrayList<>();
        for (MetricField field : list) {
            dtoList.add(parse(field));
        }
        return dtoList;
    }
    
}
