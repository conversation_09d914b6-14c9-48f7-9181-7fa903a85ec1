package com.ly.car.risk.manage.controller.params;

import com.ly.car.risk.manage.bean.Pageable;
import com.ly.car.risk.manage.constants.SensitiveWordTypeEnum;
import com.ly.car.risk.manage.constants.SensitiveWordsCategoryEnum;
import lombok.Data;

@Data
public class SensitiveWordsMatchParams extends Pageable {

    /**
     * 创建失败- 开始
     */
    private String startTime;
    /**
     * 创建时间- 结束
     */
    private String endTime;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 敏感词
     */
    private String word;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 司机车牌
     */
    private String driverCardNo;
    /**
     * 敏感词类型 {@link SensitiveWordTypeEnum}
     */
    private Integer wordType;
    /**
     * 信息来源 {@link SensitiveWordsCategoryEnum}
     */
    private Integer type;
    /**
     * 是否外呼 0-否 1-是
     */
    private Integer ivrCallFlag;


}
