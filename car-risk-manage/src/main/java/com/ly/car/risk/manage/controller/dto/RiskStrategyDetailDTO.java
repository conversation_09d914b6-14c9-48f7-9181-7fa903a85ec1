package com.ly.car.risk.manage.controller.dto;

import com.ly.car.risk.manage.controller.params.RiskStrategyParams;
import lombok.Data;

import java.util.List;

@Data
public class RiskStrategyDetailDTO {

    private String strategyNo;
    private String strategyName;
    private String type;//0-测试 1-上线运行 2-下线
    private String hitField;//设备id，用户id,手机号，IP,wifi
    private Integer hitAction;//加入用户黑名单、加入用户白名单、加入用户不派单名单、加入不可领券名单、加入司机黑名单、加入司机白名单、加入司机不可接单名单
    private String strategyDesc;
    private String expression;//策略表达式
    private String script;//表达式脚本
    private Integer disposeAction;//处置动作
    private Long id;
    private List<RiskStrategyParams.LinkRuleOrScene> linkSceneNo;
    private List<RiskStrategyParams.LinkRuleOrScene> linkRuleNo;
    private String createTime;
    private String updateTime;
    private String createUser;
    private String updateUser;
    private String operatingState;
}
