package com.ly.car.risk.manage.integration.approve.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * MktApproveFlowDetailVO
 *
 * <AUTHOR>
 * @version Id : MktApproveFlowDetailVO, v 1.0  2024/5/17 13:25,hansheng.zhang Exp $
 */
@Data
public class MktApproveFlowDetailVO implements Serializable {
    /**
     * 审批单号
     */
    private String approveNo;

    /**
     * 审批状态(doing:审批中，pass:通过，reject:驳回，revocation:撤回，invalid:作废)
     */
    private String approveStatus;
    /**
     * 审批意见
     */
    private String message;
    /**
     * 当前步骤
     */
    private Integer currStep;
    /**
     * 审批节点列表
     */
    private List<MktApproveFlowNodeDetailVO> nodeList;
}