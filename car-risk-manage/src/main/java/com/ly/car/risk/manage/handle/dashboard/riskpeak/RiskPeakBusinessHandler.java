package com.ly.car.risk.manage.handle.dashboard.riskpeak;

import com.ly.car.risk.manage.handle.dashboard.RiskPeakHandler;
import com.ly.car.risk.manage.model.enums.RiskPeakTypeEnum;
import com.ly.car.risk.manage.model.resp.dashboard.PeakItem;
import com.ly.car.risk.manage.model.resp.dashboard.RiskPeakData;
import com.ly.car.risk.manage.repo.risk.mapper.RiskHitMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Description of RiskPeakCityHandler
 *
 * <AUTHOR>
 * @date 2024/12/10
 * @desc
 */
@Service
public class RiskPeakBusinessHandler implements RiskPeakHandler {

    @Resource
    private RiskHitMapper riskHitMapper;

    @Override
    public RiskPeakTypeEnum support() {
        return RiskPeakTypeEnum.RISK_BUSINESS;
    }

    @Override
    public RiskPeakData riskPeak(List<Pair<Date, Date>> linkRelativeRatioDates) {
        RiskPeakData peakData = new RiskPeakData();
        Pair<Date, Date> currentPeriod = linkRelativeRatioDates.get(0);
        Pair<Date, Date> lastPeriod = linkRelativeRatioDates.get(1);
        List<PeakItem> currentTopScenes = riskHitMapper.findTopBusiness(currentPeriod.getKey(), currentPeriod.getValue(), 3);
        List<PeakItem> lastTopScenes = riskHitMapper.findTopBusiness(lastPeriod.getKey(), lastPeriod.getValue(), 3);
        peakData.setCode(support().name());
        peakData.setName(support().getDesc());
        peakData.setCurrentPeriodData(currentTopScenes);
        peakData.setLastPeriodData(lastTopScenes);
        return peakData;
    }
}