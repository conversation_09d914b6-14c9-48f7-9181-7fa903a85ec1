<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.HcOrderCheckMapper">

    <select id="queryList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.HcOrderCheck">
        select * from hc_order_check
        <include refid="getListCondition"/>
        order by id desc
        limit #{offset}, #{size}
    </select>

    <sql id="getListCondition">
        where create_time &gt; #{createTime}
        and create_time &lt; #{endTime}
        <if test="status != null">
            and status = #{status}
        </if>
    </sql>

</mapper>