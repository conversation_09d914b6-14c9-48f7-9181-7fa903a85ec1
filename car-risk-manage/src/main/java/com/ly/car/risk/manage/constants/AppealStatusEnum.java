package com.ly.car.risk.manage.constants;

import org.apache.commons.lang3.StringUtils;

public enum AppealStatusEnum {

    WAIT_APPEAL(0,"待申诉"),
    WAIT_CHECK(1,"待审核"),
    APPEAL_SUCCESS(2,"申诉成功"),
    APPEAL_FAIL(3,"申诉失败"),

    ;

    AppealStatusEnum(Integer code,String msg){
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (AppealStatusEnum enumItem : AppealStatusEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
