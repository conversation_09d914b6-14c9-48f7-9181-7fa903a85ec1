package com.ly.car.risk.manage.controller.request;

import com.ly.car.risk.manage.controller.dto.MetricRuleConfigDTO;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class MetricFieldAddReq {
    
    /**
     * 指标名称
     */
    private String name;
    
    /**
     * 指标编号
     */
    private String fieldNo;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 特征指标代码
     */
    private String script;
    
    /**
     * 0-全部 1-数值
     */
    private Integer category;
    
    /**
     * 0-全部 1-风控 2-安全
     */
    private Integer type;
    
    /**
     * 1-用户 2-司机 3-供应商
     */
    private Integer target;
    
    
    /**
     * 1-基于当前订单 0-基于历史订单
     */
    private Integer basedCurrent;
}
