package com.ly.car.risk.manage.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.manage.bean.BillDateConverter;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.request.DriverPunishOrderListReq;
import com.ly.car.risk.manage.repo.riskmetrics.mapper.CarMtDriverPunishRecordMapper;
import com.ly.car.risk.manage.service.dto.DriverPunishOrderDto;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DriverPunishOrderService {
    
    @Resource
    private CarMtDriverPunishRecordMapper carMtDriverPunishRecordMapper;
    
    public Pagination getList(DriverPunishOrderListReq req) {
        long total = carMtDriverPunishRecordMapper.getTotal(req);
        List<DriverPunishOrderDto> page = carMtDriverPunishRecordMapper.queryList(req);
        parse(page);
        return new Pagination<>(page, total, req);
    }
    
    private void parse(List<DriverPunishOrderDto> page) {
        Map<String, String> config = getConfig();
        String driverPunishOrder = config.getOrDefault("DRIVER_PUNISH_ORDER", "2,5");
        String driverPunishMoreOrder = config.getOrDefault("DRIVER_PUNISH_MORE_ORDER", "5");
        Integer left = driverPunishOrder.contains(",") ? Integer.parseInt(driverPunishOrder.split(",")[0]) : Integer.parseInt(driverPunishOrder);
        Integer right = Integer.parseInt(driverPunishMoreOrder);
        page.forEach(dto -> {
            if (dto.getPunishOrderCount() < left) {
                dto.setLevelName("低风险");
            } else if (dto.getPunishOrderCount() < right) {
                dto.setLevelName("中风险");
            } else {
                dto.setLevelName("高风险");
            }
        });
    }
    
    public String exportData(DriverPunishOrderListReq request, HttpServletResponse response) {
        List<DriverPunishOrderDto> list = carMtDriverPunishRecordMapper.queryAllList(request);
        parse(list);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String fileName = "萌艇追款列表_" + System.currentTimeMillis();
        EasyExcel.write(byteArrayOutputStream, DriverPunishOrderDto.class).registerConverter(new BillDateConverter()).sheet("sheet1")
                .doWrite(list);

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
        } catch (UnsupportedEncodingException e) {}
        return FileUploadCephUtil.upload(fileName + ".xlsx", byteArrayOutputStream);
    }
    
    private Map<String, String> getConfig() {
        try {
            String config = StringUtils.defaultString(ConfigCenterClient.get("RISK_COUPON_CHECK_CFG"), "{}");
            return JSON.parseObject(config, new TypeReference<HashMap<String, String>>() {
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
