package com.ly.car.risk.manage.oauth.controller;

import com.ly.car.bean.NameValue;
import com.ly.car.exception.CodeException;
import com.ly.car.http.HttpLogUtils;
import com.ly.car.http.HttpUtils;
import com.ly.car.risk.manage.oauth.UnauthorizedException;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.oauth.service.IOauthService;
import com.ly.car.risk.manage.oauth.bean.OAuthProperties;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Map;

import static com.ly.car.risk.manage.oauth.Constants.*;

@Slf4j
@RestController
@RequestMapping("oauth")
public class OauthController {
    @Resource
    private OAuthProperties oauthProperties;
    @Resource
    private IOauthService oauthService;

    @GetMapping("userInfo")
    public UserInfo userInfo(UserInfo userInfo) {
        return userInfo;
    }

    @GetMapping("getAccessToken")
    public String getAccessToken(HttpServletRequest request, HttpServletResponse response,String code) {
        // 通过接收到的授权码到SSO站点申请令牌
        String returnParams = null;
        try {
            returnParams = HttpLogUtils.post(oauthProperties.getCodeApplyForTokenUri(),
                    new NameValue("client_id", oauthProperties.getClientId()),
                    new NameValue("client_secret", oauthProperties.getClientSecret()),
                    new NameValue("redirect_uri", oauthProperties.getApplyTokenRedirectUri()),
                    new NameValue("code", code),
                    new NameValue("grant_type", oauthProperties.getGrantType())
            );
        } catch (Exception e) {
            log.error("[][][][]error",e);
            String applyForTokenUri = oauthService.applyForTokenFromSSO(request, response);
            throw new UnauthorizedException(applyForTokenUri);
        }

        Map<String, Object> returnObject = JsonUtils.json(returnParams, Map.class);
        String accessToken = MapUtils.getString(returnObject, ACCESS_TOKEN);
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }
        throw new CodeException(500, "单独登录异常：获取AccessToken失败。code=" + code + "\n接口访问：" + returnParams);
    }

    @RequestMapping("permissionRefresh")
    public String permissionRefresh(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        session.removeAttribute(SESSION_USER_INFO);
        //return login(request, response);
        return "SUCCESS";
    }

    @RequestMapping("logout")
    public String logout(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        session.removeAttribute(SESSION_USER_INFO);

        StringBuffer fullLogoutUri = new StringBuffer().append(oauthProperties.getSsoLogoutUri());
        if (null != request.getCookies()) {
            for (Cookie cookie : request.getCookies()) {
                if (ACCESS_TOKEN.equals(cookie.getName())) {
                    fullLogoutUri.append("?").append(ACCESS_TOKEN).append("=").append(cookie.getValue());
                    cookie.setMaxAge(0);
                    cookie.setPath(WEBROOT_URI_PATH);
                    response.addCookie(cookie);
                    break;
                }
            }
        }

        log.info("[logout][] [][] {}", fullLogoutUri);
        return "redirect:" + fullLogoutUri.toString();
    }

}

