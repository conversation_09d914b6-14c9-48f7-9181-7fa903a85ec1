package com.ly.car.risk.manage.mq.model;

import lombok.Data;

import java.io.Serializable;

/**
 * CommonApproveStatusChangePayload
 *
 * <AUTHOR>
 * @version Id : CommonApproveStatusChangePayload, v 1.0  2024/5/16 19:22,hansheng.zhang Exp $
 */
@Data
public class CommonApproveStatusChangePayload implements Serializable {
    /**
     * 工单编号
     */
    private String approveNo;

    /**
     * 场景key
     */
    private String sceneKey;

    /**
     * 业务编号（营销活动对应promotionCode）
     */
    private String businessNo;

    /**
     * 本次变更的操作类型（送审，审批通过，审批驳回，撤回，作废）
     */
    private String operateType;

    /**
     * 操作人(姓名+7位工号）或者（system：系统自动操作）
     */
    private String operator;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 操作说明（驳回意见）
     */
    private String message;

    /**
     * 审批流程状态（审批中，通过，驳回）
     */
    private String approveStatus;
}