package com.ly.car.risk.manage.controller.dto;

import java.util.Date;
import lombok.Data;

@Data
public class DriverCheckDTO {
    
    private Long id;
    
    private String cardName;
    
    private String mobile;
    
    private String driverCardNo;
    
    private Integer type;
    
    private String bankCardNo;
    
    private Integer result;
    
    private String remark;
    
    private Date createTime;
    
    private Date updateTime;
    
    private String idCard;
    
    private String ownerName;
    
}
