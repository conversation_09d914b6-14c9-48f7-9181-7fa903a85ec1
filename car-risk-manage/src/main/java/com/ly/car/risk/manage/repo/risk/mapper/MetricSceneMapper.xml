<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.MetricSceneMapper">

    <select id="getTotal" resultType="long">
        select count(*) from metric_scene
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.MetricScene">
        select * from metric_scene
        <include refid="getCondition"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.MetricScene">
        select *
        from metric_scene
    </select>


    <sql id="getCondition">
        where 1 = 1
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            and update_time between #{startTime} and #{endTime}
        </if>
        <if test="name != null and name != '' ">
            and `name` like CONCAT('%',#{name},'%')
        </if>
        <if test="sceneNo != null and sceneNo != '' ">
            and scene_no like CONCAT('%',#{sceneNo},'%')
        </if>
        <if test="updateUser != null and updateUser != '' ">
            and update_user like CONCAT('%',#{updateUser},'%')
        </if>
    </sql>
</mapper>