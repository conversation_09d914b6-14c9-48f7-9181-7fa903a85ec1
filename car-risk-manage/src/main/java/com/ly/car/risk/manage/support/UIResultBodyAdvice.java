package com.ly.car.risk.manage.support;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.utils.JsonUtils;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice(basePackages = "com.ly.car.risk.manage")
public class UIResultBodyAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 使用DisabledUiResult可以禁用此功能
        return !(AnnotatedElementUtils.isAnnotated(returnType.getContainingClass(), UiResult.Disabled.class)
                || AnnotatedElementUtils.isAnnotated(returnType.getMethod(), UiResult.Disabled.class));
    }

    @Override
    public Object beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        // 处理UiResult
        if (body instanceof UiResult) {
            return body;
        }
        // 处理String逻辑
        if (body instanceof String) {
            UiResult<?> uiResult = UiResult.ok(body);
            return JsonUtils.json(uiResult);
        }
        // 包裹对象
        return UiResult.ok(body);
    }
}
