package com.ly.car.risk.manage.model.resp;

import com.ly.car.risk.manage.model.resp.dashboard.StrategyDistributionData;
import com.ly.car.risk.manage.model.resp.dashboard.SupplierRiskOrderData;
import lombok.Data;

import java.util.List;

/**
 * Description of DashboardOfflineStrategyResp
 *
 * <AUTHOR>
 * @date 2024/12/10
 * @desc
 */
@Data
public class DashboardOfflineStrategyResp {

    private int driverCount;

    private int userCount;

    private List<StrategyDistributionData> strategyDistributionOverview;

    private List<StrategyDistributionData> strategyDistributionDetail;

    private List<SupplierRiskOrderData> supplierRiskOrderData;
}