package com.ly.car.risk.manage.controller.params;

import com.ly.car.risk.manage.bean.Pageable;
import lombok.Data;


@Data
public class OrderQueryParams extends Pageable {

    private Integer cityId;
    private String supplierCode;
    private Integer channelType;
    private Integer idType;//1-unionID/2-设备ID/3-memberID/4-用户手机号/5-乘车人手机号/6-司机车牌号/7-司机手机号
    private String idValue;
    private String orderNo;//同程订单号及渠道订单号、供应商订单号
    private Integer orderStatus;//订单状态
    private Integer isHitRisk;//是否命中风控 1-是 -0否
    private Integer isRisk;//是否风险单 1-是 0-否
    private Integer productType;// 10-及时专车 11-出租车 12-顺风车 13-接送
    private String startTime;
    private String endTime;

    private String orderId;

}
