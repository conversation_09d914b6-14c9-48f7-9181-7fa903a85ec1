package com.ly.car.risk.manage.constants;

import com.ly.car.risk.manage.controller.dto.RiskTypeDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;


public enum RiskCustomerRiskTypeEnum {

    black_list(1, "黑名单"),
    white_list(2, "白名单"),
    ban_coupon_list(3, "禁止领券名单"),
    ban_reward_list(4, "禁止奖励名单"),
    ban_send_list(5, "禁止派单名单"),
    ban_receive_list(6, "禁止接单名单"),
    ban_one_to_one_list(7, "一对一名单"),
    ban_register_list(8,"禁止认证名单"),
    ban_ync_receive_list(9,"禁止网约车接单名单"),
    ban_bus_create_order(10,"禁止汽车票下单名单"),
    ban_credit_auth_list(11,"禁止信用授权名单"),
    tx_black_list(21,"腾讯黑名单"),
    tx_ban_one_to_one_list(22, "腾讯一对一名单"),

    ;
    private Integer code;
    private String msg;

    RiskCustomerRiskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getCodeByMsg(String msg) {
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getMsg().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }

    public static List<RiskTypeDTO> getRiskType(){
        List<RiskTypeDTO> list = new ArrayList<>();
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            RiskTypeDTO dto = new RiskTypeDTO();
            dto.setValue(enumItem.getCode());
            dto.setLabel(enumItem.getMsg());
            dto.setDisabled(false);
            list.add(dto);
        }
        return list;
    }


    public static RiskCustomerRiskTypeEnum getEnumByCode(Integer code) {
        if(null == code){
            return null;
        }
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }

}
