package com.ly.car.risk.manage.integration.marketingmng;


import com.ly.car.risk.manage.bean.ResultMsg;
import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.integration.marketingmng.model.ConfigLogDTO;

/**
 * mng客户端
 *
 */
public interface MarketingMngClient {

    /**
     * 保存
     * @param log
     * @throws BizException
     */
    void save(ConfigLogDTO log) throws BizException;

    /**
     * 查询
     * @param target
     * @param targetId
     * @return
     * @throws BizException
     */
    ResultMsg logList(String target, String targetId) ;

}
