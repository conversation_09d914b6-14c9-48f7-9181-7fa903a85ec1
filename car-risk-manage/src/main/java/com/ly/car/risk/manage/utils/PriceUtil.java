package com.ly.car.risk.manage.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * @Desc 价格处理
 * <AUTHOR>
 * @Date 2020/11/2 17:16
 */
public class PriceUtil {

    /**
     * 分转BigDecimal(单位：元)
     * @param fen
     * @return
     */
    public static BigDecimal convertFen(Integer fen) {
        return Optional.ofNullable(fen)
                .map(o -> new BigDecimal(fen).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP))
                .orElse(BigDecimal.ZERO);
    }


    /**
     * 分转BigDecimal(单位：元)
     * @param fen
     * @return
     */
    public static BigDecimal convertFenToYuan(Integer fen) {
        return Optional.ofNullable(fen)
                .map(o -> new BigDecimal(fen).divide(new BigDecimal(100)))
                .orElse(BigDecimal.ZERO);
    }


    /**
     * 分转元(单位：元)
     * @param fen
     * @return
     */
    public static String fenToYuan(Integer fen) {
        return convertFen(fen).toString();
    }

    /**
     * 元转BigDecimal(单位：元)
     * @param yuan
     * @return
     */
    public static BigDecimal convertYuan(String yuan) {
        return Optional.ofNullable(yuan)
                .map(o -> new BigDecimal(yuan).setScale(2, RoundingMode.HALF_UP))
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 元转分(单位：分)
     * @param yuan
     * @return
     */
    public static Integer yuanToFen(String yuan) {
        return convertYuan(yuan).multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 元转分(单位：分)
     * @param yuan
     * @return
     */
    public static Integer yuanToFen(BigDecimal yuan) {
        return yuan.multiply(new BigDecimal(100)).intValue();
    }

}
