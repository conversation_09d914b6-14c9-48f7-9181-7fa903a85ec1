package com.ly.car.risk.manage.service.dashboard;

import com.ly.car.risk.manage.model.enums.OverviewTypeEnum;
import com.ly.car.risk.manage.model.req.DashboardOfflineStrategyReq;
import com.ly.car.risk.manage.model.req.DashboardRealTimeStrategyReq;
import com.ly.car.risk.manage.model.resp.DashboardOfflineStrategyResp;
import com.ly.car.risk.manage.model.resp.DashboardRealTimeStrategyResp;
import com.ly.car.risk.manage.model.resp.DashboardRiskTendResp;
import com.ly.car.risk.manage.model.resp.DashboardRiskDistributionResp;
import com.ly.car.risk.manage.model.resp.DashboardRiskPeakResp;

public interface DashboardService {

    DashboardRiskTendResp riskTend(OverviewTypeEnum overviewType);

    DashboardRiskPeakResp riskPeak(OverviewTypeEnum overviewType);

    DashboardRiskDistributionResp riskDistribution(OverviewTypeEnum overviewType);

    DashboardOfflineStrategyResp offlineStrategyInfo(DashboardOfflineStrategyReq req);
    
    DashboardRealTimeStrategyResp realTimeStrategyInfo(DashboardRealTimeStrategyReq req);
}
