package com.ly.car.risk.manage.controller;

import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.params.RiskHitLinkResponse;
import com.ly.car.risk.manage.controller.params.RiskHitRequestParams;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionAddReq;
import com.ly.car.risk.manage.service.RiskHitService;
import com.ly.car.risk.manage.service.RiskSensitiveActionService;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RequestMapping("/hitRiskManage")
@RestController
public class RiskHitController {

    @Resource
    private RiskHitService             riskHitService;

    @RequestMapping("/getList")
    public ResponseEntity<Pagination> getRiskHitList(@RequestBody RiskHitRequestParams params){
        return ResponseEntity.ok(riskHitService.getList(params));
    }

    @RequestMapping("/detail")
    public ResponseEntity<List<RiskHitLinkResponse>> getRiskHitLink(@RequestParam(value = "requestId") String requestId){
        return ResponseEntity.ok(riskHitService.getDetailList(requestId));
    }

    @RequestMapping("/export")
    public ResponseEntity<String> exportRiskHitLink(@RequestBody RiskHitRequestParams request, HttpServletResponse response) {
        return ResponseEntity.ok(riskHitService.exportData(request, response));
    }
}
