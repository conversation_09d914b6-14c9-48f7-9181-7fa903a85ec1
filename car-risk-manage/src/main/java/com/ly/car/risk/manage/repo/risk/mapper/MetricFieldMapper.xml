<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.MetricFieldMapper">

    <select id="getTotal" resultType="long">
        select count(*) from metric_field
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.MetricField">
        select * from metric_field
        <include refid="getCondition"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.MetricField">
        select *
        from metric_field
    </select>


    <sql id="getCondition">
        where 1 = 1
        <if test="updateUser != null and updateUser != '' ">
            and update_user like CONCAT('%',#{updateUser},'%')
        </if>
        <if test="name != null and name != '' ">
            and `name` like CONCAT('%',#{name},'%')
        </if>
        <if test="type != null">
            and `type` = #{type}
        </if>
        <if test="category != null">
            and `category` = #{category}
        </if>
    </sql>
</mapper>