package com.ly.car.risk.manage.controller;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.bean.ResultMsg;
import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.controller.dto.MetricStrategyDTO;
import com.ly.car.risk.manage.controller.request.MetricStrategyAddReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyDeleteReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyGetReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyListReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyUpdateReq;
import com.ly.car.risk.manage.integration.approve.model.MktApproveFlowDetailVO;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricScene;
import com.ly.car.risk.manage.service.MetricRuleService;
import com.ly.car.risk.manage.service.MetricSceneService;
import com.ly.car.risk.manage.service.MetricStrategyService;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.ly.sof.utils.log.LoggerUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/metricConfig/metricStrategyManage")
public class MetricStrategyController {

    /** Logger日志 */
    protected final Logger logger           = LoggerFactory.getLogger(getClass());

    @Resource
    private MetricStrategyService metricStrategyService;
    @Resource
    private MetricSceneService    metricSceneService;
    @Resource
    private MetricRuleService     metricRuleService;
    
    @RequestMapping("add")
    public String add(@RequestBody MetricStrategyAddReq req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        metricStrategyService.add(req, userInfo);
        return "success";
    }

    /**
     * 送审
     * @param strategyNo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/sendApprove/{strategyNo}", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg sendApprove(@PathVariable("strategyNo") String strategyNo, HttpServletRequest request) {
//        LogContextUtils.setMarker(LogModuleConstants.SEND_APPROVE, "");
//        LogContextUtils.setFilter2(getOperator());
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        LoggerUtils.info(logger, "送审策略id：{}", strategyNo);
        try {
            metricStrategyService.sendApprove(strategyNo, userInfo);
            return new ResultMsg(Boolean.TRUE,  "success", null);
        } catch (Exception e) {
            LoggerUtils.error(logger, "送审失败: {}", e.getMessage(), e);
            return new ResultMsg(Boolean.FALSE, e.getMessage());
        }
    }

    @RequestMapping(value = "/viewApprove", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg viewApprove(@RequestParam("approveNo") String approveNo) {
//        LogContextUtils.setMarker(LogModuleConstants.VIEW_APPROVE, "");
//        LogContextUtils.setFilter1(approveNo);
        LoggerUtils.info(logger, "查询审批详情：{}", approveNo);
        try {
            MktApproveFlowDetailVO approveDetail = metricStrategyService.viewApprove(approveNo);
            return new ResultMsg(Boolean.TRUE,  "success", approveDetail);
        } catch (Exception e) {
            LoggerUtils.error(logger, "获取审批详情失败: {}", e.getMessage(), e);
            return new ResultMsg(Boolean.FALSE, e.getMessage());
        }
    }

    /**
     * 发布上线
     * @param strategyNo
     * @param request
     * @return
     * @throws BizException
     */
    @RequestMapping("publish/{strategyNo}")
    public ResultMsg publish(@PathVariable("strategyNo") String strategyNo, HttpServletRequest request)  {
        LoggerUtils.info(logger, "策略发布上线，策略编号：{}", strategyNo);
        try {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        metricStrategyService.publish(strategyNo, userInfo);
            return new ResultMsg(Boolean.TRUE,  "success");
        } catch (Exception e) {
            LoggerUtils.error(logger, "策略发布上线失败: {}", e.getMessage(), e);
            return new ResultMsg(Boolean.FALSE, e.getMessage());
        }
    }

    @RequestMapping("delete")
    public String delete(@RequestBody MetricStrategyDeleteReq req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        metricStrategyService.delete(req, userInfo);
        return "success";
    }
    
    @RequestMapping("update")
    public String update(@RequestBody MetricStrategyUpdateReq req, HttpServletRequest request) throws BizException {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        metricStrategyService.update(req, userInfo);
        return "success";
    }
    
    @RequestMapping("getList")
    public Pagination getList(@RequestBody MetricStrategyListReq req) {
        return metricStrategyService.getList(req);
    }
    
    @RequestMapping("getLinkRule")
    public List getLinkRule(@RequestBody MetricStrategyGetReq req) {
        return metricStrategyService.getLinkRule(req);
    }
    
    @RequestMapping("getChildScene")
    public List<MetricScene> getAllList() {
        return metricSceneService.getChildScene();
    }
    
    @RequestMapping("detail")
    public MetricStrategyDTO detail(@RequestBody MetricStrategyGetReq req) {
        return metricStrategyService.detail(req);
    }
    
    @RequestMapping("getAllRuleList")
    public List getAllRuleList() {
        return metricRuleService.getAllList();
    }

}
