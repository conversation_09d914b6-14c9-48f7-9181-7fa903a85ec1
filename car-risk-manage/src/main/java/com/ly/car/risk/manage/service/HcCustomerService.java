package com.ly.car.risk.manage.service;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.exception.CodeException;
import com.ly.car.order.entity.SupplierInfo;
import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.manage.bean.BillDateConverter;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.constants.HcCustomerRiskTypeEnum;
import com.ly.car.risk.manage.constants.HcTtlEnum;
import com.ly.car.risk.manage.controller.dto.HcCustomerDTO;
import com.ly.car.risk.manage.controller.dto.HcCustomerExportDTO;
import com.ly.car.risk.manage.controller.params.HcCustomerExcelBean;
import com.ly.car.risk.manage.controller.params.HcCustomerExcelDeleteBean;
import com.ly.car.risk.manage.controller.params.HcCustomerParams;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionAddReq;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.order.mapper.SupplierInfoMapper;
import com.ly.car.risk.manage.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import com.ly.dal.util.DateUtil;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class HcCustomerService {

    @Resource
    private HcCustomerMapper   hcCustomerMapper;
    @Resource
    private SupplierInfoMapper supplierInfoMapper;
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;

    public void add(HcCustomerParams params, UserInfo userInfo){
        if (StringUtils.isBlank(params.getPlateNumber()) && StringUtils.isBlank(params.getIdCardNo())) {
            throw new CodeException(500, "司机车牌和司机身份证必须填写一项");
        }
        //用汇川的driverId查询下是否已经有该类型的名单在
        HcCustomer customer = hcCustomerMapper.selectOne(new QueryWrapper<HcCustomer>()
                .eq("driver_card_no",StringUtils.defaultString(params.getPlateNumber()))
                .eq(StringUtils.isNotBlank(params.getDriverPhone()),"driver_phone",params.getDriverPhone())
                .eq("id_card_no",StringUtils.defaultString(params.getIdCardNo()))
                .eq("customer_type", params.getCustomerType())
                .gt("invalid_time", new Date())
                .last("limit 1")
        );
        if(customer != null){
            //说明有了，这个时候在原先的基础上进行时间延长
            customer.setUpdateTime(new Date());
            customer.setUpdateUser(userInfo.getUsername()+userInfo.getWorkId());
            if(customer.getTtl() != 7){
                customer.setInvalidTime(DateUtil.addDay(customer.getInvalidTime(),getDays(customer.getTtl())));
            }
            customer.setRemark(customer.getRemark());
            customer.setOrderId(params.getOrderId());
            hcCustomerMapper.updateById(customer);
        } else {
            HcCustomer hcCustomer = new HcCustomer();
            hcCustomer.setDriverCardNo(params.getPlateNumber());
            hcCustomer.setDriverPhone(params.getDriverPhone());
            hcCustomer.setCreateTime(new Date());
            hcCustomer.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
            hcCustomer.setUpdateTime(new Date());
            hcCustomer.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
            hcCustomer.setCustomerType(params.getCustomerType());
            hcCustomer.setOrderId(params.getOrderId());
            hcCustomer.setIdCardNo(params.getIdCardNo());
            hcCustomer.setDriverId(params.getDriverId());
            if(params.getTtl() == 7){
                hcCustomer.setInvalidTime(DateUtil.string2Date("2099-01-01 00:00:00"));
            } else {
                hcCustomer.setInvalidTime(DateUtil.addDay(new Date(),getDays(params.getTtl())));
            }
            hcCustomer.setTtl(params.getTtl());
            hcCustomer.setRemark(params.getRemark());
            hcCustomer.setUnRemark(params.getDelRemark());
            hcCustomer.setSupplierCode(params.getSupplierCode());
            hcCustomerMapper.insert(hcCustomer);
        }
    }

    public void delete(HcCustomerParams params,UserInfo userInfo){
        HcCustomer hcCustomer = new HcCustomer();
        hcCustomer.setId(params.getId());
        hcCustomer.setUpdateTime(new Date());
        hcCustomer.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        hcCustomer.setUnRemark(params.getDelRemark());
        hcCustomer.setInvalidTime(new Date());
        List<HcCustomer> list = this.hcCustomerMapper.getByCondition(hcCustomer);
        if (!CollectionUtils.isEmpty(list)) {
            if (list.get(0).getInvalidTime().after(new Date())) {
                this.hcCustomerMapper.updateById(hcCustomer);
            }
        }
    }
 
    public Pagination queryList(HcCustomerParams params){
        long total = this.hcCustomerMapper.total(params);
        List<HcCustomer> list = this.hcCustomerMapper.queryList(params);
        List<HcCustomerDTO> dtoList = new ArrayList<>();
        for(HcCustomer customer : list){
            HcCustomerDTO dto = new HcCustomerDTO();
            dto.setId(customer.getId());
            dto.setDriverId(customer.getDriverId());
            dto.setIdCardNo(DesensitizedUtil.idCardNum(customer.getIdCardNo(), 4, 4));
            dto.setDriverPhone(DesensitizedUtil.mobilePhone(customer.getDriverPhone()));
            dto.setDriverCardNo(customer.getDriverCardNo());
            dto.setCreateTime(DateUtil.date2String(customer.getCreateTime()));
            dto.setCreateUser(customer.getCreateUser());
            dto.setUpdateTime(DateUtil.date2String(customer.getUpdateTime()));
            dto.setUpdateUser(customer.getUpdateUser());
            dto.setRemark(customer.getRemark());
            dto.setUnRemark(customer.getUnRemark());
            dto.setSupplierCode(customer.getSupplierCode());
            dto.setCustomerType(customer.getCustomerType());
            dto.setInvalidTime(DateUtil.date2String(customer.getInvalidTime()));
            dto.setIsDeleted(customer.getInvalidTime().before(new Date())?"无效":"有效");
            dto.setTtl(customer.getTtl());
            dtoList.add(dto);
        }
        return new Pagination<>(dtoList,total,params);
    }

    public Integer getDays(Integer ttl){
        if(ttl == 2){
            return 7;
        } else if(ttl == 3){
            return 30;
        } else if(ttl == 4){
            return 90;
        } else if(ttl == 5){
            return 180;
        } else if(ttl == 6){
            return 365;
        } else {
            return 1;
        }
    }
    
    public UiResult importList(MultipartFile file, UserInfo userInfo) {
        UiResult uiResult = new UiResult<>();
        
        List<HcCustomerExcelBean> list = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), HcCustomerExcelBean.class, new PageReadListener<HcCustomerExcelBean>(list::addAll)).sheet().doRead();
            
            List<SupplierInfo> supplierInfoList = supplierInfoMapper.queryAllSupplier();
            Map<String, String> supplierMap = supplierInfoList
                    .parallelStream()
                    .filter(e -> !e.getCode().contains("_"))
                    .collect(Collectors.toMap(SupplierInfo::getCompanyName, SupplierInfo::getCode, (v1, v2) -> v1));
            
            for (HcCustomerExcelBean bean : list) {
                HcCustomerParams params = new HcCustomerParams();
                params.setDriverCardNo(bean.getDriverCardNo());
                params.setPlateNumber(bean.getDriverCardNo());
                params.setDriverPhone(bean.getDriverPhone());
                params.setIdCardNo(bean.getIdCardNo());
                params.setTtl(HcTtlEnum.getCodeByMsg(bean.getTtlName()));
                params.setRemark(bean.getRemark());
                params.setCustomerType(HcCustomerRiskTypeEnum.getCodeByMsg(bean.getCustomerTypeName()));
                params.setDriverId(bean.getDriverId());
                params.setSupplierCode(supplierMap.getOrDefault(bean.getSupplierName(), ""));
                add(params, userInfo);
            }
            
        } catch (Exception e) {
            log.error("司机风险名单导入错误:", e);
            uiResult = UiResult.fail(500, "当前导入失败");
            return uiResult;
        }
        uiResult.setData("success");
        return uiResult;
    }
    
    public String export(HcCustomerParams req) {
        List<HcCustomer> list = hcCustomerMapper.export(req);
        
        List<SupplierInfo> supplierInfoList = supplierInfoMapper.queryAllSupplier();
        Map<String, String> supplierMap = supplierInfoList
                .parallelStream()
                .filter(e -> !e.getCode().contains("_"))
                .collect(Collectors.toMap(SupplierInfo::getCode, SupplierInfo::getCompanyName, (v1, v2) -> v1));
        
        List<HcCustomerExportDTO> dtoList = list.parallelStream().map(customer -> {
            HcCustomerExportDTO dto = new HcCustomerExportDTO();
            dto.setId(customer.getId());
            dto.setDriverId(customer.getDriverId());
            dto.setIdCardNo(DesensitizedUtil.idCardNum(customer.getIdCardNo(), 4,4));
            dto.setDriverPhone(DesensitizedUtil.mobilePhone(customer.getDriverPhone()));
            dto.setDriverCardNo(customer.getDriverCardNo());
            dto.setCreateTime(DateUtil.date2String(customer.getCreateTime()));
            dto.setCreateUser(customer.getCreateUser());
            dto.setUpdateTime(DateUtil.date2String(customer.getUpdateTime()));
            dto.setUpdateUser(customer.getUpdateUser());
            dto.setRemark(customer.getRemark());
            dto.setUnRemark(customer.getUnRemark());
            dto.setCustomerType(customer.getCustomerType());
            dto.setInvalidTime(DateUtil.date2String(customer.getInvalidTime()));
            dto.setIsDeleted(customer.getInvalidTime().before(new Date()) ? "无效" : "有效");
            
            dto.setCustomerTypeName(HcCustomerRiskTypeEnum.getMsgByCode(dto.getCustomerType()));
            dto.setSupplierName(supplierMap.getOrDefault(dto.getSupplierCode(), ""));
            return dto;
        }).collect(Collectors.toList());
        
        
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String fileName = "司机风险名单_" + System.currentTimeMillis() + ".xlsx";
        EasyExcel.write(byteArrayOutputStream, HcCustomerExportDTO.class)
                .registerConverter(new BillDateConverter())
                .sheet("sheet1")
                .doWrite(dtoList);
        String upload = FileUploadCephUtil.upload(fileName, byteArrayOutputStream);
        addSensitiveAction(upload);
        return upload;
    }
    
    private void addSensitiveAction(String upload) {
        RiskSensitiveActionAddReq record = new RiskSensitiveActionAddReq();
        record.setModule(SensitiveModuleEnum.hc_customer_list.name());
        record.setAction(SensitiveActionEnum.export.name());
        record.setKey(SensitiveKeyEnum.file.name());
        record.setValue(upload);
        riskSensitiveActionService.add(record);
    }
    
    public UiResult deleteImport(MultipartFile file, UserInfo userInfo) {
        UiResult uiResult = new UiResult<>();
        List<Integer> indexs = new ArrayList<>();
        int index = 1;
        
        List<HcCustomerExcelDeleteBean> list = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), HcCustomerExcelDeleteBean.class, new PageReadListener<HcCustomerExcelDeleteBean>(list::addAll)).sheet().doRead();
            
            for (HcCustomerExcelDeleteBean bean : list) {
                index++ ;
                if (bean.getId() != null) {
                    
                    HcCustomerParams params = new HcCustomerParams();
                    params.setId(bean.getId());
                    params.setDelRemark(StringUtils.defaultString(bean.getRemark()));
                    delete(params, userInfo);
                    
                } else {
                    bean.setCustomerType(HcCustomerRiskTypeEnum.getCodeByMsg(bean.getCustomerTypeValue()));
                    bean.setIdCardNo(StringUtils.defaultString(bean.getIdCardNo()));
                    bean.setDriverPhone(StringUtils.defaultString(bean.getDriverPhone()));
                    bean.setDriverId(StringUtils.defaultString(bean.getDriverId()));
                    bean.setDriverCardNo(StringUtils.defaultString(bean.getDriverCardNo()));
                    List<HcCustomer> customerList = hcCustomerMapper.getByCondition(bean);
                    
                    if(CollectionUtils.isEmpty(customerList)){
                        indexs.add(index);
                    }
                    
                    for (HcCustomer hcCustomer : customerList) {
                        HcCustomerParams params = new HcCustomerParams();
                        params.setId(hcCustomer.getId());
                        params.setDelRemark(StringUtils.defaultString(bean.getRemark()));
                        delete(params, userInfo);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("司机风险名单删除导入错误:", e);
            uiResult = UiResult.fail(500, "当前导入失败");
            return uiResult;
        }
        
        if (!CollectionUtils.isEmpty(indexs)) {
            log.warn("未匹配到数据：{}", JSONObject.toJSONString(indexs));
            throw new CodeException(-1, "存在未匹配到的数据，行数：" + indexs.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        
        uiResult.setData("success");
        return uiResult;
    }
}
