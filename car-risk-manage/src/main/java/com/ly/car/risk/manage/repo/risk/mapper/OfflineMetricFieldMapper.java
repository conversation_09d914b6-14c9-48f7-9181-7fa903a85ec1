package com.ly.car.risk.manage.repo.risk.mapper;

import com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricField;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【offline_metric_field(离线风控策略配置-指标特征值)】的数据库操作Mapper
* @createDate 2025-02-28 16:54:14
* @Entity generator.domain.OfflineMetricField
*/
public interface OfflineMetricFieldMapper {

    int deleteByPrimaryKey(Long id);

    int insert(OfflineMetricField record);

    int insertSelective(OfflineMetricField record);

    OfflineMetricField selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OfflineMetricField record);

    int updateByPrimaryKey(OfflineMetricField record);

    long getTotal(Object query);

    List<OfflineMetricField> queryList(Object query);

    List<OfflineMetricField> queryAllList();
}
