package com.ly.car.risk.manage.repo.traffic.mapper;

import com.ly.car.traffic.entity.BasicDataCity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: chunming.zhou
 * @create: 2021-08-20 16:14
 **/
public interface BasicDataCityMapper {

    @Select("SELECT * FROM `basic_data_city` where status = 1 and level = 3")
    List<BasicDataCity> all();


    /**
     * 省份
     * @return
     */
    @Select("SELECT distinct pname as name,pid as tc_id FROM `basic_data_city` where status = 1 and level = 3")
    List<BasicDataCity> provinceList();

    /**
     * 省份
     * @return
     */
    @Select("SELECT *  FROM `basic_data_city` where status != -1 and level = 2 ")
    List<BasicDataCity> provinceInfoList();

    /**
     * 城市列表
     * @return
     */
    @Select(" <script> " +
            "   SELECT * " +
            "   FROM `basic_data_city` " +
            "   where status = 1 and level = 3 " +
            "   and tc_id in (" +
            "   <foreach collection='cityIdList' item='item'  separator=','> " +
            "    #{item} " +
            "   </foreach>" +
            "   )"+
            "</script>" +
            "")
    List<BasicDataCity> cityListByCityId(@Param("cityIdList") Set<Long> cityIdList);

    /**
     * 区域列表（省市区都有）
     * @return
     */
    @Select(" <script> " +
            "   SELECT * " +
            "   FROM `basic_data_city` " +
            "   where  " +
            "   tc_id in (" +
            "   <foreach collection='cityIdList' item='item'  separator=','> " +
            "    #{item} " +
            "   </foreach>" +
            "   )"+
            "</script>" +
            "")
    List<BasicDataCity> areaListByIdList(@Param("cityIdList") Set<Integer> cityIdList);

    /**
     * 城市
     * @param pid
     * @return
     */
    @Select("SELECT * FROM `basic_data_city` where status = 1 and pid=#{pid} and level = 3")
    List<BasicDataCity> cityList(@Param("pid")Integer pid);

    /**
     * 区/县
     * @param pid
     * @return
     */
    @Select("SELECT * FROM `basic_data_city` where  pid=#{pid} and level = 4")
    List<BasicDataCity> countyList(@Param("pid") Integer pid);

    /**
     * 街道
     * @return
     */
    @Select("SELECT * FROM `basic_data_city` where  pid=#{pid} and level = 5")
    List<BasicDataCity> streetList(@Param("pid") Integer pid);


    @Select("select * from basic_data_city where tc_id = #{tcId} and status != -1")
    BasicDataCity selectByTcId(Integer tcId);

}
