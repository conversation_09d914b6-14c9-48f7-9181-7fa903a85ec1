package com.ly.car.risk.manage.constants;

import java.util.HashMap;
import java.util.Map;

public enum RiskTypeEnum {

    TRAJECTORY_EX(1, "轨迹异常"),
    COUPON_EX(2, "套券"),
    QUICK_ORDER_EX(3, "统一刷单"),
    STB_EX(4, "小跑大"),
    BTS_EX(5,"大跑小"),
    BRUSH_ORDER_EX(6,"刷单"),
    AMOUNT_EX(7,"金额异常");

    private Integer code;
    private String msg;

    RiskTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static Map<Integer,String> getAllEnum(){
        Map<Integer,String> allEnums = new HashMap<>();
        for(RiskTypeEnum type : values()){
            allEnums.put(type.code,type.msg);
        }
        return allEnums;
    }

    public static String getMsgByCode(Integer code){
        for(RiskTypeEnum type : values()){
            if(type.getCode().equals(code)){
                return type.getMsg();
            }
        }
        return null;
    }


}
