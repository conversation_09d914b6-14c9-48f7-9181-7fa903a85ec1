package com.ly.car.risk.manage.integration.marketingmng.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @version Id : ConfigLogDTO, v 0.1 2018/10/22 上午10:52 lingfenglee Exp $
 */
@Setter
@Getter
@ToString
public class ConfigLogDTO implements Serializable {

    private static final long serialVersionUID = 8939462991084819989L;

    /** 操作对象 */
    private String            target;
    /** 对象编号 */
    private String            targetId;
    /** 操作类型 */
    private String            operateType;
    /** 操作内容 */
    private String            operateContent;
    /** 操作前对象 */
    private String            preOprData;
    /** 操作后对象 */
    private String            afterOprData;
    /** 操作人姓名 */
    private String            operatorName;
    /** 操作人工号 */
    private String            operatorNo;
    /** 创建时间 */
    private String            gmtCreateTime;

}
