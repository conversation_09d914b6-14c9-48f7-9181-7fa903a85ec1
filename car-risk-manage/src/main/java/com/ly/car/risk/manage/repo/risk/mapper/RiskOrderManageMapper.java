package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.ly.car.risk.entity.RiskOrderManage;
import com.ly.car.risk.manage.model.resp.dashboard.DistributionItem;
import com.ly.car.risk.manage.model.resp.dashboard.StrategyDistributionData;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskOrderManage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

public interface RiskOrderManageMapper extends BaseMapper<RiskOrderManage> {

    long getListTotal(Object query);

    List<RiskOrderManage> getList(Object query);

    List<RiskOrderManage> getExportList(Object query);

    @Update("<script>" +
            "   update risk_order_manage set status = #{status} where order_id = #{orderId}" +
            "</script>")
    int riskOrderEnd(@Param("orderId")String orderId,@Param("status")Integer status);

    List<DistributionItem> findTopRules(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("top") int top);

    List<DistributionItem> findRulePeriodData(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("rules") List<String> rules);

    int findRiskDriverCount(@Param("startDate") String startDate , @Param("endDate")String endDate);
    int findRiskUserCount(@Param("startDate") String startDate , @Param("endDate")String endDate);

    List<StrategyDistributionData> queryStrategyDistribution(@Param("startDate") String startDate , @Param("endDate")String endTime, @Param("top")int top);

    /**
     * 根据订单号更新处罚结果
     * @param judgeResultType 处罚结果
     * @param orderId 订单号
     */
    int updateJudgeResultByOrderId(@Param("judgeResultType") Integer judgeResultType, @Param("orderId") String orderId);
}
