package com.ly.car.risk.manage.controller.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 离线风控策略配置-指标特征值
 * @TableName offline_metric_field
 */
@Data
public class OfflineMetricFieldDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 指标编号
     */
    private String fieldNo;

    /**
     * 描述
     */
    private String description;

    /**
     * 获取指标数值的计算方式（sql）
     */
    private String script;

    /**
     * 指标分类 0-全部 1-数值
     */
    private Integer category;

    /**
     * 指标类型 0-全部 1-风控 2-安全
     */
    private Integer type;

    /**
     * 关联策略数
     */
    private int fieldRelationCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 操作人
     */
    private String updateUser;

    private static final long serialVersionUID = 1L;
}