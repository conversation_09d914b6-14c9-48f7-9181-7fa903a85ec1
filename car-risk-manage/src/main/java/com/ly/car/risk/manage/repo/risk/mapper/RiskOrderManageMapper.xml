<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.RiskOrderManageMapper">

    <resultMap id="distributionItemMap" type="com.ly.car.risk.manage.model.resp.dashboard.DistributionItem">
        <result column="rule" property="rule"/>
        <result column="match_count" property="matchCount"/>
        <result column="current_percent" property="currentPercent"/>
        <result column="last_percent" property="lastPercent"/>
    </resultMap>

    <resultMap id="strategyDistributionMap" type="com.ly.car.risk.manage.model.resp.dashboard.StrategyDistributionData">
        <result column="rule" property="rule"/>
        <result column="rule_name" property="ruleName"/>
        <result column="count" property="count"/>
        <result column="rate" property="rate"/>
    </resultMap>

    <sql id="getListCondition">
        where 1=1
        <if test="orderId != null">
            and rom.order_id = #{orderId}
        </if>
        <if test="cityId != null and cityId.size > 0">
            and rom.city_id in
            <foreach collection="cityId" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCode != null  and supplierCode.size > 0 ">
            and rom.supplier_code in
            <foreach collection="supplierCode" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeFull != null and supplierCodeFull.size > 0">
            and rom.supplier_code_full in
            <foreach collection="supplierCodeFull" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="channelId != null and channelId.size > 0 ">
            and rom.channel_id in
            <foreach collection="channelId" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="riskType != null and riskType.size > 0 ">
            and rom.risk_type in
            <foreach collection="riskType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="ruleNo != null and ruleNo != '' ">
            and rom.rule_no like CONCAT("%",#{ruleNo},"%")
        </if>
        <if test="operateUser != null and operateUser != ''">
            and rom.operate_user = #{operateUser}
        </if>
        <if test="startTime != null and startTime != '' ">
            and rom.create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != '' ">
            and rom.create_time &lt;= #{endTime}
        </if>
        <if test="isRisk != null">
            and rom.is_risk = #{isRisk}
        </if>
        <if test="startFinishTime != null and startFinishTime != '' and endFinishTime != null and endFinishTime != ''">
            and rom.finish_time between #{startFinishTime} and #{endFinishTime}
        </if>
        <if test="status != null">
            and ts.work_status = #{status}
        </if>
        <if test="judgeResult != null">
            and ts.judge_result = #{judgeResult}
        </if>
        <if test="env != null and env != '' ">
            and rom.env = #{env}
        </if>
    </sql>

    <select id="getListTotal" resultType="long">
        select count(*) from risk_order_manage rom
        <if test="judgeResult != null or status != null">
            left join tc_supplier_work_order ts on rom.order_id = ts.order_id
        </if>
        <include refid="getListCondition"/>
    </select>

    <select id="getList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.RiskOrderManage">
        select rom.* from risk_order_manage rom
        <if test="judgeResult != null or status != null">
            left join tc_supplier_work_order ts on rom.order_id = ts.order_id
        </if>
        <include refid="getListCondition"/>
        order by rom.id desc
        limit #{offset}, #{size}
    </select>

    <select id="getExportList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.RiskOrderManage">
        select rom.* from risk_order_manage rom
        <if test="judgeResult != null or status != null">
            left join tc_supplier_work_order ts on rom.order_id = ts.order_id
        </if>
        <include refid="getListCondition"/>
        order by rom.id desc
        limit 10000
    </select>

    <select id="findTopRules" resultMap="distributionItemMap">
        SELECT top_rules.rule_no                as rule,
               rule_count                       as match_count,
               (rule_count / total_count) * 100 AS current_percent
        FROM (SELECT rule_no,
                     COUNT(*) AS rule_count
              FROM risk_order_manage
              WHERE create_time BETWEEN #{startDate} AND #{endDate}
              GROUP BY rule_no
              ORDER BY rule_count DESC
                  LIMIT #{top}) AS top_rules,
             (SELECT COUNT(*) AS total_count
              FROM risk_order_manage
              WHERE create_time BETWEEN #{startDate} AND #{endDate}) AS total
        ORDER BY top_rules.rule_count DESC
    </select>

    <select id="findRulePeriodData" resultMap="distributionItemMap">
        SELECT top_rules.rule_no as rule,
        rule_count as match_count,
        (rule_count / total_count) * 100 AS last_percent
        FROM (SELECT rule_no,
        COUNT(*) AS rule_count
        FROM risk_order_manage
        WHERE create_time BETWEEN #{startDate} AND #{endDate}
        and rule_no in
        <foreach collection="rules" item="rule" index="index" open="(" close=")" separator=",">
            #{rule}
        </foreach>
        GROUP BY rule_no) AS top_rules,
        (SELECT COUNT(*) AS total_count
        FROM risk_order_manage
        WHERE create_time BETWEEN #{startDate} AND #{endDate}) AS total
    </select>

    <select id="findRiskDriverCount" resultType="java.lang.Integer">
        select count(DISTINCT driver_card_no)
        from risk_order_manage where driver_card_no != ''
        <if test="startDate!=null and startDate !=''">
            and create_time > #{startDate}
        </if>
        <if test="endDate!=null and endDate !=''">
            and create_time &lt; #{endDate}
        </if>
    </select>

    <select id="findRiskUserCount" resultType="java.lang.Integer">
        select count(DISTINCT ifnull(member_id,union_id))
        from risk_order_manage where ifnull(member_id,union_id) !=''
        <if test="startDate!=null and startDate !=''">
            and create_time > #{startDate}
        </if>
        <if test="endDate!=null and endDate !=''">
            and create_time &lt; #{endDate}
        </if>
    </select>

    <select id="queryStrategyDistribution" resultMap="strategyDistributionMap">
        SELECT top_rules.rule_no as rule,
        top_rules.rule_name as rule_name,
        rule_count as `count`,
        (rule_count / total_count) * 100 AS rate
        FROM (SELECT rule_no,
        GROUP_CONCAT(distinct risk_type_name) rule_name,
        COUNT(*) AS rule_count
        FROM risk_order_manage
        <where>
            <if test="startDate!=null and startDate !=''">
                and create_time > #{startDate}
            </if>
            <if test="endDate!=null and endDate !=''">
                and create_time &lt; #{endDate}
            </if>
        </where>
        GROUP BY rule_no
        ORDER BY rule_count DESC LIMIT #{top}) AS top_rules,
        (SELECT COUNT(*) AS total_count
        FROM risk_order_manage
        <where>
            <if test="startDate!=null and startDate !=''">
                and create_time > #{startDate}
            </if>
            <if test="endDate!=null and endDate !=''">
                and create_time &lt; #{endDate}
            </if>
        </where>
        ) AS total
        ORDER BY top_rules.rule_count DESC
    </select>

    <update id="updateJudgeResultByOrderId">
        update risk_order_manage
        set judge_result_type = #{judgeResultType,jdbcType=INTEGER},
            judge_time = now()
        where order_id = #{orderId,jdbcType=VARCHAR}
    </update>


</mapper>
