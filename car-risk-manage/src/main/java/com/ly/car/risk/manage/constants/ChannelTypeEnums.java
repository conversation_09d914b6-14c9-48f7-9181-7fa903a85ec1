package com.ly.car.risk.manage.constants;

import com.ly.car.risk.manage.controller.dto.NameValue;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * @description:
 * @author: chunming.zhou
 * @create: 2022-11-29 11:17
 **/
public enum ChannelTypeEnums {
    APP(0, "APP(0)"),
    WeChart(1, "WeChart(1)"),
    OTHER(2, "其他(2)"),
    GOUP_WeChart(3, "秒走小程序(3)"),
    GAODEFX(4, "高德特殊分销(4)"),
    FIXPRICEFX(5, "外部一口价分销(5)"),
    FIXPRICE(6, "外部实用实付分销(6)"),
    DiDiFX(7, "滴滴特殊分销(7)"),
    TOUCH(8, "touch站(8)"),
    BAIDU(9, "百度小程序(9)"),
    KUAI(10, "快应用(10)"),
    OWEN(11, "独立小程序(11)"),
    FX_PLATFORM(30000, "用车分销开放平台(30000)"),
    FX_TOUCH(40000, " 分销-toucn站(40000)"),


    DEFAULT(-99, "兜底(-999)"),
    ;


    public int val;
    public String name;

    public int getVal() {
        return val;
    }

    public String getName() {
        return name;
    }

    ChannelTypeEnums(int val, String name) {
        this.val = val;
        this.name = name;
    }

    public static ChannelTypeEnums get(Integer val) {
        return Stream.of(values()).filter((o) -> {
            return val != null && o.val == val;
        }).findFirst().orElse(null);
    }


    public static List<NameValue> getAll() {
        List<NameValue> list = new ArrayList();
        for (ChannelTypeEnums enums : values()) {
            list.add(new NameValue(enums.getName(), enums.getVal() + ""));
        }
        return list;
    }

}
