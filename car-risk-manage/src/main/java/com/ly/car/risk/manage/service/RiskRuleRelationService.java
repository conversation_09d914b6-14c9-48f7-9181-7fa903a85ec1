package com.ly.car.risk.manage.service;

import com.ly.car.risk.manage.controller.params.RuleParam;
import com.ly.car.risk.manage.repo.risk.mapper.RiskRuleRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class RiskRuleRelationService {

    @Resource
    private RiskRuleRelationMapper riskRuleRelationMapper;

    public void insertOrUpdate(List<RuleParam> ruleParam,String ruleNo){

    }
}
