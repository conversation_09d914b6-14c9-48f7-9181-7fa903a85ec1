package com.ly.car.risk.manage.handle;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.List;
import java.util.Map;

/**
 * 自定义下拉框
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SpinnerWriteHandler implements SheetWriteHandler {

    private List<Map<Integer, List<String>>> list;// 需要下拉框的列

    @Override
    public void afterSheetCreate(WriteWorkbookHolder arg0, WriteSheetHolder arg1) {

        // 获取一个workbook
        Workbook workbook = arg0.getWorkbook();

        for (Map<Integer, List<String>> map : list) {
            Sheet sheet = arg1.getSheet();
            /// 开始设置下拉框
            DataValidationHelper helper = sheet.getDataValidationHelper();// 设置下拉框
            for (Map.Entry<Integer, List<String>> entry : map.entrySet()) {
                // 定义sheet的名称
                String sheetName = "SheetHidden" + entry.getKey();
                // 1.创建一个隐藏的sheet 名称为 proviceSheet
                Sheet proviceSheet = workbook.createSheet(sheetName);
                // 设置隐藏
                workbook.setSheetHidden(workbook.getSheetIndex(sheetName), true);
                // 2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
                // 设置下拉框数据
                List<String> values = entry.getValue();
                for (int i = 0, length = values.size(); i < length; i++) {
                    // i:表示你开始的行数 0表示你开始的列数
                    proviceSheet.createRow(i).createCell(0).setCellValue(values.get(i));
                }
                Name category1Name = workbook.createName();
                category1Name.setNameName(sheetName);
                // 4 $A$1:$A$N代表 以A列1行开始获取N行下拉数据
                category1Name.setRefersToFormula(sheetName + "!$A$1:$A$" + (values.size()));
                // 5 将刚才设置的sheet引用到你的下拉列表中 //起始行、终止行、起始列、终止列
                CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());
                DataValidationConstraint constraint8 = helper.createFormulaListConstraint(sheetName);
                DataValidation dataValidation3 = helper.createValidation(constraint8, addressList);
                arg1.getSheet().addValidationData(dataValidation3);
            }
        }
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder arg0, WriteSheetHolder arg1) {
        // TODO Auto-generated method stub

    }
}

