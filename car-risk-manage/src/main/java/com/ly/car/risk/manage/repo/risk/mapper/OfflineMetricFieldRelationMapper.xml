<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.OfflineMetricFieldRelationMapper">

    <resultMap id="BaseResultMap" type="com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricFieldRelation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
            <result property="fieldId" column="field_id" jdbcType="BIGINT"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="rightType" column="right_type" jdbcType="INTEGER"/>
            <result property="rightValue" column="right_value" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,strategy_id,field_id,
        operator,right_type,right_value,
        create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from offline_metric_field_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from offline_metric_field_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricFieldRelation" useGeneratedKeys="true">
        insert into offline_metric_field_relation
        ( strategy_id,field_id
        ,operator,right_type,right_value,sort
        ,create_time)
        values (#{strategyId,jdbcType=BIGINT},#{fieldId,jdbcType=BIGINT}
        ,#{operator,jdbcType=VARCHAR},#{rightType,jdbcType=INTEGER},#{rightValue,jdbcType=VARCHAR},#{sort,jdbcType=INTEGER}
        ,#{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricFieldRelation" useGeneratedKeys="true">
        insert into offline_metric_field_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="strategyId != null">strategy_id,</if>
                <if test="fieldId != null">field_id,</if>
                <if test="operator != null">operator,</if>
                <if test="rightType != null">right_type,</if>
                <if test="rightValue != null">right_value,</if>
                <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
                <if test="fieldId != null">#{fieldId,jdbcType=BIGINT},</if>
                <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
                <if test="rightType != null">#{rightType,jdbcType=INTEGER},</if>
                <if test="rightValue != null">#{rightValue,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricFieldRelation">
        update offline_metric_field_relation
        <set>
                <if test="strategyId != null">
                    strategy_id = #{strategyId,jdbcType=BIGINT},
                </if>
                <if test="fieldId != null">
                    field_id = #{fieldId,jdbcType=BIGINT},
                </if>
                <if test="operator != null">
                    operator = #{operator,jdbcType=VARCHAR},
                </if>
                <if test="rightType != null">
                    right_type = #{rightType,jdbcType=INTEGER},
                </if>
                <if test="rightValue != null">
                    right_value = #{rightValue,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricFieldRelation">
        update offline_metric_field_relation
        set 
            strategy_id =  #{strategyId,jdbcType=BIGINT},
            field_id =  #{fieldId,jdbcType=BIGINT},
            operator =  #{operator,jdbcType=VARCHAR},
            right_type =  #{rightType,jdbcType=INTEGER},
            right_value =  #{rightValue,jdbcType=VARCHAR},
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <select id="countByFieldId" resultType="int">
        select count(*)
        from offline_metric_field_relation
        where field_id = #{fieldId,jdbcType=NUMERIC}
    </select>
</mapper>
