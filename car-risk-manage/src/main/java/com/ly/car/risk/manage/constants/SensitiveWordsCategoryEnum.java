package com.ly.car.risk.manage.constants;

import com.ly.car.risk.manage.controller.dto.CommonReturnDTO;

import java.util.ArrayList;
import java.util.List;

public enum SensitiveWordsCategoryEnum {

    TEXT(0, "文本"),
    RECORD(1,"录音"),
    PIC(2,"图片");

    private Integer code;
    private String msg;

    SensitiveWordsCategoryEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static List<CommonReturnDTO> getAllEnum(){
        List<CommonReturnDTO> parentDto = new ArrayList<>();
        for (SensitiveWordsCategoryEnum enumItem : SensitiveWordsCategoryEnum.values()) {
            CommonReturnDTO dto = new CommonReturnDTO();
            dto.setKey(enumItem.code);
            dto.setValue(enumItem.msg);
            parentDto.add(dto);
        }
        return parentDto;
    }

    public static String getMsgByCode(Integer code){
        for(SensitiveWordsCategoryEnum type : values()){
            if(type.getCode().equals(code)){
                return type.getMsg();
            }
        }
        return null;
    }
}
