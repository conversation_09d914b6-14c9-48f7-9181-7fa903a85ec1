package com.ly.car.risk.manage.utils;

import com.ly.car.risk.manage.service.log.AbstractCustomLogService;
import com.ly.car.risk.manage.service.log.DefaultCustomLogService;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version Id: LogCompare, v 0.1 2018/10/29 上午11:00 lingfenglee Exp $
 */
@Target({ ElementType.TYPE, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface LogCompare {

    enum Type {
               /**
                * 普通值
                */
               STRING,
               /**
                * 枚举值
                */
               ENUM,
               /**
                * Date类型
                */
               DATE,
               /**
                * 自定义类型
                */
               CUSTOM,
               /**
                * 对象
                */
               OBJECT,
               /**
                * 对象list
                */
               LISTOBJECT
    }

    String name() default "unknown";

    Type type() default Type.STRING;

    /**
     * 当Type=CUSTOM时候，customClass所对应的class一定要重写AbstractCustomLogService
     * @return
     */
    Class<? extends AbstractCustomLogService> customClass() default DefaultCustomLogService.class;

}
