package com.ly.car.risk.manage.controller;

import com.ly.car.exception.CodeException;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.bean.UiResultWrapper;
import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.controller.dto.OfflineMetricFieldDTO;
import com.ly.car.risk.manage.controller.request.*;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.service.MetricRuleService;
import com.ly.car.risk.manage.service.MetricSceneService;
import com.ly.car.risk.manage.service.MetricStrategyService;
import com.ly.car.risk.manage.service.OfflineMetricFieldService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

@RestController
@RequestMapping("/offline/metricField")
public class OfflineRiskFieldController {

    /** Logger日志 */
    protected final Logger logger           = LoggerFactory.getLogger(getClass());

    @Resource
    private MetricStrategyService metricStrategyService;

    @Resource
    private OfflineMetricFieldService offlineMetricFieldService;

    @Resource
    private MetricSceneService    metricSceneService;
    @Resource
    private MetricRuleService     metricRuleService;
    
    @RequestMapping("add")
    public String add(@RequestBody OfflineMetricFieldAddReq req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        offlineMetricFieldService.add(req, userInfo);
        return "success";
    }


    @RequestMapping("delete")
    public String delete(@RequestBody OfflineMetricFieldDeleteReq req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        try {
            offlineMetricFieldService.delete(req, userInfo);
        } catch (BizException e) {
            throw new CodeException(500, e.getMessage());
        }
        return "success";
    }
    
    @RequestMapping("update")
    public String update(@RequestBody OfflineMetricFieldUpdateReq req, HttpServletRequest request) throws BizException {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        offlineMetricFieldService.update(req, userInfo);
        return "success";
    }
    
    @RequestMapping("getList")
    public Pagination getList(@RequestBody OfflineMetricFieldQueryReq req) {
        return offlineMetricFieldService.getList(req);
    }
    

    @RequestMapping("detail")
    public OfflineMetricFieldDTO detail(@RequestBody OfflineMetricFieldGetReq req) {
        return offlineMetricFieldService.detail(req.getId());
    }
    
    @RequestMapping("getAllList")
    public List getAllRuleList() {
        return offlineMetricFieldService.getAllList();
    }

}
