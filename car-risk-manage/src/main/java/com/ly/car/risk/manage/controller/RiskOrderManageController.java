package com.ly.car.risk.manage.controller;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.entity.RiskOrderRecord;
import com.ly.car.risk.manage.bean.BillDateConverter;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.bean.properties.UrlsProperties;
import com.ly.car.risk.manage.constants.JudgeTypeEnum;
import com.ly.car.risk.manage.constants.NewRiskOrderTypeEnum;
import com.ly.car.risk.manage.controller.dto.RiskOrderManageDTO;
import com.ly.car.risk.manage.controller.dto.RiskOrderManageListDTO;
import com.ly.car.risk.manage.controller.params.RiskOrderAddReq;
import com.ly.car.risk.manage.controller.params.RiskOrderExcelBean;
import com.ly.car.risk.manage.controller.params.RiskOrderImportReq;
import com.ly.car.risk.manage.controller.params.RiskOrderManageReq;
import com.ly.car.risk.manage.controller.params.RiskOrderManageUpdateReq;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionAddReq;
import com.ly.car.risk.manage.controller.params.*;
import com.ly.car.risk.manage.enums.JudgeResultEnum;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.SupplierAppealRecordMapper;
import com.ly.car.risk.manage.repo.risk.mapper.TcSupplierWorkOrderMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.manage.repo.risk.mapper.entity.SupplierAppealRecord;
import com.ly.car.risk.manage.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.manage.service.RiskOrderManageService;
import com.ly.car.risk.manage.service.RiskOrderRecordService;
import com.ly.car.risk.manage.service.RiskSensitiveActionService;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import com.ly.car.risk.manage.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/risk/order")
public class RiskOrderManageController {
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;
    @Resource
    private RiskOrderManageService     riskOrderManageService;
    @Resource
    private RiskOrderRecordService riskOrderRecordService;
    @Resource
    private TcSupplierWorkOrderMapper tcSupplierWorkOrderMapper;
    @Resource
    private UrlsProperties urlsProperties;
    @Resource
    private SupplierAppealRecordMapper supplierAppealRecordMapper;

    @RequestMapping("/getList")
    public Pagination<RiskOrderManageListDTO> getList(@RequestBody RiskOrderManageReq req, UserInfo userInfo){
        return riskOrderManageService.getList(req);
    }

    @RequestMapping("/add")
    public UiResult addRiskOrder(@RequestBody RiskOrderAddReq req, HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        return riskOrderManageService.addRiskOrder(req,userInfo);
    }

    @RequestMapping("/update")
    public String updateRiskOrder(@RequestBody RiskOrderManageUpdateReq req){
        return riskOrderManageService.updateRiskOrder(req);
    }

    @RequestMapping("/syncSupplier")
    public String syncSupplier(@RequestBody RiskOrderManageReq req){
        for(String orderId : req.getOrderIds()){
            //查询风险但信息
            RiskOrderManage riskOrderManage = riskOrderManageService.getManageByOrderId(orderId);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderId",orderId);
            jsonObject.put("riskType",riskOrderManage.getRiskType());
            OkHttpClientUtil.getInstance().post(urlsProperties.getWorkerOrder(), JsonUtils.json(jsonObject),null);
        }
        return "success";
    }

    @RequestMapping("/export")
    public String export(@RequestBody RiskOrderManageReq req){
        if(StringUtils.isBlank(req.getStartFinishTime())){
            if(req.getFinishTime() != null && req.getFinishTime().size() > 0){
                req.setStartFinishTime(req.getFinishTime().get(0));
                req.setEndFinishTime(req.getFinishTime().get(1));
            }
        }
        List<RiskOrderManage> supplierSettlementExportRsps = riskOrderManageService.export(req);
        List<TcSupplierWorkOrder> supplierWorkOrders = new ArrayList<>();
        List<String> orderIds = supplierSettlementExportRsps.stream().map(RiskOrderManage::getOrderId).collect(Collectors.toList());
        List<SupplierAppealRecord> supplierAppealRecordList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orderIds)){
            supplierWorkOrders = tcSupplierWorkOrderMapper.selectList(new QueryWrapper<TcSupplierWorkOrder>()
                    .in("order_id", orderIds)
            );
            supplierAppealRecordList = supplierAppealRecordMapper.selectList(new QueryWrapper<SupplierAppealRecord>()
                .in("order_id",orderIds)
            );
        }
        List<RiskOrderManageDTO> orderManageDTOS = new ArrayList<>();
        List<TcSupplierWorkOrder> finalSupplierWorkOrders = supplierWorkOrders;
        List<SupplierAppealRecord> finalSupplierAppealRecordList = supplierAppealRecordList;
        supplierSettlementExportRsps.forEach(e->{
            RiskOrderManageDTO dto = new RiskOrderManageDTO();
            BeanUtils.copyProperties(e,dto);
            TcSupplierWorkOrder tcSupplierWorkOrder = finalSupplierWorkOrders.stream().filter(data->data.getOrderId().equals(e.getOrderId())).findFirst().orElse(null);
            SupplierAppealRecord supplierAppealRecord = finalSupplierAppealRecordList.stream().filter(data->data.getOrderId().equals(e.getOrderId())).findFirst().orElse(null);
            dto.setJudgeResult(JudgeResultEnum.getDescByCode(e.getJudgeResultType()));
            if(tcSupplierWorkOrder != null && JudgeResultEnum.isDefault(e.getJudgeResultType())){
                if(tcSupplierWorkOrder.getJudgeResult() == 1){
                    dto.setJudgeResult("司机有责");
                } else if(tcSupplierWorkOrder.getJudgeResult() == 2){
                    dto.setJudgeResult("司机无责");
                } else if(tcSupplierWorkOrder.getIsSync() == 1 && tcSupplierWorkOrder.getJudgeResult() == 0){
                    dto.setJudgeResult("待供应商反馈");
                } else {
                    dto.setJudgeResult("无");
                }
            } else {
                dto.setJudgeResult("无");
            }
            if(supplierAppealRecord != null){
                dto.setAppealText(supplierAppealRecord.getAppealText());
            }
            orderManageDTOS.add(dto);
        });
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String fileName = "风险订单列表_" + System.currentTimeMillis()+".xlsx";
        EasyExcel.write(byteArrayOutputStream, RiskOrderManageDTO.class).registerConverter(new BillDateConverter()).sheet("sheet1")
                .doWrite(orderManageDTOS);
        String upload = FileUploadCephUtil.upload(fileName, byteArrayOutputStream);
        
        addSensitiveAction(upload);
        
        return upload;
    }
    
    private void addSensitiveAction(String upload) {
        RiskSensitiveActionAddReq record = new RiskSensitiveActionAddReq();
        record.setModule(SensitiveModuleEnum.risk_order_list.name());
        record.setAction(SensitiveActionEnum.export.name());
        record.setKey(SensitiveKeyEnum.file.name());
        record.setValue(upload);
        riskSensitiveActionService.add(record);
    }
    
    @RequestMapping("/import")
    public UiResult importList(MultipartFile file, RiskOrderImportReq req,HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        return this.riskOrderManageService.importList(file,req,userInfo);
    }

    public static void main(String[] args) {

    }

    /**
     * 下载模板
     * */
    @RequestMapping("/exportTemplate")
    public String exportTemplate(HttpServletResponse response){
        String fileName = null;
        List<RiskOrderExcelBean> arrayList = new ArrayList<>();
        RiskOrderExcelBean bean = new RiskOrderExcelBean();
        bean.setOrderId("YNC123456");
        bean.setJudgeResult("惩罚结果，-1：无， 0-待供应商反馈 1-司机有责 2-司机无责");
        bean.setRemark("这里是备注");
        arrayList.add(bean);
        try {
            fileName = URLEncoder.encode("风控订单模版", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), RiskOrderExcelBean.class).sheet("模板").doWrite(arrayList);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return "success";
    }

    /**
     * 查询风险类型
     * */
    @RequestMapping("queryRiskType")
    public Map<Integer,String> queryRiskType(){
        return NewRiskOrderTypeEnum.getAllRiskType();
    }

    /**
     * 查询处罚类型
     * */
    @RequestMapping("queryJudgeType")
    public Map<String,String> queryJudgeType(){
        return JudgeTypeEnum.getAllRiskType();
    }

    /**
     * 获取操作记录
     * */
    @RequestMapping("detail")
    public List<RiskOrderRecord> getRecordList(@RequestBody RiskOrderManageReq req){
        return riskOrderRecordService.getList(req.getOrderId());
    }

    /**
     * 选择填写信息后，点击确定，将订单号关联的订单审核结果同步风险订单管理界面中
     * 通过：订单是否风险单标识变更为“否”，并同步供应商审核结果，同时将申诉订单和附件信息、备注原因同步至上游渠道，状态为待上游反馈，需区分是否为渠道反馈订单，非渠道反馈订单的订单状态更新为已结束。
     * 不通过：订单是否风险单标识变更为“是”，并同步供应商审核结果及备注原因，状态为无法确认。
     * */
    @RequestMapping("manualAudit")
    public UiResult manualAudit(@RequestBody RiskOrderManageReq req) throws Exception {
        return this.riskOrderManageService.manualAudit(req);
    }


    /**
     * 申诉单查看
     * */
    @RequestMapping("queryAppeal")
    public UiResult queryAppeal(@RequestBody RiskOrderManageReq req){
        return this.riskOrderManageService.queryAppeal(req.getOrderId());
    }



    /**
     * 附件查看
     * */
    @RequestMapping("attachmentQuery")
    public Map<String,Object> attachmentQuery(@RequestBody RiskOrderManageReq req){
        return this.riskOrderManageService.attachmentQuery(req.getOrderId());
    }


}
