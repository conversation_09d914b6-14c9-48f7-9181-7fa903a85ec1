package com.ly.car.risk.manage.controller.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 风控名单管理
 * </p>
 *
 * <AUTHOR> @since 2022-08-11
 */
@Data
@Accessors(chain = true)
@ColumnWidth(15)
public class RiskCustomerManageDto {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ExcelProperty(value = "编号",index = 0)
    private Long id;
    /**
     * 客户类型
     */
    @ExcelProperty(value = "名单类型",index = 1)
    private String riskType;
    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型",index = 2)
    private String customerType;
    /**
     * ID
     */
    @ExcelProperty(value = "ID",index = 3)
    private String customerValue;
    /**
     * 操作时间
     */
    @ExcelProperty(value = "加入时间",index = 4)
    private Date createTime;
    @ExcelProperty(value = "拉出时间",index = 5)
    private Date updateTime;
    /**
     * 名单状态
     */
    @ExcelProperty(value = "名单状态",index = 6)
    private String status;
    /**
     * 有效期限
     */
    @ExcelProperty(value = "有效期限",index = 7)
    private String ttl;
    
    @ExcelProperty(value = "绑定号码",index = 8)
    private String bindUser;
    
    @ExcelProperty(value = "会员ID",index = 9)
    private String memberId;
    
    @ExcelProperty(value = "所属供应商",index = 10)
    private String supplierName;
    
    @ExcelProperty(value = "操作人",index = 11)
    private String optionName;
    @ExcelProperty(value = "操作类型",index = 12)
    private String optionType;
    @ExcelProperty(value = "拉入原因",index = 13)
    private String riskRemark;

    @ExcelProperty(value = "一对一手机号",index = 14)
    private String bingUser;
    
    @ExcelProperty(value = "订单号",index = 15)
    private String bindOrder;
    
    @ExcelProperty(value = "到期时间",index = 16)
    private Date invalidTime;
    @ExcelProperty(value = "拉黑子类型",index = 17)
    private String blackChildType;
    @ExcelIgnore
    private Boolean disabled;

    /**
     *
     * 创建人*/
    @ExcelProperty
    private String createUser;

    @ExcelIgnore
    private String delRemark;

}
