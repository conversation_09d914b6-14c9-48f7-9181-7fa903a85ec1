package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
public class SensitiveRecord extends Model<SensitiveRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String sensitiveContext;
    private Integer type;
    private String orderId;
    private String driverCardNo;
    private String unionId;
    private String memberId;
    private String messageId;
    private Date createTime;
    private String originalText;
    private Integer wordType;
    private Integer ivrCallFlag;


}
