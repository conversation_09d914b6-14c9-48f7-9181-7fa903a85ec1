package com.ly.car.risk.manage.handle.dashboard;

import com.ly.car.risk.manage.handle.dashboard.riskstrategy.OfflineStrategyHandler;
import com.ly.car.risk.manage.handle.dashboard.riskstrategy.RealTimeStrategyHandler;
import com.ly.car.risk.manage.model.enums.RiskDistributionTypeEnum;
import com.ly.car.risk.manage.model.enums.RiskPeakTypeEnum;
import com.ly.car.risk.manage.model.enums.RiskTendTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class RiskOverviewFactory implements ApplicationContextAware, InitializingBean {

    private ApplicationContext applicationContext;

    private Map<RiskTendTypeEnum, RiskTendHandler> riskTendHandlerMap;

    private Map<RiskPeakTypeEnum, RiskPeakHandler> riskPeakHandlerMap;

    private Map<RiskDistributionTypeEnum,RiskDistributionHandler> riskDistributionHandlerMap;

    @Resource
    private OfflineStrategyHandler offlineStrategyHandler;
    
    @Resource
    private RealTimeStrategyHandler realTimeStrategyHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        initRiskTendHandler();
        initRiskPeakHandler();
        initRiskDistributionHandler();

    }
    
    public RealTimeStrategyHandler findRealTimeStrategyHandler(){
        return realTimeStrategyHandler;
    }
    
    public OfflineStrategyHandler findOfflineStrategyHandler(){
        return offlineStrategyHandler;
    }

    public Map<RiskTendTypeEnum, RiskTendHandler> getRiskTendHandlerMap() {
        return this.riskTendHandlerMap;
    }

    public Map<RiskPeakTypeEnum, RiskPeakHandler> getRiskPeakHandlerMap() {
        return this.riskPeakHandlerMap;
    }

    public Map<RiskDistributionTypeEnum,RiskDistributionHandler> getRiskDistributionHandlerMap() {
        return this.riskDistributionHandlerMap;
    }


    private void initRiskDistributionHandler() {
        Map<RiskDistributionTypeEnum,RiskDistributionHandler> map = new HashMap<>();
        Map<String, RiskDistributionHandler> distributionHandlers = applicationContext.getBeansOfType(RiskDistributionHandler.class, false, true);
        for (RiskDistributionHandler handler : distributionHandlers.values()) {
            map.put(handler.support(), handler);
        }
        this.riskDistributionHandlerMap = map;
    }

    private void initRiskPeakHandler() {
        Map<RiskPeakTypeEnum, RiskPeakHandler> map = new HashMap<>();
        Map<String, RiskPeakHandler> peakHandlers = applicationContext.getBeansOfType(RiskPeakHandler.class, false, true);
        for (RiskPeakHandler handler : peakHandlers.values()) {
            map.put(handler.support(), handler);
        }
        this.riskPeakHandlerMap = map;
    }


    private void initRiskTendHandler() {
        Map<RiskTendTypeEnum, RiskTendHandler> map = new HashMap<>();
        Map<String, RiskTendHandler> tendHandlers = applicationContext.getBeansOfType(RiskTendHandler.class, false, true);
        for (RiskTendHandler handler : tendHandlers.values()) {
            map.put(handler.support(), handler);
        }
        this.riskTendHandlerMap = map;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


}
