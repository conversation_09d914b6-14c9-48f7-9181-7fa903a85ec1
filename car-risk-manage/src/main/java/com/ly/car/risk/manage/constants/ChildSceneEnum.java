package com.ly.car.risk.manage.constants;

import com.ly.car.risk.manage.controller.dto.CommonReturnChildrenDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public enum ChildSceneEnum {
    /**
     * 风险子场景 1-司推乘返佣 2-其他
     */
    OFF_LINE_RAKE_ACK("3-1", "司推乘返佣"),
    OFF_LINE_CHANNEL_BRUSH("3-3","渠道刷单"),
    OFF_LINE_SPECIAL_CAR("3-4","专车场景"),
    OFF_LINE_SFC("3-5","顺风车场景"),
    OFF_LINE_RECEIVE_ORDER("2-1","司机接单"),
    OFF_LINE_USER_RESERVE("2-2","用户预订"),
    PLACE_ORDER_USER_ORDER("2-3","用户下单"),
    OTHER("4-1", "其他"),
    SEARCH_PRICE_USER("5-1","用户询价"),
    CREATE_ORDER_USER("6-1","用户创单"),

    SELF_HITCH_REGISTER("7-1","司机注册"),
    SELF_HITCH_CERTIFICATION("7-2","司机认证"),
    SELF_HITCH_LOGIN("7-3","司机登录"),
    SELF_HITCH_RELEASE("7-4","司机发布"),
    SELF_HITCH_RECEIVE_ORDER("7-5","司机接单"),
    SELF_HITCH_FINISH_ORDER("7-6","司机完单"),
    SELF_HITCH_CASH("7-7","司机提现"),
    SELF_HITCH_UPDATE_INFO("7-10","修改个人信息"),
    SELF_HITCH_BAND_CARD("7-11","解绑卡")
    ;

    private String code;
    private String msg;

    ChildSceneEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(String code) {
        for (ChildSceneEnum enumItem : ChildSceneEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }

    public static List<CommonReturnChildrenDTO> getAllEnum(Integer code){
        List<CommonReturnChildrenDTO> childrenDTOS = new ArrayList<>();
        for (ChildSceneEnum enumItem : ChildSceneEnum.values()) {
            if(Integer.valueOf(enumItem.code.split("-")[0]).equals(code)){
                CommonReturnChildrenDTO dto = new CommonReturnChildrenDTO();
                dto.setKey(Integer.valueOf(enumItem.code.split("-")[1]));
                dto.setValue(enumItem.msg);
                childrenDTOS.add(dto);
            }
        }
        return childrenDTOS;
    }
}
