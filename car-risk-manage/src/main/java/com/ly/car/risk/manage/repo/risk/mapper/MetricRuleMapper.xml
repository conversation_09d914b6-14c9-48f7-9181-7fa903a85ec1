<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.MetricRuleMapper">

    <select id="getTotal" resultType="long">
        select count(*) from metric_rule
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.MetricRule">
        select * from metric_rule
        <include refid="getCondition"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.MetricRule">
        select *
        from metric_rule
    </select>


    <sql id="getCondition">
        where 1 = 1
        <if test="name != null and name != '' ">
            and `name` like CONCAT('%',#{name},'%')
        </if>
        <if test="ruleNo != null and ruleNo != '' ">
            and rule_no like CONCAT('%',#{ruleNo},'%')
        </if>
    </sql>
</mapper>