package com.ly.car.risk.manage.controller.request;

import com.ly.car.risk.manage.bean.Pageable;
import java.util.List;
import lombok.Data;

@Data
public class MetricStrategyListReq extends Pageable {
    
    /**
     * 策略编号
     */
    private String strategyNo;
    
    /**
     * 策略名称
     */
    private String name;
    
    /**
     * 策略分类 0-安全 1-风控
     */
    private Integer strategyType;
    private Integer level;
    private String riskType;
    private String cityId;

    /**
     * 策略运行状态
     */
    private Integer status;

    private List<String> supplierCodes;
}
