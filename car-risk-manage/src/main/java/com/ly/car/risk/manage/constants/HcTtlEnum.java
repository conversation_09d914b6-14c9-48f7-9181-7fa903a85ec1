package com.ly.car.risk.manage.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum HcTtlEnum {
    
    ONE_DAY(1, "1天"),
    SEVEN_DAYS(2, "7天"),
    ONE_MONTH(3, "一个月"),
    THREE_MONTHS(4, "三个月"),
    SIX_MONTHS(5, "六个月"),
    ONE_YEAR(6, "一年"),
    PERMANENT(7, "永久");
    
    private final Integer code;
    private final String  name;
    
    public static String getMsgByCode(Integer code) {
        for (HcTtlEnum enumItem : HcTtlEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getName();
            }
        }
        return StringUtils.EMPTY;
    }
    
    public static Integer getCodeByMsg(String msg) {
        for (HcTtlEnum enumItem : HcTtlEnum.values()) {
            if (enumItem.getName().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
}
