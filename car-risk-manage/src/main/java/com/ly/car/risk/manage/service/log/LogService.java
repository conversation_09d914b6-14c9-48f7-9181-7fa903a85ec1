package com.ly.car.risk.manage.service.log;

import com.ly.car.risk.manage.bean.ConfigLogVO;
import com.ly.car.risk.manage.bean.ResultMsg;
import com.ly.car.risk.manage.constants.LogTargetEnum;
import com.ly.car.risk.manage.constants.OperationEnum;

/**
 * 日志服务
 *
 * <AUTHOR>
 * @version Id: LogService, v 0.1 2018/10/26 下午4:06 lingfenglee Exp $
 */
public interface LogService {

    /**
     * 记录配置日志
     * @param vo
     */
    void logConfig(ConfigLogVO vo);

    /**
     * Log.
     *
     * @param operation     the operation
     * @param logTargetEnum the log target enum
     * @param code          the code
     * @param log           the log
     * @param operator      the operator
     */
    void log(OperationEnum operation, LogTargetEnum logTargetEnum, String code, String log, String operator);


    void log(boolean isSuccess, OperationEnum operation, LogTargetEnum logTargetEnum, String code, String log, String operator);

    /**
     * 查询操作记录
     * @param target
     * @param targetId
     * @return
     * @throws Exception
     */
    ResultMsg list(String target, String targetId) throws Exception;

}
