package com.ly.car.risk.manage.service;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DistributionRiskDto {
    private Long id;

    /**
     * 订单id
     */
    private String orderId;
    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 命中规则
     */
    private String ruleContent;
    /**
     * 分销员名称
     */
    private String distributeName;
    /**
     * 0-非作弊 1-作弊
     */
    private Integer isCheating;
    /**
     * 命中规则编号集合
     */
    private String ruleNoList;




    /**
     * 设备id
     * */
    private String deviceId;

    /**
     * 订单司机车牌号
     * */
    private String driverCardNo;
    /**
     * 是否是新客订单，或者是新客
     * */
    private Integer isNewOrder;

    /**
     * 返佣金额
     * */
    private BigDecimal commissionAmount;



//orderInfo
    private Date decisionTime;
    private Date finishTime;
    private Integer status;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;


    //orderAddress
    private String startAddress;
    private String endAddress;
    private String endCityName;
    private BigDecimal actualKilo;



    private String statusStr;
    private String  isNewOrderStr;
    private String phone;

    private String hitValue;

}

