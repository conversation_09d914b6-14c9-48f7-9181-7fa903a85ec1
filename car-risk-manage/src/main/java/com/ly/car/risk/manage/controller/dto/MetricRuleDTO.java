package com.ly.car.risk.manage.controller.dto;

import java.util.List;
import lombok.Data;

@Data
public class MetricRuleDTO {
    /**
     * 主键id
     */
    private Long id;
    
    /**
     * 规则名称
     */
    private String name;
    
    /**
     * 规则编号
     */
    private String ruleNo;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 规则脚本
     */
    private String script;
    
    /**
     * 规则数据
     */
    private List<MetricRuleConfigDTO> ruleJson;
    
    /**
     * 规则表达式
     */
    private String expression;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 修改时间
     */
    private String updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 操作人
     */
    private String updateUser;
    
    private Integer linkStrategyCount;
    
    private Integer linkSceneCount;
    
}
