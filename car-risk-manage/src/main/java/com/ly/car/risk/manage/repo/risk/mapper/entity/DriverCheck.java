package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import lombok.Data;

@Data
public class DriverCheck extends Model<DriverCheck> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String cardName;

    private String mobile;

    private String driverCardNo;

    private Integer type;

    private String bankCardNo;

    private Integer result;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String idCard;

    private String ownerName;
}
