package com.ly.car.risk.manage.controller;

import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.constants.*;
import com.ly.car.risk.manage.controller.dto.CommonReturnDTO;
import com.ly.car.risk.manage.controller.dto.SensitiveWordsDTO;
import com.ly.car.risk.manage.controller.params.SensitiveWordsParams;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.service.SensitiveWordsService;
import com.sun.org.apache.xpath.internal.operations.Bool;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

@RestController
@RequestMapping("sensitiveWords")
public class SensitiveWordsController {

    @Resource
    private SensitiveWordsService sensitiveWordsService;

    @RequestMapping("getList")
    public Pagination<SensitiveWordsDTO> getList(@RequestBody SensitiveWordsParams params){
        return sensitiveWordsService.getList(params);
    }

    @RequestMapping("delete")
    public Boolean update(@RequestBody SensitiveWordsParams params){
        this.sensitiveWordsService.update(params);
        return true;
    }

    @RequestMapping("export")
    public String export(@RequestBody SensitiveWordsParams params){
        return this.sensitiveWordsService.export(params);
    }

    @RequestMapping("add")
    public String add(@RequestBody SensitiveWordsParams params,HttpServletRequest request){
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        return this.sensitiveWordsService.add(params,userInfo);
    }

    @RequestMapping("detail")
    public List<SensitiveWordsDTO> detail(@RequestBody SensitiveWordsParams params){
        return this.sensitiveWordsService.detail(params.getId());
    }


   /**敏感词类型查询*/
   @RequestMapping("getWordType")
   public List<CommonReturnDTO> getWordType(){
       return SensitiveWordTypeEnum.getAllEnum();
   }


   /**风险等级*/
   @RequestMapping("getRiskType")
   public List<CommonReturnDTO> getRiskType(){
        return SensitiveWordsLevelEnum.getAllEnum();
   }

   /**处置动作*/
   @RequestMapping("getDisposal")
   public List<CommonReturnDTO> getDisposal(){
        return SensitiveWordsDisposalEnum.getAllEnum();
   }

   /**查询关联场景*/
   @RequestMapping("getLinkScene")
   public List<CommonReturnDTO> getLinkScene(){
       return SensitiveWordsLinkSceneEnum.getAllEnum();
   }

    /**查询信息分类*/
    @RequestMapping("getCategory")
    public List<CommonReturnDTO> getCategory(){
        return SensitiveWordsCategoryEnum.getAllEnum();
    }

    /**
     * 查询状态
     * */
    @RequestMapping("getStatus")
    public List<CommonReturnDTO> getStatus(){
        return SensitiveWordsEffectiveEnum.getAllEnum();
    }



}
