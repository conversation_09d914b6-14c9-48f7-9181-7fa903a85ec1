package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricRule;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricRuleFieldRelation;
import java.util.List;

public interface MetricRuleFieldRelationMapper extends BaseMapper<MetricRuleFieldRelation> {
    
    long getTotal(Object query);
    
    List<MetricRuleFieldRelation> queryList(Object query);
    
    List<MetricRuleFieldRelation> queryAllList();
}
