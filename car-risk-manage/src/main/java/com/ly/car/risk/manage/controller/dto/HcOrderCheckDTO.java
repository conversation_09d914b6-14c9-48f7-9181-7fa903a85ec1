package com.ly.car.risk.manage.controller.dto;

import lombok.Data;

import java.util.Date;

@Data
public class HcOrderCheckDTO {

    private Long id;
    private String orderId;
    private String tcOrderId;
    private String refId;
    private String refName;
    private Integer productId;
    private String productName;
    private Integer cityId;
    private String cityName;
    private String distributorOrderId;
    private Integer isRisk;
    private String isRiskName;
    private String riskRule;
    private String riskType;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private Integer status;
    private String statusName;//0-待审核 1-已结束
    private String orderStatusName;//需要转换 订单状态 //实时获取
    private String payStatus;//需要转换 订单状态 实时获取
}
