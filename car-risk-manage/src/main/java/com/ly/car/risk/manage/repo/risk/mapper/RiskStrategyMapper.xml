<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.RiskStrategyMapper">

    <select id="total" resultType="long">
        select count(*) from risk_strategy
        <include refid="getCondition"/>
    </select>
    
    <sql id="getCondition">
        where 1=1
        <if test="strategyNo != null and strategyNo != ''">
            and strategy_no = #{strategyNo}
        </if>
        <if test="strategyName != null and strategyName != ''">
            and strategy_name = #{strategyName}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
<!--        <if test="strategyNo != null and strategyNo != ''">-->
<!--            and strategy_no = #{strategy_no}-->
<!--        </if>-->
    </sql>

    <select id="getList" resultType="com.ly.car.risk.manage.repo.risk.mapper.entity.RiskStrategy">
        select * from risk_strategy
        <include refid="getCondition"/>
        order by update_time desc
        limit #{offset},#{size}
    </select>

</mapper>