package com.ly.car.risk.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.exception.CodeException;
import com.ly.car.risk.common.enums.MetricStrategyChannelEnum;
import com.ly.car.risk.common.enums.MetricStrategyProductLineEnum;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.MetricSceneDTO;
import com.ly.car.risk.manage.controller.request.MetricSceneAddReq;
import com.ly.car.risk.manage.controller.request.MetricSceneDeleteReq;
import com.ly.car.risk.manage.controller.request.MetricSceneGetReq;
import com.ly.car.risk.manage.controller.request.MetricSceneListReq;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.MetricSceneMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricSceneStrategyRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricStrategyMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricScene;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricSceneStrategyRelation;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricStrategy;
import com.ly.dal.util.DateUtil;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MetricSceneService {
    
    @Resource
    private MetricSceneMapper                 sceneMapper;
    @Resource
    private MetricSceneStrategyRelationMapper metricSceneStrategyRelationMapper;
    @Resource
    private MetricStrategyMapper              metricStrategyMapper;
    
    public void add(MetricSceneAddReq req, UserInfo userInfo) {
        // 值处理
        req.setParentId(null == req.getParentId() ? 0 : req.getParentId());
        
        // 校验
        addValid(req);
        
        MetricScene scene = new MetricScene();
        scene.setName(req.getName());
        scene.setSceneNo(req.getSceneNo());
        scene.setParentId(req.getParentId());
        scene.setName(req.getName());
        scene.setDescription(StringUtils.defaultString(req.getDescription()));
        scene.setCreateTime(new Date());
        scene.setUpdateTime(new Date());
        scene.setCreateUser(userInfo.getUsername());
        scene.setUpdateUser(userInfo.getUsername());
        scene.setDescription(req.getDescription());
        sceneMapper.insert(scene);
    }
    
    private void addValid(MetricSceneAddReq req) {
        Long count = sceneMapper.selectCount(new QueryWrapper<MetricScene>().eq("parent_id", req.getParentId()).eq("scene_no", req.getSceneNo()));
        if (count > 0) {
            throw new CodeException(500, "场景编号重复");
        }
    }
    
    public Pagination getList(MetricSceneListReq req) {
        long total = sceneMapper.getTotal(req);
        List<MetricScene> scenePage = sceneMapper.queryList(req);
        List<MetricSceneDTO> dtoList = scenePage.parallelStream().map(this::parse).collect(Collectors.toList());
        return new Pagination<>(dtoList, total, req);
    }
    
    public List<MetricScene> getParentScene() {
        return sceneMapper.selectList(new QueryWrapper<MetricScene>().eq("parent_id", 0));
    }
    
    public List<MetricScene> getChildScene() {
        return sceneMapper.selectList(new QueryWrapper<MetricScene>().ne("parent_id", 0));
    }
    
    public String delete(MetricSceneDeleteReq req) {
        sceneMapper.deleteById(req.getId());
        metricSceneStrategyRelationMapper.delete(new QueryWrapper<MetricSceneStrategyRelation>().eq("scene_id", req.getId()));
        return "success";
    }
    
    public MetricSceneDTO get(MetricSceneGetReq req) {
        MetricScene scene = sceneMapper.selectById(req.getId());
        return parse(scene);
    }
    
    private MetricSceneDTO parse(MetricScene scene) {
        MetricSceneDTO dto = new MetricSceneDTO();
        dto.setId(scene.getId());
        dto.setName(scene.getName());
        dto.setDescription(scene.getDescription());
        dto.setUpdateTime(DateUtil.date2String(scene.getUpdateTime()));
        dto.setUpdateUser(scene.getUpdateUser());
        dto.setCreateTime(DateUtil.date2String(scene.getCreateTime()));
        dto.setCreateUser(scene.getCreateUser());
        
        // 为了页面显示主场景放在主的栏位里
        if (scene.getParentId() != 0) {
            MetricScene parentScene = sceneMapper.selectById(scene.getParentId());
            dto.setParentSceneNo(null != parentScene ? parentScene.getSceneNo() : "");
            dto.setParentSceneName(null != parentScene ? parentScene.getName() : "");
            dto.setSceneNo(scene.getSceneNo());
            dto.setSceneName(scene.getName());
        } else {
            dto.setSceneNo("");
            dto.setSceneName("");
            dto.setParentSceneNo(scene.getSceneNo());
            dto.setParentSceneName(scene.getName());
        }
        
        List<MetricSceneStrategyRelation> strategyLists = metricSceneStrategyRelationMapper.selectList(new QueryWrapper<MetricSceneStrategyRelation>().eq("scene_id", dto.getId()).select("strategy_id"));
        List<Long> strategyIds = strategyLists.parallelStream().map(MetricSceneStrategyRelation::getStrategyId).collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(strategyIds)) {
            dto.setLinkStrategyCount(0);
        } else {
            
            dto.setLinkStrategyCount(strategyIds.size());
            
            List<MetricStrategy> strategyList = metricStrategyMapper.selectList(new QueryWrapper<MetricStrategy>().in("id", strategyIds));
            if (CollectionUtils.isNotEmpty(strategyList)) {
                dto.setStrategyNameStr(strategyList.parallelStream().map(MetricStrategy::getName).distinct().collect(Collectors.joining(",")));
                dto.setChannelStr(strategyList.parallelStream().map(s -> {
                    String[] split = s.getChannels().split(",");
                    return Arrays.stream(split).map(MetricStrategyChannelEnum::getDescByCode).collect(Collectors.joining(","));
                }).distinct().collect(Collectors.joining(",")));
                dto.setProductLineStr(strategyList.parallelStream().map(s -> {
                    String[] split = s.getProductLines().split(",");
                    return Arrays.stream(split).map(MetricStrategyProductLineEnum::getDescByCode).collect(Collectors.joining(","));
                }).distinct().collect(Collectors.joining(",")));
            }
            
        }
        return dto;
    }
}
