package com.ly.car.risk.manage.constants;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public enum ProductTypeEnum {
    SPECIAL_CAR(11, "预约专车"),
    PICKUP_AIRPORT(12, "接机"),
    DROPOFF_AIRPORT(13, "送机"),
    PICKUP_STATION(14, "接站"),
    DROPOFF_STATION(15, "送站"),
    ACTUAL_TIME_SPECIAL_CAR(19, "即时专车"),
    ACTUAL_TIME_TAXI(24, "即时出租车"),
    SFC(80, "顺风车"),
    WAVE_CAR(130, "线下叫车"),
    RESERVE_TAXI(65, "预约出租车");

    private Integer code;
    private String msg;

    ProductTypeEnum(Integer code,String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (ProductTypeEnum enumItem : ProductTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
