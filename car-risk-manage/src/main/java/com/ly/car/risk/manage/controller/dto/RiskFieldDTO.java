package com.ly.car.risk.manage.controller.dto;

import lombok.Data;

import java.util.List;

@Data
public class RiskFieldDTO {

    private Long id;
    private String fieldNo;
    private String fieldName;
    private String fieldType;
    private Long linkRule;
    private String updateTime;
    private String updateUser;
    private Integer isParallel;
    private String description;
    private String linkCharacteristic;
    private List<Rules> rules;
    private Integer category;
    private String categoryName;
    private Integer encryptFlag;
    private Integer realTime;
    private Integer effectiveDate;

    @Data
    public static class Rules{
        private String original;//0-内部 1-外部
        private String script;
    }
}
