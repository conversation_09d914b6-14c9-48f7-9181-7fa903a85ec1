package com.ly.car.risk.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.manage.repo.risk.mapper.CategoryRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.CategoryRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CategoryRelationService {

    @Resource
    private CategoryRelationMapper categoryRelationMapper;

    public List<CategoryRelation> queryByStrategyNo(String strategyNo){
        List<CategoryRelation> relationList = this.categoryRelationMapper.selectList(new QueryWrapper<CategoryRelation>()
                .eq("strategy_no", strategyNo)
        );
        return relationList;
    }

    public List<CategoryRelation> queryByStrategyNos(List<String> strategyNos,Integer type){
        if(CollectionUtils.isEmpty(strategyNos)){
            return new ArrayList<>();
        }
        List<CategoryRelation> relationList = this.categoryRelationMapper.selectList(new QueryWrapper<CategoryRelation>()
                .in("strategy_no", strategyNos)
                .eq("type",1)
        );
        return relationList;
    }

    public List<CategoryRelation> queryByRuleSceneNo(String ruleSceneNo,Integer type){
        List<CategoryRelation> relationList = this.categoryRelationMapper.selectList(new QueryWrapper<CategoryRelation>()
                .eq("rule_scene_no",ruleSceneNo)
                .eq("type",type)
        );
        return relationList;
    }

    public List<CategoryRelation> queryByRuleSceneNo(String ruleSceneNo){
        List<CategoryRelation> relationList = this.categoryRelationMapper.selectList(new QueryWrapper<CategoryRelation>()
                .eq("rule_scene_no",ruleSceneNo)
        );
        return relationList;
    }
}
