package com.ly.car.risk.manage.controller;

import com.alibaba.excel.EasyExcel;
import com.ly.car.risk.common.enums.SensitiveActionEnum;
import com.ly.car.risk.common.enums.SensitiveKeyEnum;
import com.ly.car.risk.common.enums.SensitiveModuleEnum;
import com.ly.car.risk.manage.bean.BillDateConverter;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.params.DistributionInfoRiskParams;
import com.ly.car.risk.manage.controller.request.RiskSensitiveActionAddReq;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.service.DistributionRiskDto;
import com.ly.car.risk.manage.service.DistributionRiskService;
import com.ly.car.risk.manage.service.RiskSensitiveActionService;
import com.ly.car.risk.manage.service.dto.DistributionRiskManageDto;
import com.ly.car.risk.manage.utils.FileUploadCephUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

@RestController
@RequestMapping("/riskControl")
public class DistributionRiskController {


    @Resource
    private DistributionRiskService    distributionRiskService;
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;

    @PostMapping("/getList")
    public Pagination<DistributionRiskManageDto> getList(@RequestBody DistributionInfoRiskParams query) {
        return distributionRiskService.getListPage(query);
    }

    @PostMapping("/export")
    public String exportList( @RequestBody DistributionInfoRiskParams query) {
        List<DistributionRiskManageDto> supplierSettlementExportRsps = distributionRiskService.exportList(query);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String fileName = "风控管理列表_" + System.currentTimeMillis()+".xlsx";
        EasyExcel.write(byteArrayOutputStream, DistributionRiskManageDto.class).registerConverter(new BillDateConverter()).sheet("sheet1")
                .doWrite(supplierSettlementExportRsps);
        String upload = FileUploadCephUtil.upload(fileName, byteArrayOutputStream);
        addSensitiveAction(upload);
        return upload;
    }

    @PostMapping("/detail")
    public List<DistributionRiskDto> detail(@RequestBody DistributionInfoRiskParams req) {
        List<DistributionRiskDto> rsp = distributionRiskService.detail(req);
        return rsp;
    }


    @PostMapping("/update")
    public Boolean updateCheating(@RequestBody DistributionInfoRiskParams req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        Boolean rsp = distributionRiskService.updateCheating(req,userInfo);
        return rsp;
    }
    
    private void addSensitiveAction(String upload) {
        RiskSensitiveActionAddReq record = new RiskSensitiveActionAddReq();
        record.setModule(SensitiveModuleEnum.risk_control_list.name());
        record.setAction(SensitiveActionEnum.export.name());
        record.setKey(SensitiveKeyEnum.file.name());
        record.setValue(upload);
        riskSensitiveActionService.add(record);
    }
}
