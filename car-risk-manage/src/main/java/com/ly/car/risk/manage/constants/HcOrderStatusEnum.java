package com.ly.car.risk.manage.constants;

import org.apache.commons.lang3.StringUtils;

public enum HcOrderStatusEnum {

    //0-已创单、10-已接单、50-乘客确认同行、100-司机出发接乘客、150-司机到达乘客上车点、200-乘客确认上车、300-已结束、400-已结算、1000-已取消',
    CREATE_ORDER(0,"已创单"),
    ACCEPT_ORDER(10,"已接单"),
    USER_CONFIRM(50,"乘客确认同行"),
    DRIVER_START(100,"司机出发接乘客"),
    DRIVER_ARRIVE(150,"司机到达乘客上车点"),
    USER_CONFIRM_BOARDING(200,"乘客确认上车"),
    ORDER_OVER(300,"已结束"),
    ORDER_BALANCE(400,"已结算"),
    ORDER_CANCEL(1000,"已取消"),

    ;
    public Integer code;
    public String msg;

    HcOrderStatusEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (HcOrderStatusEnum enumItem : HcOrderStatusEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
