package com.ly.car.risk.manage.controller;

import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.controller.dto.MetricStrategyDTO;
import com.ly.car.risk.manage.controller.dto.OfflineMetricStrategyDTO;
import com.ly.car.risk.manage.controller.request.*;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.service.OfflineMetricStrategyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.SESSION_USER_INFO;

@RestController
@RequestMapping("/offline/metricStrategy")
public class OfflineRiskStrategyController {

    /** Logger日志 */
    protected final Logger logger           = LoggerFactory.getLogger(getClass());

    @Resource
    private OfflineMetricStrategyService offlineMetricStrategyService;


    @RequestMapping("add")
    public String add(@RequestBody OfflineMetricStrategyAddReq req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        offlineMetricStrategyService.add(req, userInfo);
        return "success";
    }


    @RequestMapping("delete")
    public String delete(@RequestBody OfflineMetricStrategyDeleteReq req, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        offlineMetricStrategyService.delete(req, userInfo);
        return "success";
    }
    
    @RequestMapping("update")
    public String update(@RequestBody OfflineMetricStrategyUpdateReq req, HttpServletRequest request) throws BizException {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        offlineMetricStrategyService.update(req, userInfo);
        return "success";
    }
    
    @RequestMapping("getList")
    public Pagination getList(@RequestBody OfflineMetricStrategyQueryReq req) {
        return offlineMetricStrategyService.getList(req);
    }

    @RequestMapping("getRelationList")
    public List getRelationList(@RequestBody OfflineMetricStrategyGetReq req) {
        return offlineMetricStrategyService.getRelationList(req);
    }
    @RequestMapping("detail")
    public OfflineMetricStrategyDTO detail(@RequestBody OfflineMetricStrategyGetReq req) {
        return offlineMetricStrategyService.detail(req);
    }

}
