package com.ly.car.risk.manage.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.manage.bean.ConfigLogVO;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.constants.OperationEnum;
import com.ly.car.risk.manage.controller.dto.MetricRuleConfigDTO;
import com.ly.car.risk.manage.controller.dto.OfflineMetricFieldDTO;
import com.ly.car.risk.manage.controller.request.*;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.OfflineMetricFieldMapper;
import com.ly.car.risk.manage.repo.risk.mapper.OfflineMetricFieldRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.*;
import com.ly.car.risk.manage.service.log.LogService;
import com.ly.car.risk.manage.utils.OperatorLogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * OfflineMetricFieldService
 *
 * <AUTHOR>
 * @version Id : OfflineMetricFieldService, v 1.0  2025-3-3 10:41,ling.yang Exp $
 */
@Service
public class OfflineMetricFieldService {

    @Resource
    OfflineMetricFieldMapper offlineMetricFieldMapper;

    @Resource
    OfflineMetricFieldRelationMapper metricStrategyFieldRelationMapper;

    @Resource
    OfflineMetricFieldRelationMapper offlineMetricFieldRelationMapper;

    /**
     * 日志服务
     */
    @Resource
    private LogService logService;

    public void add(OfflineMetricFieldAddReq req, UserInfo userInfo) {
        OfflineMetricField field = new OfflineMetricField();
        field.setName(req.getName());
        field.setFieldNo(req.getFieldNo());

        field.setDescription(req.getDescription());
        field.setDescription(StringUtils.defaultString(req.getDescription()));
        field.setScript(req.getScript());//要解析规则 ？ todo
        field.setCategory(req.getCategory());
        field.setType(req.getType());
        field.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
        field.setCreateTime(new Date());
        field.setUpdateTime(new Date());
        field.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        offlineMetricFieldMapper.insert(field);

        log(field.getId().toString(), userInfo.getUsername() + userInfo.getWorkId(), OperationEnum.ADD, "新增离线指标配置");

    }
    public void delete(OfflineMetricFieldDeleteReq req, UserInfo userInfo) throws BizException {
        // 校验是否有绑定策略
        if( metricStrategyFieldRelationMapper.countByFieldId(req.getId())>0){
            throw new BizException(-1,"当前关联策略，不可删除");
        }
        offlineMetricFieldRelationMapper.delete(new QueryWrapper<OfflineMetricFieldRelation>().eq("field_id", req.getId()));

        offlineMetricFieldMapper.deleteByPrimaryKey(req.getId());
    }

    public void update(OfflineMetricFieldUpdateReq req, UserInfo userInfo) {
        OfflineMetricField oldField = offlineMetricFieldMapper.selectByPrimaryKey(req.getId());
        OfflineMetricField field = new OfflineMetricField();
        field.setName(req.getName());
        field.setFieldNo(req.getFieldNo());
        field.setDescription(StringUtils.defaultString(req.getDescription()));
        field.setScript(req.getScript());//要解析规则 ？ todo
        field.setCategory(req.getCategory());
        field.setType(req.getType());
        field.setUpdateTime(new Date());
        field.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        field.setId(req.getId());
        offlineMetricFieldMapper.updateByPrimaryKey(field);
        log(OperationEnum.MODIFY, field.getId().toString(), oldField, field, userInfo);

    }

    public Pagination getList(OfflineMetricFieldQueryReq req) {
        long total = offlineMetricFieldMapper.getTotal(req);
        List<OfflineMetricField> list = offlineMetricFieldMapper.queryList(req);
        List<OfflineMetricFieldDTO> dtoList = list.parallelStream().map(this::convertToDTO).collect(Collectors.toList());
        return new Pagination<>(dtoList, total, req);
    }

    public List<OfflineMetricFieldDTO> getAllList() {
        List<OfflineMetricField> list = offlineMetricFieldMapper.queryAllList();
        List<OfflineMetricFieldDTO> dtoList = list.parallelStream().map(this::convertToDTO).collect(Collectors.toList());
        return dtoList;
    }

    private OfflineMetricFieldDTO convertToDTO(OfflineMetricField offlineMetricField) {
        OfflineMetricFieldDTO dto = new OfflineMetricFieldDTO();
        dto.setId(offlineMetricField.getId());
        dto.setName(offlineMetricField.getName());
        dto.setFieldNo(offlineMetricField.getFieldNo());
        dto.setDescription(offlineMetricField.getDescription());
        dto.setScript(offlineMetricField.getScript());
        dto.setCategory(offlineMetricField.getCategory());
        dto.setType(offlineMetricField.getType());
        dto.setCreateUser(offlineMetricField.getCreateUser());
        dto.setCreateTime(offlineMetricField.getCreateTime());
        dto.setUpdateTime(offlineMetricField.getUpdateTime());
        dto.setUpdateUser(offlineMetricField.getUpdateUser());
        dto.setFieldRelationCount(metricStrategyFieldRelationMapper.countByFieldId(offlineMetricField.getId()));
        return dto;
    }

    public OfflineMetricFieldDTO detail(Long id) {
        OfflineMetricField rule = offlineMetricFieldMapper.selectByPrimaryKey(id);
        return convertToDTO(rule);
    }

    //有表达式，就直接用就好了，将1，2这些替换为data.xxx
    public String parseScript(String expression, List<MetricRuleConfigDTO> ruleJson) {
        List<String> operatorList = new ArrayList<>();
        operatorList.add("(");
        operatorList.add(")");
        operatorList.add("|");
        operatorList.add("&");
        Map<Integer, MetricRuleConfigDTO> ruleMap = ruleJson.parallelStream().collect(Collectors.toMap(MetricRuleConfigDTO::getSort, v -> v, (old, cur) -> old));
        expression = expression.replace("|", "||").replace("&", "&&");
        //对当前表达式进行数字分割
        String[] strings = expression.split("");
        Pattern pattern = Pattern.compile("[0-9]");
        String matchNum = "";
        List<String> pipeiList = new ArrayList<>();
        int i = 0;
        for (String str : strings) {
            i = i + 1;
            Matcher isNum = pattern.matcher(str);
            if (isNum.matches()) {
                //当前为数字就看看下面是不是数字，先记一下
                if (StringUtils.isBlank(matchNum)) {
                    matchNum = str;
                } else {
                    matchNum = matchNum + str;
                }
                if (i == strings.length) {
                    pipeiList.add(matchNum);
                }
            } else {
                if (StringUtils.isNotBlank(matchNum)) {
                    pipeiList.add(matchNum);
                    matchNum = "";
                }
                pipeiList.add(str);
            }
        }
        String expressionAfter = "";
        for (String str : pipeiList) {
            if (StringUtils.isNumeric(str)) {
                MetricRuleConfigDTO ruleConfig = ruleMap.get(Integer.valueOf(str));
                if (ruleConfig.getRightType() == 1) {
                    ruleConfig.setValue("data." + ruleConfig.getValue());
                }
                if (ruleConfig.getOperator().equals("=")) {
                    ruleConfig.setOperator("==");
                }
                expressionAfter = expressionAfter + "data.field" + ruleConfig.getLeftFieldId() + ruleConfig.getOperator() + ruleConfig.getValue();
            } else {
                expressionAfter = expressionAfter + str;
            }
        }
        String classStr = "class checkRule{ " +
                "   public boolean check(def data) { " +
                "if(" +
                expressionAfter +
                "){ " +
                " return true ; " +
                "} else { " +
                " return false ; " +
                " }" +
                "}" +
                "}";
        ;
        return classStr;
    }

    /**
     * 日志记录
     * @param operation 操作
     * @param code 操作数据唯一编号
     * @param old 旧配置
     * @param current 当前配置
     * @param operator 操作人
     */
    private void log(OperationEnum operation, String code, OfflineMetricField old, OfflineMetricField current, UserInfo operator) {
        log(code, operator.getUsername() + operator.getWorkId(), operation,
                OperatorLogUtils.getDiffLog(old, JSON.parseObject(JSON.toJSONString(current), OfflineMetricField.class))
        );
    }

    /**
     * 记录日志
     *
     * @param targetId
     * @param operatorName
     * @param operation
     * @param content
     */
    private void log(String targetId, String operatorName, OperationEnum operation, String content) {
        ConfigLogVO configLogVO = new ConfigLogVO();
        configLogVO.setTarget("offlineMetricField");
        configLogVO.setTargetId(targetId);
        configLogVO.setOperateType(operation.getDesc());
        configLogVO.setOperateContent(content);
        configLogVO.setOperatorName(operatorName);
        logService.logConfig(configLogVO);
    }

}