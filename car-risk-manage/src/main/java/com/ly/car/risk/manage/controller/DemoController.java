package com.ly.car.risk.manage.controller;

import com.ly.car.common.bean.model.UiResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("demo")
public class DemoController {

    @UiResult.Disabled
    @GetMapping("say")
    public String say() {
        return "say";
    }

    @GetMapping("test")
    public UiResult test() {
        UiResult uiResult = new UiResult();
        uiResult.setData("Hello world");
        return uiResult;
    }

    //需要登录
    @GetMapping("hello")
    public String hello() {
        return "hello";
    }
}
