package com.ly.car.risk.manage;

import com.ly.car.risk.manage.oauth.service.AuthorizationInterceptor;
import com.ly.car.risk.manage.oauth.service.UserInfoArgumentResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new UserInfoArgumentResolver());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getAuthorizationInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/oauth/logout",
                        "/oauth/getAccessToken",
                        "/demo/say",
                        "/demo/test",
                        "/common/*",
                        "/risk/order/*",
                        "/data/*",
                        "/riskCustomer/exportTemplate",
                        "/riskListManage/riskCustomer/clearInvalid",
                        "/dataPanel/query",
                        "/dashboard/*",
                        "/monitor.jsp");
    }


    @Bean
    public AuthorizationInterceptor getAuthorizationInterceptor() {
        return new AuthorizationInterceptor();
    }
}
