package com.ly.car.risk.manage.repo.riskmetrics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.manage.controller.request.DriverPunishOrderListReq;
import com.ly.car.risk.manage.repo.riskmetrics.entity.CarMtDriverPunishRecord;
import com.ly.car.risk.manage.service.dto.DriverPunishOrderDto;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public interface CarMtDriverPunishRecordMapper extends BaseMapper<CarMtDriverPunishRecord> {
    
    long getTotal(Object query);
    
    List<DriverPunishOrderDto> queryList(Object query);
    
    List<DriverPunishOrderDto> queryAllList(Object query);
}