package com.ly.car.risk.manage.model.resp.dashboard;

import lombok.Data;

/**
 * Description of SupplierRiskOrderData
 *
 * <AUTHOR>
 * @date 2024/12/12
 * @desc
 */
@Data
public class SupplierRiskOrderData {

    // 供应商
    private String supplierCode;
    // 推送数量
    private int publishCount;
    // 有责订单
    private int faultCount;
    // 无责订单
    private int nofaultCount;
    // 待反馈数量
    private int waitFeedbackCount;
    // 准确率
    private double accuracyRate;
}