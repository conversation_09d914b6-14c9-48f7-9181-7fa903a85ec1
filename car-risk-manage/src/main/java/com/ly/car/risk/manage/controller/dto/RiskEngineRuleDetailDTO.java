package com.ly.car.risk.manage.controller.dto;

import com.ly.car.risk.manage.controller.params.RuleParam;
import lombok.Data;

import java.util.List;

@Data
public class RiskEngineRuleDetailDTO {

    private String ruleName;
    private String ruleNo;
    private String ruleDesc;
    private String expression;
    private List<RuleParam> ruleJson;
    private Long id;
    private Integer isDeleted;
    private String createTime;
    private String createUser;
    private String updateTime;
    private String updateUser;
}
