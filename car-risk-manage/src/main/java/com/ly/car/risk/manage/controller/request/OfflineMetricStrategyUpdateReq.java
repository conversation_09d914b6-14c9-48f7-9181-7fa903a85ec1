package com.ly.car.risk.manage.controller.request;

import com.ly.car.risk.manage.repo.risk.mapper.entity.OfflineMetricFieldRelation;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 离线风控策略配置-策略
 * @TableName offline_metric_strategy
 */
@Data
public class OfflineMetricStrategyUpdateReq implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略编号
     */
    private String strategyNo;

    /**
     * 运行状态 0-测试 1-上线运行 2-下线
     */
    private Integer status;

    /**
     * 风险类型 1-刷单 2-金额异常 3-轨迹异常 4-套券 5-快速划单 6-费用异议 7-账户异常 
     */
    private Integer riskType;

    /**
     * 执行频率
     */
    private String  during;

    /**
     * 策略说明
     */
    private String description;

    /**
     * 业务线(多选)：YNC-网约车 SFC-顺风车 .e.g: YNC,SFC（英文逗号分隔）
     */
    private List<String> productLines;

    /**
     * 策略文案
     */
    private String strategyWord;

    /**
     * 管控对象 0-司机 1-用户
     */
    private Integer controlType;

    /**
     * 命中字段 .e.g: 手机号、IP
     */
    private String hitField;

    /**
     * 命中动作 0-全局黑名单 1-1对1黑名单
     */
    private Integer hitAction;

    /**
     * 管控时间 正数为管控天数， -1:永久 -2:当天有效(自然天)
     */
    private Integer controlTime;

    /**
     * 策略表达式
     */
    private String expression;

    /**
     * 处置动作 0-禁止 1-增强校验 2-通过
     */
    private Integer disposeAction;

    /**
     * 通知类型 1-企微 
     */
    private Integer noticeType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 操作人
     */
    private String updateUser;
    
    private List<String> channels;
    
    private List<String> supplierCodes;
    
    private Integer cityId;

    private List<OfflineMetricFieldRelation> relationList;

    private static final long serialVersionUID = 1L;
}