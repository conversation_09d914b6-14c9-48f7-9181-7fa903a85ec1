package com.ly.car.risk.manage.model.enums;

import lombok.Getter;

/**
 * Description of RiskDistributionTypeEnum
 *
 * <AUTHOR>
 * @date 2024/12/9
 * @desc
 */
@Getter
public enum RiskDistributionTypeEnum {

    ONLINE_RULE_MATCH("实时规则命中分布"),

    RISK_ALERT("预警规则命中分布"),

    OFFLINE_RULE_MATCH("离线规则命中分布"),

    ;

    private String desc;

    RiskDistributionTypeEnum(String desc) {
        this.desc = desc;
    }
}