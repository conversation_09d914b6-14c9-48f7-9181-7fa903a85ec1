package com.ly.car.risk.manage.bean.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("config.urls")
@Data
public class UrlsProperties {
    private String quickCarUrl;

    private String memberUrl;
    private String memberAppKey;
    private String memberAppSecret;
    private String trafficUrl;
    private String supermanUrl;
    private String flashUrl;
    private String strangeUrl;

    private String workerOrder;
    private String processUrl;

    private String currentSysUrl;
    private String mng2Domain;

    private String mngLogSaveUrl;
    private String mngLogListUrl;

    private String publicMqServer;
}
