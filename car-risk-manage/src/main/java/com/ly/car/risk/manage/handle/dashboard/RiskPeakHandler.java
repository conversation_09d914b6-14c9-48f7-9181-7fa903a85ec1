package com.ly.car.risk.manage.handle.dashboard;

import com.ly.car.risk.manage.model.enums.RiskPeakTypeEnum;
import com.ly.car.risk.manage.model.resp.dashboard.RiskPeakData;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;

/**
 * Description of RiskPeakHandler
 *
 * <AUTHOR>
 * @date 2024/12/10
 * @desc
 */
public interface RiskPeakHandler {

    RiskPeakTypeEnum support();

    RiskPeakData riskPeak(List<Pair<Date, Date>> linkRelativeRatioDates);
}