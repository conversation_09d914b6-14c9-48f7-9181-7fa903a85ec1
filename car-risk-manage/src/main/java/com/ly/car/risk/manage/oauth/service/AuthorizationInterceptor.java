package com.ly.car.risk.manage.oauth.service;

import com.ly.car.risk.manage.oauth.UnauthorizedException;
import com.ly.car.risk.manage.oauth.bean.MenuDTO;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Collection;
import java.util.List;

import static com.ly.car.risk.manage.oauth.Constants.*;

@Slf4j
public class AuthorizationInterceptor implements HandlerInterceptor {
    @Resource
    private IOauthService oauthService;
    @Resource
    private PermissionService permissionService;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {
        //1.accessToken 前端给的
        //2.取Session中sessionAccessToken
        //3.accessToken == sessionAccessToken && userInfo!=null -> 登录有效
        //4.否则，重新登录
        final HttpSession session = request.getSession();
        String sessionAccessToken = (String) session.getAttribute(ACCESS_TOKEN);
        String accessToken = getAccessToken(request);
        UserInfo userInfo = (UserInfo) session.getAttribute(SESSION_USER_INFO);
        log.info("[][][][]校验用户信息{}",JsonUtils.json(userInfo));

        // 登录有效
        if (StringUtils.equals(sessionAccessToken, accessToken) && userInfo != null && StringUtils.isNotBlank(accessToken)) {
            log.info("[login][] [][] 登录验证{}OK...", userInfo.getUsername());
            return true;
        }

        // 登录失效，清除登录信息
        userInfo = null;
        session.removeAttribute(ACCESS_TOKEN);
        session.removeAttribute(SESSION_USER_INFO);

        // 登录逻辑
        if (StringUtils.isNotEmpty(accessToken)) {
            // 存UserInfo和accessToken
            synchronized (session) {
                // 二次判断是否登录
                userInfo = (UserInfo) session.getAttribute(SESSION_USER_INFO);
                if (userInfo == null) {
                    userInfo = oauthService.getUserInfoByToken(accessToken);
                }
                //登录成功
                if (userInfo != null) {
                    // 获取菜单
                    List<MenuDTO> menus = permissionService.getMenu(userInfo.getUserId());
                    userInfo.setMenus(menus);
//                    if (CollectionUtils.isNotEmpty(menus)) {
                        request.setAttribute(ACCESS_TOKEN, accessToken);
                        session.setAttribute(SESSION_USER_INFO, userInfo);
                        return true;
//                    } else {
//                        log.error("[login][] [][] 登录失败：获取菜单失败");
//                    }
                } else {
                    log.error("[login][] [][] 登录失败：获取用户信息失败");
                }
            }
        }
        String applyForTokenUri = oauthService.applyForTokenFromSSO(request, response);
        log.info("[][][][]applyForTokenUri:{}",applyForTokenUri);
        throw new UnauthorizedException(applyForTokenUri);
    }

    private String getAccessToken(HttpServletRequest request) {
        String accessToken = null;
        if (request.getCookies() != null) {
            for (Cookie cookie : request.getCookies()) {
                if (ACCESS_TOKEN.equals(cookie.getName())) {
                    accessToken = cookie.getValue();
                    log.info("[getAccessToken][] [][]当前token:{},value:{}", cookie.getName(), accessToken);
                    break;
                }
            }
        }
        return accessToken;
    }

}
