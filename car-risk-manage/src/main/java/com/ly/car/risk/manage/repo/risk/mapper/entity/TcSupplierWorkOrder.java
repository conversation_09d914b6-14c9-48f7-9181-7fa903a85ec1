package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TcSupplierWorkOrder extends Model<TcSupplierWorkOrder> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 同程订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 操作类型 0-创建工单 1-回复工单 2-完结工单
     */
    private Integer operateType;

    /**
     * t3工单号
     */
    private String platformWorkOrderNo;

    /**
     * 合作方工单号
     */
    private String cpWorkOrderNo;

    /**
     * 0-无 1-刷单 2-金额异常 3-轨迹异常 4-套券 5-快速划单 6-费用异议 7-账户异常
     */
    private Integer woCateCode;

    /**
     * 描述
     */
    private String msg;

    /**
     * 时间发生或处理时间
     */
    private Date eventTime;

    /**
     * 司机车牌号
     */
    private String driverCardNo;

    /**
     * 用户反馈上传证据
     */
    private String attachments;

    /**
     * 处罚唯一标识
     */
    private String unionId;

    /**
     * 0-初始化 6-价格清零 7-退款 8-调价
     */
    private Integer judgeType;

    /**
     * 处罚原因
     */
    private String judgeReason;

    /**
     * 总调价/退款金额
     */
    private BigDecimal changeFee;

    /**
     * 1-司机有责 2-司机无责
     */
    private Integer judgeResult;

    /**
     * 0-初始化 1-执行处置 2-撤销处置
     */
    private Integer disposeActType;

    /**
     * 处置结果 1-成功 2-失败
     */
    private Integer disposeResult;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否同步供应商 0-未同步 1-已同步
     * */
    private Integer isSync;

    /**
     *  是否分销 0-非分销 1-分销
     * */
    private Integer isDistributor;

    /**
     * 工单状态 0-初始化 1-待供应商反馈 2-待审核 3-待上游反馈 4-已结束 5-无法确认
     * */
    private Integer workStatus;

    /**
     * 审核状态 0-无 1-审核通过 2-审核不通过
     * */
    private Integer auditStatus;

    /**
     * 审核备注
     * */
    private String auditRemark;

    private String yueEventId;
}
