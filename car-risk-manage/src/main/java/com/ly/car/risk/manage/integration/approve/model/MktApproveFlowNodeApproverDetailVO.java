package com.ly.car.risk.manage.integration.approve.model;

import lombok.Data;

/**
 * MktApproveFlowNodeApproverDetailVO
 *
 * <AUTHOR>
 * @version Id : MktApproveFlowNodeApproverDetailVO, v 1.0  2024/5/17 13:26,hansheng.zhang Exp $
 */
@Data
public class MktApproveFlowNodeApproverDetailVO {
    /**
     * 信息（审批节点：最后的审批信息，操作节点：操作原因）
     */
    private String message;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间
     */
    private String operateTime;
    /**
     * 审批状态（wait:未审批，pass:审批通过，reject审批驳回）
     */
    private String approveStatus;
}