package com.ly.car.risk.manage.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description supplier_work_order
 * <AUTHOR>
 * @date 2023-03-10
 */
@Data
public class SupplierWorkOrder extends Model<SupplierWorkOrder> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单
     */
    private String supplierOrderId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     *
     */
    private String violationId;

    /**
     * 司机车牌号
     */
    private String driverCardNo;

    /**
     * 0-初始化
     */
    private Integer disposeCode;

    /**
     * 风险类型
     */
    private Integer riskType;

    /**
     * 违规申诉截止时间戳
     */
    private Date appealEndTime;

    /**
     * 处罚唯一标识
     */
    private String disposeUnionId;

    /**
     * 0-初始化 1-罚款 5-禁封 6-价格清零 7-退款 8-调价
     */
    private Integer disposeType;

    /**
     * 原因
     */
    private String disposeReason;

    /**
     * 总扣减金额
     */
    private BigDecimal priceChangeFee;

    /**
     * 罚款金额(类型为罚款时必填)
     */
    private BigDecimal priceDisposeFare;

    /**
     * 禁封开始时间
     */
    private Date disposeStartTime;

    /**
     * 禁封结束时间
     */
    private Date disposeEndTime;

    /**
     * 0-初始化 1-识别有责 2-违规撤销
     */
    private Integer violationStatus;

    /**
     * 违规发生时间
     */
    private Date violationTime;

    /**
     * 0-未同步 1-已同步
     */
    private Integer isSync;

    /**
     * 申诉id
     */
    private String appealId;

    /**
     * 申诉说明
     */
    private String appealNote;

    /**
     * 申诉证据
     */
    private String appealEvidence;

    /**
     * 0-初始化 1-通过 2-驳回
     */
    private Integer appealResult;

    /**
     * 申诉结果描述
     */
    private String appealResultDesc;

    /**
     * 违规信息同步时间
     */
    private Date sysSyncTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     * */
    private Date updateTime;

    private Integer operateType;

    /**
     * 供应商工单号
     * */
    private String platformWorkOrderNo;

    /**
     * 我们工单号
     * */
    private String cpWorkOrderNo;

    private String msg;



}