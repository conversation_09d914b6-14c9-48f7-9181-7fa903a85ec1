package com.ly.car.risk.manage.handle.dashboard.riskdistribution;

import cn.hutool.core.collection.CollUtil;
import com.ly.car.risk.manage.handle.dashboard.RiskDistributionHandler;
import com.ly.car.risk.manage.model.enums.RiskDistributionTypeEnum;
import com.ly.car.risk.manage.model.resp.dashboard.DistributionItem;
import com.ly.car.risk.manage.model.resp.dashboard.RiskDistributionData;
import com.ly.car.risk.manage.repo.risk.mapper.RiskAlertApproveMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description of RiskDistributionOnlineRuleHandler
 *
 * <AUTHOR>
 * @date 2024/12/10
 * @desc
 */
@Service
public class RiskDistributionAlertHandler implements RiskDistributionHandler {

    @Resource
    private RiskAlertApproveMapper riskAlertApproveMapper;

    @Override
    public RiskDistributionTypeEnum support() {
        return RiskDistributionTypeEnum.RISK_ALERT;
    }

    @Override
    public RiskDistributionData riskDistribution(List<Pair<Date, Date>> linkRelativeRatioDates) {
        RiskDistributionData distributionData = new RiskDistributionData();
        Pair<Date, Date> currentPeriod = linkRelativeRatioDates.get(0);
        Pair<Date, Date> lastPeriod = linkRelativeRatioDates.get(1);
        List<DistributionItem> topRules = riskAlertApproveMapper.findTopStrategy(currentPeriod.getKey(), currentPeriod.getValue(), 10);
        List<String> rules = topRules.stream().map(DistributionItem::getRule).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(rules)){
            List<DistributionItem> rulePeriodData = riskAlertApproveMapper.findStrategyPeriodData(lastPeriod.getKey(), lastPeriod.getValue(), rules);
            fillRulePeriodData(topRules,rulePeriodData);
        }
        distributionData.setCode(support().name());
        distributionData.setName(support().getDesc());
        distributionData.setPeriodData(topRules);
        return distributionData;
    }

    private void fillRulePeriodData(List<DistributionItem> topRules, List<DistributionItem> rulePeriodData) {
        Map<String, DistributionItem> periodMap = rulePeriodData.stream().collect(Collectors.toMap(p -> p.getRule(), v -> v, (k1, k2) -> k1));
        for(DistributionItem item : topRules){
            DistributionItem distributionItem = periodMap.get(item.getRule());
            if(null !=distributionItem){
                item.setLastPercent(distributionItem.getLastPercent());
            }
        }
    }
}