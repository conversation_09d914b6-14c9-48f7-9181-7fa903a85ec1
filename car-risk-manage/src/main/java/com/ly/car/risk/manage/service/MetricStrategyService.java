package com.ly.car.risk.manage.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.ly.car.risk.common.enums.ApproveSceneKeyEnum;
import com.ly.car.risk.common.enums.ApproveStatusEnum;
import com.ly.car.risk.common.enums.StrategyStatusEnum;
import com.ly.car.risk.manage.bean.ConfigLogVO;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.bean.exception.BizException;
import com.ly.car.risk.manage.bean.properties.UrlsProperties;
import com.ly.car.risk.manage.constants.LogTargetEnum;
import com.ly.car.risk.manage.constants.OperationEnum;
import com.ly.car.risk.manage.controller.dto.MetricStrategyDTO;
import com.ly.car.risk.manage.controller.dto.MetriclinkObject;
import com.ly.car.risk.manage.controller.request.MetricStrategyAddReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyDeleteReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyGetReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyListReq;
import com.ly.car.risk.manage.controller.request.MetricStrategyUpdateReq;
import com.ly.car.risk.manage.integration.approve.model.*;
import com.ly.car.risk.manage.integration.approve.service.CommonApproveService;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.MetricRuleMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricSceneMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricSceneStrategyRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricStrategyMapper;
import com.ly.car.risk.manage.repo.risk.mapper.MetricStrategyRuleRelationMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricRule;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricScene;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricSceneStrategyRelation;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricStrategy;
import com.ly.car.risk.manage.repo.risk.mapper.entity.MetricStrategyRuleRelation;
import com.ly.car.risk.manage.service.log.LogService;
import com.ly.car.risk.manage.utils.OperatorLogUtils;
import com.ly.dal.util.DateUtil;

import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.ly.sof.utils.log.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MetricStrategyService {

    /** Logger日志 */
    protected final Logger logger           = LoggerFactory.getLogger(getClass());

    @Resource
    private MetricStrategyMapper              metricStrategyMapper;
    @Resource
    private MetricStrategyRuleRelationMapper  metricStrategyRuleRelationMapper;
    @Resource
    private MetricSceneStrategyRelationMapper metricSceneStrategyRelationMapper;
    @Resource
    private MetricSceneMapper                 metricSceneMapper;
    @Resource
    private MetricRuleMapper                  metricRuleMapper;

    @Resource
    private UrlsProperties urlsProperties;

    /**
     * 日志服务
     */
    @Resource
    private LogService logService;

    @Resource
    private CommonApproveService commonApproveService;

    public void add(MetricStrategyAddReq req, UserInfo userInfo) {
        MetricStrategy strategy = new MetricStrategy();
        strategy.setName(req.getName());
        strategy.setStrategyNo(req.getStrategyNo());
        strategy.setChannels(String.join(",", req.getChannels()));
        strategy.setStrategyType(req.getStrategyType());
        strategy.setLevel(req.getLevel());
        strategy.setRiskType(req.getRiskType());
        strategy.setCityId(req.getCityId());
        strategy.setExpression(StringUtils.defaultString(req.getExpression()));
        if (StringUtils.isNotBlank(req.getExpression())) {
            strategy.setScript(parseScriptNew(req.getExpression()));
        }
        strategy.setDescription(StringUtils.defaultString(req.getDescription()));
        strategy.setProductLines(String.join(",", req.getProductLines()));
        strategy.setStrategyWord(StringUtils.defaultString(req.getStrategyWord()));
        strategy.setControlTime(req.getControlTime());
        strategy.setControlType(req.getControlType());
        strategy.setHitField(req.getHitField());
        strategy.setHitAction(req.getHitAction());
        strategy.setDisposeAction(req.getDisposeAction());
        strategy.setRealTime(1);
        strategy.setStatus(req.getStatus());
        strategy.setNoticeType(req.getNoticeType());
        strategy.setSupplierCodes(CollectionUtils.isEmpty(req.getSupplierCodes()) ? "" : String.join(",", req.getSupplierCodes()));
        
        strategy.setCreateTime(new Date());
        strategy.setUpdateTime(new Date());
        strategy.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
        strategy.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        StrategyStatusEnum strategyStatusEnum = StrategyStatusEnum.getEnumByCode(req.getStatus());
       switch (strategyStatusEnum){
           case WAITING_ONLINE:
               strategy.setApproveStatus(ApproveStatusEnum.WAITING_SEND.getCode());
               break;
           case ONLINE:
           case OFFLINE:
           case TEST:
               strategy.setApproveStatus(ApproveStatusEnum.NO_APPROVAL.getCode());
               break;
       }
        metricStrategyMapper.insert(strategy);
        
        if (CollectionUtils.isNotEmpty(req.getLinkSceneIds())) {
            for (Long sceneId : req.getLinkSceneIds()) {
                MetricSceneStrategyRelation relation = new MetricSceneStrategyRelation();
                relation.setSceneId(sceneId);
                relation.setStrategyId(strategy.getId());
                relation.setCreateTime(new Date());
                metricSceneStrategyRelationMapper.insert(relation);
            }
        }
        if (CollectionUtils.isNotEmpty(req.getLinkRuleList())) {
            
            Set<Long> duplicateMap = new HashSet<>();
            
            for (MetriclinkObject obj : req.getLinkRuleList()) {
                if (duplicateMap.contains(obj.getId())) {
                    continue;
                } else {
                    duplicateMap.add(obj.getId());
                }
                
                MetricStrategyRuleRelation relation = new MetricStrategyRuleRelation();
                relation.setRuleId(obj.getId());
                relation.setStrategyId(strategy.getId());
                relation.setCreateTime(new Date());
                metricStrategyRuleRelationMapper.insert(relation);
            }
        }

        log(strategy.getStrategyNo(), userInfo.getUsername() + userInfo.getWorkId(), OperationEnum.ADD, "新增配置");
    }

//    @Value("${config.urls.currentSysUrl}")
//    private String rootUrl;
    /**
     * 送审
     * @param strategyNo
     */
    public String sendApprove(String strategyNo,UserInfo userInfo) throws Exception {

//        return "1111";
        // todo
        MetricStrategy metricStrategy =  metricStrategyMapper.queryByStrategyNo(strategyNo);
        if (metricStrategy == null) {
            throw new BizException(-1, "策略不存在");
        }

        LoggerUtils.info(logger, "送审策略编号code：{}", metricStrategy.getStrategyNo());

        if (metricStrategy.getApproveStatus() != ApproveStatusEnum.WAITING_SEND.getCode()) {
            throw new BizException(-1,"非待送审状态，当前状态: " + metricStrategy.getApproveStatus());
        }
//
//        if (!StringUtils.equals(metricStrategy.getStatus(), "1")) {
//            throw new BizException(-1, "非有效活动");
//        }

        MktApproveApplyReqVO request = new MktApproveApplyReqVO();
        request.setSceneKey(ApproveSceneKeyEnum.CAR_RISK_METRIC_STRATEGY.getCode());
        request.setBusinessNo(metricStrategy.getStrategyNo());
        request.setBusinessName(metricStrategy.getName());
        request.setApplyUserNo(userInfo.getWorkId());
        request.setApplyUserName(userInfo.getUsername());


        MktApproveRenderDataVO renderData = new MktApproveRenderDataVO();
        request.setRenderData(renderData);

        Map<String, Object> originData = new HashMap<>();
        request.setOriginData(originData);

//        Map<String, MktApproveRowDataItemVO> rowData = Maps.newHashMap();
//        buildRowDisplayData(rowData, "code", metricStrategy.getStrategyNo(), metricStrategy.getStrategyNo());
//        buildRowDisplayData(rowData, "name", metricStrategy.getName(), metricStrategy.getName());
//        buildRowDisplayData(rowData, "createBy", metricStrategy.getCreateUser(), metricStrategy.getCreateUser());
//        request.setRowData(rowData);

        String detailUrl = urlsProperties.getCurrentSysUrl() +
                "/#/metricConfig/metricStrategyManage/getList";
        request.setDetailUrl(detailUrl);

        MktApproveApplyResVO sendResult  = commonApproveService.send(request);
        String approveNo = sendResult.getApproveNo();
        if (StringUtils.isNotBlank(approveNo)) {
            String approveStatus = sendResult.getApproveStatus();
            if (StringUtils.equals(approveStatus, "pass")) {
                // 直接审批通过
                metricStrategyMapper.updateApproveAsApproved(metricStrategy.getStrategyNo(), approveNo);
                log(metricStrategy.getStrategyNo(), userInfo.getUsername()+userInfo.getWorkId(), OperationEnum.SEND_APPROVE, "审批通过，审批单号: " + approveNo);
            } else {
                // 变更成审批中
                metricStrategyMapper.updateApproveAsSend(metricStrategy.getStrategyNo(), approveNo);
                log(metricStrategy.getStrategyNo(),userInfo.getUsername()+userInfo.getWorkId(), OperationEnum.SEND_APPROVE, "送审成功，审批单号: " + approveNo);
            }
        }

        return approveNo;
    }

    /**
     * 日志记录
     * @param operation 操作
     * @param code 操作数据唯一编号
     * @param old 旧配置
     * @param current 当前配置
     * @param operator 操作人
     */
    private void log(OperationEnum operation, String code, MetricStrategy old, MetricStrategy current, UserInfo operator) {
        log(code, operator.getUsername() + operator.getWorkId(), operation,
                OperatorLogUtils.getDiffLog(old, JSON.parseObject(JSON.toJSONString(current), MetricStrategy.class))
        );
    }

    /**
     * 记录日志
     *
     * @param targetId
     * @param operatorName
     * @param operation
     * @param content
     */
    private void log(String targetId, String operatorName, OperationEnum operation, String content) {
        ConfigLogVO configLogVO = new ConfigLogVO();
        configLogVO.setTarget("carMetricStrategy");
        configLogVO.setTargetId(targetId);
        configLogVO.setOperateType(operation.getDesc());
        configLogVO.setOperateContent(content);
        configLogVO.setOperatorName(operatorName);
        logService.logConfig(configLogVO);
    }


    private void buildRowDisplayData(Map<String, MktApproveRowDataItemVO> rowData, String fieldName, Object value, String displayValue) {
        MktApproveRowDataItemVO displayData = new MktApproveRowDataItemVO();
        displayData.setOriginValue(value);
        displayData.setRenderValue(displayValue);
        rowData.put(fieldName, displayData);
    }


    public void delete(MetricStrategyDeleteReq req, UserInfo userInfo) {
        metricSceneStrategyRelationMapper.delete(new QueryWrapper<MetricSceneStrategyRelation>().eq("strategy_id", req.getId()));
        metricStrategyMapper.deleteById(req.getId());
        metricStrategyRuleRelationMapper.delete(new QueryWrapper<MetricStrategyRuleRelation>().eq("strategy_id", req.getId()));
    }
    
    public void update(MetricStrategyUpdateReq req, UserInfo userInfo) throws BizException {
        MetricStrategy oldStrategy = metricStrategyMapper.selectById(req.getId());

        //  校验当前配置是否已下线，仅已下线的配置支持修改
        if (oldStrategy.getStatus() != StrategyStatusEnum.OFFLINE.getCode()
                && req.getStatus() != StrategyStatusEnum.OFFLINE.getCode()) {
            throw new BizException(-1, "需调整为已下线时才可修改 ");
        }
        // 更新时只能更新为 【测试】、【待上线】状态
        if (req.getStatus() != StrategyStatusEnum.TEST.getCode()
                && req.getStatus() != StrategyStatusEnum.WAITING_ONLINE.getCode()
                && req.getStatus() != StrategyStatusEnum.OFFLINE.getCode()) {
            throw new BizException(-1, "更新时只能更新为 【测试】、【待上线】状态 ");
        }

        MetricStrategy strategy = new MetricStrategy();
        strategy.setId(req.getId());
        strategy.setName(req.getName());
        strategy.setStrategyNo(req.getStrategyNo());
        strategy.setChannels(String.join(",", req.getChannels()));
        strategy.setStrategyType(req.getStrategyType());
        strategy.setLevel(req.getLevel());
        strategy.setRiskType(req.getRiskType());
        strategy.setCityId(req.getCityId());
        strategy.setExpression(req.getExpression());
        if (StringUtils.isNotBlank(req.getExpression())) {
            strategy.setScript(parseScriptNew(req.getExpression()));
        }
        strategy.setDescription(StringUtils.defaultString(req.getDescription()));
        strategy.setProductLines(String.join(",", req.getProductLines()));
        strategy.setStrategyWord(StringUtils.defaultString(req.getStrategyWord()));
        strategy.setControlTime(req.getControlTime());
        strategy.setControlType(req.getControlType());
        strategy.setHitField(req.getHitField());
        strategy.setHitAction(req.getHitAction());
        strategy.setDisposeAction(req.getDisposeAction());
        strategy.setRealTime(1);
        strategy.setStatus(req.getStatus());
        strategy.setNoticeType(req.getNoticeType());
        strategy.setSupplierCodes(CollectionUtils.isEmpty(req.getSupplierCodes()) ? "" : String.join(",", req.getSupplierCodes()));
        
        strategy.setUpdateTime(new Date());
        strategy.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        strategy.setApproveStatus(ApproveStatusEnum.WAITING_SEND.getCode());

        StrategyStatusEnum strategyStatusEnum = StrategyStatusEnum.getEnumByCode(req.getStatus());
        switch (strategyStatusEnum){
            case WAITING_ONLINE:
                strategy.setApproveStatus(ApproveStatusEnum.WAITING_SEND.getCode());
                break;
            case ONLINE:
            case OFFLINE:
            case TEST:
                strategy.setApproveStatus(ApproveStatusEnum.NO_APPROVAL.getCode());
                break;
        }

        metricStrategyMapper.updateById(strategy);
        
        metricSceneStrategyRelationMapper.delete(new QueryWrapper<MetricSceneStrategyRelation>().eq("strategy_id", strategy.getId()));
        metricStrategyRuleRelationMapper.delete(new QueryWrapper<MetricStrategyRuleRelation>().eq("strategy_id", strategy.getId()));
        
        if (CollectionUtils.isNotEmpty(req.getLinkSceneIds())) {
            for (Long sceneId : req.getLinkSceneIds()) {
                MetricSceneStrategyRelation relation = new MetricSceneStrategyRelation();
                relation.setSceneId(sceneId);
                relation.setStrategyId(strategy.getId());
                relation.setCreateTime(new Date());
                metricSceneStrategyRelationMapper.insert(relation);
            }
        }
        if (CollectionUtils.isNotEmpty(req.getLinkRuleList())) {
            Set<Long> duplicateMap = new HashSet<>();
            
            for (MetriclinkObject obj : req.getLinkRuleList()) {
                if (duplicateMap.contains(obj.getId())) {
                    continue;
                } else {
                    duplicateMap.add(obj.getId());
                }
                MetricStrategyRuleRelation relation = new MetricStrategyRuleRelation();
                relation.setRuleId(obj.getId());
                relation.setStrategyId(strategy.getId());
                relation.setCreateTime(new Date());
                metricStrategyRuleRelationMapper.insert(relation);
            }
        }

        log(OperationEnum.MODIFY, strategy.getStrategyNo(), oldStrategy, strategy, userInfo);
    }
    
    public Pagination getList(MetricStrategyListReq req) {
        long total = metricStrategyMapper.getTotal(req);
        List<MetricStrategy> list = metricStrategyMapper.queryList(req);
        List<MetricStrategyDTO> dtoList = list.parallelStream().map(this::parse).collect(Collectors.toList());
        return new Pagination<>(dtoList, total, req);
    }
    
    private MetricStrategyDTO parse(MetricStrategy strategy) {
        List<MetricSceneStrategyRelation> withStrategyRelations = metricSceneStrategyRelationMapper.selectList(new QueryWrapper<MetricSceneStrategyRelation>().eq("strategy_id", strategy.getId()));
        List<MetricStrategyRuleRelation> withRuleRelations = metricStrategyRuleRelationMapper.selectList(new QueryWrapper<MetricStrategyRuleRelation>().eq("strategy_id", strategy.getId()));
        
        MetricStrategyDTO dto = new MetricStrategyDTO();
        dto.setId(strategy.getId());
        dto.setStatus(strategy.getStatus());
        dto.setStrategyNo(strategy.getStrategyNo());
        dto.setName(strategy.getName());
        dto.setApproveStatus(strategy.getApproveStatus());
        dto.setApproveNo(strategy.getApproveNo());
        dto.setLinkSceneCount(withStrategyRelations.size());
        dto.setLinkRuleCount(withRuleRelations.size());
        dto.setUpdateTime(DateUtil.date2String(strategy.getUpdateTime()));
        dto.setUpdateUser(strategy.getUpdateUser());
        dto.setDescription(StringUtils.defaultString(strategy.getDescription()));
        dto.setProductLines(Arrays.stream(strategy.getProductLines().split(",")).collect(Collectors.toList()));
        dto.setChannels(Arrays.stream(strategy.getChannels().split(",")).collect(Collectors.toList()));
        dto.setStrategyWord(StringUtils.defaultString(strategy.getStrategyWord()));
        dto.setControlTime(strategy.getControlTime());
        dto.setControlType(strategy.getControlType());
        dto.setDisposeAction(strategy.getDisposeAction());
        dto.setHitField(strategy.getHitField());
        dto.setHitAction(strategy.getHitAction());
        dto.setExpression(strategy.getExpression());
        dto.setScript(strategy.getScript());
        dto.setCityId(strategy.getCityId());
        dto.setStrategyType(strategy.getStrategyType());
        dto.setLevel(strategy.getLevel());
        dto.setRiskType(strategy.getRiskType());
        dto.setNoticeType(strategy.getNoticeType());
        dto.setSupplierCodes(Arrays.stream(strategy.getSupplierCodes().split(",")).collect(Collectors.toList()));
        
        List<MetriclinkObject> linkScenes = new ArrayList<>();
        List<MetriclinkObject> linkRules = new ArrayList<>();
        for (MetricSceneStrategyRelation relation : withStrategyRelations) {
            
            MetricScene scene = metricSceneMapper.selectById(relation.getSceneId());
            
            MetriclinkObject MetriclinkObject = new MetriclinkObject();
            MetriclinkObject.setId(scene.getId());
            MetriclinkObject.setName(scene.getName());
            MetriclinkObject.setNo(scene.getSceneNo());
            linkScenes.add(MetriclinkObject);
        }
        for (MetricStrategyRuleRelation relation : withRuleRelations) {
            
            MetricRule rule = metricRuleMapper.selectById(relation.getRuleId());
            
            MetriclinkObject MetriclinkObject = new MetriclinkObject();
            MetriclinkObject.setId(rule.getId());
            MetriclinkObject.setName(rule.getName());
            MetriclinkObject.setNo(rule.getRuleNo());
            linkRules.add(MetriclinkObject);
        }
        dto.setLinkSceneList(linkScenes);
        dto.setLinkSceneIds(linkScenes.parallelStream().map(MetriclinkObject::getId).collect(Collectors.toList()));
        dto.setLinkRuleList(linkRules);
        return dto;
    }
    
    public MetricStrategyDTO detail(MetricStrategyGetReq req) {
        MetricStrategy metricStrategy = metricStrategyMapper.selectById(req.getId());
        return parse(metricStrategy);
    }
    
    public List<MetriclinkObject> getLinkRule(MetricStrategyGetReq req) {
        List<MetriclinkObject> linkRules = new ArrayList<>();
        
        List<MetricStrategyRuleRelation> withRuleRelations = metricStrategyRuleRelationMapper.selectList(new QueryWrapper<MetricStrategyRuleRelation>().eq("strategy_id", req.getId()));
        
        for (MetricStrategyRuleRelation relation : withRuleRelations) {
            
            MetricRule rule = metricRuleMapper.selectById(relation.getRuleId());
            
            MetriclinkObject MetriclinkObject = new MetriclinkObject();
            MetriclinkObject.setId(rule.getId());
            MetriclinkObject.setName(rule.getName());
            MetriclinkObject.setNo(rule.getRuleNo());
            linkRules.add(MetriclinkObject);
        }
        return linkRules;
    }
    
    public String parseScriptNew(String expression) {
        //有表达式，就直接用就好了，将1，2这些替换为data.xxx
        List<String> operatorList = new ArrayList<>();
        operatorList.add("(");
        operatorList.add(")");
        operatorList.add("|");
        operatorList.add("&");
        expression = expression.replace("|", "||").replace("&", "&&");
        //对当前表达式进行数字分割
        String[] strings = expression.split("");
        Pattern pattern = Pattern.compile("[0-9]");
        String matchNum = "";
        List<String> pipeiList = new ArrayList<>();
        int i = 0;
        for (String str : strings) {
            i = i + 1;
            Matcher isNum = pattern.matcher(str);
            if (isNum.matches()) {
                //当前为数字就看看下面是不是数字，先记一下
                if (StringUtils.isBlank(matchNum)) {
                    matchNum = str;
                } else {
                    matchNum = matchNum + str;
                }
                if (i == strings.length) {
                    pipeiList.add(matchNum);
                }
            } else {
                if (StringUtils.isNotBlank(matchNum)) {
                    pipeiList.add(matchNum);
                    matchNum = "";
                }
                pipeiList.add(str);
            }
        }
        String expressionAfter = "";
        for (String str : pipeiList) {
            if (StringUtils.isNumeric(str)) {
                expressionAfter = expressionAfter + "data.rule" + str;
            } else {
                expressionAfter = expressionAfter + str;
            }
        }
        
        String classStr = "class checkStrategy{ " +
                "   public boolean check(def data) { " +
                "if(" +
                expressionAfter +
                "){ " +
                " return true ; " +
                "} else { " +
                " return false ; " +
                " }" +
                "}" +
                "}";
        ;
        return classStr;
    }

    public MktApproveFlowDetailVO viewApprove(String approveNo) throws BizException {
        return commonApproveService.query(approveNo);
    }

    /**
     * 发布策略，仅审批完成的状态支持发布
     * @param strategyNo
     * @param userInfo
     * @throws BizException
     */
    public void publish(String strategyNo, UserInfo userInfo) throws BizException {
        LoggerUtils.info(logger, "发布策略，策略编号code：{}", strategyNo);

        //  校验当前策略是否允许发布
        if (StringUtils.isBlank(strategyNo)) {
            throw new BizException(-1, "请传入strategyNo");
        }
        MetricStrategy metricStrategy = metricStrategyMapper.queryByStrategyNo(strategyNo);
        if (metricStrategy == null) {
            throw new BizException(-1, "策略不存在");
        }

        if (metricStrategy.getApproveStatus() != ApproveStatusEnum.APPROVED.getCode()
                || metricStrategy.getStatus() != StrategyStatusEnum.WAITING_ONLINE.getCode()) {
            throw new BizException(-1, "当前状态不允许发布上线");
        }

        // 发布策略
        int count = metricStrategyMapper.updateStatus(strategyNo, StrategyStatusEnum.ONLINE.getCode());
        LoggerUtils.info(logger, "发布策略，策略编号code：{} , 条数：{}", strategyNo, count);

        // 操作日志记录
        log(strategyNo, userInfo.getUsername() + userInfo.getWorkId(), OperationEnum.PUBLISH, "发布上线");
    }
}
