package com.ly.car.risk.manage.integration.approve.model;

import lombok.Data;

import java.io.Serializable;

/**
 * MktApproveRowDataItemVO
 *
 * <AUTHOR>
 * @version Id : MktApproveRowDataItemVO, v 1.0  2024/5/16 15:29,hansheng.zhang Exp $
 */
@Data
public class MktApproveRowDataItemVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 原始值
     */
    private Object originValue;
    /**
     * 渲染值
     */
    private String renderValue;
}