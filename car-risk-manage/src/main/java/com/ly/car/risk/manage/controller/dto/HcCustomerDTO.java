package com.ly.car.risk.manage.controller.dto;

import lombok.Data;

@Data
public class HcCustomerDTO {

    private Long id;
    private String driverId;
    private String idCardNo;
    private String driverPhone;
    private String driverCardNo;
    private String createTime;
    private String createUser;
    private String updateTime;
    private String updateUser;
    private String remark;
    private String unRemark;
    private Integer customerType;
    private String isDeleted;
    private String invalidTime;
    private String supplierCode;
    private Integer ttl;
}
