package com.ly.car.risk.manage.controller.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SensitiveRecordDTO {

    /**
     * 主键id（自增）
     */
    private Long id;

    /**
     * 敏感内容
     */
    private String sensitiveContext;

    /**
     * 类型：0-文字 1-图片 2-语音
     */
    private String typeDesc;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 司机车牌号
     */
    private String driverCardNo;

    /**
     * 用户唯一标识（如微信unionId）
     */
    private String userId;

    /**
     * 创建时间（默认值：1970-01-01 00:00:00）
     */
    private Date createTime;

    /**
     * 原始文本内容
     */
    private String originalText;

    /**
     * 敏感词类型：5-线下交易 7-资金风险（默认0，当前统一用5）
     */
    private String wordTypeDesc;


    /**
     * 是否外呼：0-未执行 1-已执行（默认0）
     */
    private String ivrCallFlagDesc;

}
