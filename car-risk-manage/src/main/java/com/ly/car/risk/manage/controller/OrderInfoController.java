package com.ly.car.risk.manage.controller;

import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.OrderInfoDTO;
import com.ly.car.risk.manage.controller.dto.OrderInfoDetailDTO;
import com.ly.car.risk.manage.controller.params.OrderQueryParams;
import com.ly.car.risk.manage.service.OrderInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;

@RestController
@RequestMapping("riskOrderInformation")
public class OrderInfoController {

    @Resource
    OrderInfoService orderInfoService;

    @RequestMapping("getList")
    public Pagination<OrderInfoDTO> queryList(@RequestBody OrderQueryParams params){
        if(StringUtils.isBlank(params.getStartTime()) || StringUtils.isBlank(params.getEndTime())){
            return new Pagination<>(new ArrayList<>(),0,params);
        }
//        return null;
        return orderInfoService.queryList(params);
    }

    @RequestMapping("detail")
    public OrderInfoDetailDTO getDetail(@RequestBody OrderQueryParams params){

        return orderInfoService.getDetail(params.getOrderId());
    }
}
