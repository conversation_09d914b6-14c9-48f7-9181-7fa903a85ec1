package com.ly.car.risk.manage.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum HcCustomerRiskTypeEnum {
    
    WHITE_LIST(0, "白名单"),
    BLACK_LIST(1, "黑名单"),
    DISABLE_WITHDRAW_WHITE_LIST(2, "禁止提现白名单"),
    DISABLE_WITHDRAW_BLACK_LIST(3, "禁止提现黑名单"),
    DISABLE_PUBLISH_WHITE_LIST(4, "禁止发布白名单"),
    DISABLE_PUBLISH_BLACK_LIST(5, "禁止发布黑名单"),
    DISABLE_RECEIVE_ORDER_WHITE_LIST(6, "禁止接单白名单"),
    DISABLE_RECEIVE_ORDER_BLACK_LIST(7, "禁止接单黑名单"),
    DISABLE_LOGIN_WHITE_LIST(8, "禁止登录白名单"),
    DISABLE_LOGIN_BLACK_LIST(9, "禁止登录黑名单"),
    DISABLE_REGISTER_WHITE_LIST(10, "禁止注册白名单"),
    DISABLE_REGISTER_BLACK_LIST(11, "禁止注册黑名单"),
    DISABLE_INVITE_WHITE_LIST(12, "禁止邀请白名单"),
    DISABLE_INVITE_BLACK_LIST(13, "禁止邀请黑名单"),
    DISABLE_AWARD_WHITE_LIST(14, "禁止获奖白名单"),
    DISABLE_AWARD_BLACK_LIST(15, "禁止获奖黑名单");
    
    private final Integer code;
    private final String  name;
    
    public static String getMsgByCode(Integer code) {
        for (HcCustomerRiskTypeEnum enumItem : HcCustomerRiskTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getName();
            }
        }
        return StringUtils.EMPTY;
    }
    
    public static Integer getCodeByMsg(String msg) {
        for (HcCustomerRiskTypeEnum enumItem : HcCustomerRiskTypeEnum.values()) {
            if (enumItem.getName().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
}
