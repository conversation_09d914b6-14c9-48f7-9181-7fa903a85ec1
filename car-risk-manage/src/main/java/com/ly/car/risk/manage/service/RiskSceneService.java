package com.ly.car.risk.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.car.risk.manage.bean.Pagination;
import com.ly.car.risk.manage.controller.dto.RiskSceneAllListDTO;
import com.ly.car.risk.manage.controller.dto.RiskSceneListDTO;
import com.ly.car.risk.manage.controller.params.RiskSceneParams;
import com.ly.car.risk.manage.oauth.bean.UserInfo;
import com.ly.car.risk.manage.repo.risk.mapper.RiskSceneMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.CategoryRelation;
import com.ly.car.risk.manage.repo.risk.mapper.entity.HcOrderCheck;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskScene;
import com.ly.car.risk.manage.utils.RandomUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskSceneService {

    @Resource
    private RiskSceneMapper riskSceneMapper;
    @Resource
    private CategoryRelationService categoryRelationService;

    public void add(RiskSceneParams params, UserInfo userInfo){
        RiskScene scene = new RiskScene();
        scene.setGuid(RandomUtil.getRandomString(32));
        scene.setName(params.getSceneName());
        if(params.getSceneType() == 1){
            //如果时子场景，则先查下父场景信息
            scene.setIsChild(1);
            RiskScene parentScene = this.riskSceneMapper.selectOne(new QueryWrapper<RiskScene>()
                    .eq("guid", params.getGuid())
            );
            scene.setParentScene(parentScene.getName());
            scene.setChildScene(params.getSceneName());
            scene.setParentSceneId(parentScene.getGuid());
        } else {
            scene.setIsChild(0);
            scene.setParentScene(params.getSceneName());
        }
        scene.setLinkCategory(0);
        scene.setLinkCategoryNo("");
        scene.setCreateTime(new Date());
        scene.setUpdateTime(new Date());
        scene.setCreateUser(userInfo.getUsername());
        scene.setUpdateUser(userInfo.getUsername());
        scene.setIsDeleted(0);
        scene.setProductLine("");
        scene.setDescription(params.getDescription());
        this.riskSceneMapper.insert(scene);
    }

    public Pagination getList(RiskSceneParams params){
        long total = this.riskSceneMapper.getTotal(params);
        List<RiskScene> scenePage = riskSceneMapper.queryList(params);
        List<RiskSceneListDTO> dtoList = new ArrayList<>();
        for(RiskScene scene : scenePage){
            RiskSceneListDTO dto = new RiskSceneListDTO();
            dto.setId(scene.getId());
            dto.setGuid(scene.getGuid());
            dto.setParentScene(scene.getParentScene());
            dto.setChildScene(scene.getChildScene());
            //用场景编号查询下查下关系表
            List<CategoryRelation> list = this.categoryRelationService.queryByRuleSceneNo(scene.getGuid(),1);
            List<String> strList = list.stream().map(CategoryRelation::getStrategyNo).distinct().collect(Collectors.toList());
            dto.setLinkCategory(strList.size());
            dto.setUpdateTime(DateUtil.date2String(scene.getUpdateTime()));
            dto.setUpdateUser(scene.getUpdateUser());
            dtoList.add(dto);
        }
        return new Pagination<>(dtoList,total,params);
    }

    public List<RiskSceneAllListDTO> getAllList(){
        List<RiskScene> scenePage = riskSceneMapper.queryAllList();
        List<RiskSceneAllListDTO> dtoList = new ArrayList<>();
        for(RiskScene scene : scenePage){
            if(scene.getIsChild() == 0){
                RiskSceneAllListDTO dto = new RiskSceneAllListDTO();
                RiskSceneListDTO riskSceneListDTO = new RiskSceneListDTO();
                riskSceneListDTO.setId(scene.getId());
                riskSceneListDTO.setGuid(scene.getGuid());
                riskSceneListDTO.setParentScene(scene.getParentScene());
                riskSceneListDTO.setChildScene(scene.getChildScene());
                riskSceneListDTO.setLinkCategory(scene.getLinkCategory());
                riskSceneListDTO.setUpdateTime(DateUtil.date2String(scene.getUpdateTime()));
                riskSceneListDTO.setUpdateUser(scene.getUpdateUser());
                dto.setMainScene(riskSceneListDTO);
                dtoList.add(dto);
            }
        }
        for(RiskSceneAllListDTO dto : dtoList){
            List<RiskSceneListDTO> scene = new ArrayList<>();
            List<RiskScene> collect = scenePage.stream().filter(data -> data.getIsChild() == 1)
                    .filter(data -> data.getParentSceneId().equals(dto.getMainScene().getGuid()))
                    .collect(Collectors.toList());
            for(RiskScene riskScene : collect){
                RiskSceneListDTO childDto = new RiskSceneListDTO();
                childDto.setId(riskScene.getId());
                childDto.setGuid(riskScene.getGuid());
                childDto.setParentScene(riskScene.getParentScene());
                childDto.setChildScene(riskScene.getChildScene());
                childDto.setLinkCategory(riskScene.getLinkCategory());
                childDto.setUpdateTime(DateUtil.date2String(riskScene.getUpdateTime()));
                childDto.setUpdateUser(riskScene.getUpdateUser());
                scene.add(childDto);
            }
            dto.setChildScene(scene);
        }
        return dtoList;
    }


    public List<RiskScene> getParentScene(){
        List<RiskScene> list = this.riskSceneMapper.selectList(new QueryWrapper<RiskScene>()
            .eq("is_child",0)
            .eq("is_deleted",0)
        );
        return list;
    }

    public String delete(RiskSceneParams params){
        RiskScene riskScene = new RiskScene();
        riskScene.setId(params.getId());
        riskScene.setIsDeleted(1);
        riskScene.setUpdateTime(new Date());
        this.riskSceneMapper.updateById(riskScene);
        return "success";
    }

    public RiskScene detail(Long id){
        return this.riskSceneMapper.selectById(id);
    }

//    public void update(RiskSceneParams riskSceneParams,UserInfo userInfo){
//        RiskScene riskScene = new RiskScene();
//        riskScene.setUpdateUser(userInfo.getUsername()+userInfo.getWorkId());
//        riskScene.setUpdateTime(new Date());
//    }

}
