config:
  oauth:
    stateApplyForCodeUri: http://tccommon.qas.17usoft.com/oauth/authorize
    codeApplyForTokenUri: http://tccommon.qas.17usoft.com/oauth/token
    getUserInfoByTokenUri: http://tccommon.qas.17usoft.com/oauth/rs/getuserinfo
    ssoLogoutUri: http://tccommon.qas.17usoft.com/oauth/logout
    applyTokenRedirectUri: http://tcwireless.qa.17usoft.com/usecarRiskAdmin/#/oauth
    clientId: car.risk.admin.test
    clientSecret: a2db23028ec9aa6ded0d5e9217f3a9fa
  permission:
    getMenuUrl: http://servicegw.qa.ly.com/gateway/authoritydsf/interface/dsf/base/authority/menu/getmenulist
    appKey: car.risk.admin
    appCode: e3e9bb77-73d4-4c84-919c-50ee75a70d77
    labradorToken: feff99ba-5c02-4b99-b4e1-911a755377d2
  urls:
    workerOrder: http://tcwireless.t.17usoft.com/car_risk_process/workOrder/generateWorkOrder
    processUrl: http://tcwireless.t.17usoft.com/car_risk_process
    currentSysUrl: https://tcwireless.qa.17usoft.com/usecarRiskAdmin
    mng2Domain: http://marketing.travel.qa.17usoft.com/marketingmng2
    mngLogSaveUrl: http://marketing.travel.qa.17usoft.com/marketingmng/log/save
    mngLogListUrl: http://marketing.travel.qa.17usoft.com/marketingmng/log/mng2/
    #苏州公共业务集群
    publicMqServer: mqnameserver.qa.17usoft.com:9876
  turbo-mq:
    approveStatusChangeTopic: travel_marketing_mng2_approve_topic_status_change_qa
    approveStatusChangeGroup: car_risk_approve_group_approve_status_change_qa