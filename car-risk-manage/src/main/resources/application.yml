###
server:
  port: ${PORT0:8080}
  servlet:
    context-path: /car_risk_manage

###
spring:
  profiles:
    active: @profile.name@
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  devtools:
    restart:
      enabled: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
logging:
  config: classpath:logback-spring.xml

###
config:
  data-sources:
    TECarRisk:
      alias: risk
    TCInternalCarOrder:
      alias: order
    TECarShardingOrder:
      alias: shardingOrder
    DCDB_TCInternalCarOrder:
      alias: dcdbOrder
    TEWirelessTraffic:
      alias: traffic
    TEHitchOrder:
      alias: hitchOrder
    TCTravelMktCarRiskMetrics:
      alias: riskMetrics
    monitor:
      checkMode: INVOKE_API
    TCTravelCarRisk:
      alias: dcdbCarRisk
