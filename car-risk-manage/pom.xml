<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.3</version>
        <relativePath/>
    </parent>
    <groupId>com.ly.car</groupId>
    <artifactId>car-risk-manage</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <app.name>car.java.risk.manage</app.name>
        <tcbase.env>${profile.name}</tcbase.env>
        <kafka.version>0.11.0.3</kafka.version>
        <mysql.version>5.1.38</mysql.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-common-bean</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-sharding-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-order-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-traffic-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.dal</groupId>
            <artifactId>dal-new</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.tcbase</groupId>
            <artifactId>configcenterclient</artifactId>
        </dependency>

        <!-- easyexcel 依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!-- Ceph 分布式存储 start http://dev.17usoft.com/app/?hostIp=**************#/home/<USER>/10-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.9.0</version>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9</version>
        </dependency>
        <!-- Ceph 分布式存储 end -->

        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.1</version>
        </dependency>


        <!-- 解析表达式 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.32</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.0.1-jre</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-utils</artifactId>
            <version>3.0.0.8-RUBIK.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.turbomq</groupId>
            <artifactId>turbomq-client</artifactId>
            <version>4.2.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.0</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.ly.car</groupId>
                <artifactId>car-risk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>car_risk_manage</finalName>

        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>

            <resource>
                <filtering>true</filtering>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/application*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>

        <plugins>

            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>

        </plugins>

    </build>

    <profiles>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.name>test</profile.name>
                <tcbase.env>test</tcbase.env>
                <dsf.host>qa.dsf2.17usoft.com</dsf.host>
                <dsf.env>test</dsf.env>
            </properties>
        </profile>

        <profile>
            <id>qa</id>
            <properties>
                <profile.name>qa</profile.name>
                <tcbase.env>qa</tcbase.env>
                <dsf.host>qa.dsf2.17usoft.com</dsf.host>
                <dsf.env>qa</dsf.env>
            </properties>
        </profile>

        <profile>
            <id>uat</id>
            <properties>
                <profile.name>uat</profile.name>
                <tcbase.env>uat</tcbase.env>
                <dsf.host>qa.dsf2.17usoft.com</dsf.host>
                <dsf.env>uat</dsf.env>
            </properties>
        </profile>

        <profile>
            <id>stage</id>
            <properties>
                <profile.name>stage</profile.name>
                <dsf.host>t.dsf2.17usoft.com</dsf.host>
                <dsf.env>stage</dsf.env>
            </properties>
        </profile>

        <profile>
            <id>stage_test2</id>
            <properties>
                <profile.name>stage_test2</profile.name>
                <dsf.host>t.dsf2.17usoft.com</dsf.host>
                <dsf.env>stage_test2</dsf.env>
            </properties>
        </profile>

        <profile>
            <id>product</id>
            <properties>
                <profile.name>product</profile.name>
                <dsf.host>dsf2.17usoft.com</dsf.host>
                <dsf.env>product</dsf.env>
            </properties>
        </profile>

    </profiles>

    <repositories>
        <repository>
            <id>17usoft</id>
            <name>LY Share Repository</name>
            <url>http://nexus.17usoft.com/repository/mvn-all/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>releases</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>https://nexus.17usoft.com/repository/mvn-flight-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>https://nexus.17usoft.com/repository/mvn-flight-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

</project>