# `/riskCheck/queryRiskLevel` 接口数据来源入口分析

## 1. 数据来源概述

`/riskCheck/queryRiskLevel` 接口主要查询 `risk_customer_manage` 表中的风险名单数据。这些数据的来源有多个入口，包括系统自动添加、人工操作添加和外部系统同步等。

## 2. 风险名单数据来源入口

### 2.1 系统自动添加

系统根据风控规则自动识别风险用户，将其添加到风险名单中。

#### 2.1.1 调用链

```
风控规则触发 -> DistributionRiskManageService.addByCustomerNoOrder -> RiskCustomerService.addRiskCustomer -> 插入数据库
```

#### 2.1.2 代码实现

```java
@Service
@Slf4j
public class DistributionRiskManageService {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    public void addByCustomerNoOrder(String customerValue, Integer riskType, Integer customerType, Integer ttl) {
        try {
            riskCustomerService.addRiskCustomer(customerValue, riskType, customerType, ttl);
        } catch (Exception e) {
            log.error("[DistributionRiskManageService][addByCustomerNoOrder][][]添加风险名单异常", e);
        }
    }
}

@Service
@Slf4j
public class RiskCustomerServiceImpl implements RiskCustomerService {
    
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    @Override
    public void addRiskCustomer(String customerValue, Integer riskType, Integer customerType, Integer ttl) {
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(riskType);
        riskCustomerManage.setCustomerType(customerType);
        riskCustomerManage.setCustomerValue(customerValue);
        riskCustomerManage.setStatus(1);
        riskCustomerManage.setTtl(ttl);
        riskCustomerManage.setOptionType(1);
        riskCustomerManage.setCreateUser("规则拉黑");
        riskCustomerManage.setOptionName("规则拉黑");
        riskCustomerManage.setRiskRemark("");
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        Date invalidTime = new Date();
        if (ttl == -1) {
            invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
        } else {
            invalidTime = DateUtil.addDay(new Date(), ttl);
        }
        riskCustomerManage.setInvalidTime(invalidTime);
        riskCustomerManageMapper.insert(riskCustomerManage);
    }
}
```

#### 2.1.3 触发场景

1. **订单风险检测**：当订单被识别为风险订单时，系统会自动将相关用户添加到风险名单中。

```java
@Service
@Slf4j
public class OrderRiskCheckService {
    
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    
    public void checkOrderRisk(OrderDTO order) {
        // 风险检测逻辑
        if (isRiskOrder(order)) {
            // 添加到黑名单
            distributionRiskManageService.addByCustomerNoOrder(
                order.getUserId(), 
                RiskCustomerRiskTypeEnum.black_list.getCode(), 
                RiskCustomerTypeEnum.USER_ID.getCode(), 
                365
            );
        }
    }
    
    private boolean isRiskOrder(OrderDTO order) {
        // 风险订单判断逻辑
        return false;
    }
}
```

2. **支付风险检测**：当支付被识别为风险支付时，系统会自动将相关用户添加到风险名单中。

```java
@Service
@Slf4j
public class PaymentRiskCheckService {
    
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    
    public void checkPaymentRisk(PaymentDTO payment) {
        // 风险检测逻辑
        if (isRiskPayment(payment)) {
            // 添加到黑名单
            distributionRiskManageService.addByCustomerNoOrder(
                payment.getPayAccount(), 
                RiskCustomerRiskTypeEnum.black_list.getCode(), 
                RiskCustomerTypeEnum.PAY_ACCOUNT.getCode(), 
                365
            );
        }
    }
    
    private boolean isRiskPayment(PaymentDTO payment) {
        // 风险支付判断逻辑
        return false;
    }
}
```

3. **用户行为风险检测**：当用户行为被识别为风险行为时，系统会自动将相关用户添加到风险名单中。

```java
@Service
@Slf4j
public class UserBehaviorRiskCheckService {
    
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    
    public void checkUserBehaviorRisk(UserBehaviorDTO behavior) {
        // 风险检测逻辑
        if (isRiskBehavior(behavior)) {
            // 添加到黑名单
            distributionRiskManageService.addByCustomerNoOrder(
                behavior.getUserId(), 
                RiskCustomerRiskTypeEnum.black_list.getCode(), 
                RiskCustomerTypeEnum.USER_ID.getCode(), 
                365
            );
        }
    }
    
    private boolean isRiskBehavior(UserBehaviorDTO behavior) {
        // 风险行为判断逻辑
        return false;
    }
}
```

### 2.2 人工操作添加

通过管理后台手动添加风险用户，将其添加到风险名单中。

#### 2.2.1 调用链

```
管理后台操作 -> RiskCustomerController.add -> RiskCustomerService.add -> 插入数据库
```

#### 2.2.2 代码实现

```java
@RestController
@RequestMapping("/riskCustomer")
@Slf4j
public class RiskCustomerController {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    @PostMapping("/add")
    public UiResult add(@RequestBody RiskCustomerAddRequest request) {
        log.info("[RiskCustomerController][add][][]添加风险名单入参:{}", JsonUtils.json(request));
        try {
            riskCustomerService.add(request);
            return UiResult.ok();
        } catch (Exception e) {
            log.error("[RiskCustomerController][add][][]添加风险名单异常", e);
            return UiResult.error("添加风险名单异常");
        }
    }
}

@Service
@Slf4j
public class RiskCustomerServiceImpl implements RiskCustomerService {
    
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    @Override
    public void add(RiskCustomerAddRequest request) {
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(request.getRiskType());
        riskCustomerManage.setCustomerType(request.getCustomerType());
        riskCustomerManage.setCustomerValue(request.getCustomerValue());
        riskCustomerManage.setStatus(1);
        riskCustomerManage.setTtl(request.getTtl());
        riskCustomerManage.setOptionType(2);
        riskCustomerManage.setCreateUser(request.getCreateUser());
        riskCustomerManage.setOptionName(request.getOptionName());
        riskCustomerManage.setRiskRemark(request.getRiskRemark());
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        Date invalidTime = new Date();
        if (request.getTtl() == -1) {
            invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
        } else {
            invalidTime = DateUtil.addDay(new Date(), request.getTtl());
        }
        riskCustomerManage.setInvalidTime(invalidTime);
        riskCustomerManageMapper.insert(riskCustomerManage);
    }
}
```

#### 2.2.3 管理后台界面

管理后台提供了添加风险名单的界面，管理员可以通过该界面手动添加风险用户。

![管理后台界面](https://example.com/admin-interface.png)

界面包含以下字段：
- 客户类型：下拉选择（用户ID、用户手机号、用户设备号等）
- 客户值：输入框
- 风险类型：下拉选择（黑名单、白名单、禁止领券名单等）
- 有效期：下拉选择（永久、1天、7天、30天、365天）
- 风险备注：输入框
- 操作人：自动填充当前登录用户

### 2.3 外部系统同步

通过外部系统（如ERP系统）同步风险用户，将其添加到风险名单中。

#### 2.3.1 调用链

```
外部系统调用 -> RiskDriverTask.syncDriverFromErp -> RiskCustomerService.syncDriverByErp -> 插入数据库
```

#### 2.3.2 代码实现

```java
@Component
@Slf4j
public class RiskDriverTask {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncDriverFromErp() {
        log.info("[RiskDriverTask][syncDriverFromErp][][]开始同步ERP司机数据");
        try {
            riskCustomerService.syncDriverByErp();
            log.info("[RiskDriverTask][syncDriverFromErp][][]同步ERP司机数据成功");
        } catch (Exception e) {
            log.error("[RiskDriverTask][syncDriverFromErp][][]同步ERP司机数据异常", e);
        }
    }
}

@Service
@Slf4j
public class RiskCustomerServiceImpl implements RiskCustomerService {
    
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    @Resource
    private ErpClient erpClient;
    
    @Override
    public void syncDriverByErp() {
        // 调用ERP接口获取风险司机数据
        List<ErpDriverDTO> drivers = erpClient.getRiskDrivers();
        if (CollectionUtils.isEmpty(drivers)) {
            log.info("[RiskCustomerService][syncDriverByErp][][]ERP无风险司机数据");
            return;
        }
        
        log.info("[RiskCustomerService][syncDriverByErp][][]ERP风险司机数据数量:{}", drivers.size());
        
        // 批量添加风险司机
        for (ErpDriverDTO driver : drivers) {
            try {
                RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
                riskCustomerManage.setRiskType(RiskCustomerRiskTypeEnum.black_list.getCode());
                riskCustomerManage.setCustomerType(RiskCustomerTypeEnum.DRIVER_CARD_NO.getCode());
                riskCustomerManage.setCustomerValue(driver.getCardNo());
                riskCustomerManage.setStatus(1);
                riskCustomerManage.setTtl(-1);
                riskCustomerManage.setOptionType(1);
                riskCustomerManage.setCreateUser("ERP同步");
                riskCustomerManage.setOptionName("ERP同步");
                riskCustomerManage.setRiskRemark("ERP风险司机");
                riskCustomerManage.setCreateTime(new Date());
                riskCustomerManage.setUpdateTime(new Date());
                riskCustomerManage.setInvalidTime(DateUtil.string2Date("2099-12-31 23:59:59"));
                riskCustomerManageMapper.insert(riskCustomerManage);
            } catch (Exception e) {
                log.error("[RiskCustomerService][syncDriverByErp][][]添加ERP风险司机异常", e);
            }
        }
    }
}
```

#### 2.3.3 外部系统接口

ERP系统提供了获取风险司机数据的接口，接口返回风险司机列表。

```java
@Service
@Slf4j
public class ErpClient {
    
    @Value("${erp.api.url}")
    private String erpApiUrl;
    
    @Resource
    private RestTemplate restTemplate;
    
    public List<ErpDriverDTO> getRiskDrivers() {
        try {
            String url = erpApiUrl + "/api/risk/drivers";
            ResponseEntity<List<ErpDriverDTO>> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<ErpDriverDTO>>() {}
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("[ErpClient][getRiskDrivers][][]调用ERP接口异常", e);
            return Collections.emptyList();
        }
    }
}
```

## 3. 风险名单数据维护

### 3.1 数据失效

系统会定期检查风险名单数据，将过期的数据标记为失效。

#### 3.1.1 调用链

```
定时任务 -> RiskCustomerTask.invalidRiskCustomer -> RiskCustomerService.invalid -> 更新数据库
```

#### 3.1.2 代码实现

```java
@Component
@Slf4j
public class RiskCustomerTask {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    @Scheduled(cron = "0 0 1 * * ?")
    public void invalidRiskCustomer() {
        log.info("[RiskCustomerTask][invalidRiskCustomer][][]开始失效风险名单");
        try {
            String result = riskCustomerService.invalid();
            log.info("[RiskCustomerTask][invalidRiskCustomer][][]失效风险名单结果:{}", result);
        } catch (Exception e) {
            log.error("[RiskCustomerTask][invalidRiskCustomer][][]失效风险名单异常", e);
        }
    }
}

@Service
@Slf4j
public class RiskCustomerServiceImpl implements RiskCustomerService {
    
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    @Override
    public String invalid() {
        List<RiskCustomerManage> list = riskCustomerManageMapper.selectInvalidData();
        log.info("[RiskCustomerService][invalid][],[]需要失效的风控名单数据：{}", list.size());
        if (!CollectionUtils.isEmpty(list)) {
            int count = 0;
            for (RiskCustomerManage entity : list) {
                entity.setStatus(2);
                entity.setUpdateTime(new Date());
                entity.setInvalidTime(new Date());
                entity.setOptionType(1);
                riskCustomerManageMapper.updateById(entity);
                count++;
            }
            log.info("[RiskCustomerService][invalid][],[]需要失效的风控名单执行成功：{}", count);
        }
        return "success";
    }
}
```

### 3.2 数据升级

系统会定期检查一对一黑名单数据，将符合条件的数据升级为全局黑名单。

#### 3.2.1 调用链

```
定时任务 -> RiskCustomerTask.upgradeDriver -> RiskCustomerService.upgradeDriver -> 更新数据库
```

#### 3.2.2 代码实现

```java
@Component
@Slf4j
public class RiskCustomerTask {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    @Scheduled(cron = "0 0 2 * * ?")
    public void upgradeDriver() {
        log.info("[RiskCustomerTask][upgradeDriver][][]开始升级黑名单司机");
        try {
            riskCustomerService.upgradeDriver();
            log.info("[RiskCustomerTask][upgradeDriver][][]升级黑名单司机成功");
        } catch (Exception e) {
            log.error("[RiskCustomerTask][upgradeDriver][][]升级黑名单司机异常", e);
        }
    }
}

@Service
@Slf4j
public class RiskCustomerServiceImpl implements RiskCustomerService {
    
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    @Override
    public void upgradeDriver() {
        log.info("[RiskCustomerService][upgradeDriver][][]开始升级黑名单司机");
        Date date = new Date();
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().gt("invalid_time", date)
                        .eq("risk_type", 7)
        );
        
        if (CollectionUtils.isEmpty(manageList)) {
            log.info("[RiskCustomerService][upgradeDriver][][]无需升级的黑名单司机");
            return;
        }
        
        log.info("[RiskCustomerService][upgradeDriver][][]需要升级的黑名单司机数量:{}", manageList.size());
        
        // 统计每个司机的一对一黑名单数量
        Map<String, Long> driverCountMap = manageList.stream()
                .collect(Collectors.groupingBy(RiskCustomerManage::getCustomerValue, Collectors.counting()));
        
        // 升级符合条件的司机
        for (Map.Entry<String, Long> entry : driverCountMap.entrySet()) {
            String driverCardNo = entry.getKey();
            Long count = entry.getValue();
            
            // 如果一对一黑名单数量超过阈值，升级为全局黑名单
            if (count >= 3) {
                try {
                    // 查询是否已经存在全局黑名单
                    List<RiskCustomerManage> existList = riskCustomerManageMapper.selectList(
                            new QueryWrapper<RiskCustomerManage>().gt("invalid_time", date)
                                    .eq("risk_type", 1)
                                    .eq("customer_value", driverCardNo)
                    );
                    
                    if (CollectionUtils.isEmpty(existList)) {
                        // 添加全局黑名单
                        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
                        riskCustomerManage.setRiskType(RiskCustomerRiskTypeEnum.black_list.getCode());
                        riskCustomerManage.setCustomerType(RiskCustomerTypeEnum.DRIVER_CARD_NO.getCode());
                        riskCustomerManage.setCustomerValue(driverCardNo);
                        riskCustomerManage.setStatus(1);
                        riskCustomerManage.setTtl(-1);
                        riskCustomerManage.setOptionType(1);
                        riskCustomerManage.setCreateUser("系统升级");
                        riskCustomerManage.setOptionName("系统升级");
                        riskCustomerManage.setRiskRemark("一对一黑名单升级");
                        riskCustomerManage.setCreateTime(new Date());
                        riskCustomerManage.setUpdateTime(new Date());
                        riskCustomerManage.setInvalidTime(DateUtil.string2Date("2099-12-31 23:59:59"));
                        riskCustomerManageMapper.insert(riskCustomerManage);
                        
                        log.info("[RiskCustomerService][upgradeDriver][][]升级司机成功:{}", driverCardNo);
                    }
                } catch (Exception e) {
                    log.error("[RiskCustomerService][upgradeDriver][][]升级司机异常:{}", driverCardNo, e);
                }
            }
        }
    }
}
```

## 4. 数据查询入口

### 4.1 风险等级查询

通过 `/riskCheck/queryRiskLevel` 接口查询用户的风险等级。

#### 4.1.1 调用链

```
客户端请求 -> RiskCheckController.queryRiskLevel -> CarRiskService.queryRiskLevel -> RiskCustomerService.getListByValueByGroup -> 查询数据库
```

#### 4.1.2 代码实现

```java
@RestController
@RequestMapping("/riskCheck")
@Slf4j
public class RiskCheckController {
    
    @Resource
    private CarRiskService carRiskService;
    
    @PostMapping("/queryRiskLevel")
    public UiResult queryRiskLevel(@RequestBody RiskLevelQueryRequest request) {
        log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级入参:{}", JsonUtils.json(request));
        if (StringUtils.isBlank(request.getProductLine())) {
            request.setProductLine(ProductLineEnum.YNC.getCode());
        }
        UiResult<RiskResultDTO> result = carRiskService.queryRiskLevel(request);
        log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级出参:{}", JsonUtils.json(result));
        return result;
    }
}
```

### 4.2 风险名单查询

通过 `/riskCustomer/query` 接口查询风险名单。

#### 4.2.1 调用链

```
客户端请求 -> RiskCustomerController.query -> RiskCustomerService.query -> 查询数据库
```

#### 4.2.2 代码实现

```java
@RestController
@RequestMapping("/riskCustomer")
@Slf4j
public class RiskCustomerController {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    @PostMapping("/query")
    public UiResult query(@RequestBody RiskCustomerQueryRequest request) {
        log.info("[RiskCustomerController][query][][]查询风险名单入参:{}", JsonUtils.json(request));
        try {
            PageInfo<RiskCustomerManage> pageInfo = riskCustomerService.query(request);
            return UiResult.ok(pageInfo);
        } catch (Exception e) {
            log.error("[RiskCustomerController][query][][]查询风险名单异常", e);
            return UiResult.error("查询风险名单异常");
        }
    }
}

@Service
@Slf4j
public class RiskCustomerServiceImpl implements RiskCustomerService {
    
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    
    @Override
    public PageInfo<RiskCustomerManage> query(RiskCustomerQueryRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<RiskCustomerManage> list = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq(request.getRiskType() != null, "risk_type", request.getRiskType())
                        .eq(request.getCustomerType() != null, "customer_type", request.getCustomerType())
                        .like(StringUtils.isNotBlank(request.getCustomerValue()), "customer_value", request.getCustomerValue())
                        .eq(request.getStatus() != null, "status", request.getStatus())
                        .orderByDesc("create_time")
        );
        return new PageInfo<>(list);
    }
}
```

## 5. 总结

`/riskCheck/queryRiskLevel` 接口查询的风险名单数据来源于多个入口，包括系统自动添加、人工操作添加和外部系统同步等。这些数据通过不同的调用链最终写入 `risk_customer_manage` 表，然后被 `/riskCheck/queryRiskLevel` 接口查询使用。

系统还提供了数据维护功能，包括数据失效和数据升级，确保风险名单数据的有效性和准确性。

通过这些入口和维护机制，系统能够及时识别和管理风险用户，为业务系统提供风险等级查询服务，保障业务安全。
