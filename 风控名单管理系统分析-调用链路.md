# 风控名单管理系统分析 - 调用链路

本文档分析了 `RiskCustomerManageMapper` 方法的调用链路，从 Controller 到 Service 再到 Mapper。

## 主要调用链路

### 1. 添加风控名单调用链路

```mermaid
graph TD
    A[用户界面] --> B[RiskCustomerController.add]
    B --> C[RiskCustomerService.add]
    C --> D{名单是否存在?}
    D -->|存在| E[riskCustomerManageMapper.updateById]
    D -->|不存在| F[riskCustomerManageMapper.insert]
    E --> G[riskCustomerRecordMapper.insert]
    F --> G
```

### 2. 删除风控名单调用链路

```mermaid
graph TD
    A[用户界面] --> B[RiskCustomerController.delete]
    B --> C[RiskCustomerService.delete]
    C --> D[riskCustomerManageMapper.selectById]
    D --> E{名单是否有效?}
    E -->|有效| F[riskCustomerManageMapper.updateById]
    F --> G[riskCustomerRecordMapper.insert]
```

### 3. 用户拉黑司机调用链路 (BlackDriverShieldController)

```mermaid
graph TD
    A[客户端] --> B[BlackDriverShieldController.userShield]
    B --> C[BlackDriverService.userShield]
    C --> D[riskCustomerManageMapper.selectList]
    D --> E{拉黑记录是否存在?}
    E -->|存在| F[riskCustomerManageMapper.updateById]
    E -->|不存在| G[riskCustomerManageMapper.insert]
    F --> H[riskCustomerRecordMapper.insert]
    G --> H
```

### 4. 用户拉黑司机调用链路 (BlackListApiController)

```mermaid
graph TD
    A[客户端] --> B[BlackListApiController.blackDriver]
    B --> C[BlackListService.blackDriver]
    C --> D[riskCustomerManageMapper.selectList]
    D --> E{拉黑记录是否存在?}
    E -->|存在| F[riskCustomerManageMapper.updateById]
    E -->|不存在| G[riskCustomerManageMapper.insert]
    F --> H[riskCustomerRecordMapper.insert]
    G --> H
```

### 5. 用户取消拉黑司机调用链路

```mermaid
graph TD
    A[客户端] --> B[BlackDriverShieldController.userRemove]
    B --> C[BlackDriverService.userRemove]
    C --> D[riskCustomerManageMapper.selectOne]
    D --> E{拉黑记录是否存在?}
    E -->|存在| F[riskCustomerManageMapper.updateById]
    F --> G[riskCustomerRecordMapper.insert]
```

### 6. 客服拉黑司机调用链路

```mermaid
graph TD
    A[后台管理界面] --> B[BlackListController.blackDriver]
    B --> C[BlackListService.blackDriverFromManage]
    C --> D[遍历司机列表]
    D --> E[获取分布式锁]
    E --> F{是否获取到锁?}
    F -->|是| G{白名单是否存在?}
    G -->|不存在| H{拉黑记录是否存在?}
    H -->|存在| I[riskCustomerManageMapper.updateById]
    H -->|不存在| J[riskCustomerManageMapper.insert]
    I --> K[riskCustomerRecordMapper.insert]
    J --> K
```

### 7. 风控名单定时失效调用链路

```mermaid
graph TD
    A[定时任务] --> B[RiskCustomerController.invalid]
    B --> C[RiskCustomerService.invalid]
    C --> D[riskCustomerManageMapper.selectInvalidData]
    D --> E[遍历需要失效的记录]
    E --> F[riskCustomerManageMapper.updateById]
```

### 8. 同步司机黑名单调用链路

```mermaid
graph TD
    A[系统调用] --> B[RiskCustomerController.syncBlackDriver]
    B --> C[RiskCustomerService.syncDriver]
    C --> D{同步类型?}
    D -->|添加| E[riskCustomerManageMapper.insert]
    D -->|更新| F[riskCustomerManageMapper.updateById]
    D -->|删除| G[riskCustomerManageMapper.updateById]
```

## 详细调用链路

### car-risk-manage 模块

#### 1. RiskCustomerController.add

```
RiskCustomerController.add
└── RiskCustomerService.add
    ├── 参数校验
    ├── 名单覆盖逻辑校验 (matchExist)
    ├── 查询是否已存在 (getByTypeAndValueAndRiskType 或 getByTypeAndValueAndRiskTypeAndUser)
    ├── 如果已存在
    │   ├── riskCustomerManageMapper.updateById
    │   └── riskCustomerRecordMapper.insert (记录操作日志)
    └── 如果不存在
        ├── riskCustomerManageMapper.insert
        └── riskCustomerRecordMapper.insert (记录操作日志)
```

#### 2. RiskCustomerController.delete

```
RiskCustomerController.delete
└── RiskCustomerService.delete
    ├── riskCustomerManageMapper.selectById
    ├── 如果名单存在且有效
    │   ├── riskCustomerManageMapper.updateById (更新状态为无效)
    │   └── riskCustomerRecordMapper.insert (记录操作日志)
    └── 返回操作结果
```

#### 3. RiskCustomerController.excelImport

```
RiskCustomerController.excelImport
└── RiskCustomerService.excelImport
    ├── 解析 Excel 文件
    ├── 遍历每一行数据
    └── 对每一行数据调用 add 方法
        └── RiskCustomerService.add
            ├── 参数校验
            ├── 名单覆盖逻辑校验 (matchExist)
            ├── 查询是否已存在
            ├── 如果已存在
            │   ├── riskCustomerManageMapper.updateById
            │   └── riskCustomerRecordMapper.insert (记录操作日志)
            └── 如果不存在
                ├── riskCustomerManageMapper.insert
                └── riskCustomerRecordMapper.insert (记录操作日志)
```

### car-risk-process 模块

#### 1. BlackDriverShieldController.userShield

```
BlackDriverShieldController.userShield
└── BlackDriverService.userShield
    ├── riskCustomerManageMapper.selectList (查询是否已存在)
    ├── 如果已存在
    │   ├── riskCustomerManageMapper.updateById
    │   └── riskCustomerRecordMapper.insert (记录操作日志)
    └── 如果不存在
        ├── riskCustomerManageMapper.insert
        └── riskCustomerRecordMapper.insert (记录操作日志)
```

#### 2. BlackListApiController.blackDriver

```
BlackListApiController.blackDriver
└── BlackListService.blackDriver
    ├── 参数校验
    ├── riskCustomerManageMapper.selectList (查询是否已存在)
    ├── 如果已存在
    │   ├── riskCustomerManageMapper.updateById
    │   └── riskCustomerRecordMapper.insert (记录操作日志)
    └── 如果不存在
        ├── riskCustomerManageMapper.insert
        └── riskCustomerRecordMapper.insert (记录操作日志)
```

#### 3. RiskCustomerController.invalid

```
RiskCustomerController.invalid
└── RiskCustomerService.invalid
    ├── riskCustomerManageMapper.selectInvalidData (查询需要失效的风控名单)
    └── 遍历每条记录
        └── riskCustomerManageMapper.updateById (更新状态为失效)
```

#### 4. BlackListController.blackDriverFromManage

```
BlackListController.blackDriver
└── BlackListService.blackDriverFromManage
    ├── 遍历要拉黑的司机列表
    ├── 对每个司机
    │   ├── 获取分布式锁
    │   ├── 如果获取到锁
    │   │   ├── 检查是否已存在白名单 (existWhiteList)
    │   │   ├── 如果不存在白名单
    │   │   │   ├── 查询是否已存在拉黑记录
    │   │   │   ├── 如果已存在
    │   │   │   │   ├── riskCustomerManageMapper.updateById
    │   │   │   │   └── riskCustomerRecordMapper.insert (记录操作日志)
    │   │   │   └── 如果不存在
    │   │   │       ├── riskCustomerManageMapper.insert
    │   │   │       └── riskCustomerRecordMapper.insert (记录操作日志)
    │   │   └── 释放锁
    │   └── 继续下一个司机
    └── 返回操作结果
```
