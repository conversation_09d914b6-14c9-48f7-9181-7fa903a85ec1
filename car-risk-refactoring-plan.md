# car-risk项目`/riskCheck/unifyCheck`接口重构方案

## 1. 当前架构分析

### 1.1 当前架构图

```mermaid
graph TD
    A[RiskCheckController.unifyCheck] --> B[RiskStrategyHandler.unifyCheck]
    B --> C1[参数校验 paramCheck]
    B --> C2[黑名单检查 blackListHandler.blackListCheck]
    C2 --> D1[MTBlackListHandler]
    C2 --> D2[CommonBlackListHandler]
    B --> C3[特殊场景检查 specialHandlerCheck]
    C3 --> E1[SpecialSceneHandlerFactory]
    E1 --> F1[DriverAuthenticationSceneHandler]
    E1 --> F2[BankCardChangeSceneHandler]
    E1 --> F3[其他特殊场景处理器...]
    B --> C4[策略检查前准备 beforeStrategyCheck]
    B --> C5[策略检查 strategyCheck]
    C5 --> G1[构建上下文 buildContext]
    C5 --> G2[加密参数 encryptParam]
    C5 --> G3[查找策略 findStrategy]
    C5 --> G4[填充订单参数 fillOrderParam]
    C5 --> G5[执行策略 strategyHandle]
    G5 --> H1[checkStrategy]
    H1 --> I1[checkRule]
    I1 --> J1[getFieldScriptResult]
    I1 --> J2[checkRuleScript]
    H1 --> I2[checkStrategyScript]
    C5 --> G6[策略后处理 strategyPostProcessing]
    B --> C6[策略检查后处理 afterStrategyCheck]
```

### 1.2 当前流程图

```mermaid
flowchart TD
    A[开始] --> B[接收UnifyCheckRequest请求]
    B --> C[参数校验]
    C --> D{黑名单检查}
    D -->|命中黑名单| E[返回风控拦截结果]
    D -->|未命中黑名单| F{是否为分销单}
    F -->|是| G[返回风控通过结果]
    F -->|否| H{特殊场景检查}
    H -->|命中特殊场景| E
    H -->|未命中特殊场景| I[策略检查前准备]
    I --> J[策略检查]
    J --> K[构建上下文]
    K --> L[加密参数]
    L --> M[查找策略]
    M --> N[填充订单参数]
    N --> O[执行策略]
    O --> P[策略后处理]
    P --> Q{是否命中策略}
    Q -->|是| R{是否需要拦截}
    R -->|是| S[执行处置动作]
    S --> E
    R -->|否| T[返回风控通过结果]
    Q -->|否| T
    P --> U[策略检查后处理]
    U --> V[返回风控结果]
```

### 1.3 当前设计问题

1. **高耦合性**：各个组件之间耦合度高，例如`RiskStrategyHandler`包含了太多职责，从参数校验到黑名单检查、特殊场景检查、策略检查等。

2. **扩展性差**：添加新的风控场景或规则需要修改多个类，违反了开闭原则。

3. **代码重复**：多个处理器中存在相似的代码逻辑，如参数校验、结果处理等。

4. **缺乏抽象**：缺少清晰的抽象层次，导致代码难以理解和维护。

5. **硬编码**：许多业务逻辑和规则直接硬编码在代码中，难以配置和调整。

6. **异常处理不统一**：异常处理分散在各个方法中，缺乏统一的异常处理机制。

7. **性能问题**：串行执行各个检查步骤，没有利用并行处理提高性能。

## 2. 重构设计方案

### 2.1 重构架构图

```mermaid
graph TD
    A[RiskCheckController] --> B[RiskCheckFacade]
    B --> C[RiskCheckContext]
    B --> D[RiskCheckPipeline]
    D --> E1[参数校验处理器 ParamValidationProcessor]
    D --> E2[黑名单检查处理器 BlacklistCheckProcessor]
    E2 --> F1[黑名单策略 BlacklistStrategy]
    F1 --> G1[通用黑名单策略 CommonBlacklistStrategy]
    F1 --> G2[萌艇黑名单策略 MTBlacklistStrategy]
    F1 --> G3[其他黑名单策略...]
    D --> E3[特殊场景处理器 SpecialSceneProcessor]
    E3 --> F2[特殊场景策略 SpecialSceneStrategy]
    F2 --> H1[司机认证策略 DriverAuthenticationStrategy]
    F2 --> H2[银行卡变更策略 BankCardChangeStrategy]
    F2 --> H3[其他特殊场景策略...]
    D --> E4[策略检查处理器 StrategyCheckProcessor]
    E4 --> F3[策略执行引擎 StrategyExecutionEngine]
    F3 --> I1[策略加载器 StrategyLoader]
    F3 --> I2[规则执行器 RuleExecutor]
    F3 --> I3[指标计算器 MetricCalculator]
    D --> E5[处置动作处理器 DisposalActionProcessor]
    D --> E6[结果处理器 ResultProcessor]
    B --> J[RiskCheckResult]
```

### 2.2 重构流程图

```mermaid
flowchart TD
    A[开始] --> B[创建RiskCheckContext]
    B --> C[构建RiskCheckPipeline]
    C --> D[执行参数校验处理器]
    D --> E{参数校验通过?}
    E -->|否| F[返回参数错误结果]
    E -->|是| G[执行黑名单检查处理器]
    G --> H{命中黑名单?}
    H -->|是| I[设置黑名单命中结果]
    H -->|否| J[执行特殊场景处理器]
    J --> K{命中特殊场景?}
    K -->|是| L[设置特殊场景命中结果]
    K -->|否| M[执行策略检查处理器]
    M --> N{命中策略?}
    N -->|是| O[设置策略命中结果]
    N -->|否| P[设置通过结果]
    I --> Q[执行处置动作处理器]
    L --> Q
    O --> Q
    P --> Q
    Q --> R[执行结果处理器]
    R --> S[返回风控结果]
```

### 2.3 重构核心设计原则

1. **责任链模式**：使用责任链模式设计处理流程，每个处理器负责特定的检查逻辑。

2. **策略模式**：使用策略模式实现不同类型的检查策略，如不同的黑名单策略、特殊场景策略等。

3. **工厂模式**：使用工厂模式创建处理器和策略对象。

4. **上下文对象**：使用上下文对象传递请求和结果，避免参数传递的复杂性。

5. **依赖注入**：使用依赖注入降低组件间的耦合度。

6. **模板方法模式**：使用模板方法模式定义处理器的基本流程。

7. **观察者模式**：使用观察者模式实现处理结果的通知和监控。

### 2.4 重构核心组件

#### 2.4.1 RiskCheckContext

```java
@Data
public class RiskCheckContext {
    // 请求参数
    private UnifyCheckRequest request;
    // 处理结果
    private RiskCheckResult result;
    // 处理状态
    private RiskCheckStatus status;
    // 上下文数据
    private Map<String, Object> attributes;
    
    // 构造方法
    public RiskCheckContext(UnifyCheckRequest request) {
        this.request = request;
        this.result = new RiskCheckResult();
        this.status = RiskCheckStatus.INIT;
        this.attributes = new HashMap<>();
    }
    
    // 获取属性
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
    
    // 设置属性
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    // 设置结果
    public void setResult(boolean riskFlag, String riskMsg) {
        result.setRiskFlag(riskFlag);
        result.setRiskMsg(riskMsg);
        status = riskFlag ? RiskCheckStatus.REJECTED : RiskCheckStatus.PASSED;
    }
    
    // 是否继续处理
    public boolean shouldContinue() {
        return status == RiskCheckStatus.INIT || status == RiskCheckStatus.PROCESSING;
    }
}
```

#### 2.4.2 RiskCheckProcessor

```java
public interface RiskCheckProcessor {
    // 处理方法
    void process(RiskCheckContext context);
    
    // 获取处理器顺序
    int getOrder();
    
    // 是否支持处理
    boolean supports(RiskCheckContext context);
}
```

#### 2.4.3 AbstractRiskCheckProcessor

```java
public abstract class AbstractRiskCheckProcessor implements RiskCheckProcessor {
    
    @Override
    public void process(RiskCheckContext context) {
        if (!supports(context) || !context.shouldContinue()) {
            return;
        }
        
        try {
            doProcess(context);
        } catch (Exception e) {
            handleException(context, e);
        }
    }
    
    // 具体处理逻辑
    protected abstract void doProcess(RiskCheckContext context);
    
    // 异常处理
    protected void handleException(RiskCheckContext context, Exception e) {
        if (e instanceof BizException) {
            context.setResult(true, e.getMessage());
        } else {
            context.setResult(true, "系统异常");
            // 记录日志
        }
    }
}
```

#### 2.4.4 RiskCheckPipeline

```java
@Service
public class RiskCheckPipeline {
    
    private final List<RiskCheckProcessor> processors;
    
    public RiskCheckPipeline(List<RiskCheckProcessor> processors) {
        this.processors = processors.stream()
            .sorted(Comparator.comparing(RiskCheckProcessor::getOrder))
            .collect(Collectors.toList());
    }
    
    public void process(RiskCheckContext context) {
        for (RiskCheckProcessor processor : processors) {
            processor.process(context);
            if (!context.shouldContinue()) {
                break;
            }
        }
    }
}
```

#### 2.4.5 RiskCheckFacade

```java
@Service
public class RiskCheckFacade {
    
    private final RiskCheckPipeline pipeline;
    
    public RiskCheckFacade(RiskCheckPipeline pipeline) {
        this.pipeline = pipeline;
    }
    
    public RiskCheckResult check(UnifyCheckRequest request) {
        RiskCheckContext context = new RiskCheckContext(request);
        pipeline.process(context);
        return context.getResult();
    }
}
```

#### 2.4.6 BlacklistCheckProcessor

```java
@Service
public class BlacklistCheckProcessor extends AbstractRiskCheckProcessor {
    
    private final BlacklistStrategyFactory strategyFactory;
    
    public BlacklistCheckProcessor(BlacklistStrategyFactory strategyFactory) {
        this.strategyFactory = strategyFactory;
    }
    
    @Override
    protected void doProcess(RiskCheckContext context) {
        UnifyCheckRequest request = context.getRequest();
        
        // 获取黑名单策略
        BlacklistStrategy strategy = strategyFactory.getStrategy(request);
        
        // 执行黑名单检查
        BlacklistCheckResult result = strategy.check(request);
        
        // 处理结果
        if (result.isHit()) {
            context.setResult(true, result.getMessage());
            context.setAttribute("hitType", 1);
            context.setAttribute("hitRule", result.getRuleType());
            context.setAttribute("customerValue", result.getCustomerValue());
            context.setAttribute("customerType", result.getCustomerType());
        }
    }
    
    @Override
    public int getOrder() {
        return 200;
    }
    
    @Override
    public boolean supports(RiskCheckContext context) {
        return true;
    }
}
```

#### 2.4.7 BlacklistStrategy

```java
public interface BlacklistStrategy {
    
    // 检查方法
    BlacklistCheckResult check(UnifyCheckRequest request);
    
    // 是否支持
    boolean supports(UnifyCheckRequest request);
}
```

#### 2.4.8 StrategyCheckProcessor

```java
@Service
public class StrategyCheckProcessor extends AbstractRiskCheckProcessor {
    
    private final StrategyExecutionEngine executionEngine;
    
    public StrategyCheckProcessor(StrategyExecutionEngine executionEngine) {
        this.executionEngine = executionEngine;
    }
    
    @Override
    protected void doProcess(RiskCheckContext context) {
        UnifyCheckRequest request = context.getRequest();
        
        // 执行策略检查
        StrategyExecutionResult result = executionEngine.execute(request);
        
        // 处理结果
        if (result.isHit()) {
            context.setResult(true, result.getMessage());
            context.setAttribute("hitType", 0);
            context.setAttribute("hitRule", result.getMatchRules());
            context.setAttribute("strategyNos", result.getStrategyNo());
            context.setAttribute("hitField", result.getHitField());
            context.setAttribute("controlTarget", result.getControlTarget());
            context.setAttribute("disposeAction", result.getDisposeAction());
        } else {
            context.setResult(false, "风控策略通过");
        }
    }
    
    @Override
    public int getOrder() {
        return 400;
    }
    
    @Override
    public boolean supports(RiskCheckContext context) {
        return true;
    }
}
```

### 2.5 重构优势

1. **高内聚低耦合**：每个组件只负责单一职责，组件之间通过接口和上下文对象交互，降低了耦合度。

2. **可扩展性强**：
   - 添加新的处理器只需实现`RiskCheckProcessor`接口
   - 添加新的黑名单策略只需实现`BlacklistStrategy`接口
   - 添加新的特殊场景策略只需实现`SpecialSceneStrategy`接口

3. **可配置性强**：
   - 处理器的执行顺序可通过`getOrder()`方法配置
   - 处理器的启用/禁用可通过`supports()`方法配置
   - 策略的选择可通过工厂方法配置

4. **可测试性强**：
   - 每个组件都有明确的接口和职责，便于单元测试
   - 可以通过Mock依赖组件进行隔离测试

5. **代码复用**：
   - 通过抽象类和接口提取公共逻辑，减少代码重复
   - 通过模板方法模式定义通用处理流程

6. **异常处理统一**：
   - 在`AbstractRiskCheckProcessor`中统一处理异常
   - 提供了异常处理的扩展点

7. **性能优化**：
   - 可以通过并行处理器实现并行检查
   - 可以通过缓存策略减少重复计算

## 3. 重构实施步骤

### 3.1 第一阶段：核心框架搭建

1. 定义核心接口和抽象类：
   - `RiskCheckContext`
   - `RiskCheckProcessor`
   - `AbstractRiskCheckProcessor`
   - `RiskCheckPipeline`
   - `RiskCheckFacade`

2. 实现基础处理器：
   - `ParamValidationProcessor`
   - `BlacklistCheckProcessor`
   - `SpecialSceneProcessor`
   - `StrategyCheckProcessor`
   - `DisposalActionProcessor`
   - `ResultProcessor`

### 3.2 第二阶段：策略实现

1. 实现黑名单策略：
   - `BlacklistStrategy`接口
   - `CommonBlacklistStrategy`实现
   - `MTBlacklistStrategy`实现

2. 实现特殊场景策略：
   - `SpecialSceneStrategy`接口
   - `DriverAuthenticationStrategy`实现
   - `BankCardChangeStrategy`实现

3. 实现策略执行引擎：
   - `StrategyExecutionEngine`
   - `StrategyLoader`
   - `RuleExecutor`
   - `MetricCalculator`

### 3.3 第三阶段：迁移和测试

1. 编写单元测试：
   - 为每个处理器编写单元测试
   - 为每个策略编写单元测试
   - 为整个流程编写集成测试

2. 迁移现有代码：
   - 将现有逻辑迁移到新的框架中
   - 保持接口兼容性，确保平滑过渡

3. 性能测试和优化：
   - 进行性能测试，确保重构后的性能不低于重构前
   - 根据测试结果进行优化

### 3.4 第四阶段：扩展和完善

1. 实现监控和日志：
   - 添加处理器执行时间监控
   - 添加关键节点日志记录

2. 实现配置中心集成：
   - 将处理器和策略的配置迁移到配置中心
   - 支持动态调整处理器顺序和策略选择

3. 实现缓存机制：
   - 添加策略缓存
   - 添加规则缓存
   - 添加指标计算结果缓存

## 4. 总结

通过以上重构设计，我们将原有的紧耦合、难扩展的代码结构转变为松耦合、易扩展的组件化架构。新的架构具有以下特点：

1. **组件化**：将系统拆分为多个独立的组件，每个组件负责特定的功能。

2. **可扩展**：通过接口和策略模式，支持灵活扩展新的处理器和策略。

3. **可配置**：通过配置中心，支持动态调整处理流程和策略选择。

4. **可测试**：通过依赖注入和接口抽象，支持单元测试和集成测试。

5. **高性能**：通过并行处理和缓存机制，提高系统性能。

6. **可维护**：通过清晰的责任划分和代码结构，提高代码可维护性。

这种设计不仅解决了当前系统的问题，还为未来的功能扩展和性能优化提供了良好的基础。
