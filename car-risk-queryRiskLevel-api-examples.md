# `/riskCheck/queryRiskLevel` 接口调用示例

## 1. 接口概述

`/riskCheck/queryRiskLevel` 接口用于查询用户的风险等级，通过传入用户相关信息（如用户ID、设备ID等），查询该用户是否在风险名单中，并返回相应的风险等级。

## 2. 接口定义

- **URL**: `/riskCheck/queryRiskLevel`
- **方法**: POST
- **Content-Type**: application/json

## 3. 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| --- | --- | --- | --- |
| memberId | String | 否 | 用户ID |
| unionId | String | 否 | 用户统一ID |
| deviceId | String | 否 | 设备ID |
| userPhone | String | 否 | 用户手机号 |
| passengerCellphone | String | 否 | 乘客手机号 |
| productLine | String | 否 | 产品线（默认为"YNC"，即网约车） |
| channel | String | 否 | 渠道 |

**注意**：以上参数至少需要提供一个，用于查询风险名单。

## 4. 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 是否成功 |
| errCode | Integer | 错误码 |
| msg | String | 错误信息 |
| data | Object | 响应数据 |
| data.code | Integer | 状态码（0-通过, 405-不通过） |
| data.message | String | 状态信息 |
| data.ruleNo | String | 规则编号 |
| data.customer | String | 客户值 |
| data.level | Integer | 风险等级（0-无风险, 1-低, 2-中低, 3-中, 4-中高, 5-高） |
| data.cashRate | String | 现金比率 |
| data.riskFlag | Integer | 风险标志（0-无风险, 1-有风险） |

## 5. 调用示例

### 5.1 Java 调用示例

```java
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.dto.RiskLevelQueryRequest;
import com.ly.car.risk.process.dto.RiskResultDTO;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

public class RiskCheckExample {
    
    private static final String API_URL = "http://risk-service/riskCheck/queryRiskLevel";
    
    public static void main(String[] args) {
        // 创建请求对象
        RiskLevelQueryRequest request = new RiskLevelQueryRequest();
        request.setMemberId("12345678");
        request.setDeviceId("d12345678");
        request.setUserPhone("13800138000");
        request.setProductLine("YNC");
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 创建请求实体
        HttpEntity<RiskLevelQueryRequest> entity = new HttpEntity<>(request, headers);
        
        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        UiResult<RiskResultDTO> result = restTemplate.postForObject(API_URL, entity, UiResult.class);
        
        // 处理响应
        if (result.isSuccess()) {
            RiskResultDTO data = (RiskResultDTO) result.getData();
            System.out.println("风险等级: " + data.getLevel());
            System.out.println("风险标志: " + data.getRiskFlag());
        } else {
            System.out.println("请求失败: " + result.getMsg());
        }
    }
}
```

### 5.2 Python 调用示例

```python
import requests
import json

API_URL = "http://risk-service/riskCheck/queryRiskLevel"

# 创建请求对象
request = {
    "memberId": "12345678",
    "deviceId": "d12345678",
    "userPhone": "13800138000",
    "productLine": "YNC"
}

# 设置请求头
headers = {
    "Content-Type": "application/json"
}

# 发送请求
response = requests.post(API_URL, data=json.dumps(request), headers=headers)

# 处理响应
if response.status_code == 200:
    result = response.json()
    if result["success"]:
        data = result["data"]
        print(f"风险等级: {data['level']}")
        print(f"风险标志: {data['riskFlag']}")
    else:
        print(f"请求失败: {result['msg']}")
else:
    print(f"请求失败: {response.status_code}")
```

### 5.3 JavaScript 调用示例

```javascript
const API_URL = "http://risk-service/riskCheck/queryRiskLevel";

// 创建请求对象
const request = {
    memberId: "12345678",
    deviceId: "d12345678",
    userPhone: "13800138000",
    productLine: "YNC"
};

// 发送请求
fetch(API_URL, {
    method: "POST",
    headers: {
        "Content-Type": "application/json"
    },
    body: JSON.stringify(request)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        const data = result.data;
        console.log(`风险等级: ${data.level}`);
        console.log(`风险标志: ${data.riskFlag}`);
    } else {
        console.log(`请求失败: ${result.msg}`);
    }
})
.catch(error => {
    console.log(`请求异常: ${error}`);
});
```

### 5.4 cURL 调用示例

```bash
curl -X POST \
  http://risk-service/riskCheck/queryRiskLevel \
  -H 'Content-Type: application/json' \
  -d '{
    "memberId": "12345678",
    "deviceId": "d12345678",
    "userPhone": "13800138000",
    "productLine": "YNC"
}'
```

## 6. 请求示例

### 6.1 请求示例 1：通过用户ID查询

```json
{
  "memberId": "12345678",
  "productLine": "YNC",
  "channel": "APP"
}
```

### 6.2 请求示例 2：通过设备ID查询

```json
{
  "deviceId": "d12345678",
  "productLine": "YNC",
  "channel": "APP"
}
```

### 6.3 请求示例 3：通过手机号查询

```json
{
  "userPhone": "13800138000",
  "productLine": "YNC",
  "channel": "APP"
}
```

### 6.4 请求示例 4：通过多个参数查询

```json
{
  "memberId": "12345678",
  "unionId": "u12345678",
  "deviceId": "d12345678",
  "userPhone": "13800138000",
  "passengerCellphone": "13900139000",
  "productLine": "YNC",
  "channel": "APP"
}
```

## 7. 响应示例

### 7.1 无风险响应示例

```json
{
  "success": true,
  "errCode": 0,
  "msg": "请求成功",
  "data": {
    "code": 0,
    "message": "风控通过",
    "ruleNo": "",
    "customer": "",
    "level": 0,
    "cashRate": "1.0",
    "riskFlag": 0
  }
}
```

### 7.2 白名单响应示例

```json
{
  "success": true,
  "errCode": 0,
  "msg": "请求成功",
  "data": {
    "code": 0,
    "message": "风控通过",
    "ruleNo": "",
    "customer": "13800138000",
    "level": 0,
    "cashRate": "1.0",
    "riskFlag": 0
  }
}
```

### 7.3 黑名单响应示例

```json
{
  "success": true,
  "errCode": 0,
  "msg": "风控不通过",
  "data": {
    "code": 405,
    "message": "风控不通过",
    "ruleNo": "1001",
    "customer": "13800138000",
    "level": 5,
    "cashRate": "0.8",
    "riskFlag": 1
  }
}
```

### 7.4 异常响应示例

```json
{
  "success": false,
  "errCode": 500,
  "msg": "系统异常",
  "data": null
}
```

## 8. 错误码说明

| 错误码 | 描述 |
| --- | --- |
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 系统异常 |

## 9. 注意事项

1. **参数校验**：请求参数至少需要提供一个，用于查询风险名单。

2. **产品线**：如果不指定产品线，默认为"YNC"（网约车）。

3. **响应处理**：接口返回的 `success` 字段表示请求是否成功，而 `data.code` 字段表示风控是否通过。

4. **风险等级**：风险等级（`data.level`）的取值范围为 0-5，0 表示无风险，5 表示高风险。

5. **风险标志**：风险标志（`data.riskFlag`）的取值为 0 或 1，0 表示无风险，1 表示有风险。

6. **异常处理**：调用接口时需要做好异常处理，确保系统的稳定性。

## 10. 常见问题

### 10.1 接口返回成功，但风控不通过

这是正常情况，接口返回的 `success` 字段表示请求是否成功，而 `data.code` 字段表示风控是否通过。如果 `data.code` 为 405，表示风控不通过。

### 10.2 接口返回 500 错误

可能是系统异常，请检查请求参数是否正确，如果确认参数正确，请联系系统管理员。

### 10.3 接口返回 400 错误

请求参数错误，请检查请求参数是否符合要求。

### 10.4 如何判断用户是否有风险

可以通过 `data.riskFlag` 字段判断用户是否有风险，0 表示无风险，1 表示有风险。

### 10.5 如何获取具体的风险等级

可以通过 `data.level` 字段获取具体的风险等级，0 表示无风险，1-5 表示不同级别的风险。
