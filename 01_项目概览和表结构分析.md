# risk_customer_manage表业务流程分析 - 项目概览和表结构

## 1. 项目整体架构概览

### 1.1 项目模块结构
基于代码分析，该项目采用多模块架构：

```
car-risk/
├── car-risk-common/          # 公共模块
├── car-risk-manage/          # 管理端模块
└── car-risk-process/         # 业务处理模块
```

### 1.2 核心模块职责

#### car-risk-manage模块
- **职责**: 风控名单的管理端操作
- **主要功能**: 
  - 风控名单的增删改查
  - Excel批量导入导出
  - 名单审核管理
  - 操作记录管理

#### car-risk-process模块  
- **职责**: 风控业务逻辑处理
- **主要功能**:
  - 风控规则引擎
  - 名单匹配和过滤
  - 外部API接口
  - 定时任务处理

## 2. risk_customer_manage表结构分析

### 2.1 表字段结构（基于DTO分析）

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | Long | 自增主键 | 唯一标识 |
| risk_type | Integer | 风险类型 | 1-黑名单,2-白名单,3-禁止领券等 |
| customer_type | Integer | 客户类型 | 1-用户ID,2-手机号,6-车牌号等 |
| customer_value | String | 客户值 | 具体的标识值 |
| status | Integer | 状态 | 1-有效,2-无效,3-已清理 |
| ttl | Integer | 有效期天数 | -1永久,0自定义,其他为天数 |
| create_time | Date | 创建时间 | 记录创建时间 |
| update_time | Date | 更新时间 | 记录更新时间 |
| invalid_time | Date | 失效时间 | 名单失效的具体时间 |
| bind_user | String | 绑定用户 | 一对一名单的绑定手机号 |
| member_id | String | 会员ID | 关联的会员标识 |
| supplier_name | String | 供应商名称 | 所属供应商 |
| option_name | String | 操作人 | 操作人员信息 |
| option_type | Integer | 操作类型 | 1-系统,2-用户 |
| risk_remark | String | 拉黑原因 | 风险备注说明 |
| bind_order | String | 绑定订单 | 关联的订单号 |
| black_child_type | String | 拉黑子类型 | 黑名单细分类型 |
| create_user | String | 创建人 | 记录创建人 |
| driver_id | String | 司机ID | 关联的司机标识 |

### 2.2 风险类型枚举（RiskCustomerRiskTypeEnum）

```java
public enum RiskCustomerRiskTypeEnum {
    black_list(1, "黑名单"),                    // 全局黑名单
    white_list(2, "白名单"),                    // 全局白名单  
    ban_coupon_list(3, "禁止领券名单"),          // 营销限制
    ban_reward_list(4, "禁止奖励名单"),          // 奖励限制
    ban_send_list(5, "禁止派单名单"),            // 派单限制
    ban_receive_list(6, "禁止接单名单"),         // 接单限制
    ban_one_to_one_list(7, "一对一名单"),        // 一对一限制
    ban_register_list(8, "禁止认证名单"),        // 认证限制
    ban_ync_receive_list(9, "禁止网约车接单名单"), // 网约车限制
    ban_bus_create_order(10, "禁止汽车票下单名单"), // 汽车票限制
    ban_credit_auth_list(11, "禁止信用授权名单"),   // 信用限制
    tx_black_list(21,"腾讯黑名单"),              // 腾讯黑名单
    tx_ban_one_to_one_list(22, "腾讯一对一名单")  // 腾讯一对一
}
```

### 2.3 客户类型枚举（RiskCustomerCustomerTypeEnum）

```java
public enum RiskCustomerCustomerTypeEnum {
    user_id(1, "用户/ID"),           // 用户ID
    user_phone(2, "用户/手机号"),     // 用户手机号
    user_device_id(3, "用户/设备号"), // 设备标识
    user_unionid(4, "用户/unionid"), // 微信unionid
    user_pay_account(5, "用户/支付账号"), // 支付账号
    car_number(6, "司机/车牌号"),     // 车牌号
    hc_member_id(7,"司机/司机id"),    // 司机ID
    hc_phone(8,"司机/手机号"),        // 司机手机号
    hc_id_card(9,"司机/身份证号"),    // 司机身份证
    user_cert_no(10, "用户/证件号")   // 用户证件号
}
```

### 2.4 状态枚举

```java
public enum RiskCustomerStatusEnum {
    valid(1, "有效"),      // 名单有效
    invalid(2, "无效"),    // 名单无效
    cleared(3, "已清理")   // 已清理
}
```

## 3. 核心业务概念

### 3.1 名单类型说明

#### 3.1.1 黑名单（black_list）
- **作用**: 全局拦截，命中后直接拒绝服务
- **优先级**: 最高（除白名单外）
- **适用场景**: 恶意用户、风险用户的全面封禁

#### 3.1.2 白名单（white_list）  
- **作用**: 全局放行，命中后跳过所有风控检查
- **优先级**: 最高
- **适用场景**: VIP用户、内部测试用户

#### 3.1.3 一对一名单（ban_one_to_one_list）
- **作用**: 特定用户与特定司机的绑定限制
- **特点**: 需要bind_user字段配合使用
- **适用场景**: 特定用户与特定司机的冲突处理

#### 3.1.4 禁止类名单
- **禁止领券**: 限制营销活动参与
- **禁止接单**: 限制司机接单能力  
- **禁止派单**: 限制向司机派单
- **禁止认证**: 限制用户认证流程

### 3.2 有效期管理

#### 3.2.1 TTL字段说明
- **-1**: 永久有效
- **0**: 自定义时间（使用invalid_time字段）
- **其他正数**: 从创建时间开始的天数

#### 3.2.2 失效判断逻辑
```sql
-- 系统自动失效的数据查询
SELECT * FROM risk_customer_manage 
WHERE status = 1 
AND (
    (ttl != -1 AND ttl != 0 AND NOW() >= DATE_ADD(create_time, INTERVAL ttl DAY))
    OR 
    (ttl = 0 AND NOW() >= invalid_time)
)
```

## 4. 数据库设计特点

### 4.1 索引设计（推测）
基于查询SQL分析，应该存在以下索引：
- `idx_customer_type_value_risk_type`: (customer_type, customer_value, risk_type)
- `idx_invalid_time`: (invalid_time)  
- `idx_status`: (status)
- `idx_create_time`: (create_time)

### 4.2 数据一致性保证
- 通过status字段进行软删除
- 通过invalid_time进行时间维度的数据有效性控制
- 通过risk_customer_record表记录所有操作历史

### 4.3 业务规则约束
- 同一客户同一风险类型在有效期内不允许重复添加
- 白名单与黑名单互斥
- 禁止类名单与黑白名单互斥
- 一对一名单需要额外的bind_user约束

## 5. 关联表结构

### 5.1 risk_customer_record表
- **作用**: 记录所有对risk_customer_manage表的操作历史
- **关键字段**: 
  - customer_id: 关联主表ID
  - operate_type: 操作类型（1-新增,2-删除,3-修改）
  - operate_user: 操作人
  - remark: 操作备注

### 5.2 hc_customer表  
- **作用**: 司机端专用的风控名单表
- **与主表关系**: 业务上互补，技术上独立

## 6. 小结

risk_customer_manage表是整个风控系统的核心数据表，承载了多种类型的风控名单管理功能。表设计考虑了：

1. **多维度标识**: 支持用户ID、手机号、设备号、车牌号等多种客户标识
2. **多种风险类型**: 从全局黑白名单到细分的业务限制名单
3. **灵活的有效期管理**: 支持永久、自定义时间、相对天数等多种有效期设置
4. **完整的操作审计**: 通过record表记录所有变更历史
5. **软删除机制**: 通过状态字段实现数据的逻辑删除

下一步将详细分析各个业务模块的具体实现流程。
