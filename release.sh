# 自动完成release tag 并推送的远程服务器

tag_name="RELEASE"

if [ "$1" != "" ];then
  tag_name=$1
fi

date_str=`date "+%Y%m%d.%H%M"`
version="$tag_name-${date_str}"

GIT_TAG="git tag ${version}"
GIT_PUSH="git push origin ${version}"

echo "${GIT_TAG}"
`${GIT_TAG}`

if [ $? -ne 0 ];then
  echo "fail: git tag ${version}"
else
  echo "success: git tag ${version}"
  echo ""

  echo "${GIT_PUSH}"
  `${GIT_PUSH}`
  if [ $? -ne 0 ];then
    echo "fail: ${GIT_PUSH}"
  else
    echo "success: ${GIT_PUSH}"
  fi
fi