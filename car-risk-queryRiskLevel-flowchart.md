# `/riskCheck/queryRiskLevel` 接口流程图

## 1. 整体流程图

```mermaid
flowchart TD
    A[开始] --> B[接收请求参数]
    B --> C[设置默认产品线]
    C --> D[调用carRiskService.queryRiskLevel]
    D --> E{判断产品线}
    E -->|YNC| F[获取处理器]
    E -->|其他| G[返回默认结果]
    F --> H[构建上下文]
    H --> I[执行处理器]
    I --> J[转换结果]
    J --> K[返回结果]
    G --> K
    K --> L[结束]
```

## 2. 处理器执行流程图

```mermaid
flowchart TD
    A[开始] --> B[接收上下文参数]
    B --> C{是否在白名单中?}
    C -->|是| D[返回无风险]
    C -->|否| E{是否在黑名单中?}
    E -->|是| F[返回高风险]
    E -->|否| G{是否有下一个处理器?}
    G -->|是| H[执行下一个处理器]
    G -->|否| I[返回默认结果]
    D --> J[结束]
    F --> J
    H --> J
    I --> J
```

## 3. 数据流图

```mermaid
flowchart LR
    A[客户端] -->|请求| B[RiskCheckController]
    B -->|请求参数| C[CarRiskServiceImpl]
    C -->|获取处理器| D[YncConvertHandlerService]
    D -->|返回处理器| C
    C -->|构建上下文| D
    D -->|查询风险名单| E[RiskCustomerService]
    E -->|查询数据库| F[(risk_customer_manage表)]
    F -->|返回风险名单| E
    E -->|返回风险名单| D
    D -->|返回上下文| C
    C -->|执行处理器| G[RuleRisk1001Service]
    G -->|返回结果| C
    C -->|转换结果| D
    D -->|返回结果| C
    C -->|返回结果| B
    B -->|响应| A
```

## 4. 时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as RiskCheckController
    participant Service as CarRiskServiceImpl
    participant ConvertService as YncConvertHandlerService
    participant Handler as RuleRisk1001Service
    participant CustomerService as RiskCustomerService
    participant DB as risk_customer_manage表
    
    Client->>Controller: 发送请求
    Controller->>Controller: 设置默认产品线
    Controller->>Service: queryRiskLevel(request)
    Service->>ConvertService: getCheckRiskLevelHandler()
    ConvertService-->>Service: 返回处理器
    Service->>ConvertService: convertCheckRiskLevelCtx(request)
    ConvertService->>CustomerService: getListByValueByGroup(params, date)
    CustomerService->>DB: 查询风险名单
    DB-->>CustomerService: 返回风险名单
    CustomerService-->>ConvertService: 返回风险名单
    ConvertService->>ConvertService: 设置上下文属性
    ConvertService-->>Service: 返回上下文
    Service->>Handler: doHandler(context)
    Handler->>Handler: 检查是否在白名单中
    Handler->>Handler: 检查是否在黑名单中
    Handler-->>Service: 返回结果
    Service->>ConvertService: convertRiskFlag(result)
    ConvertService-->>Service: 返回转换后的结果
    Service-->>Controller: 返回结果
    Controller-->>Client: 返回响应
```

## 5. 组件图

```mermaid
flowchart TD
    A[客户端] --> B[RiskCheckController]
    B --> C[CarRiskService]
    C --> D[YncConvertHandlerService]
    D --> E[RiskCustomerService]
    E --> F[RiskCustomerManageMapper]
    F --> G[(risk_customer_manage表)]
    D --> H[FilterCheckPriceHandler]
    H --> I[RuleRisk1001Service]
    I --> J[RiskCustomerRiskTypeEnum]
    I --> K[RiskLevelEnum]
```

## 6. 状态图

```mermaid
stateDiagram-v2
    [*] --> 接收请求
    接收请求 --> 设置默认产品线
    设置默认产品线 --> 判断产品线
    判断产品线 --> 网约车产品线: YNC
    判断产品线 --> 其他产品线: 其他
    其他产品线 --> 返回默认结果
    网约车产品线 --> 获取处理器
    获取处理器 --> 构建上下文
    构建上下文 --> 查询风险名单
    查询风险名单 --> 执行处理器
    执行处理器 --> 检查白名单
    检查白名单 --> 返回无风险: 在白名单中
    检查白名单 --> 检查黑名单: 不在白名单中
    检查黑名单 --> 返回高风险: 在黑名单中
    检查黑名单 --> 执行下一个处理器: 不在黑名单中且有下一个处理器
    检查黑名单 --> 返回默认结果: 不在黑名单中且无下一个处理器
    执行下一个处理器 --> 检查白名单
    返回无风险 --> 转换结果
    返回高风险 --> 转换结果
    返回默认结果 --> 转换结果
    转换结果 --> 返回响应
    返回响应 --> [*]
```

## 7. 实体关系图

```mermaid
erDiagram
    RiskCustomerManage {
        bigint id PK
        int customer_type
        string customer_value
        int risk_type
        int status
        int ttl
        datetime create_time
        datetime update_time
        datetime invalid_time
        int option_type
        string create_user
        string option_name
        string risk_remark
        string bind_user
        string bind_order
        string supplier_name
        int black_type
        string member_id
    }
    
    RiskLevelQueryRequest {
        string memberId
        string unionId
        string deviceId
        string userPhone
        string passengerCellphone
        string productLine
        string channel
    }
    
    RiskResultDTO {
        int code
        string message
        string ruleNo
        string customer
        int level
        string cashRate
        object obj
        int riskFlag
    }
    
    FilterSceneContext {
        string orderId
        int mainScene
        int childScene
        list riskCustomerManageList
        UiResult uiResult
        string payAccount
        string memberId
        string unionId
        int isNewUser
        decimal esAmount
        map rateMap
        object specialCarRuleConfig
    }
    
    RiskLevelQueryRequest ||--o{ FilterSceneContext : "转换"
    RiskCustomerManage }o--o{ FilterSceneContext : "包含"
    FilterSceneContext ||--o{ RiskResultDTO : "生成"
```

## 8. 业务流程图

```mermaid
graph TD
    A[用户] -->|发起请求| B[业务系统]
    B -->|调用风控接口| C[风控系统]
    C -->|查询风险等级| D[/riskCheck/queryRiskLevel]
    D -->|查询风险名单| E[(risk_customer_manage表)]
    E -->|返回风险名单| D
    D -->|判断风险等级| F{是否有风险?}
    F -->|无风险| G[允许操作]
    F -->|有风险| H[拒绝操作]
    G -->|返回结果| B
    H -->|返回结果| B
    B -->|展示结果| A
```

## 9. 部署架构图

```mermaid
graph TD
    A[客户端] -->|HTTP请求| B[负载均衡]
    B -->|分发请求| C[应用服务器集群]
    C -->|查询数据| D[数据库集群]
    C -->|读取配置| E[配置中心]
    C -->|记录日志| F[日志系统]
    C -->|监控指标| G[监控系统]
```
