# Car-Risk 在线风控策略系统技术分析报告

## 1. 项目总览

### 1.1 项目架构
Car-Risk项目采用微服务架构，主要分为两个核心模块：
- **car-risk-process**: 对外风控处理服务，提供实时风控检测接口
- **car-risk-manage**: 风控策略管理后台，提供策略配置和管理功能

### 1.2 技术栈
- **框架**: Spring Boot + MyBatis Plus
- **数据库**: MySQL (分库分表)
- **缓存**: Redis + Redisson
- **消息队列**: Kafka + TurboMQ
- **脚本引擎**: Groovy (动态规则执行)
- **监控**: 自定义AOP监控

### 1.3 核心业务场景
系统支持多种风控场景：
- **用户下单风控** (场景2-3: 网约车下单, 场景2-2: 顺风车预订)
- **司机接单风控** (场景5-1: 司机接单, 场景5-2: 调度接单)
- **司机注册风控** (场景1-1: 司机注册)
- **司机认证风控** (场景1-2: 司机认证)
- **活动券领取风控** (场景6-1: 活动券领取)
- **订单完成风控** (场景8-1: 订单完成)
- **取消提醒风控** (场景7-1: 取消提醒)

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "外部调用方"
        A[网约车业务系统]
        B[顺风车业务系统]
        C[司机端APP]
        D[乘客端APP]
        E[运营管理系统]
    end
    
    subgraph "Car-Risk-Process 风控处理服务"
        F[RiskCheckController<br/>对外风控接口]
        G[RiskStrategyHandler<br/>策略处理器]
        H[BlackListHandler<br/>黑名单处理器]
        I[SpecialSceneHandler<br/>特殊场景处理器]
        J[RiskHitService<br/>命中记录服务]
    end
    
    subgraph "Car-Risk-Manage 管理后台"
        K[RiskStrategyController<br/>策略管理接口]
        L[RiskRuleController<br/>规则管理接口]
        M[RiskFieldController<br/>指标管理接口]
        N[RiskCustomerController<br/>名单管理接口]
    end
    
    subgraph "数据存储层"
        O[(风控策略库<br/>risk_strategy)]
        P[(风控规则库<br/>metric_rule)]
        Q[(风控指标库<br/>metric_field)]
        R[(风控名单库<br/>risk_customer_manage)]
        S[(命中记录库<br/>risk_hit)]
        T[(订单数据库<br/>order_info)]
    end
    
    subgraph "外部依赖"
        U[第三方风控API]
        V[标签服务]
        W[用户服务]
        X[订单服务]
    end
    
    A --> F
    B --> F
    C --> F
    D --> F
    E --> K
    E --> L
    E --> M
    E --> N
    
    F --> G
    G --> H
    G --> I
    G --> J
    
    G --> O
    G --> P
    G --> Q
    H --> R
    J --> S
    
    F --> U
    F --> V
    F --> W
    F --> X
    
    K --> O
    L --> P
    M --> Q
    N --> R
```

### 2.2 核心处理流程

风控系统的核心处理流程分为以下几个阶段：

1. **参数校验阶段**: 验证请求参数的完整性和合法性
2. **黑名单校验阶段**: 检查用户/司机是否在黑白名单中
3. **特殊场景处理阶段**: 处理特定业务场景的风控逻辑
4. **策略执行阶段**: 执行动态配置的风控策略
5. **结果处理阶段**: 根据风控结果执行相应的处置动作

## 3. 核心数据表结构

### 3.1 风控策略相关表

#### 3.1.1 metric_strategy (风控策略表)
```sql
CREATE TABLE `metric_strategy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) NOT NULL COMMENT '策略名称',
  `strategy_no` varchar(100) NOT NULL COMMENT '策略编号',
  `channels` varchar(500) DEFAULT NULL COMMENT '渠道多选 852-微信',
  `strategy_type` int(11) DEFAULT NULL COMMENT '策略分类',
  `level` int(11) DEFAULT NULL COMMENT '风险等级 1-低 2-中 3-高',
  `risk_type` varchar(100) DEFAULT NULL COMMENT '风险类型',
  `city_id` varchar(100) DEFAULT NULL COMMENT '城市id，0全国',
  `expression` text COMMENT '策略表达式',
  `script` text COMMENT '执行脚本',
  `description` text COMMENT '策略描述',
  `product_lines` varchar(200) DEFAULT NULL COMMENT '业务线：YNC-网约车 SFC-顺风车',
  `strategy_word` varchar(500) DEFAULT NULL COMMENT '策略返回文案',
  `control_time` int(11) DEFAULT NULL COMMENT '管控时间 单位天',
  `control_type` int(11) DEFAULT NULL COMMENT '管控对象 0-司机 1-用户',
  `hit_field` varchar(100) DEFAULT NULL COMMENT '命中字段',
  `hit_action` int(11) DEFAULT NULL COMMENT '命中动作0:加全局黑 1-加1v1',
  `dispose_action` int(11) DEFAULT NULL COMMENT '处置动作 0-禁止 1-增强校验 2-通过',
  `status` int(11) DEFAULT '1' COMMENT '运行状态 0-测试 1-上线运行 2-下线',
  `real_time` int(11) DEFAULT '1' COMMENT '0-离线 1-实时',
  `supplier_codes` varchar(500) DEFAULT NULL COMMENT '供应商编码',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_no` (`strategy_no`)
) COMMENT='风控策略配置表';
```

#### 3.1.2 metric_rule (风控规则表)
```sql
CREATE TABLE `metric_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `rule_no` varchar(100) NOT NULL COMMENT '规则编号',
  `description` text COMMENT '规则描述',
  `script` text COMMENT '规则脚本',
  `expression` text COMMENT '规则表达式',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(100) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_no` (`rule_no`)
) COMMENT='风控规则配置表';
```

#### 3.1.3 metric_field (风控指标表)
```sql
CREATE TABLE `metric_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `field_no` varchar(100) NOT NULL COMMENT '指标编号',
  `field_name` varchar(255) NOT NULL COMMENT '指标名称',
  `script` text COMMENT '指标计算脚本',
  `category` varchar(100) DEFAULT NULL COMMENT '指标分类',
  `type` varchar(100) DEFAULT NULL COMMENT '指标类型',
  `target` varchar(100) DEFAULT NULL COMMENT '指标目标',
  `based_current` int(11) DEFAULT '0' COMMENT '是否基于当前订单 0-否 1-是',
  `description` text COMMENT '指标描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_no` (`field_no`)
) COMMENT='风控指标配置表';
```

### 3.2 风控名单相关表

#### 3.2.1 risk_customer_manage (风控客户管理表)
```sql
CREATE TABLE `risk_customer_manage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_type` int(11) NOT NULL COMMENT '客户类型 1-用户ID 2-用户手机号 6-司机车牌号',
  `customer_value` varchar(255) NOT NULL COMMENT '客户值',
  `risk_type` int(11) NOT NULL COMMENT '风险类型 1-黑名单 2-白名单 7-一对一名单',
  `status` int(11) DEFAULT '1' COMMENT '状态 1-有效 2-失效 3-已删除',
  `ttl` int(11) DEFAULT '-1' COMMENT '有效期限 天数 -1表示永久',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `invalid_time` datetime DEFAULT NULL COMMENT '失效时间',
  `option_type` int(11) DEFAULT NULL COMMENT '操作类型 1-系统操作 2-人工操作',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `option_name` varchar(100) DEFAULT NULL COMMENT '操作人',
  `risk_remark` text COMMENT '风险备注',
  `bind_user` varchar(100) DEFAULT NULL COMMENT '绑定用户(一对一名单中的乘客手机号)',
  `bind_order` varchar(100) DEFAULT NULL COMMENT '绑定订单',
  `supplier_name` varchar(200) DEFAULT NULL COMMENT '供应商名称',
  PRIMARY KEY (`id`),
  KEY `idx_customer_type_value` (`customer_type`, `customer_value`),
  KEY `idx_risk_type_status` (`risk_type`, `status`),
  KEY `idx_invalid_time` (`invalid_time`)
) COMMENT='风控客户管理表';
```

### 3.3 关联关系表

#### 3.3.1 metric_scene_strategy_relation (场景策略关联表)
```sql
CREATE TABLE `metric_scene_strategy_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `scene_id` bigint(20) NOT NULL COMMENT '场景ID',
  `strategy_id` bigint(20) NOT NULL COMMENT '策略ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_scene_id` (`scene_id`),
  KEY `idx_strategy_id` (`strategy_id`)
) COMMENT='场景策略关联表';
```

#### 3.3.2 metric_strategy_rule_relation (策略规则关联表)
```sql
CREATE TABLE `metric_strategy_rule_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategy_id` bigint(20) NOT NULL COMMENT '策略ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_rule_id` (`rule_id`)
) COMMENT='策略规则关联表';
```

#### 3.3.3 metric_rule_field_relation (规则指标关联表)
```sql
CREATE TABLE `metric_rule_field_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `left_field_id` bigint(20) NOT NULL COMMENT '左侧指标ID',
  `operator` varchar(20) NOT NULL COMMENT '操作符 >, <, =, >=, <=',
  `right_type` varchar(20) NOT NULL COMMENT '右侧类型 CONST-常量 FIELD-指标',
  `right_value` varchar(500) NOT NULL COMMENT '右侧值',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_left_field_id` (`left_field_id`)
) COMMENT='规则指标关联表';
```

## 4. 下一步分析

本文档提供了Car-Risk系统的总体架构和核心数据表结构。接下来的文档将详细分析：

1. **对外风控接口的详细调用链路**
2. **管理端策略配置的完整流程**
3. **黑名单处理的核心逻辑**
4. **动态策略执行引擎的实现**
5. **风控命中记录和处置流程**
6. **各个数据表的读写入口分析**

每个模块都将包含详细的流程图、时序图和代码实现分析。
