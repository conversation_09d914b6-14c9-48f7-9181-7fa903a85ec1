# 风控名单管理系统分析 - Mapper 层

## RiskCustomerManageMapper 接口

`RiskCustomerManageMapper` 是风控名单管理系统的核心 Mapper 接口，负责与数据库交互，实现对 `RiskCustomerManage` 实体的增删改查操作。

### car-risk-manage 模块中的定义

```java
package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-10-09
 */
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {

    long getListTotal(Object query);

    List<RiskCustomerManage> getList(Object query);

    List<RiskCustomerManage> getListExport(Object query);

    RiskCustomerManage getByTypeAndValueAndRiskType(@Param("customerType") Integer customerType,
                                                  @Param("customerValue") String customerValue,
                                                  @Param("riskType") Integer riskType,
                                                  @Param("time")Date time
                                                  );

    RiskCustomerManage getByTypeAndValueAndRiskTypeAndUser(@Param("customerType") Integer customerType,
                                                  @Param("customerValue") String customerValue,
                                                  @Param("riskType") Integer riskType,
                                                  @Param("bindUser") String bindUser,
                                                  @Param("time")Date time
                                                  );

    /**
     * 清除数据为无效的数据和已过期数据
     * @return
     */
    int clearInvalid();

    int clearInvalidById(@Param("id")Long id);

    /**
     * 非1v1 重复数据
     * @return
     */
    List<RiskCustomerManage> getDupList();

    /**
     * 1v1重复数据
     * @return
     */
    List<RiskCustomerManage> getDup1v1List();
    
    List<RiskCustomerManage> getByCondition(Object query);
}
```

### car-risk-process 模块中的定义

```java
package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-10-09
 */
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {

    @Select("select "
            + "  * "
            + "from "
            + "  risk_customer_manage "
            + "where "
            + "  status = 1 "
            + "  and ( "
            + "    ( "
            + "      ttl != -1 "
            + "      and ttl != 0 "
            + "      and NOW() >= DATE_ADD(create_time, INTERVAL ttl day) "
            + "    ) "
            + "    or ( "
            + "      ttl = 0 "
            + "      and NOW() >= invalid_time "
            + "    ) "
            + "  )")
    List<RiskCustomerManage> selectInvalidData();

    List<RiskCustomerManage> getListByValue(@Param("params")FilterParams params, @Param("invalidTime")Date dateTime);

    List<RiskCustomerManage> getListByValueByGroup(@Param("params") CommonCustomerParam params, @Param("invalidTime")Date dateTime);

    long getValidCount(@Param("customerType") int customerType,
                     @Param("customerValue") String customerValue,
                     @Param("riskType") int riskType);

    List<RiskCustomerManage> queryAllValidRiskRecord(@Param("offset") int offset, @Param("limit") int limit);
}
```

## RiskCustomerManageMapper.xml 配置

`RiskCustomerManageMapper.xml` 是 `RiskCustomerManageMapper` 接口的 MyBatis 配置文件，定义了 SQL 语句。

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.manage.repo.risk.mapper.RiskCustomerManageMapper">

    <select id="getList" resultType="com.ly.car.risk.entity.RiskCustomerManage">
        select * from risk_customer_manage
        <include refid="getListCondition"/>
        order by id desc
        limit #{offset}, #{size}
    </select>

    <select id="getDupList" resultType="com.ly.car.risk.entity.RiskCustomerManage">
        select risk_type, customer_type, customer_value
        from risk_customer_manage
        WHERE status = 1 and risk_type != 7
        GROUP BY risk_type, customer_type, customer_value
        HAVING COUNT(*) > 1
    </select>

    <!-- 其他 SQL 语句 -->

    <update id="clearInvalid">
        update risk_customer_manage
        set update_time = CURRENT_TIMESTAMP,status = 3 ,option_type=1  ,option_name = '系统操作'
        where status=2 or invalid_time &lt; NOW()
    </update>

    <update id="clearInvalidById">
        update risk_customer_manage
        set update_time = CURRENT_TIMESTAMP,
            status      = 3,
            option_type=1,
            option_name = '系统操作'
        where id = #{id}
    </update>

    <select id="getByCondition" resultType="com.ly.car.risk.entity.RiskCustomerManage">
        select *
        from risk_customer_manage
        where customer_type = #{customerType}
          and customer_value = #{customerValue}
          and risk_type = #{riskType}
          and bind_user = #{bindUser}
          and member_id = #{memberId}
    </select>
</mapper>
```

## RiskCustomerRecordMapper 接口

`RiskCustomerRecordMapper` 是风控名单操作记录的 Mapper 接口，负责与数据库交互，实现对 `RiskCustomerRecord` 实体的增删改查操作。

```java
package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.manage.repo.risk.mapper.entity.RiskCustomerRecord;

import java.util.List;

public interface RiskCustomerRecordMapper extends BaseMapper<RiskCustomerRecord> {
    List<RiskCustomerRecord> getByCustomerId(Long customerId);
}
```

## HcCustomerMapper 接口

`HcCustomerMapper` 是汇川司机风控名单的 Mapper 接口，负责与数据库交互，实现对 `HcCustomer` 实体的增删改查操作。

```java
package com.ly.car.risk.manage.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.manage.controller.params.HcCustomerParams;
import com.ly.car.risk.manage.repo.risk.mapper.entity.HcCustomer;

import java.util.List;

public interface HcCustomerMapper extends BaseMapper<HcCustomer> {

    long total(Object query);

    List<HcCustomer> queryList(Object query);
    
    List<HcCustomer> export(Object query);
    
    List<HcCustomer> getByCondition(Object query);
}
```

## BaseMapper 接口

所有的 Mapper 接口都继承自 MyBatis-Plus 的 `BaseMapper` 接口，该接口提供了基本的 CRUD 操作方法。

```java
package com.baomidou.mybatisplus.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface BaseMapper<T> {
    /**
     * 插入一条记录
     *
     * @param entity 实体对象
     * @return 插入成功记录数
     */
    int insert(T entity);

    /**
     * 根据 ID 删除
     *
     * @param id 主键ID
     * @return 删除成功记录数
     */
    int deleteById(Serializable id);

    /**
     * 根据实体(ID)删除
     *
     * @param entity 实体对象
     * @return 删除成功记录数
     */
    int deleteById(T entity);

    /**
     * 根据 columnMap 条件，删除记录
     *
     * @param columnMap 表字段 map 对象
     * @return 删除成功记录数
     */
    int deleteByMap(@Param("cm") Map<String, Object> columnMap);

    /**
     * 根据 entity 条件，删除记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null,里面的 entity 用于生成 where 语句）
     * @return 删除成功记录数
     */
    int delete(@Param("ew") Wrapper<T> queryWrapper);

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表(不能为 null 以及 empty)
     * @return 删除成功记录数
     */
    int deleteBatchIds(@Param("coll") Collection<? extends Serializable> idList);

    /**
     * 根据 ID 修改
     *
     * @param entity 实体对象
     * @return 修改成功记录数
     */
    int updateById(@Param("et") T entity);

    /**
     * 根据 whereEntity 条件，更新记录
     *
     * @param entity        实体对象 (set 条件值,可以为 null)
     * @param updateWrapper 实体对象封装操作类（可以为 null,里面的 entity 用于生成 where 语句）
     * @return 修改成功记录数
     */
    int update(@Param("et") T entity, @Param("ew") Wrapper<T> updateWrapper);

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     * @return 实体
     */
    T selectById(Serializable id);

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表(不能为 null 以及 empty)
     * @return 实体集合
     */
    List<T> selectBatchIds(@Param("coll") Collection<? extends Serializable> idList);

    /**
     * 查询（根据 columnMap 条件）
     *
     * @param columnMap 表字段 map 对象
     * @return 实体集合
     */
    List<T> selectByMap(@Param("cm") Map<String, Object> columnMap);

    /**
     * 根据 entity 条件，查询一条记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @return 实体
     */
    T selectOne(@Param("ew") Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @return 满足条件记录数
     */
    Long selectCount(@Param("ew") Wrapper<T> queryWrapper);

    /**
     * 根据 entity 条件，查询全部记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @return 实体集合
     */
    List<T> selectList(@Param("ew") Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @return 字段映射对象 Map 集合
     */
    List<Map<String, Object>> selectMaps(@Param("ew") Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录
     * <p>注意： 只返回第一个字段的值</p>
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @return 字段映射对象集合
     */
    List<Object> selectObjs(@Param("ew") Wrapper<T> queryWrapper);

    /**
     * 根据 entity 条件，查询全部记录（并翻页）
     *
     * @param page         分页查询条件（可以为 RowBounds.DEFAULT）
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @return 实体分页对象
     */
    <E extends IPage<T>> E selectPage(E page, @Param("ew") Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录（并翻页）
     *
     * @param page         分页查询条件
     * @param queryWrapper 实体对象封装操作类
     * @return 字段映射对象 Map 分页对象
     */
    <E extends IPage<Map<String, Object>>> E selectMapsPage(E page, @Param("ew") Wrapper<T> queryWrapper);
}
```
