package com.ly.car.risk.process.risk.strategy.engine;

import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.risk.strategy.engine.executor.RuleExecutor;
import com.ly.car.risk.process.risk.strategy.engine.loader.StrategyLoader;
import com.ly.car.risk.process.risk.strategy.engine.model.RiskRuleDetail;
import com.ly.car.risk.process.risk.strategy.engine.model.RiskRuleResult;
import com.ly.car.risk.process.risk.strategy.engine.model.RiskStrategyDetail;
import com.ly.car.risk.process.risk.strategy.engine.model.RiskStrategyResult;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略执行引擎
 */
@Component
@Slf4j
public class StrategyExecutionEngine {
    
    private final StrategyLoader strategyLoader;
    private final RuleExecutor ruleExecutor;
    
    public StrategyExecutionEngine(StrategyLoader strategyLoader, RuleExecutor ruleExecutor) {
        this.strategyLoader = strategyLoader;
        this.ruleExecutor = ruleExecutor;
    }
    
    /**
     * 执行策略检查
     * 
     * @param request 统一风控检查请求
     * @return 策略执行结果
     */
    public StrategyExecutionResult execute(UnifyCheckRequest request) {
        try {
            // 1. 构建参数
            Map<String, Object> params = buildParams(request);
            
            // 2. 查找策略
            List<RiskStrategyDetail> strategies = findStrategies(request);
            
            if (CollectionUtils.isEmpty(strategies)) {
                log.info("未找到适用的策略");
                return StrategyExecutionResult.notHit();
            }
            
            // 3. 执行策略
            Map<String, RiskStrategyResult> strategyResults = executeStrategies(strategies, params);
            
            // 4. 处理结果
            return processResults(strategies, strategyResults);
        } catch (Exception e) {
            LoggerUtils.error(log, "策略执行异常", e);
            return StrategyExecutionResult.error("策略执行异常");
        }
    }
    
    /**
     * 构建参数
     * 
     * @param request 统一风控检查请求
     * @return 参数
     */
    private Map<String, Object> buildParams(UnifyCheckRequest request) {
        // 这里可以根据实际需求构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("scene", request.getScene());
        params.put("productLine", request.getProductLine());
        params.put("channel", request.getChannel());
        params.put("memberId", request.getMemberId());
        params.put("unionId", request.getUnionId());
        params.put("orderId", request.getOrderId());
        params.put("userPhone", request.getUserPhone());
        params.put("passengerPhone", request.getPassengerPhone());
        params.put("carNum", request.getCarNum());
        params.put("ip", request.getIp());
        params.put("supplierCode", request.getSupplierCode());
        params.put("supplierName", request.getSupplierName());
        params.put("deviceId", request.getDeviceId());
        params.put("ext", request.getExt());
        
        return params;
    }
    
    /**
     * 查找策略
     * 
     * @param request 统一风控检查请求
     * @return 策略列表
     */
    private List<RiskStrategyDetail> findStrategies(UnifyCheckRequest request) {
        return strategyLoader.loadStrategies(
            request.getProductLine(),
            request.getChannel(),
            request.getScene(),
            request.getSupplierCode()
        );
    }
    
    /**
     * 执行策略
     * 
     * @param strategies 策略列表
     * @param params 参数
     * @return 策略执行结果
     */
    private Map<String, RiskStrategyResult> executeStrategies(List<RiskStrategyDetail> strategies, Map<String, Object> params) {
        Map<String, RiskStrategyResult> results = new HashMap<>();
        
        for (RiskStrategyDetail strategy : strategies) {
            RiskStrategyResult result = executeStrategy(strategy, params);
            results.put(strategy.getStrategyNo(), result);
        }
        
        return results;
    }
    
    /**
     * 执行单个策略
     * 
     * @param strategy 策略
     * @param params 参数
     * @return 策略执行结果
     */
    private RiskStrategyResult executeStrategy(RiskStrategyDetail strategy, Map<String, Object> params) {
        List<RiskRuleDetail> rules = strategy.getRules();
        
        if (CollectionUtils.isEmpty(rules)) {
            return RiskStrategyResult.builder()
                .strategyId(strategy.getStrategyId())
                .strategyNo(strategy.getStrategyNo())
                .strategyMatched(false)
                .build();
        }
        
        // 执行规则
        Map<String, RiskRuleResult> ruleResults = executeRules(rules, params);
        
        // 判断策略是否命中
        boolean strategyMatched = evaluateStrategy(strategy, ruleResults);
        
        // 构建结果
        return RiskStrategyResult.builder()
            .strategyId(strategy.getStrategyId())
            .strategyNo(strategy.getStrategyNo())
            .strategyMatched(strategyMatched)
            .matchRules(ruleResults.values().stream()
                .filter(RiskRuleResult::isRuleMatched)
                .map(RiskRuleResult::getRuleNo)
                .collect(Collectors.toList()))
            .build();
    }
    
    /**
     * 执行规则
     * 
     * @param rules 规则列表
     * @param params 参数
     * @return 规则执行结果
     */
    private Map<String, RiskRuleResult> executeRules(List<RiskRuleDetail> rules, Map<String, Object> params) {
        Map<String, RiskRuleResult> results = new HashMap<>();
        
        for (RiskRuleDetail rule : rules) {
            RiskRuleResult result = ruleExecutor.execute(rule, params);
            results.put(rule.getRuleNo(), result);
        }
        
        return results;
    }
    
    /**
     * 评估策略是否命中
     * 
     * @param strategy 策略
     * @param ruleResults 规则执行结果
     * @return 是否命中
     */
    private boolean evaluateStrategy(RiskStrategyDetail strategy, Map<String, RiskRuleResult> ruleResults) {
        // 这里可以根据策略的脚本或表达式评估策略是否命中
        // 简单实现：只要有一个规则命中，策略就命中
        return ruleResults.values().stream().anyMatch(RiskRuleResult::isRuleMatched);
    }
    
    /**
     * 处理结果
     * 
     * @param strategies 策略列表
     * @param strategyResults 策略执行结果
     * @return 策略执行结果
     */
    private StrategyExecutionResult processResults(List<RiskStrategyDetail> strategies, Map<String, RiskStrategyResult> strategyResults) {
        // 过滤出命中的策略
        List<RiskStrategyDetail> matchedStrategies = strategies.stream()
            .filter(s -> {
                RiskStrategyResult result = strategyResults.get(s.getStrategyNo());
                return result != null && result.isStrategyMatched();
            })
            .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(matchedStrategies)) {
            return StrategyExecutionResult.notHit();
        }
        
        // 过滤掉测试策略
        List<RiskStrategyDetail> nonTestStrategies = matchedStrategies.stream()
            .filter(s -> s.getStatus() != 0)
            .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(nonTestStrategies)) {
            return StrategyExecutionResult.notHit("命中测试策略，风控策略通过");
        }
        
        // 按照风险等级排序，取最高等级的策略
        RiskStrategyDetail highestLevelStrategy = nonTestStrategies.stream()
            .sorted(Comparator.comparing(RiskStrategyDetail::getLevel).reversed())
            .findFirst()
            .orElse(null);
        
        if (highestLevelStrategy == null) {
            return StrategyExecutionResult.notHit();
        }
        
        // 如果策略的处置动作是通过，则返回通过
        if (highestLevelStrategy.getDisposeAction() == 2) {
            return StrategyExecutionResult.notHit("命中策略无需拦截");
        }
        
        // 构建结果
        RiskStrategyResult result = strategyResults.get(highestLevelStrategy.getStrategyNo());
        
        return StrategyExecutionResult.builder()
            .hit(true)
            .message(highestLevelStrategy.getStrategyWord() != null ? highestLevelStrategy.getStrategyWord() : "风控拦截")
            .strategyNo(highestLevelStrategy.getStrategyNo())
            .matchRules(result.getMatchRules())
            .matchedStrategies(nonTestStrategies.stream().map(RiskStrategyDetail::getStrategyNo).collect(Collectors.toList()))
            .hitField(highestLevelStrategy.getHitField())
            .controlTarget(highestLevelStrategy.getControlType() != null ? String.valueOf(highestLevelStrategy.getControlType()) : null)
            .disposeAction(highestLevelStrategy.getDisposeAction() != null ? String.valueOf(highestLevelStrategy.getDisposeAction()) : null)
            .build();
    }
}
