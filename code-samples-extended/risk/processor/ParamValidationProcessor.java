package com.ly.car.risk.process.risk.processor;

import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.core.AbstractProcessor;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.risk.RiskCheckContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;

/**
 * 参数校验处理器
 */
@Component
@Slf4j
public class ParamValidationProcessor extends AbstractProcessor<RiskCheckContext> {

    @Override
    protected void doProcess(RiskCheckContext context) {
        UnifyCheckRequest request = context.getRequest();

        // 初始化扩展字段
        if (null == request.getExt()) {
            request.setExt(new HashMap<>());
        }

        // 校验场景
        if (StringUtils.isBlank(request.getScene())) {
            throw new BizException(-1, "场景值不可为空");
        }

        // 校验业务线
        if (StringUtils.isBlank(request.getProductLine())) {
            throw new BizException(-1, "业务线不可为空");
        }

        // 萌艇业务线不校验渠道
        if (!Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
            if (StringUtils.isBlank(request.getChannel())) {
                throw new BizException(-1, "渠道不可为空");
            }
        }

        // 校验场景枚举
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        if (sceneEnum == null) {
            throw new BizException(-1, "场景值异常");
        }

        // 根据场景校验特定参数
        validateSceneParams(request, sceneEnum);

        log.info("参数校验通过");
    }

    /**
     * 根据场景校验特定参数
     *
     * @param request 请求
     * @param sceneEnum 场景枚举
     */
    private void validateSceneParams(UnifyCheckRequest request, StrategySceneEnum sceneEnum) {
        switch (sceneEnum) {
            case DRIVER_REGISTER:
                if (StringUtils.isBlank(request.getDeviceId())) {
                    throw new BizException(-1, "deviceId不可为空");
                }
                break;
            case USER_CREATE_ORDER:
            case USER_DISPATCHING_ORDER:
                if (StringUtils.isBlank(request.getMemberId())) {
                    throw new BizException(-1, "memberId不可为空");
                }
                break;
            case ACT_COUPON_LQ:
                if (StringUtils.isBlank(request.getMemberId()) && StringUtils.isBlank(request.getUnionId())) {
                    throw new BizException(-1, "memberId和unionId不可同时为空");
                }
                break;
            case CANCEL_REMINDER:
                if (StringUtils.isBlank(request.getOrderId())) {
                    throw new BizException(-1, "orderId不可为空");
                }
                break;
            default:
                // 其他场景不需要特殊校验
                break;
        }
    }

    @Override
    public boolean supports(RiskCheckContext context) {
        return true;
    }

    @Override
    public int getOrder() {
        return 100;
    }
}
