package com.ly.car.risk.process.risk.processor;

import com.alibaba.fastjson.JSON;
import com.ly.car.risk.process.core.AbstractProcessor;
import com.ly.car.risk.process.risk.RiskCheckContext;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 日志处理器，负责记录请求和响应日志
 */
@Component
@Slf4j
public class LoggingProcessor extends AbstractProcessor<RiskCheckContext> {
    
    @Override
    protected boolean preProcess(RiskCheckContext context) {
        LoggerUtils.info(log, "unifyCheck req:{}", JSON.toJSONString(context.getRequest()));
        return true;
    }
    
    @Override
    protected void doProcess(RiskCheckContext context) {
        // 不做任何处理
    }
    
    @Override
    protected void postProcess(RiskCheckContext context) {
        LoggerUtils.info(log, "unifyCheck resp:{}", JSON.toJSONString(context.getResult()));
    }
    
    @Override
    public boolean supports(RiskCheckContext context) {
        return true;
    }
    
    @Override
    public int getOrder() {
        return Integer.MIN_VALUE; // 最高优先级，确保最先执行
    }
}
