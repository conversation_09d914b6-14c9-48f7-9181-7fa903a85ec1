package com.ly.car.risk.process.risk.processor;

import com.ly.car.risk.process.core.AbstractProcessor;
import com.ly.car.risk.process.risk.RiskCheckContext;
import com.ly.car.risk.process.risk.strategy.blacklist.BlacklistCheckResult;
import com.ly.car.risk.process.risk.strategy.blacklist.BlacklistStrategy;
import com.ly.car.risk.process.risk.strategy.blacklist.BlacklistStrategyFactory;
import com.ly.car.risk.process.service.RiskHitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 黑名单检查处理器
 */
@Component
@Slf4j
public class BlacklistCheckProcessor extends AbstractProcessor<RiskCheckContext> {
    
    private final BlacklistStrategyFactory strategyFactory;
    private final RiskHitService riskHitService;
    
    public BlacklistCheckProcessor(BlacklistStrategyFactory strategyFactory, RiskHitService riskHitService) {
        this.strategyFactory = strategyFactory;
        this.riskHitService = riskHitService;
    }
    
    @Override
    protected boolean preProcess(RiskCheckContext context) {
        // 分销单的mid是批量虚拟账号，不校验mid相关，防止误伤
        if (context.getRequest().isDistributionFlag()) {
            context.getRequest().setMemberId("");
        }
        return true;
    }
    
    @Override
    protected void doProcess(RiskCheckContext context) {
        // 获取黑名单策略
        BlacklistStrategy strategy = strategyFactory.getStrategy(context.getRequest());
        
        log.info("使用黑名单策略: {}", strategy.getClass().getSimpleName());
        
        // 执行黑名单检查
        BlacklistCheckResult result = strategy.check(context.getRequest());
        
        // 处理结果
        if (result.isHit()) {
            log.info("命中黑名单: {}", result);
            
            context.setRiskResult(true, result.getMessage());
            context.setCustomerType(result.getCustomerType());
            
            // 记录命中信息
            context.getRequest().setHitType(1);
            context.getRequest().setHitRule(result.getRuleType());
            context.getRequest().setCustomerValue(result.getCustomerValue());
            context.getRequest().setCustomerType(result.getCustomerType());
            
            // 记录风控命中
            riskHitService.initHitRisk(context.getRequest(), context.getResult());
        } else {
            log.info("未命中黑名单");
        }
    }
    
    @Override
    public boolean supports(RiskCheckContext context) {
        return true;
    }
    
    @Override
    public int getOrder() {
        return 200;
    }
}
