package com.ly.car.risk.process.core;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 处理上下文基类
 */
@Data
public abstract class Context {
    
    /**
     * 处理状态
     */
    private ContextStatus status = ContextStatus.INIT;
    
    /**
     * 错误信息
     */
    private Exception error;
    
    /**
     * 上下文属性
     */
    private final Map<String, Object> attributes = new HashMap<>();
    
    /**
     * 获取属性
     * 
     * @param key 属性键
     * @param <T> 属性类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
    
    /**
     * 设置属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    /**
     * 是否继续处理
     * 
     * @return 是否继续处理
     */
    public boolean shouldContinue() {
        return status == ContextStatus.INIT || status == ContextStatus.PROCESSING;
    }
    
    /**
     * 设置错误
     * 
     * @param error 错误信息
     */
    public void setError(Exception error) {
        this.error = error;
        this.status = ContextStatus.ERROR;
    }
    
    /**
     * 设置成功
     */
    public void setSuccess() {
        this.status = ContextStatus.SUCCESS;
    }
    
    /**
     * 设置失败
     */
    public void setFail() {
        this.status = ContextStatus.FAIL;
    }
}
