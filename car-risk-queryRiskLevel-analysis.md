# `/riskCheck/queryRiskLevel` 接口业务处理流程分析

## 1. 接口概述

`/riskCheck/queryRiskLevel` 是一个风控查询接口，主要用于查询用户的风险等级。该接口位于 `RiskCheckController` 类中，通过传入用户相关信息（如用户ID、设备ID等），查询该用户是否在风险名单中，并返回相应的风险等级。

## 2. 接口定义

```java
@PostMapping("/queryRiskLevel")
public UiResult queryRiskLevel(@RequestBody RiskLevelQueryRequest request) {
    log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级入参:{}", JsonUtils.json(request));
    if (StringUtils.isBlank(request.getProductLine())) {
        request.setProductLine(ProductLineEnum.YNC.getCode());
    }
    UiResult<RiskResultDTO> result = carRiskService.queryRiskLevel(request);
    log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级出参:{}", JsonUtils.json(result));
    return result;
}
```

## 3. 请求参数

`RiskLevelQueryRequest` 类包含以下主要字段：

- `memberId`: 用户ID
- `unionId`: 用户统一ID
- `deviceId`: 设备ID
- `userPhone`: 用户手机号
- `passengerCellphone`: 乘客手机号
- `productLine`: 产品线（默认为"YNC"，即网约车）
- `channel`: 渠道

## 4. 业务处理流程

### 4.1 流程图

```mermaid
graph TD
    A[开始] --> B[接收请求参数]
    B --> C[设置默认产品线]
    C --> D[调用carRiskService.queryRiskLevel]
    D --> E[获取风险名单]
    E --> F{是否在白名单中?}
    F -->|是| G[返回无风险]
    F -->|否| H{是否在黑名单中?}
    H -->|是| I[返回高风险]
    H -->|否| J[返回无风险]
    I --> K[结束]
    G --> K
    J --> K
```

### 4.2 详细流程

1. **接收请求参数**：接收 `RiskLevelQueryRequest` 对象，包含用户ID、设备ID等信息。

2. **设置默认产品线**：如果请求中没有指定产品线，则默认设置为"YNC"（网约车）。

3. **调用服务层方法**：调用 `carRiskService.queryRiskLevel(request)` 方法进行风险等级查询。

4. **服务层处理**：在 `CarRiskServiceImpl` 类的 `queryRiskLevel` 方法中进行处理：
   
   ```java
   @Override
   public UiResult<RiskResultDTO> queryRiskLevel(RiskLevelQueryRequest request) {
       try {
           if (request.getProductLine().equals(ProductLineEnum.YNC.getCode())) {
               FilterCheckPriceHandler handler = yncConvertHandlerService.getCheckRiskLevelHandler();
               FilterSceneContext context = yncConvertHandlerService.convertCheckRiskLevelCtx(request);
               UiResult<RiskResultDTO> result = handler.doHandler(context);
               return yncConvertHandlerService.convertRiskFlag(result);
           }
           return UiResult.ok(new RiskResultDTO());
       } catch (Exception e) {
           log.error("[CarRiskServiceImpl][queryRiskLevel][][]查询风险等级异常", e);
           return UiResult.ok(new RiskResultDTO());
       }
   }
   ```

5. **获取处理器**：通过 `yncConvertHandlerService.getCheckRiskLevelHandler()` 获取处理器，该方法返回 `ruleRisk1001Service` Bean。

6. **构建上下文**：通过 `yncConvertHandlerService.convertCheckRiskLevelCtx(request)` 构建上下文对象 `FilterSceneContext`，包含以下步骤：
   - 创建 `CommonCustomerParam` 对象，设置用户ID、设备ID等信息
   - 调用 `riskCustomerService.getListByValueByGroup(cusParams, date)` 查询风险名单
   - 设置上下文的其他属性，如会员ID、统一ID等

7. **执行处理器**：调用 `handler.doHandler(context)` 执行处理器，即 `RuleRisk1001Service` 的 `doHandler` 方法：
   
   ```java
   @Override
   public UiResult doHandler(FilterSceneContext context) {
       UiResult result = context.getUiResult();
       result.setData(new RiskResultDTO());
       // 是否白名单，有一个直接返回，什么逻辑都不走
       RiskCustomerManage whiteManage = context.getRiskCustomerManageList().stream()
           .filter(e->e.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode()))
           .findFirst().orElse(null);
       if(whiteManage != null){
           result = UiResult.ok();
           RiskResultDTO dto = new RiskResultDTO(0,"风控通过","", whiteManage.getCustomerValue(),RiskLevelEnum.NO.getCode());
           dto.setCashRate("1.0");
           result.setData(dto);
           return result;
       }
       // 判断是否黑名单
       RiskCustomerManage blackManage = context.getRiskCustomerManageList().stream()
           .filter(e-> e.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode()))
           .findFirst().orElse(null);
       log.info("[RuleRisk1001Service][][][]1001查询黑名单:{}", JsonUtils.json(blackManage));
       if(blackManage != null){
           result = UiResult.ok();
           result.setMsg("风控不通过");
           RiskResultDTO dto = new RiskResultDTO(405,"风控不通过",ruleNo,blackManage.getCustomerValue(), RiskLevelEnum.HIGH.getCode());
           dto.setCashRate(context.getRateMap().get(ruleNo));
           result.setData(dto);
           return result;
       }
       if(this.nextHandler != null){
           return this.nextHandler.doHandler(context);
       }
       return result;
   }
   ```

8. **处理结果**：
   - 如果用户在白名单中，返回风险等级为0（无风险）
   - 如果用户在黑名单中，返回风险等级为5（高风险）
   - 如果用户既不在白名单也不在黑名单中，返回默认结果（无风险）

9. **转换结果**：通过 `yncConvertHandlerService.convertRiskFlag(result)` 转换结果，设置 `riskFlag` 字段。

10. **返回结果**：将处理结果返回给调用方。

## 5. 数据表分析

### 5.1 主要数据表

接口涉及的主要数据表是 `risk_customer_manage`，该表存储了风险名单信息。

#### 5.1.1 risk_customer_manage 表结构

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| id | Long | 主键ID |
| customer_type | Integer | 客户类型（1-用户ID, 2-用户手机号, 3-用户设备号, 4-用户unionID, 5-支付账号, 6-司机车牌号等） |
| customer_value | String | 客户值（对应的ID、手机号、设备号等） |
| risk_type | Integer | 风险类型（1-黑名单, 2-白名单, 3-禁止领券名单, 4-禁止奖励名单, 5-禁止派单名单, 6-禁止接单名单, 7-一对一名单等） |
| status | Integer | 状态（1-有效, 2-无效） |
| ttl | Integer | 有效期（-1-永久, 1-1天, 7-7天, 30-一个月, 365-一年） |
| create_time | Date | 创建时间 |
| update_time | Date | 更新时间 |
| invalid_time | Date | 失效时间 |
| option_type | Integer | 操作类型（1-系统操作, 2-人工操作, 3-用户操作） |
| create_user | String | 创建人 |
| option_name | String | 操作人 |
| risk_remark | String | 风险备注 |
| bind_user | String | 绑定用户（一对一名单使用） |
| bind_order | String | 绑定订单 |
| supplier_name | String | 供应商名称 |

### 5.2 SQL查询

查询风险名单的SQL语句（`RiskCustomerManageMapper.xml` 中的 `getListByValueByGroup` 方法）：

```sql
select * from risk_customer_manage where invalid_time > #{invalidTime} and (
    <trim prefixOverrides="or">
        <if test=" params.memberId != null and params.memberId != '' ">
            or customer_value = #{params.memberId}
        </if>
        <if test=" params.unionId != null and params.unionId != '' ">
            or customer_value = #{params.unionId}
        </if>
        <if test=" params.userPhone != null and params.userPhone != '' ">
            or customer_value = #{params.userPhone}
        </if>
        <if test=" params.passengerCellphone != null and params.passengerCellphone != '' ">
            or customer_value = #{params.passengerCellphone}
        </if>
        <if test=" params.driverCardNo != null and params.driverCardNo != '' ">
            or customer_value = #{params.driverCardNo}
        </if>
        <if test=" params.payAccount != null and params.payAccount != '' ">
            or customer_value = #{params.payAccount}
        </if>
        <if test=" params.deviceId != null and params.deviceId != '' ">
            or customer_value = #{params.deviceId}
        </if>
        <if test=" params.driverId != null and params.driverId != '' ">
            or customer_value = #{params.driverId}
        </if>
        <if test=" params.mobile != null and params.mobile != '' ">
            or customer_value = #{params.mobile}
        </if>
        <if test=" params.invitePhone != null and params.invitePhone != '' ">
            or customer_value = #{params.invitePhone}
        </if>
        <if test=" params.idCardNos != null and params.idCardNos.size >0 ">
            or ( customer_type = 10 and customer_value in
            <foreach collection="params.idCardNos" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
               )
        </if>
    </trim>
)
```

## 6. 数据来源入口

### 6.1 风险名单数据来源

风险名单数据（`risk_customer_manage` 表）的主要来源有：

1. **系统自动添加**：通过风控规则自动识别风险用户，调用 `RiskCustomerService.addRiskCustomer` 方法添加。

2. **人工操作添加**：通过管理后台手动添加风险用户，调用 `RiskCustomerService.add` 方法添加。

3. **外部系统同步**：通过外部系统（如ERP系统）同步风险用户，调用 `RiskCustomerService.syncDriverByErp` 方法添加。

### 6.2 调用链分析

1. **系统自动添加调用链**：
   - 风控规则触发 -> `DistributionRiskManageService.addByCustomerNoOrder` -> `RiskCustomerService.addRiskCustomer` -> 插入数据库

2. **人工操作添加调用链**：
   - 管理后台操作 -> `RiskCustomerController.add` -> `RiskCustomerService.add` -> 插入数据库

3. **外部系统同步调用链**：
   - 外部系统调用 -> `RiskDriverTask.syncDriverFromErp` -> `RiskCustomerService.syncDriverByErp` -> 插入数据库

## 7. 风险等级定义

风险等级在 `RiskLevelEnum` 枚举类中定义：

```java
public enum RiskLevelEnum {
    /**
     * 0-无风险 1-低 2-中低 3-中 4-中高 5-高
     */
    NO(0, "无风险"),
    LOW(1, "低"),
    MEDIUM_LOW(2, "中低"),
    MEDIUM(3, "中"),
    MEDIUM_HIGH(4, "中高"),
    HIGH(5, "高");
    
    private Integer code;
    private String msg;
    
    // 其他方法
}
```

## 8. 总结

`/riskCheck/queryRiskLevel` 接口的主要功能是查询用户的风险等级，通过查询 `risk_customer_manage` 表中的风险名单信息，判断用户是否在白名单或黑名单中，并返回相应的风险等级。

该接口的处理流程相对简单，主要包括以下步骤：
1. 接收请求参数
2. 查询风险名单
3. 判断是否在白名单或黑名单中
4. 返回风险等级

该接口是风控系统的重要组成部分，为其他业务系统提供风险等级查询服务，帮助业务系统识别风险用户，保障业务安全。
