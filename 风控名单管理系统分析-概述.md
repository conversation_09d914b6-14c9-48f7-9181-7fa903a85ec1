# 风控名单管理系统分析 - 概述

本文档是对风控名单管理系统中 `riskCustomerManageMapper.insert` 和 `riskCustomerManageMapper.update` 方法的业务调用流程分析。由于内容较多，我们将分析拆分为多个文件：

1. [风控名单管理系统分析-概述.md](风控名单管理系统分析-概述.md) - 本文件，提供总体概述
2. [风控名单管理系统分析-实体类定义.md](风控名单管理系统分析-实体类定义.md) - 相关实体类的定义
3. [风控名单管理系统分析-Mapper层.md](风控名单管理系统分析-Mapper层.md) - Mapper 层的定义和实现
4. [风控名单管理系统分析-car-risk-manage模块.md](风控名单管理系统分析-car-risk-manage模块.md) - car-risk-manage 模块中的调用流程
5. [风控名单管理系统分析-car-risk-process模块.md](风控名单管理系统分析-car-risk-process模块.md) - car-risk-process 模块中的调用流程
6. [风控名单管理系统分析-流程图.md](风控名单管理系统分析-流程图.md) - 主要流程的流程图
7. [风控名单管理系统分析-总结.md](风控名单管理系统分析-总结.md) - 总结和建议

## 系统概述

风控名单管理系统主要由两个模块组成：

1. **car-risk-manage**: 主要负责风控名单的管理功能，包括添加、删除、查询等操作，主要面向后台管理人员。
2. **car-risk-process**: 主要负责风控名单的处理和应用功能，包括黑名单检查、风险评估等，主要面向业务处理流程。

这两个模块都会使用 `riskCustomerManageMapper.insert` 和 `riskCustomerManageMapper.update` 方法来操作风控名单数据。

## 主要业务流程

风控名单管理系统的主要业务流程包括：

1. **添加风控名单**：通过后台管理界面或API接口添加风控名单，包括黑名单、白名单等。
2. **更新风控名单**：修改风控名单的状态、有效期等信息。
3. **删除风控名单**：将风控名单标记为无效或删除。
4. **查询风控名单**：根据条件查询风控名单信息。
5. **风控名单应用**：在业务流程中应用风控名单进行风险控制。

在接下来的文档中，我们将详细分析这些业务流程中 `riskCustomerManageMapper.insert` 和 `riskCustomerManageMapper.update` 方法的调用情况。
