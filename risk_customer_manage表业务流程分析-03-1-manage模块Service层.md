# risk_customer_manage表业务流程分析 - 03-1 car-risk-manage模块Service层

## 1. RiskCustomerService概述

car-risk-manage模块的RiskCustomerService是风控名单管理的核心业务服务，主要负责后台管理功能的业务逻辑处理。

### 1.1 服务职责

- 风控名单的CRUD操作
- Excel批量导入/导出功能
- 数据校验和业务规则处理
- 操作记录管理
- 数据脱敏处理

### 1.2 依赖注入

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
@Service
public class RiskCustomerService {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private HttpSession httpSession;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
}
````
</augment_code_snippet>

## 2. 核心业务方法分析

### 2.1 分页查询业务逻辑

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
public Pagination<RiskCustomerManageDto> getListPage(RiskCustomerListParams queryParams) {
    // 1. 设置失效时间查询条件
    if(queryParams.getStatus() != null){
        queryParams.setInvalidTime(DateUtil.date2String(new Date()));
    }
    
    // 2. 执行分页查询
    long total = riskCustomerManageMapper.getListTotal(queryParams);
    List<RiskCustomerManage> list = riskCustomerManageMapper.getList(queryParams);
    
    // 3. 查询操作记录
    List<Long> ids = list.stream().map(RiskCustomerManage::getId).collect(Collectors.toList());
    List<RiskCustomerRecord> records = new ArrayList<>();
    if(!CollectionUtils.isEmpty(ids)){
        records = riskCustomerRecordMapper.selectList(new QueryWrapper<RiskCustomerRecord>()
                .in("customer_id",ids));
        records = records.stream().filter(data->data.getOperateType()==2).collect(Collectors.toList());
    }
    
    // 4. 数据转换和脱敏处理
    List<RiskCustomerManageDto> manageList = list.stream().map(this::convertToDto).collect(Collectors.toList());
    
    return new Pagination<>(manageList, total, queryParams);
}
````
</augment_code_snippet>

**业务流程分析：**

1. **查询条件处理**：根据状态设置失效时间查询条件
2. **分页查询执行**：分别查询总数和分页数据
3. **关联数据查询**：查询相关的操作记录
4. **数据转换**：将实体转换为DTO并进行脱敏处理
5. **结果封装**：封装为分页结果返回

### 2.2 数据转换和脱敏逻辑

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private RiskCustomerManageDto convertToDto(RiskCustomerManage riskCustomerManage) {
    RiskCustomerManageDto dto = new RiskCustomerManageDto();
    BeanUtils.copyProperties(riskCustomerManage, dto);
    
    // 1. 设置失效时间显示逻辑
    dto.setUpdateTime(riskCustomerManage.getInvalidTime());
    
    // 2. 脱敏处理绑定用户手机号
    dto.setBindUser(DesensitizedUtil.mobilePhone(riskCustomerManage.getBindUser()));
    
    // 3. 枚举值转换为中文描述
    dto.setRiskType(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
    dto.setCustomerType(RiskCustomerCustomerTypeEnum.getMsgByCode(riskCustomerManage.getCustomerType()));
    dto.setStatus(RiskCustomerStatusEnum.getMsgByCode(riskCustomerManage.getStatus()));
    dto.setTtl(RiskCustomerTtlEnum.getMsgByCode(riskCustomerManage.getTtl()));
    dto.setOptionType(RiskCustomerOptionTypeEnum.getMsgByCode(riskCustomerManage.getOptionType()));
    
    // 4. 状态判断逻辑
    if(riskCustomerManage.getInvalidTime().after(new Date())){
        dto.setDisabled(false);
        dto.setUpdateTime(null);
        dto.setStatus(RiskCustomerStatusEnum.getMsgByCode(1));
    } else {
        dto.setDisabled(true);
        dto.setStatus(RiskCustomerStatusEnum.getMsgByCode(2));
    }
    
    // 5. 敏感数据脱敏
    if (Objects.equals(RiskCustomerCustomerTypeEnum.user_phone.getCode(), riskCustomerManage.getCustomerType())) {
        dto.setCustomerValue(DesensitizedUtil.mobilePhone(dto.getCustomerValue()));
    } else if (Objects.equals(RiskCustomerCustomerTypeEnum.user_cert_no.getCode(), riskCustomerManage.getCustomerType())) {
        dto.setCustomerValue(DesensitizedUtil.idCardNum(dto.getCustomerValue(), 4, 4));
    }
    
    return dto;
}
````
</augment_code_snippet>

## 3. 新增风控名单业务逻辑

### 3.1 添加方法核心逻辑

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
public Boolean add(RiskCustomerAddParams params) {
    // 1. 参数校验
    if(Objects.nonNull(params.getCustomerType()) && Objects.nonNull(params.getCustomerValue()) 
       && Objects.nonNull(params.getRiskType()) && Objects.nonNull(params.getTtl())){
        
        // 2. 获取当前用户信息
        UserInfo userInfo = (UserInfo) httpSession.getAttribute(SESSION_USER_INFO);

        // 3. 校验名单是否已存在
        matchExist(params.getRiskType(), params.getCustomerType(), params.getCustomerValue());
        
        // 4. 查询现有记录
        RiskCustomerManage entity = queryExistingRecord(params);
        
        // 5. 计算新的失效时间
        Date newInvalidTime = convertTime(params.getTtl());
        
        // 6. 处理更新或新增逻辑
        if(Objects.nonNull(entity)){
            return updateExistingRecord(entity, params, userInfo, newInvalidTime);
        } else {
            return insertNewRecord(params, userInfo);
        }
    }
    return false;
}
````
</augment_code_snippet>

### 3.2 重复数据校验逻辑

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private void matchExist(Integer riskType, Integer customerType, String customerValue) {
    // 1. 查询现有有效记录
    RiskCustomerManage existEntity = riskCustomerManageMapper.getByTypeAndValueAndRiskType(
        customerType, customerValue, riskType, new Date());
    
    // 2. 校验重复逻辑
    if(Objects.nonNull(existEntity)){
        // 检查是否为覆盖场景
        if(isOverrideScenario(riskType, existEntity.getRiskType())){
            // 允许覆盖的场景，如白名单覆盖黑名单
            return;
        } else {
            throw new CodeException(-1, "名单已存在，不允许重复添加");
        }
    }
}

private boolean isOverrideScenario(Integer newRiskType, Integer existRiskType) {
    // 白名单可以覆盖黑名单
    if(newRiskType.equals(RiskCustomerRiskTypeEnum.white_list.getCode()) 
       && existRiskType.equals(RiskCustomerRiskTypeEnum.black_list.getCode())){
        return true;
    }
    // 其他覆盖规则...
    return false;
}
````
</augment_code_snippet>

### 3.3 时间转换逻辑

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private Date convertTime(Integer ttl) {
    Date invalidTime;
    if(ttl.equals(RiskCustomerTtlEnum.forever.getCode())){
        // 永久有效：设置为2099年
        invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
    } else if(ttl.equals(RiskCustomerTtlEnum.custom.getCode())){
        // 自定义时间：由前端传入具体时间
        invalidTime = params.getInvalidTime();
    } else {
        // 按天数计算：当前时间 + ttl天
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, ttl);
        invalidTime = calendar.getTime();
    }
    return invalidTime;
}
````
</augment_code_snippet>

## 4. Excel批量操作业务逻辑

### 4.1 Excel导入流程

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
public Integer excelImport(MultipartFile file, UserInfo userInfo) {
    List<RiskCustomerExcelBean> dataList = new ArrayList<>();
    
    // 1. 读取Excel文件
    try {
        ExcelReader excelReader = EasyExcel.read(file.getInputStream(), RiskCustomerExcelBean.class, 
            new PageReadListener<RiskCustomerExcelBean>(dataList::addAll)).build();
        excelReader.read();
        excelReader.finish();
    } catch (IOException e) {
        throw new CodeException(-1, "文件读取失败");
    }
    
    // 2. 数据校验
    validateExcelData(dataList);
    
    // 3. 批量处理
    int successCount = 0;
    for (RiskCustomerExcelBean bean : dataList) {
        try {
            RiskCustomerAddParams params = convertExcelBeanToParams(bean);
            if(add(params)) {
                successCount++;
            }
        } catch (Exception e) {
            log.error("导入数据失败：{}", JsonUtils.json(bean), e);
        }
    }
    
    return successCount;
}
````
</augment_code_snippet>

### 4.2 Excel数据校验

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private void validateExcelData(List<RiskCustomerExcelBean> dataList) {
    if(CollectionUtils.isEmpty(dataList)) {
        throw new CodeException(-1, "Excel文件为空");
    }
    
    for (int i = 0; i < dataList.size(); i++) {
        RiskCustomerExcelBean bean = dataList.get(i);
        
        // 1. 必填字段校验
        if(StringUtils.isBlank(bean.getCustomerValue())) {
            throw new CodeException(-1, String.format("第%d行客户值不能为空", i + 2));
        }
        
        // 2. 枚举值校验
        if(!RiskCustomerCustomerTypeEnum.isValidCode(bean.getCustomerType())) {
            throw new CodeException(-1, String.format("第%d行客户类型无效", i + 2));
        }
        
        // 3. 格式校验
        if(bean.getCustomerType().equals(RiskCustomerCustomerTypeEnum.user_phone.getCode())) {
            if(!PhoneUtil.isValidPhone(bean.getCustomerValue())) {
                throw new CodeException(-1, String.format("第%d行手机号格式错误", i + 2));
            }
        }
        
        // 4. 业务规则校验
        validateBusinessRules(bean, i + 2);
    }
}
````
</augment_code_snippet>

## 5. 删除操作业务逻辑

### 5.1 单个删除逻辑

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
public Boolean delete(Long id, UserInfo userInfo, String delRemark) {
    // 1. 查询记录是否存在
    RiskCustomerManage entity = riskCustomerManageMapper.selectById(id);
    if(entity == null) {
        throw new CodeException(-1, "记录不存在");
    }
    
    // 2. 检查删除权限
    if(!hasDeletePermission(entity, userInfo)) {
        throw new CodeException(-1, "无删除权限");
    }
    
    // 3. 逻辑删除（更新状态）
    entity.setStatus(RiskCustomerStatusEnum.deleted.getCode());
    entity.setUpdateTime(new Date());
    entity.setOptionType(RiskCustomerOptionTypeEnum.user.getCode());
    entity.setOptionName(userInfo.getUsername() + userInfo.getWorkId());
    
    int result = riskCustomerManageMapper.updateById(entity);
    
    // 4. 记录删除操作
    if(result > 0) {
        recordDeleteOperation(entity, userInfo, delRemark);
        return true;
    }
    
    return false;
}
````
</augment_code_snippet>

### 5.2 删除操作记录

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private void recordDeleteOperation(RiskCustomerManage entity, UserInfo userInfo, String delRemark) {
    RiskCustomerRecord record = new RiskCustomerRecord();
    record.setCustomerId(entity.getId());
    record.setOperateType(RiskCustomerRecordOptionTypeEnum.delete.getCode());
    record.setCreateUser(entity.getCreateUser());
    record.setOperateUser(userInfo.getUsername() + userInfo.getWorkId());
    record.setCustomerType(entity.getCustomerType());
    record.setRemark(StringUtils.isNotBlank(delRemark) ? delRemark : "删除操作");
    record.setCreateTime(new Date());
    record.setCustomerValue(entity.getCustomerValue());
    
    riskCustomerRecordMapper.insert(record);
}
````
</augment_code_snippet>

## 6. 导出功能业务逻辑

### 6.1 数据导出流程

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
public List<RiskCustomerManageDto> exportList(RiskCustomerListParams query) {
    // 1. 设置导出限制
    query.setSize(50000); // 最大导出5万条
    
    // 2. 查询数据
    Pagination<RiskCustomerManageDto> result = this.getListPage(query);
    
    // 3. 数据脱敏处理
    if(!CollectionUtils.isEmpty(result.getList())){
        result.getList().forEach(dto -> {
            // 对导出数据进行额外脱敏
            dto.setBindUser(DesensitizedUtil.mobilePhone(dto.getBindUser()));
            // 可以根据导出权限进行不同级别的脱敏
            if(!hasFullExportPermission()) {
                dto.setCustomerValue(desensitizeForExport(dto.getCustomerValue(), dto.getCustomerType()));
            }
        });
    }
    
    return result.getList();
}
````
</augment_code_snippet>

## 7. 业务规则和校验

### 7.1 一对一名单特殊处理

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private RiskCustomerManage queryExistingRecord(RiskCustomerAddParams params) {
    RiskCustomerManage entity;
    
    // 一对一名单需要额外校验绑定用户
    if (params.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()) 
        || params.getRiskType().equals(RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list.getCode())) {
        
        entity = riskCustomerManageMapper.getByTypeAndValueAndRiskTypeAndUser(
            params.getCustomerType(), 
            params.getCustomerValue(), 
            params.getRiskType(), 
            params.getBindUser(), 
            new Date());
    } else {
        entity = riskCustomerManageMapper.getByTypeAndValueAndRiskType(
            params.getCustomerType(), 
            params.getCustomerValue(), 
            params.getRiskType(), 
            new Date());
    }
    
    return entity;
}
````
</augment_code_snippet>

### 7.2 有效期覆盖规则

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
private boolean shouldUpdateRecord(RiskCustomerManage existEntity, Date newInvalidTime) {
    // 1. 如果新的失效时间更早，不允许覆盖
    if(newInvalidTime.before(existEntity.getInvalidTime())){
        throw new CodeException(-1, "名单已存在，且现有有效期更长，不允许覆盖");
    }
    
    // 2. 如果新的失效时间更晚，允许延长有效期
    if(newInvalidTime.after(existEntity.getInvalidTime())){
        return true;
    }
    
    // 3. 如果失效时间相同，检查其他字段是否需要更新
    return hasOtherFieldsChanged(existEntity, params);
}
````
</augment_code_snippet>

## 8. 错误处理和日志记录

### 8.1 异常处理策略

```java
@Service
@Slf4j
public class RiskCustomerService {
    
    public Boolean add(RiskCustomerAddParams params) {
        try {
            // 业务逻辑处理
            return processAdd(params);
        } catch (CodeException e) {
            // 业务异常，直接抛出
            log.warn("添加风控名单业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            // 系统异常，记录日志并转换
            log.error("添加风控名单系统异常：{}", JsonUtils.json(params), e);
            throw new CodeException(-1, "系统异常，请稍后重试");
        }
    }
}
```

### 8.2 操作日志记录

```java
private void logOperation(String operation, Object params, Object result) {
    log.info("风控名单操作 - 操作类型：{}，参数：{}，结果：{}", 
        operation, JsonUtils.json(params), JsonUtils.json(result));
}
```

这个分析涵盖了car-risk-manage模块Service层的核心业务逻辑，接下来我将分析car-risk-process模块的Service层。
