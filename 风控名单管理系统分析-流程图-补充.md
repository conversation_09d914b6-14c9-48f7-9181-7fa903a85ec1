# 风控名单管理系统分析 - 流程图补充

本文档补充了一些重要的流程图，以便更好地理解 `RiskCustomerManageMapper` 方法的调用流程。

## 1. Excel 批量导入风控名单流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Mapper as RiskCustomerManageMapper
    participant RecordMapper as RiskCustomerRecordMapper
    participant DB as 数据库

    User->>Controller: 上传 Excel 文件
    Controller->>Service: excelImport(file, userInfo)
    Service->>Service: 解析 Excel 文件
    
    loop 遍历每一行数据
        Service->>Service: 构造 RiskCustomerAddParams
        Service->>Service: add(params)
        Service->>Mapper: 查询是否已存在
        Mapper->>DB: 查询数据库
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: 返回查询结果

        alt 已存在
            Service->>Mapper: updateById(entity)
            Mapper->>DB: 更新数据库
            DB-->>Mapper: 更新成功
            Mapper-->>Service: 返回更新结果
        else 不存在
            Service->>Mapper: insert(entity)
            Mapper->>DB: 插入数据库
            DB-->>Mapper: 插入成功
            Mapper-->>Service: 返回插入结果
        end

        Service->>RecordMapper: insert(record)
        RecordMapper->>DB: 插入操作记录
        DB-->>RecordMapper: 插入成功
        RecordMapper-->>Service: 返回插入结果
    end
    
    Service-->>Controller: 返回操作结果
    Controller-->>User: 返回操作结果
```

## 2. Excel 批量删除风控名单流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Mapper as RiskCustomerManageMapper
    participant RecordMapper as RiskCustomerRecordMapper
    participant DB as 数据库

    User->>Controller: 上传 Excel 文件
    Controller->>Service: excelDeleteImport(file, userInfo)
    Service->>Service: 解析 Excel 文件
    
    loop 遍历每一行数据
        Service->>Mapper: selectById(id)
        Mapper->>DB: 查询数据库
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: 返回查询结果

        alt 名单存在且有效
            Service->>Mapper: updateById(entity)
            Mapper->>DB: 更新数据库
            DB-->>Mapper: 更新成功
            Mapper-->>Service: 返回更新结果

            Service->>RecordMapper: insert(record)
            RecordMapper->>DB: 插入操作记录
            DB-->>RecordMapper: 插入成功
            RecordMapper-->>Service: 返回插入结果
        end
    end
    
    Service-->>Controller: 返回操作结果
    Controller-->>User: 返回操作结果
```

## 3. 同步司机黑名单流程图

```mermaid
sequenceDiagram
    participant System as 系统调用
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库

    System->>Controller: syncBlackDriver(params)
    Controller->>Service: syncDriver(params)
    
    alt 添加黑名单
        Service->>Mapper: selectOne(查询是否已存在)
        Mapper->>DB: 查询数据库
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: 返回查询结果

        alt 不存在
            Service->>Mapper: insert(entity)
            Mapper->>DB: 插入数据库
            DB-->>Mapper: 插入成功
            Mapper-->>Service: 返回插入结果
        else 已存在
            Service->>Mapper: updateById(entity)
            Mapper->>DB: 更新数据库
            DB-->>Mapper: 更新成功
            Mapper-->>Service: 返回更新结果
        end
    else 删除黑名单
        Service->>Mapper: selectOne(查询是否已存在)
        Mapper->>DB: 查询数据库
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: 返回查询结果

        alt 存在
            Service->>Mapper: updateById(entity)
            Mapper->>DB: 更新数据库
            DB-->>Mapper: 更新成功
            Mapper-->>Service: 返回更新结果
        end
    end
    
    Service-->>Controller: 返回操作结果
    Controller-->>System: 返回操作结果
```

## 4. 批量查询司机黑名单流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库

    Client->>Controller: /blackList/batchQueryDriverBlack
    Controller->>Service: batchQueryDriverBlack(request)
    
    loop 遍历车牌号列表
        Service->>Mapper: selectList(查询黑名单)
        Mapper->>DB: 查询数据库
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: 返回查询结果
        
        Service->>Service: 筛选有效黑名单
        Service->>Service: 筛选有效白名单
        Service->>Service: 排除白名单中的黑名单(在黑名单不在白名单的记录)
        Service->>Service: 组装黑名单信息，转换为CarBlackInfo对象
    end
    
    Service-->>Controller: 返回黑名单列表
    Controller-->>Client: 返回黑名单列表
```

## 5. 清除无效风控名单流程图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Controller as DataController
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库

    Admin->>Controller: clearInvalid()
    Controller->>Mapper: clearInvalid()
    Mapper->>DB: 更新数据库
    DB-->>Mapper: 更新成功
    Mapper-->>Controller: 返回更新结果
    Controller-->>Admin: 返回操作结果
```

## 6. 查询重复风控名单流程图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Controller as DataController
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库

    Admin->>Controller: getDupList()
    Controller->>Mapper: getDupList()
    Mapper->>DB: 查询数据库
    DB-->>Mapper: 返回查询结果
    Mapper-->>Controller: 返回查询结果
    Controller-->>Admin: 返回重复名单列表
```

## 7. 查询一对一重复风控名单流程图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Controller as DataController
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库

    Admin->>Controller: getDup1v1List()
    Controller->>Mapper: getDup1v1List()
    Mapper->>DB: 查询数据库
    DB-->>Mapper: 返回查询结果
    Mapper-->>Controller: 返回查询结果
    Controller-->>Admin: 返回一对一重复名单列表
```

## 8. 检查司机是否被拉黑流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库

    Client->>Controller: checkDriverIn(request)
    Controller->>Service: checkDriverIn(request)
    Service->>Mapper: selectList(查询是否被拉黑)
    Mapper->>DB: 查询数据库
    DB-->>Mapper: 返回查询结果
    Mapper-->>Service: 返回查询结果
    
    alt 存在拉黑记录
        Service-->>Controller: 返回 true
    else 不存在拉黑记录
        Service-->>Controller: 返回 false
    end
    
    Controller-->>Client: 返回检查结果
```
