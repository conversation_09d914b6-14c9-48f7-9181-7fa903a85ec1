# `/riskCheck/queryRiskLevel` 接口数据表分析

## 1. 数据表概述

`/riskCheck/queryRiskLevel` 接口主要涉及 `risk_customer_manage` 表，该表存储了风险名单信息，用于判断用户是否存在风险。

## 2. risk_customer_manage 表详解

### 2.1 表结构

| 字段名 | 类型 | 是否为空 | 默认值 | 描述 |
| --- | --- | --- | --- | --- |
| id | bigint(20) | NO | AUTO_INCREMENT | 主键ID |
| customer_type | int(11) | NO | | 客户类型（1-用户ID, 2-用户手机号, 3-用户设备号, 4-用户unionID, 5-支付账号, 6-司机车牌号等） |
| customer_value | varchar(255) | NO | | 客户值（对应的ID、手机号、设备号等） |
| risk_type | int(11) | NO | | 风险类型（1-黑名单, 2-白名单, 3-禁止领券名单, 4-禁止奖励名单, 5-禁止派单名单, 6-禁止接单名单, 7-一对一名单等） |
| status | int(11) | NO | 1 | 状态（1-有效, 2-无效） |
| ttl | int(11) | NO | | 有效期（-1-永久, 1-1天, 7-7天, 30-一个月, 365-一年） |
| create_time | datetime | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | NO | CURRENT_TIMESTAMP | 更新时间 |
| invalid_time | datetime | NO | | 失效时间 |
| option_type | int(11) | NO | | 操作类型（1-系统操作, 2-人工操作, 3-用户操作） |
| create_user | varchar(255) | YES | | 创建人 |
| option_name | varchar(255) | YES | | 操作人 |
| risk_remark | varchar(255) | YES | | 风险备注 |
| bind_user | varchar(255) | YES | | 绑定用户（一对一名单使用） |
| bind_order | varchar(255) | YES | | 绑定订单 |
| supplier_name | varchar(255) | YES | | 供应商名称 |
| black_type | int(11) | YES | | 黑名单类型 |
| member_id | varchar(255) | YES | | 会员ID |

### 2.2 索引设计

| 索引名 | 类型 | 字段 | 描述 |
| --- | --- | --- | --- |
| PRIMARY | PRIMARY KEY | id | 主键索引 |
| idx_customer_value | INDEX | customer_value | 客户值索引，用于快速查询特定客户值的记录 |
| idx_invalid_time | INDEX | invalid_time | 失效时间索引，用于查询有效的记录 |
| idx_status | INDEX | status | 状态索引，用于查询有效/无效的记录 |
| idx_risk_type | INDEX | risk_type | 风险类型索引，用于查询特定风险类型的记录 |
| idx_customer_type | INDEX | customer_type | 客户类型索引，用于查询特定客户类型的记录 |

### 2.3 数据示例

| id | customer_type | customer_value | risk_type | status | ttl | create_time | update_time | invalid_time | option_type | create_user | option_name | risk_remark | bind_user | bind_order | supplier_name | black_type | member_id |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | 2 | 13800138000 | 1 | 1 | 365 | 2023-01-01 00:00:00 | 2023-01-01 00:00:00 | 2024-01-01 00:00:00 | 1 | system | system | 风险用户 | | | | | |
| 2 | 3 | d12345678 | 2 | 1 | -1 | 2023-01-01 00:00:00 | 2023-01-01 00:00:00 | 2099-12-31 23:59:59 | 2 | admin | admin | 白名单用户 | | | | | |
| 3 | 6 | 粤B12345 | 7 | 1 | 30 | 2023-01-01 00:00:00 | 2023-01-01 00:00:00 | 2023-01-31 00:00:00 | 1 | system | system | 一对一黑名单 | 13900139000 | YNC123456 | | | |

## 3. 数据表关系

`/riskCheck/queryRiskLevel` 接口主要查询 `risk_customer_manage` 表，不涉及其他表的关联查询。

## 4. 数据流向

### 4.1 数据输入

接口接收 `RiskLevelQueryRequest` 对象，包含以下字段：
- `memberId`: 用户ID
- `unionId`: 用户统一ID
- `deviceId`: 设备ID
- `userPhone`: 用户手机号
- `passengerCellphone`: 乘客手机号
- `productLine`: 产品线
- `channel`: 渠道

### 4.2 数据查询

接口将请求参数转换为 `CommonCustomerParam` 对象，然后调用 `RiskCustomerService.getListByValueByGroup` 方法查询风险名单：

```sql
select * from risk_customer_manage where invalid_time > #{invalidTime} and (
    <trim prefixOverrides="or">
        <if test=" params.memberId != null and params.memberId != '' ">
            or customer_value = #{params.memberId}
        </if>
        <if test=" params.unionId != null and params.unionId != '' ">
            or customer_value = #{params.unionId}
        </if>
        <if test=" params.userPhone != null and params.userPhone != '' ">
            or customer_value = #{params.userPhone}
        </if>
        <if test=" params.passengerCellphone != null and params.passengerCellphone != '' ">
            or customer_value = #{params.passengerCellphone}
        </if>
        <if test=" params.driverCardNo != null and params.driverCardNo != '' ">
            or customer_value = #{params.driverCardNo}
        </if>
        <if test=" params.payAccount != null and params.payAccount != '' ">
            or customer_value = #{params.payAccount}
        </if>
        <if test=" params.deviceId != null and params.deviceId != '' ">
            or customer_value = #{params.deviceId}
        </if>
        <if test=" params.driverId != null and params.driverId != '' ">
            or customer_value = #{params.driverId}
        </if>
        <if test=" params.mobile != null and params.mobile != '' ">
            or customer_value = #{params.mobile}
        </if>
        <if test=" params.invitePhone != null and params.invitePhone != '' ">
            or customer_value = #{params.invitePhone}
        </if>
        <if test=" params.idCardNos != null and params.idCardNos.size >0 ">
            or ( customer_type = 10 and customer_value in
            <foreach collection="params.idCardNos" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
               )
        </if>
    </trim>
)
```

### 4.3 数据输出

接口返回 `UiResult<RiskResultDTO>` 对象，包含以下字段：
- `code`: 状态码（0-通过, 405-不通过）
- `message`: 状态信息
- `ruleNo`: 规则编号
- `customer`: 客户值
- `level`: 风险等级（0-无风险, 1-低, 2-中低, 3-中, 4-中高, 5-高）
- `cashRate`: 现金比率
- `riskFlag`: 风险标志（0-无风险, 1-有风险）

## 5. 数据表维护

### 5.1 数据插入

风险名单数据的插入主要通过以下方式：

1. **系统自动添加**：通过风控规则自动识别风险用户，调用 `RiskCustomerService.addRiskCustomer` 方法添加。

```java
public void addRiskCustomer(String customerValue, Integer riskType, Integer customerType, Integer ttl) {
    RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
    riskCustomerManage.setRiskType(riskType);
    riskCustomerManage.setCustomerType(customerType);
    riskCustomerManage.setCustomerValue(customerValue);
    riskCustomerManage.setStatus(1);
    riskCustomerManage.setTtl(ttl);
    riskCustomerManage.setOptionType(1);
    riskCustomerManage.setCreateUser("规则拉黑");
    riskCustomerManage.setOptionName("规则拉黑");
    riskCustomerManage.setRiskRemark("");
    riskCustomerManage.setCreateTime(new Date());
    riskCustomerManage.setUpdateTime(new Date());
    Date invalidTime = new Date();
    if (ttl == -1) {
        invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
    } else {
        invalidTime = DateUtil.addDay(new Date(), ttl);
    }
    riskCustomerManage.setInvalidTime(invalidTime);
    riskCustomerManageMapper.insert(riskCustomerManage);
}
```

2. **人工操作添加**：通过管理后台手动添加风险用户，调用 `RiskCustomerService.add` 方法添加。

3. **外部系统同步**：通过外部系统（如ERP系统）同步风险用户，调用 `RiskCustomerService.syncDriverByErp` 方法添加。

### 5.2 数据更新

风险名单数据的更新主要通过以下方式：

1. **状态更新**：修改风险名单的状态，如失效、删除等。

```java
public String invalid() {
    List<RiskCustomerManage> list = riskCustomerManageMapper.selectInvalidData();
    log.info("[RiskCustomerService][invalid][],[]需要失效的风控名单数据：{}", list.size());
    if (!CollectionUtils.isEmpty(list)) {
        int count = 0;
        for (RiskCustomerManage entity : list) {
            entity.setStatus(2);
            entity.setUpdateTime(new Date());
            entity.setInvalidTime(new Date());
            entity.setOptionType(1);
            riskCustomerManageMapper.updateById(entity);
            count++;
        }
        log.info("[RiskCustomerService][invalid][],[]需要失效的风控名单执行成功：{}", count);
    }
    return "success";
}
```

2. **有效期更新**：修改风险名单的有效期。

3. **风险类型升级**：将一对一黑名单升级为全局黑名单。

```java
@Scheduled(cron = "0 0 1 * * ?")
public void upgradeDriver() {
    log.info("[RiskCustomerService][upgradeDriver][][]开始升级黑名单司机");
    Date date = new Date();
    List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
            new QueryWrapper<RiskCustomerManage>().gt("invalid_time", date)
                    .eq("risk_type", 7)
    );
    // 升级逻辑...
}
```

### 5.3 数据删除

风险名单数据通常不会物理删除，而是通过修改状态或设置失效时间来逻辑删除。

## 6. 数据表优化建议

### 6.1 索引优化

1. **复合索引**：考虑为常用的查询条件创建复合索引，如 `(customer_value, risk_type, status, invalid_time)`，提高查询效率。

2. **分区表**：考虑按照时间范围（如按月）对表进行分区，提高大表的查询效率。

### 6.2 查询优化

1. **OR 条件优化**：当前查询使用多个 OR 条件，可能导致索引失效，可以考虑使用 UNION ALL 优化。

```sql
SELECT * FROM risk_customer_manage WHERE invalid_time > #{invalidTime} AND customer_value = #{params.memberId}
UNION ALL
SELECT * FROM risk_customer_manage WHERE invalid_time > #{invalidTime} AND customer_value = #{params.unionId}
UNION ALL
SELECT * FROM risk_customer_manage WHERE invalid_time > #{invalidTime} AND customer_value = #{params.userPhone}
-- 其他条件...
```

2. **缓存优化**：考虑对风险名单数据进行缓存，减少数据库查询次数。

### 6.3 表结构优化

1. **字段类型优化**：考虑使用更合适的字段类型，如将 `varchar(255)` 优化为更精确的长度。

2. **冗余字段优化**：考虑删除不必要的冗余字段，减少表的存储空间。

3. **历史数据归档**：考虑将过期的风险名单数据归档到历史表，减少主表的数据量。

## 7. 数据表监控

### 7.1 性能监控

1. **慢查询监控**：监控 `risk_customer_manage` 表的慢查询，及时优化。

2. **索引使用监控**：监控索引的使用情况，确保查询使用了合适的索引。

### 7.2 数据量监控

1. **表大小监控**：监控 `risk_customer_manage` 表的大小，及时进行数据归档。

2. **增长趋势监控**：监控表的增长趋势，预估未来的存储需求。

### 7.3 数据质量监控

1. **数据一致性监控**：监控数据的一致性，确保没有重复或冲突的记录。

2. **数据完整性监控**：监控数据的完整性，确保必要的字段都有值。

## 8. 总结

`/riskCheck/queryRiskLevel` 接口主要查询 `risk_customer_manage` 表，通过判断用户是否在风险名单中，返回相应的风险等级。该表的设计考虑了多种客户类型和风险类型，能够满足不同场景的风险检查需求。

为了提高查询效率，表设计了多个索引，但仍有优化空间，如创建复合索引、优化查询语句等。同时，为了保证数据的可靠性和安全性，应该加强数据表的监控和维护。
