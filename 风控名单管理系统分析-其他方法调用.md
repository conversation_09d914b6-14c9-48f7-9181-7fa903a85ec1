# 风控名单管理系统分析 - RiskCustomerManageMapper 其他方法调用

本文档分析了 `RiskCustomerManageMapper` 中除 `insert` 和 `update` 之外的其他方法的调用情况，包括 Controller 入口、URL 和调用链。

## 1. selectById 方法

### car-risk-manage 模块

#### RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| delete | `/riskListManage/riskCustomer/delete` | 删除风控名单 | Controller.delete -> Service.delete -> Mapper.selectById |
| detail | `/riskListManage/riskCustomer/detail` | 查询风控名单详情 | Controller.detail -> Service.detail -> Mapper.selectById |

### car-risk-process 模块

#### BlackDriverShieldController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| userRemove | `/blackDriver/userRemove` | 用户取消拉黑司机 | Controller.userRemove -> Service.userRemove -> Mapper.selectOne(使用 QueryWrapper) |

## 2. selectList 方法

### car-risk-manage 模块

#### RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| getList | `/riskListManage/riskCustomer/getList` | 查询风控名单列表 | Controller.getList -> Service.getListPage -> Mapper.getList |
| exportList | `/riskListManage/riskCustomer/export` | 导出风控名单列表 | Controller.exportList -> Service.exportList -> Mapper.getListExport |

### car-risk-process 模块

#### BlackDriverShieldController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| getList | `/blackDriver/getList` | 查询用户拉黑司机列表 | Controller.getList -> Service.getList -> Mapper.selectList |
| orderBlack | `/blackDriver/orderBlack` | 查询订单是否拉黑 | Controller.orderBlack -> Service.orderBlack -> Mapper.selectList |

#### BlackListApiController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| listDriver | `/blackList/driver/list` | 查询用户拉黑司机列表 | Controller.listDriver -> Service.listDriver -> Mapper.selectList |
| batchQueryDriverBlack | `/blackList/batchQueryDriverBlack` | 批量查询司机黑名单 | Controller.batchQueryDriverBlack -> Service.batchQueryDriverBlack -> Mapper.selectList |

#### AdminPortalController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| queryBlackDriver | `/admin/queryBlackDriver` | 查询黑名单司机 | Controller.queryBlackDriver -> Mapper.selectList |

## 3. selectOne 方法

### car-risk-manage 模块

#### HcCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/hcCustomer/add` | 添加汇川司机风控名单 | Controller.add -> Service.add -> Mapper.selectOne |

### car-risk-process 模块

#### BlackDriverShieldController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| userRemove | `/blackDriver/userRemove` | 用户取消拉黑司机 | Controller.userRemove -> Service.userRemove -> Mapper.selectOne |

## 4. getByTypeAndValueAndRiskType 方法

### car-risk-manage 模块

#### RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/riskCustomer/add` | 添加风控名单 | Controller.add -> Service.add -> Mapper.getByTypeAndValueAndRiskType |

## 5. getByTypeAndValueAndRiskTypeAndUser 方法

### car-risk-manage 模块

#### RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/riskCustomer/add` | 添加风控名单 | Controller.add -> Service.add -> Mapper.getByTypeAndValueAndRiskTypeAndUser |

## 6. getListTotal 方法

### car-risk-manage 模块

#### RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| getList | `/riskListManage/riskCustomer/getList` | 查询风控名单列表 | Controller.getList -> Service.getListPage -> Mapper.getListTotal |

## 7. getValidCount 方法

### car-risk-process 模块

#### BlackListService

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| existList | 内部方法 | 检查是否存在指定类型的名单 | Service.existList -> Mapper.getValidCount |
| existWhiteList | 内部方法 | 检查是否存在白名单 | Service.existWhiteList -> Service.existList -> Mapper.getValidCount |
| existBlackList | 内部方法 | 检查是否存在黑名单 | Service.existBlackList -> Service.existList -> Mapper.getValidCount |

## 8. selectInvalidData 方法

### car-risk-process 模块

#### RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| invalid | `/risk/customer/invalid` | 风控名单定时失效 | Controller.invalid -> Service.invalid -> Mapper.selectInvalidData |

## 9. getListByValue 方法

### car-risk-process 模块

#### CustomerHandler (策略处理器)

| 方法 | 调用场景 | 功能描述 | 调用链 |
|-----|---------|---------|-------|
| check | 风控检查 | 检查客户是否在风控名单中 | Handler.check -> Service.getListByValue -> Mapper.getListByValue |

## 10. getListByValueByGroup 方法

### car-risk-process 模块

#### CustomerHandler (策略处理器)

| 方法 | 调用场景 | 功能描述 | 调用链 |
|-----|---------|---------|-------|
| check | 风控检查 | 按组检查客户是否在风控名单中 | Handler.check -> Service.getListByValueByGroup -> Mapper.getListByValueByGroup |

## 11. clearInvalid 方法

### car-risk-manage 模块

#### DataController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| clearInvalid | `/data/clearInvalid` | 清除无效的风控名单 | Controller.clearInvalid -> Mapper.clearInvalid |

## 12. clearInvalidById 方法

### car-risk-manage 模块

#### DataController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| clearInvalidById | `/data/clearInvalidById` | 根据ID清除无效的风控名单 | Controller.clearInvalidById -> Mapper.clearInvalidById |

## 13. getDupList 和 getDup1v1List 方法

### car-risk-manage 模块

#### DataController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| getDupList | `/data/getDupList` | 获取重复的风控名单 | Controller.getDupList -> Mapper.getDupList |
| getDup1v1List | `/data/getDup1v1List` | 获取重复的一对一风控名单 | Controller.getDup1v1List -> Mapper.getDup1v1List |

## 14. queryAllValidRiskRecord 方法

### car-risk-process 模块

#### DataSyncController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| syncAllRiskRecord | `/sync/syncAllRiskRecord` | 同步所有有效的风控记录 | Controller.syncAllRiskRecord -> Service.syncAllRiskRecord -> Mapper.queryAllValidRiskRecord |
