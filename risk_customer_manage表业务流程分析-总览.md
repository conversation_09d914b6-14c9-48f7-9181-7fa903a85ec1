# risk_customer_manage表业务流程分析 - 总览

## 项目概述

car-risk项目是一个基于Spring Boot的风控系统，主要用于处理车辆相关业务的风险控制。该系统采用多模块架构设计，包含两个核心模块：

- **car-risk-manage**：管理层模块，负责风控名单的后台管理功能
- **car-risk-process**：处理层模块，负责风控名单的业务处理和应用

## risk_customer_manage表概述

`risk_customer_manage`表是风控系统的核心数据表，用于存储风控客户名单信息，包括黑名单、白名单、一对一名单等各种风控名单类型。

### 表结构字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 主键ID |
| customer_type | Integer | 客户类型（1-用户ID，2-用户手机号，6-司机车牌号等） |
| customer_value | String | 客户值（如车牌号、手机号等） |
| risk_type | Integer | 风险类型（1-黑名单，2-白名单，7-一对一名单等） |
| status | Integer | 状态（1-有效，2-失效，3-已删除） |
| ttl | Integer | 有效期限（天数，-1表示永久） |
| create_time | Date | 创建时间 |
| update_time | Date | 更新时间 |
| invalid_time | Date | 失效时间 |
| option_type | Integer | 操作类型（1-系统操作，2-人工操作，3-客户操作） |
| create_user | String | 创建人 |
| option_name | String | 操作人 |
| risk_remark | String | 风险备注（拉黑原因） |
| bind_user | String | 绑定用户（乘客手机号） |
| bind_order | String | 绑定订单 |
| supplier_name | String | 供应商名称 |

## 业务流程分类

基于代码分析，risk_customer_manage表的业务处理流程主要分为以下几大类：

### 1. 管理端操作流程（car-risk-manage模块）
- 手动添加风控名单
- 手动删除风控名单
- Excel批量导入/删除
- 名单查询和导出
- 汇川司机风控名单管理

### 2. 业务处理流程（car-risk-process模块）
- 风控规则引擎查询
- 定时失效处理
- 数据同步处理
- 规则自动拉黑
- 缓存更新处理

### 3. 外部接口调用流程
- 风控查询接口
- 营销风控查询
- 司机接单风控检查
- 用户下单风控检查

## 主要入口地址汇总

### car-risk-manage模块入口

| 功能 | HTTP方法 | URL | 说明 |
|------|----------|-----|------|
| 查询风控名单列表 | POST | `/riskListManage/riskCustomer/getList` | 分页查询风控名单 |
| 添加风控名单 | POST | `/riskListManage/riskCustomer/add` | 手动添加单个风控名单 |
| 删除风控名单 | POST | `/riskListManage/riskCustomer/delete` | 删除指定风控名单 |
| 导出风控名单 | POST | `/riskListManage/riskCustomer/export` | 导出风控名单Excel |
| Excel批量导入 | POST | `/riskListManage/riskCustomer/import` | 批量导入风控名单 |
| Excel批量删除 | POST | `/riskListManage/riskCustomer/deleteImport` | 批量删除风控名单 |
| 查询名单详情 | POST | `/riskListManage/riskCustomer/detail` | 查询单个名单详情 |

### car-risk-process模块入口

| 功能 | HTTP方法 | URL | 说明 |
|------|----------|-----|------|
| 风控名单定时失效 | POST | `/risk/customer/invalid` | 定时任务失效过期名单 |
| 名单初始化 | POST | `/risk/customer/initCustomer` | 初始化名单失效时间 |
| 同步司机黑名单 | POST | `/risk/customer/syncBlackDriver` | 同步司机黑名单数据 |
| 营销风控查询 | POST | `/marketingRisk/query` | 营销活动风控查询 |
| 风控能力查询 | POST | `/ability/queryAll` | 查询所有风控名单 |
| 黑名单查询 | POST | `/ability/queryBlack` | 查询黑名单信息 |

## 数据流向图

```mermaid
graph TB
    A[前端管理界面] --> B[car-risk-manage模块]
    C[业务系统] --> D[car-risk-process模块]
    E[定时任务] --> D
    F[外部系统] --> D
    
    B --> G[RiskCustomerManageMapper]
    D --> G
    
    G --> H[risk_customer_manage表]
    
    H --> I[风控规则引擎]
    H --> J[缓存系统Redis]
    H --> K[操作记录表]
```

## 核心业务场景

### 1. 风控名单管理场景
- 客服人员通过管理后台添加/删除风控名单
- 批量导入Excel文件进行名单维护
- 查询和导出风控名单数据

### 2. 风控检查场景
- 用户下单时检查是否在风控名单中
- 司机接单时检查是否在黑名单中
- 营销活动时检查用户风控状态

### 3. 自动化处理场景
- 定时任务自动失效过期名单
- 规则引擎自动拉黑风险用户
- 缓存自动更新和同步

## 文档结构说明

本分析报告将分为以下几个详细文档：

1. **实体类和枚举定义分析** - 详细分析相关实体类和枚举常量
2. **Mapper层数据访问分析** - 分析所有数据库操作方法
3. **Service层业务逻辑分析** - 分析核心业务处理逻辑
4. **Controller层接口分析** - 分析所有HTTP接口入口
5. **规则引擎集成分析** - 分析风控规则引擎如何使用名单数据
6. **定时任务和数据同步分析** - 分析自动化处理流程
7. **调用链路详细分析** - 分析完整的调用链路和数据流向

每个文档都将包含详细的代码分析、流程图和调用链说明，确保新加入的研发人员能够快速理解和上手项目。
