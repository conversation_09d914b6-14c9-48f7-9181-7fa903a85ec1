# car-risk项目 `/riskCheck/unifyCheck` 接口分析 - 第3部分：策略检查详解

## 1. 策略检查概述

策略检查是风控检查的核心，它通过执行一系列策略来判断请求是否存在风险。策略检查的主要步骤包括：

1. 构建上下文
2. 加密参数
3. 查找策略
4. 填充订单参数
5. 执行策略
6. 策略后处理

## 2. 策略检查详细流程

### 2.1 构建上下文 (buildContext)

构建上下文主要是将请求参数和其他必要信息封装到`StrategyContext`对象中：

```java
private StrategyContext buildContext(Map<String, Object> params, UnifyCheckRequest request) {
    StrategyContext strategyContext = new StrategyContext();
    strategyContext.setParams(params);
    strategyContext.setRequest(request);
    return strategyContext;
}
```

`StrategyContext`类的定义如下：

```java
@Data
public class StrategyContext {
    // 入参
    private Map<String, Object> params;
    // 响应
    private RiskSceneResult result;
    // 所要用到的策略集合
    private List<RiskStrategyDetail> strategyList;
    // 每个策略执行的结果
    private Map<String, RiskStrategyResult> strategyResultMap;
    // 入参，用于收集流程的信息
    private UnifyCheckRequest request;
}
```

### 2.2 加密参数 (encryptParam)

加密参数主要是对某些敏感字段进行加密处理，以便与数据库中的数据进行匹配：

```java
private void encryptParam(StrategyContext strategyContext) {
    // 不需要加解密操作
}
```

目前该方法为空实现，表示不需要加解密操作。

### 2.3 查找策略 (findStrategy)

查找策略主要是根据请求参数查找适用的风控策略：

```java
public void findStrategy(StrategyContext context) {
    Map<String, Object> params = context.getParams();
    // 1. 找到对应策略，策略里面包含了规则及指标
    String productLine = (String) params.get(PRODUCT_LINE);
    String channel = null == params.get(CHANNEL) ? StringUtils.EMPTY : (String) params.get(CHANNEL);
    String scene = (String) params.get(SCENE);
    String supplierCode = (String) params.get(SUPPLIER_CODE);
    List<RiskStrategyDetail> strategyList = strategyHelper.findStrategy(productLine, channel, scene, supplierCode);
    String strategyNos = strategyList.stream().map(RiskStrategyDetail::getStrategyNo).collect(Collectors.joining(","));
    LoggerUtils.info(log, "获取到对应策略:{} ", strategyNos);
    context.setStrategyList(strategyList);
}
```

`RiskStrategyHelper`的`findStrategy`方法实现如下：

```java
public List<RiskStrategyDetail> findStrategy(String productLine, String channel, String scene, String supplierCode) {
    List<RiskStrategyDetail> strategyList = riskStrategy.stream().filter(p -> p.getProductLine().contains(productLine)
                    && (StringUtils.isBlank(channel) || p.getChannels().contains(channel) || p.getChannels().contains("all"))
                    && Objects.equals(p.getScene(), scene))
            .collect(Collectors.toList());
    
    // 供应商为空不过滤
    if (StringUtils.isBlank(supplierCode)) {
        return strategyList;
    }
    
    // 精准过滤出策略才执行
    List<RiskStrategyDetail> supplierStrategylist = strategyList.stream().filter(s -> s.getSupplierCodes().contains(supplierCode)).collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(supplierStrategylist)) {
        return supplierStrategylist;
    }
    
    return strategyList;
}
```

### 2.4 填充订单参数 (fillOrderParam)

填充订单参数主要是为了支持基于当前订单的指标计算：

```java
private void fillOrderParam(StrategyContext strategyContext) {
    Map<String, Object> params = strategyContext.getParams();
    
    // 指标需要对当前订单校验，补充订单详情。
    // 需要限制调用次数：目前仅用于8-1
    if (CollectionUtils.isNotEmpty(strategyContext.getStrategyList())
            && StringUtils.isNotBlank((String) params.get(ORDER_ID))
            && Objects.equals(StrategySceneEnum.FINISH_ORDER.getScene(), strategyContext.getRequest().getScene())) {
        
        boolean basedOnCurrent = strategyContext.getStrategyList().stream().anyMatch(s -> s.getRules().stream().anyMatch(r -> r.getRiskFields().stream().anyMatch(f -> Objects.equals(f.getBasedCurrent(), 1))));
        if (basedOnCurrent) {
            CarOrderDetail orderDetail = carOrderService.queryOrderDetail((String) params.get(ORDER_ID));
            params.put(ORDER, orderDetail);
        }
    }
}
```

### 2.5 执行策略 (strategyHandle)

执行策略主要是对每个策略进行检查，判断是否命中：

```java
private void strategyHandle(StrategyContext context) {
    List<RiskStrategyDetail> strategyList = context.getStrategyList();
    if (CollUtil.isEmpty(strategyList)) {
        return;
    }
    Map<String, RiskStrategyResult> strategyResultMap = new HashMap<>();
    for (RiskStrategyDetail strategyDetail : strategyList) {
        RiskStrategyResult simpleStrategyResult = checkStrategy(strategyDetail, context.getParams());
        strategyResultMap.put(strategyDetail.getStrategyNo(), simpleStrategyResult);
    }
    LoggerUtils.info(log, "策略执行结果:{}", JSON.toJSONString(strategyResultMap));
    context.setStrategyResultMap(strategyResultMap);
}
```

`checkStrategy`方法实现如下：

```java
private RiskStrategyResult checkStrategy(RiskStrategyDetail strategyDetail, Map<String, Object> params) {
    String strategyScript = strategyDetail.getScript();
    List<RiskRuleDetail> rules = strategyDetail.getRules();
    if (CollUtil.isEmpty(rules)) {
        return RiskStrategyResult.builder().strategyMatched(false).build();
    }
    Map<String, Object> ruleDataMap = new HashMap<>();
    List<String> matchRules = new ArrayList<>();
    for (RiskRuleDetail rule : rules) {
        // 1.校验规则
        RiskRuleResult simpleRuleResult = checkRule(rule, params);
        LoggerUtils.info(log, "规则:{} 执行结果为:{}", rule.getRuleNo(), simpleRuleResult.getRuleMatched());
        // 2.收集规则结果
        ruleDataMap.put("rule" + rule.getRuleId(), simpleRuleResult.getRuleMatched());
        if (simpleRuleResult.getRuleMatched()) {
            matchRules.add(simpleRuleResult.getRuleNo());
        }
    }
    // 3.校验策略
    boolean strategyMatched = checkStrategyScript(strategyScript, ruleDataMap, strategyDetail.getStrategyNo());
    
    return RiskStrategyResult.builder()
            .strategyId(strategyDetail.getStrategyId())
            .strategyNo(strategyDetail.getStrategyNo())
            .strategyMatched(strategyMatched)
            .matchRules(matchRules).build();
}
```

`checkRule`方法实现如下：

```java
private RiskRuleResult checkRule(RiskRuleDetail rule, Map<String, Object> params) {
    String ruleScript = rule.getScript();
    List<RiskFieldDetail> riskFields = rule.getRiskFields();
    Map<String, Object> fieldDataMap = new HashMap<>();
    for (RiskFieldDetail riskField : riskFields) {
        String fieldScript = riskField.getScript();
        // 通过groovy脚本，从策略中获取值
        HashMap<String, Object> result = getFieldScriptResult(fieldScript, params, riskField.getFieldNo());
        String num = Optional.ofNullable(result.get("num")).map(String::valueOf).orElse("");
        double value;
        if (StringUtils.isBlank(num)) {
            value = 0.0;
        } else {
            value = new BigDecimal(num).setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
        fieldDataMap.put("field" + riskField.getFieldId(), value);
    }
    boolean ruleResult = checkRuleScript(ruleScript, fieldDataMap, rule.getRuleNo());
    return RiskRuleResult.builder().ruleId(rule.getRuleId()).ruleNo(rule.getRuleNo()).ruleMatched(ruleResult).build();
}
```

### 2.6 策略后处理 (strategyPostProcessing)

策略后处理主要是根据策略执行结果进行处理，包括记录命中信息、执行处置动作等：

```java
private void strategyPostProcessing(StrategyContext strategyContext) {
    // 响应
    RiskSceneResult result = new RiskSceneResult();
    strategyContext.setResult(result);
    
    Map<String, Object> params = strategyContext.getParams();
    // 先释放掉
    params.remove(ORDER);
    Map<String, RiskStrategyResult> strategyResultMap = strategyContext.getStrategyResultMap();
    if (CollUtil.isEmpty(strategyResultMap)) {
        result.setRiskFlag(false);
        result.setRiskMsg("风控策略通过");
        return;
    }
    List<Long> matchStrategyIds = strategyResultMap.values().stream().filter(p -> p.getStrategyMatched()).map(p -> p.getStrategyId()).collect(Collectors.toList());
    List<RiskStrategyDetail> strategyList = strategyContext.getStrategyList().stream()
            .filter(p -> matchStrategyIds.contains(p.getStrategyId())).collect(Collectors.toList());
    
    // 过滤掉用于测试的策略
    strategyList = strategyList.stream().filter(p -> p.getStatus() != 0).collect(Collectors.toList());
    if (CollUtil.isEmpty(strategyList)) {
        result.setRiskFlag(false);
        result.setRiskMsg("命中测试策略，风控策略通过");
        return;
    }
    
    // 走到这必定有值
    RiskStrategyDetail disposeStrategy = strategyList.stream().sorted(Comparator.comparing(RiskStrategyDetail::getLevel).reversed()).findFirst().get();
    
    List<String> matchedStrategyList = strategyList.stream().map(p -> p.getStrategyNo()).collect(Collectors.toList());
    result.setMatchedStrategy(matchedStrategyList);
    
    // 如果有命中策略，计入risk_hit中
    RiskStrategyResult disposeStrategyResult = strategyResultMap.get(disposeStrategy.getStrategyNo());
    UnifyCheckRequest request = strategyContext.getRequest();
    if (null != request && null != disposeStrategyResult) {
        Integer controlType = disposeStrategy.getControlType();
        String hitField = disposeStrategy.getHitField();
        Integer disposeAction = disposeStrategy.getDisposeAction();
        
        request.setHitType(0);
        request.setHitRule(String.join(",", disposeStrategyResult.getMatchRules()));
        request.setStrategyNos(disposeStrategyResult.getStrategyNo());
        request.setHitField(StringUtils.defaultString(hitField));
        request.setControlTarget(null == controlType ? "" : String.valueOf(controlType));
        request.setDisposeAction(null == disposeAction ? "" : String.valueOf(disposeAction));
        riskHitService.initHitRisk(request, strategyContext.getResult());
    }
    
    // 过滤掉通过的策略，如果没有策略了，说明命中的策略都是通过的
    strategyList = strategyList.stream().filter(p -> p.getDisposeAction() != 2).collect(Collectors.toList());
    if (CollUtil.isEmpty(strategyList)) {
        result.setRiskFlag(false);
        result.setRiskMsg("命中策略无需拦截");
        return;
    }
    
    result.setRiskFlag(true);
    result.setRiskMsg(StringUtils.isBlank(disposeStrategy.getStrategyWord()) ? "风控拦截" : disposeStrategy.getStrategyWord());
    
    // 根据命中策略，进行处置
    for (RiskStrategyDetail strategy : strategyList) {
        doStrategyAction(params, strategy);
    }
}
```

`doStrategyAction`方法实现如下：

```java
private void doStrategyAction(Map<String, Object> params, RiskStrategyDetail disposeStrategy) {
    if (StringUtils.isBlank(disposeStrategy.getHitField()) || null == disposeStrategy.getControlTime() || disposeStrategy.getControlTime() == 0) {
        return;
    }
    Integer controlTime = disposeStrategy.getControlTime();
    Integer riskType = disposeStrategy.getHitAction();
    String hitField = disposeStrategy.getHitField();
    String customerValue = null;
    Integer customerType = null;
    String orderId = (String) params.getOrDefault(ORDER_ID, StringUtils.EMPTY);
    String productLine = (String) params.getOrDefault(PRODUCT_LINE, StringUtils.EMPTY);
    String passengerPhone = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
    String supplierName = (String) params.getOrDefault(SUPPLIER_NAME, StringUtils.EMPTY);
    
    if (Objects.equals(hitField, "driverCardNo")) {
        customerValue = (String) params.getOrDefault(CAR_NUM, StringUtils.EMPTY);
        customerType = RiskCustomerCustomerTypeEnum.driver_card_no.getCode();
    } else if (Objects.equals(hitField, "phone")) {
        customerValue = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
        customerType = RiskCustomerCustomerTypeEnum.user_phone.getCode();
    }
    //处理拉黑类型
    Integer resultDisposeType = 7;//默认是1v1
    if (riskType == 0) {
        resultDisposeType = 1;//如果命中动作是0全局拉黑的情况
    }
    
    // 萌艇不落名单
    if (!Objects.equals(StringUtils.upperCase(productLine), "MT")) {
        disposeCenterService.actionCustomer(customerValue, passengerPhone, controlTime
                , orderId, disposeStrategy.getStrategyNo(), resultDisposeType, customerType, supplierName);
    }
}
```

## 3. 策略检查后处理 (afterStrategyCheck)

策略检查后的处理主要是根据特定场景进行一些额外的操作：

```java
private void afterStrategyCheck(UnifyCheckRequest request, RiskSceneResult riskSceneResult) {
    // MT 且 司机认证
    if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())
            && Objects.equals(request.getScene(), StrategySceneEnum.DRIVER_AUTHENTICATION.getScene())) {
        // 非风险则通知MT 司机认证成功
        if (null != riskSceneResult && !riskSceneResult.isRiskFlag()) {
            RiskResultNewDTO dto = new RiskResultNewDTO();
            Map<String, Object> data = new HashMap<>();
            data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
            data.put("idCard", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO));
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
        }
    }
}
```

在下一部分中，我们将详细分析风控策略的数据模型和规则引擎的实现。
