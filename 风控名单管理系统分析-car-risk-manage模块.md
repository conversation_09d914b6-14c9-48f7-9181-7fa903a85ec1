# 风控名单管理系统分析 - car-risk-manage 模块

## 模块概述

`car-risk-manage` 模块主要负责风控名单的管理功能，包括添加、删除、查询等操作，主要面向后台管理人员。该模块中有多个 Controller 和 Service 类使用了 `riskCustomerManageMapper.insert` 和 `riskCustomerManageMapper.update` 方法。

## Controller 层

### 1. RiskCustomerController

`RiskCustomerController` 是风控名单管理的主要控制器，提供了添加、删除、查询等接口。

```java
@RestController
@RequestMapping("/riskListManage/riskCustomer")
public class RiskCustomerController {

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;

    @PostMapping("/getList")
    public Pagination<RiskCustomerManageDto> getList(@RequestBody RiskCustomerListParams query) {
        return riskCustomerService.getListPage(query);
    }

    @PostMapping("/export")
    public String exportList(@RequestBody RiskCustomerListParams query) {
        // 导出风控名单列表
        // ...
    }

    @PostMapping("/add")
    public Boolean add(@RequestBody RiskCustomerAddParams params) {
        return riskCustomerService.add(params);
    }

    @PostMapping("/delete")
    public Boolean delete(@RequestBody BaseEntityParams req, UserInfo userInfo) {
        return riskCustomerService.delete(req.getId(), userInfo, req.getDelRemark());
    }

    /**
     * excel批量添加风控名单
     */
    @PostMapping("/import")
    public Integer excelImport(MultipartFile file, UserInfo userInfo) {
        return riskCustomerService.excelImport(file, userInfo);
    }

    // 其他接口...
}
```

### 2. HcCustomerController

`HcCustomerController` 是汇川司机风控名单管理的控制器，提供了添加、删除、查询等接口。

```java
@RequestMapping("riskListManage/hcCustomer")
@RestController
public class HcCustomerController {

    @Resource
    private HcCustomerService hcCustomerService;
    @Resource
    private SupplierInfoMapper supplierInfoMapper;

    @RequestMapping("add")
    public String add(@RequestBody HcCustomerParams params, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        this.hcCustomerService.add(params, userInfo);
        return "success";
    }

    @RequestMapping("delete")
    public String delete(@RequestBody HcCustomerParams params, HttpServletRequest request) {
        UserInfo userInfo = (UserInfo) request.getSession().getAttribute(SESSION_USER_INFO);
        this.hcCustomerService.delete(params, userInfo);
        return "success";
    }

    // 其他接口...
}
```

## Service 层

### 1. RiskCustomerService

`RiskCustomerService` 是风控名单管理的主要服务类，实现了添加、删除、查询等业务逻辑。

```java
@Service
@Slf4j
public class RiskCustomerService {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    // 其他依赖...

    /**
     * 添加风控名单
     */
    public Boolean add(RiskCustomerAddParams params) {
        // 参数校验
        if (params != null) {
            // 校验名单是否已存在，已存在的不允许重复添加（风险名单覆盖逻辑校验）
            matchExist(params.getRiskType(), params.getCustomerType(), params.getCustomerValue());
            RiskCustomerManage entity;
            if (params.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()) || params.getRiskType().equals(RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list.getCode())) {
                entity = riskCustomerManageMapper.getByTypeAndValueAndRiskTypeAndUser(params.getCustomerType(), params.getCustomerValue(), params.getRiskType(), params.getBindUser(), new Date());
            } else {
                entity = riskCustomerManageMapper.getByTypeAndValueAndRiskType(params.getCustomerType(), params.getCustomerValue(), params.getRiskType(), new Date());
            }
            Date newInvalidTime = convertTime(params.getTtl());
            //  需要校验有效期，保留有效期最长的一条数据
            if (Objects.nonNull(entity)) {
                if (newInvalidTime.before(entity.getInvalidTime())) {
                    //名单已存在，不允许重复添加
                    throw new CodeException(-1, "名单已存在，不允许重复添加");
                }
                entity.setCustomerValue(params.getCustomerValue());
                entity.setCustomerType(params.getCustomerType());
                entity.setRiskRemark(params.getRiskRemark());
                entity.setTtl(params.getTtl());
                entity.setRiskType(params.getRiskType());
                entity.setOptionType(RiskCustomerOptionTypeEnum.user.getCode());
                entity.setOptionName(userInfo.getUsername() + userInfo.getWorkId());
                entity.setStatus(RiskCustomerStatusEnum.valid.getCode());
                entity.setInvalidTime(convertTime(params.getTtl()));
                entity.setUpdateTime(new Date());
                entity.setBindUser(StringUtils.defaultString(params.getBindUser()));
                entity.setMemberId(StringUtils.defaultString(params.getMemberId()));
                entity.setSupplierName(StringUtils.defaultString(params.getSupplierName()));
                riskCustomerManageMapper.updateById(entity);

                RiskCustomerRecord record = new RiskCustomerRecord();
                record.setCustomerId(entity.getId());
                record.setOperateType(2); // 修改
                record.setCreateUser(entity.getCreateUser());
                record.setOperateUser(userInfo.getUsername() + userInfo.getWorkId());
                record.setCustomerType(params.getCustomerType());
                record.setRemark(params.getRiskRemark());
                record.setCreateTime(new Date());
                record.setCustomerValue(params.getCustomerValue());
                riskCustomerRecordMapper.insert(record);
            } else {
                entity = new RiskCustomerManage();
                entity.setCustomerValue(params.getCustomerValue());
                entity.setCustomerType(params.getCustomerType());
                entity.setRiskRemark(params.getRiskRemark());
                entity.setTtl(params.getTtl());
                entity.setRiskType(params.getRiskType());
                entity.setOptionType(RiskCustomerOptionTypeEnum.user.getCode());
                entity.setOptionName(userInfo.getUsername() + userInfo.getWorkId());
                entity.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
                entity.setStatus(RiskCustomerStatusEnum.valid.getCode());
                entity.setInvalidTime(convertTime(params.getTtl()));
                entity.setCreateTime(new Date());
                entity.setUpdateTime(new Date());
                entity.setBindUser(StringUtils.defaultString(params.getBindUser()));
                entity.setMemberId(StringUtils.defaultString(params.getMemberId()));
                entity.setSupplierName(StringUtils.defaultString(params.getSupplierName()));
                riskCustomerManageMapper.insert(entity);

                RiskCustomerRecord record = new RiskCustomerRecord();
                record.setCustomerId(entity.getId());
                record.setOperateType(1); // 新增
                record.setCreateUser(entity.getCreateUser());
                record.setOperateUser(userInfo.getUsername() + userInfo.getWorkId());
                record.setCustomerType(params.getCustomerType());
                record.setRemark(params.getRiskRemark());
                record.setCreateTime(new Date());
                record.setCustomerValue(params.getCustomerValue());
                riskCustomerRecordMapper.insert(record);
            }
            return true;
        }
        return false;
    }

    /**
     * 删除风控名单
     */
    public Boolean delete(Long id, UserInfo userInfo, String delRemark) {
        RiskCustomerManage entity1 = riskCustomerManageMapper.selectById(id);
        if (entity1 != null && entity1.getStatus().equals(RiskCustomerStatusEnum.valid.getCode())) {
            entity1.setStatus(RiskCustomerStatusEnum.invalid.getCode());
            entity1.setOptionName(userInfo.getUsername() + userInfo.getWorkId());
            entity1.setUpdateTime(new Date());
            entity1.setInvalidTime(new Date());
            riskCustomerManageMapper.updateById(entity1);

            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(entity1.getId());
            record.setOperateType(3); // 删除
            record.setCreateUser(entity1.getCreateUser());
            record.setOperateUser(userInfo.getUsername() + userInfo.getWorkId());
            record.setCustomerType(entity1.getCustomerType());
            record.setRemark(StringUtils.defaultString(delRemark));
            record.setCreateTime(new Date());
            record.setCustomerValue(entity1.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return true;
        }
        return false;
    }

    /**
     * Excel 批量导入风控名单
     */
    public Integer excelImport(MultipartFile file, UserInfo userInfo) {
        // Excel 批量导入逻辑
        // ...
    }

    // 其他方法...
}
```

### 2. HcCustomerService

`HcCustomerService` 是汇川司机风控名单管理的服务类，实现了添加、删除、查询等业务逻辑。

```java
@Service
@Slf4j
public class HcCustomerService {

    @Resource
    private HcCustomerMapper hcCustomerMapper;
    @Resource
    private SupplierInfoMapper supplierInfoMapper;
    @Resource
    private RiskSensitiveActionService riskSensitiveActionService;

    /**
     * 添加汇川司机风控名单
     */
    public void add(HcCustomerParams params, UserInfo userInfo) {
        if (StringUtils.isBlank(params.getPlateNumber()) && StringUtils.isBlank(params.getIdCardNo())) {
            throw new CodeException(500, "司机车牌和司机身份证必须填写一项");
        }
        //用汇川的driverId查询下是否已经有该类型的名单在
        HcCustomer customer = hcCustomerMapper.selectOne(new QueryWrapper<HcCustomer>()
                .eq("driver_card_no", StringUtils.defaultString(params.getPlateNumber()))
                .eq(StringUtils.isNotBlank(params.getDriverPhone()), "driver_phone", params.getDriverPhone())
                .eq("id_card_no", StringUtils.defaultString(params.getIdCardNo()))
                .eq("customer_type", params.getCustomerType())
                .gt("invalid_time", new Date())
                .last("limit 1")
        );
        if (customer != null) {
            //说明有了，这个时候在原先的基础上进行时间延长
            customer.setUpdateTime(new Date());
            customer.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
            if (customer.getTtl() != 7) {
                customer.setInvalidTime(DateUtil.addDay(customer.getInvalidTime(), getDays(customer.getTtl())));
            }
            customer.setRemark(customer.getRemark());
            customer.setOrderId(params.getOrderId());
            hcCustomerMapper.updateById(customer);
        } else {
            HcCustomer hcCustomer = new HcCustomer();
            hcCustomer.setDriverCardNo(params.getPlateNumber());
            hcCustomer.setDriverPhone(params.getDriverPhone());
            hcCustomer.setCreateTime(new Date());
            hcCustomer.setCreateUser(userInfo.getUsername() + userInfo.getWorkId());
            hcCustomer.setUpdateTime(new Date());
            hcCustomer.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
            hcCustomer.setCustomerType(params.getCustomerType());
            hcCustomer.setOrderId(params.getOrderId());
            hcCustomer.setIdCardNo(params.getIdCardNo());
            hcCustomer.setDriverId(params.getDriverId());
            if (params.getTtl() == 7) {
                hcCustomer.setInvalidTime(DateUtil.string2Date("2099-01-01 00:00:00"));
            } else {
                hcCustomer.setInvalidTime(DateUtil.addDay(new Date(), getDays(params.getTtl())));
            }
            hcCustomer.setTtl(params.getTtl());
            hcCustomer.setRemark(params.getRemark());
            hcCustomer.setUnRemark(params.getDelRemark());
            hcCustomer.setSupplierCode(params.getSupplierCode());
            hcCustomerMapper.insert(hcCustomer);
        }
    }

    /**
     * 删除汇川司机风控名单
     */
    public void delete(HcCustomerParams params, UserInfo userInfo) {
        HcCustomer hcCustomer = new HcCustomer();
        hcCustomer.setId(params.getId());
        hcCustomer.setUpdateTime(new Date());
        hcCustomer.setUpdateUser(userInfo.getUsername() + userInfo.getWorkId());
        hcCustomer.setUnRemark(params.getDelRemark());
        hcCustomer.setInvalidTime(new Date());
        List<HcCustomer> list = this.hcCustomerMapper.getByCondition(hcCustomer);
        if (!CollectionUtils.isEmpty(list)) {
            if (list.get(0).getInvalidTime().after(new Date())) {
                this.hcCustomerMapper.updateById(hcCustomer);
            }
        }
    }

    // 其他方法...
}
```

## 调用流程

### 1. 添加风控名单流程

1. 用户通过前端界面填写风控名单信息，点击添加按钮。
2. 请求发送到 `RiskCustomerController` 的 `add` 方法。
3. `RiskCustomerController` 调用 `RiskCustomerService` 的 `add` 方法。
4. `RiskCustomerService` 的 `add` 方法执行以下步骤：
   - 校验参数
   - 检查名单是否已存在
   - 如果已存在，更新名单信息（调用 `riskCustomerManageMapper.updateById`）
   - 如果不存在，创建新名单（调用 `riskCustomerManageMapper.insert`）
   - 记录操作日志（调用 `riskCustomerRecordMapper.insert`）
5. 返回操作结果。

### 2. 删除风控名单流程

1. 用户通过前端界面选择要删除的风控名单，点击删除按钮。
2. 请求发送到 `RiskCustomerController` 的 `delete` 方法。
3. `RiskCustomerController` 调用 `RiskCustomerService` 的 `delete` 方法。
4. `RiskCustomerService` 的 `delete` 方法执行以下步骤：
   - 查询名单信息
   - 更新名单状态为无效（调用 `riskCustomerManageMapper.updateById`）
   - 记录操作日志（调用 `riskCustomerRecordMapper.insert`）
5. 返回操作结果。

### 3. Excel 批量导入风控名单流程

1. 用户通过前端界面上传 Excel 文件。
2. 请求发送到 `RiskCustomerController` 的 `excelImport` 方法。
3. `RiskCustomerController` 调用 `RiskCustomerService` 的 `excelImport` 方法。
4. `RiskCustomerService` 的 `excelImport` 方法执行以下步骤：
   - 解析 Excel 文件
   - 遍历每一行数据
   - 对每一行数据调用 `add` 方法（最终会调用 `riskCustomerManageMapper.insert` 或 `riskCustomerManageMapper.updateById`）
5. 返回操作结果。

### 4. 添加汇川司机风控名单流程

1. 用户通过前端界面填写汇川司机风控名单信息，点击添加按钮。
2. 请求发送到 `HcCustomerController` 的 `add` 方法。
3. `HcCustomerController` 调用 `HcCustomerService` 的 `add` 方法。
4. `HcCustomerService` 的 `add` 方法执行以下步骤：
   - 校验参数
   - 检查名单是否已存在
   - 如果已存在，更新名单信息（调用 `hcCustomerMapper.updateById`）
   - 如果不存在，创建新名单（调用 `hcCustomerMapper.insert`）
5. 返回操作结果。

### 5. 删除汇川司机风控名单流程

1. 用户通过前端界面选择要删除的汇川司机风控名单，点击删除按钮。
2. 请求发送到 `HcCustomerController` 的 `delete` 方法。
3. `HcCustomerController` 调用 `HcCustomerService` 的 `delete` 方法。
4. `HcCustomerService` 的 `delete` 方法执行以下步骤：
   - 查询名单信息
   - 更新名单状态为无效（调用 `hcCustomerMapper.updateById`）
5. 返回操作结果。
