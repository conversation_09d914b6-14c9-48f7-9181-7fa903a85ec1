# risk_customer_manage表业务流程分析 - 06 调用链路详细分析

## 1. 调用链路架构概述

risk_customer_manage表的调用链路涵盖了从外部请求到数据库操作的完整流程，包括HTTP接口调用、Service业务处理、Mapper数据访问等多个层次。

### 1.1 整体调用链路图

```mermaid
graph TB
    A[外部系统/前端] --> B[Nginx负载均衡]
    B --> C[Spring Boot应用]
    C --> D[Controller层]
    D --> E[Service层]
    E --> F[Mapper层]
    F --> G[MySQL数据库]
    
    E --> H[Redis缓存]
    E --> I[消息队列]
    E --> J[外部服务调用]
    
    K[定时任务] --> E
    L[规则引擎] --> E
```

### 1.2 主要调用场景分类

1. **管理端操作链路** - 后台管理系统的CRUD操作
2. **业务端查询链路** - 实时风控检查和查询
3. **数据同步链路** - 外部系统数据同步
4. **定时任务链路** - 自动化处理流程
5. **规则引擎链路** - 风控规则执行流程

## 2. 管理端操作调用链路

### 2.1 新增风控名单调用链路

#### 2.1.1 完整调用流程

```mermaid
sequenceDiagram
    participant Client as 前端客户端
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Mapper as RiskCustomerManageMapper
    participant DB as MySQL数据库
    participant Cache as Redis缓存
    participant Record as RiskCustomerRecordMapper
    
    Client->>Controller: POST /riskListManage/riskCustomer/add
    Controller->>Service: add(RiskCustomerAddParams)
    Service->>Service: 参数校验
    Service->>Mapper: getByTypeAndValueAndRiskType()
    Mapper->>DB: SELECT查询现有记录
    DB-->>Mapper: 返回查询结果
    Mapper-->>Service: 返回现有记录
    Service->>Service: 业务规则校验
    Service->>Mapper: insert(RiskCustomerManage)
    Mapper->>DB: INSERT新记录
    DB-->>Mapper: 返回插入结果
    Service->>Record: insert(RiskCustomerRecord)
    Record->>DB: INSERT操作记录
    Service->>Cache: 更新缓存
    Service-->>Controller: 返回操作结果
    Controller-->>Client: 返回HTTP响应
```

#### 2.1.2 详细调用代码分析

**Controller层入口：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/controller/RiskCustomerController.java" mode="EXCERPT">
````java
@PostMapping("/add")
public Boolean add(@RequestBody RiskCustomerAddParams params) {
    return riskCustomerService.add(params);
}
````
</augment_code_snippet>

**Service层业务处理：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/service/RiskCustomerService.java" mode="EXCERPT">
````java
public Boolean add(RiskCustomerAddParams params) {
    // 1. 参数校验
    validateParams(params);
    
    // 2. 获取用户信息
    UserInfo userInfo = (UserInfo) httpSession.getAttribute(SESSION_USER_INFO);
    
    // 3. 校验重复数据
    matchExist(params.getRiskType(), params.getCustomerType(), params.getCustomerValue());
    
    // 4. 查询现有记录
    RiskCustomerManage entity = queryExistingRecord(params);
    
    // 5. 计算失效时间
    Date newInvalidTime = convertTime(params.getTtl());
    
    // 6. 执行插入或更新
    if (Objects.nonNull(entity)) {
        return updateExistingRecord(entity, params, userInfo, newInvalidTime);
    } else {
        return insertNewRecord(params, userInfo);
    }
}
````
</augment_code_snippet>

**Mapper层数据访问：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.java" mode="EXCERPT">
````java
// 查询现有记录
RiskCustomerManage getByTypeAndValueAndRiskType(@Param("customerType") Integer customerType,
                                                @Param("customerValue") String customerValue,
                                                @Param("riskType") Integer riskType,
                                                @Param("time")Date time);

// 插入新记录（继承自BaseMapper）
int insert(RiskCustomerManage entity);
````
</augment_code_snippet>

#### 2.1.3 SQL执行分析

**查询现有记录SQL：**
```sql
SELECT * FROM risk_customer_manage 
WHERE customer_type = ? 
  AND customer_value = ? 
  AND risk_type = ? 
  AND invalid_time > ? 
  AND status = 1
ORDER BY create_time DESC 
LIMIT 1
```

**插入新记录SQL：**
```sql
INSERT INTO risk_customer_manage (
    customer_type, customer_value, risk_type, status, ttl,
    create_time, update_time, invalid_time, option_type,
    create_user, option_name, risk_remark, bind_user,
    supplier_name, member_id
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 2.2 分页查询调用链路

#### 2.2.1 查询流程图

```mermaid
sequenceDiagram
    participant Client as 前端客户端
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Mapper as RiskCustomerManageMapper
    participant RecordMapper as RiskCustomerRecordMapper
    participant DB as MySQL数据库
    
    Client->>Controller: POST /riskListManage/riskCustomer/getList
    Controller->>Service: getListPage(RiskCustomerListParams)
    Service->>Service: 设置查询条件
    Service->>Mapper: getListTotal(queryParams)
    Mapper->>DB: SELECT COUNT(*)
    DB-->>Mapper: 返回总数
    Service->>Mapper: getList(queryParams)
    Mapper->>DB: SELECT分页数据
    DB-->>Mapper: 返回分页结果
    Service->>RecordMapper: 查询操作记录
    RecordMapper->>DB: SELECT操作记录
    Service->>Service: 数据转换和脱敏
    Service-->>Controller: 返回分页结果
    Controller-->>Client: 返回HTTP响应
```

#### 2.2.2 性能优化点分析

**索引使用分析：**
- 主要查询条件：`customer_type`, `customer_value`, `risk_type`, `status`, `invalid_time`
- 推荐复合索引：`idx_customer_type_value_risk_type_status`
- 分页排序索引：`idx_create_time`

**查询优化策略：**
1. 使用LIMIT进行分页，避免大结果集
2. 通过索引覆盖减少回表查询
3. 分离总数查询和数据查询
4. 操作记录按需查询，避免N+1问题

### 2.3 Excel批量导入调用链路

#### 2.3.1 导入流程分析

```mermaid
sequenceDiagram
    participant Client as 前端客户端
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant ExcelReader as EasyExcel
    participant Mapper as RiskCustomerManageMapper
    participant DB as MySQL数据库
    
    Client->>Controller: POST /riskListManage/riskCustomer/import
    Controller->>Service: excelImport(file, userInfo)
    Service->>ExcelReader: 读取Excel文件
    ExcelReader-->>Service: 返回数据列表
    Service->>Service: 数据校验
    loop 批量处理
        Service->>Service: 转换Excel数据
        Service->>Mapper: 查询重复数据
        Service->>Mapper: 插入或更新记录
        Service->>Mapper: 插入操作记录
    end
    Service-->>Controller: 返回成功数量
    Controller-->>Client: 返回处理结果
```

#### 2.3.2 批量处理优化

**分批处理策略：**
```java
public Integer excelImport(MultipartFile file, UserInfo userInfo) {
    List<RiskCustomerExcelBean> dataList = readExcelFile(file);
    
    // 分批处理，每批1000条
    int batchSize = 1000;
    int successCount = 0;
    
    for (int i = 0; i < dataList.size(); i += batchSize) {
        int endIndex = Math.min(i + batchSize, dataList.size());
        List<RiskCustomerExcelBean> batch = dataList.subList(i, endIndex);
        
        successCount += processBatch(batch, userInfo);
    }
    
    return successCount;
}
```

## 3. 业务端查询调用链路

### 3.1 风控能力查询链路

#### 3.1.1 多维度查询流程

```mermaid
sequenceDiagram
    participant Client as 业务系统
    participant Controller as CustomerAbilityController
    participant Mapper as RiskCustomerManageMapper
    participant Cache as Redis缓存
    participant DB as MySQL数据库
    
    Client->>Controller: POST /customer/queryAll
    Controller->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>Controller: 返回缓存数据
    else 缓存未命中
        Controller->>Mapper: selectList(QueryWrapper)
        Mapper->>DB: 执行多条件查询
        DB-->>Mapper: 返回查询结果
        Mapper-->>Controller: 返回风控名单
        Controller->>Cache: 更新缓存
    end
    Controller->>Controller: 业务规则处理
    Controller-->>Client: 返回风控结果
```

#### 3.1.2 查询SQL优化

**多维度查询SQL：**
```sql
SELECT * FROM risk_customer_manage 
WHERE invalid_time > NOW()
  AND status = 1
  AND (
    customer_value = ? OR  -- 车牌号
    customer_value = ? OR  -- 手机号
    customer_value = ? OR  -- 会员ID
    customer_value = ?     -- 其他标识
  )
```

**索引优化建议：**
- 主要查询字段：`customer_value`, `invalid_time`, `status`
- 推荐索引：`idx_customer_value_invalid_time_status`
- 覆盖索引：包含常用查询字段，减少回表

### 3.2 营销风控查询链路

#### 3.2.1 营销风控流程

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/controller/ability/MarketingRiskController.java" mode="EXCERPT">
````java
@RequestMapping("query")
public UiResult marketingRiskQuery(@RequestBody CustomerAbilityParam param){
    // 1. 构建查询条件
    QueryWrapper<RiskCustomerManage> queryWrapper = new QueryWrapper<RiskCustomerManage>()
        .gt("invalid_time", new Date())
        .and(qw-> qw.eq(StringUtils.isNotBlank(param.getPassengerPhone()), "customer_value", param.getPassengerPhone())
                .or().eq(StringUtils.isNotBlank(param.getMemberId()), "customer_value", param.getMemberId())
                .or().eq(StringUtils.isNotBlank(param.getUnionId()), "customer_value", param.getUnionId())
        );
    
    // 2. 执行查询
    List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(queryWrapper);
    
    // 3. 风控决策
    RiskResultNewDTO riskResultNewDTO = new RiskResultNewDTO();
    if(CollectionUtils.isEmpty(customerManageList)){
        return UiResult.ok(riskResultNewDTO); // 未命中
    }
    
    // 4. 白名单优先
    RiskCustomerManage whiteRecord = customerManageList.stream()
        .filter(data->data.getRiskType()==2)
        .findFirst().orElse(null);
        
    if(whiteRecord == null){
        riskResultNewDTO.setCode(1);
        riskResultNewDTO.setMessage("命中黑名单");
    }
    
    return UiResult.ok(riskResultNewDTO);
}
````
</augment_code_snippet>

## 4. 数据同步调用链路

### 4.1 司机黑名单同步链路

#### 4.1.1 同步流程图

```mermaid
sequenceDiagram
    participant External as 外部系统
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Lock as 分布式锁
    participant Mapper as RiskCustomerManageMapper
    participant OrderService as CarOrderService
    participant DB as MySQL数据库
    
    External->>Controller: POST /risk/customer/syncBlackDriver
    Controller->>Service: syncDriver(DriverSyncParams)
    Service->>Lock: 获取分布式锁
    Lock-->>Service: 锁获取成功
    Service->>Mapper: 查询现有记录
    Mapper->>DB: SELECT现有黑名单
    DB-->>Mapper: 返回查询结果
    alt 记录不存在或已失效
        Service->>OrderService: 查询司机ID
        Service->>Mapper: insert新记录
        Service->>Mapper: insert操作记录
    else 记录存在且有效
        Service->>Mapper: update失效时间
        Service->>Mapper: insert操作记录
    end
    Service->>Lock: 释放锁
    Service-->>Controller: 返回同步结果
    Controller-->>External: 返回HTTP响应
```

#### 4.1.2 分布式锁实现

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
public String syncDriver(DriverSyncParams params) {
    String lockKey = "risk_customer_sync_" + params.getDriverCardNo();
    RLock lock = redissonClient.getLock(lockKey);
    
    try {
        if (lock.tryLock(10, TimeUnit.SECONDS)) {
            return doSyncDriver(params);
        } else {
            log.warn("获取同步锁失败：{}", params.getDriverCardNo());
            return "lock_failed";
        }
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        return "interrupted";
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
````
</augment_code_snippet>

### 4.2 订单关联司机ID查询链路

#### 4.2.1 订单类型判断和查询

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
private String queryDriverIdByOrder(String orderId) {
    if (OrderUtils.isNewOrder(orderId)) {
        // 新订单系统查询
        CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(orderId);
        if (null != carOrderDetail && null != carOrderDetail.getCarInfo()) {
            return carOrderDetail.getCarInfo().getDriverCode();
        }
    } else if (orderId.startsWith("SFC")) {
        // 顺风车订单查询
        SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(orderId);
        if (sfcOrder != null) {
            SfcSupplierOrder sfcSupplierOrder = this.sfcSupplierOrderMapper.selectOne(
                new QueryWrapper<SfcSupplierOrder>()
                    .eq("order_id", sfcOrder.getOrderId())
                    .eq("supplier_order_id", sfcOrder.getSupplierOrderId())
            );
            return sfcSupplierOrder != null ? sfcSupplierOrder.getDriverId() : null;
        }
    }
    return null;
}
````
</augment_code_snippet>

## 5. 定时任务调用链路

### 5.1 定时失效处理链路

#### 5.1.1 定时任务执行流程

```mermaid
sequenceDiagram
    participant Scheduler as 定时调度器
    participant Controller as RiskCustomerController
    participant Service as RiskCustomerService
    participant Mapper as RiskCustomerManageMapper
    participant DB as MySQL数据库
    participant Cache as Redis缓存
    
    Scheduler->>Controller: POST /risk/customer/invalid
    Controller->>Service: invalid()
    Service->>Mapper: selectInvalidData()
    Mapper->>DB: 查询需失效数据
    DB-->>Mapper: 返回失效数据列表
    loop 批量更新
        Service->>Mapper: updateById(entity)
        Mapper->>DB: UPDATE状态为失效
    end
    Service->>Cache: 清理相关缓存
    Service-->>Controller: 返回处理结果
    Controller-->>Scheduler: 返回执行状态
```

#### 5.1.2 失效数据查询SQL

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.java" mode="EXCERPT">
````sql
SELECT * FROM risk_customer_manage 
WHERE status = 1 
  AND (
    (ttl != -1 AND ttl != 0 AND NOW() >= DATE_ADD(create_time, INTERVAL ttl DAY))
    OR 
    (ttl = 0 AND NOW() >= invalid_time)
  )
````
</augment_code_snippet>

### 5.2 数据清理任务链路

#### 5.2.1 清理任务流程

```java
@Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
public void scheduledCleanup() {
    try {
        // 1. 清理过期数据
        int cleanupCount = riskCustomerManageMapper.clearInvalid();
        
        // 2. 清理重复数据
        cleanupDuplicateData();
        
        // 3. 清理缓存
        cleanupExpiredCache();
        
        // 4. 数据统计
        generateDataStatistics();
        
        log.info("数据清理完成，清理数量：{}", cleanupCount);
    } catch (Exception e) {
        log.error("数据清理任务异常", e);
        sendAlertNotification("数据清理任务异常", e.getMessage());
    }
}
```

## 6. 性能监控和调用链追踪

### 6.1 调用链监控

#### 6.1.1 链路追踪实现

```java
@RestController
@Slf4j
public class RiskCustomerController {
    
    @PostMapping("/add")
    @Timed(name = "risk_customer_add", description = "风控名单添加耗时")
    public Boolean add(@RequestBody RiskCustomerAddParams params) {
        String traceId = MDC.get("traceId");
        log.info("风控名单添加开始，traceId：{}，参数：{}", traceId, JsonUtils.json(params));
        
        long startTime = System.currentTimeMillis();
        try {
            Boolean result = riskCustomerService.add(params);
            long endTime = System.currentTimeMillis();
            
            log.info("风控名单添加完成，traceId：{}，耗时：{}ms，结果：{}", 
                traceId, endTime - startTime, result);
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("风控名单添加异常，traceId：{}，耗时：{}ms", 
                traceId, endTime - startTime, e);
            throw e;
        }
    }
}
```

#### 6.1.2 数据库操作监控

```java
@Component
public class RiskCustomerMapperInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        String methodName = invocation.getMethod().getName();
        Object[] args = invocation.getArgs();
        
        long startTime = System.currentTimeMillis();
        try {
            Object result = invocation.proceed();
            long endTime = System.currentTimeMillis();
            
            // 记录SQL执行时间
            log.info("SQL执行完成，方法：{}，耗时：{}ms", methodName, endTime - startTime);
            
            // 记录慢查询
            if (endTime - startTime > 1000) {
                log.warn("慢查询告警，方法：{}，耗时：{}ms，参数：{}", 
                    methodName, endTime - startTime, JsonUtils.json(args));
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("SQL执行异常，方法：{}，耗时：{}ms", 
                methodName, endTime - startTime, e);
            throw e;
        }
    }
}
```

### 6.2 性能指标收集

#### 6.2.1 业务指标监控

```java
@Component
public class RiskCustomerMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter addCounter;
    private final Counter queryCounter;
    private final Timer queryTimer;
    
    public RiskCustomerMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.addCounter = Counter.builder("risk_customer_add_total")
            .description("风控名单添加总数")
            .register(meterRegistry);
        this.queryCounter = Counter.builder("risk_customer_query_total")
            .description("风控名单查询总数")
            .register(meterRegistry);
        this.queryTimer = Timer.builder("risk_customer_query_duration")
            .description("风控名单查询耗时")
            .register(meterRegistry);
    }
    
    public void recordAdd(boolean success) {
        addCounter.increment(Tags.of("success", String.valueOf(success)));
    }
    
    public void recordQuery(String queryType, int resultCount, long duration) {
        queryCounter.increment(Tags.of("type", queryType, "result_count", String.valueOf(resultCount)));
        queryTimer.record(duration, TimeUnit.MILLISECONDS);
    }
}
```

## 7. 异常处理和降级策略

### 7.1 异常处理链路

#### 7.1.1 分层异常处理

```java
// Controller层异常处理
@ControllerAdvice
public class RiskCustomerExceptionHandler {
    
    @ExceptionHandler(CodeException.class)
    public UiResult handleCodeException(CodeException e) {
        log.warn("业务异常：{}", e.getMessage());
        return UiResult.fail(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(DataAccessException.class)
    public UiResult handleDataAccessException(DataAccessException e) {
        log.error("数据访问异常", e);
        return UiResult.fail(-1, "数据访问异常，请稍后重试");
    }
}

// Service层异常处理
@Service
public class RiskCustomerService {
    
    public Boolean add(RiskCustomerAddParams params) {
        try {
            return doAdd(params);
        } catch (DuplicateKeyException e) {
            log.warn("重复数据异常：{}", e.getMessage());
            throw new CodeException(-1, "数据已存在");
        } catch (Exception e) {
            log.error("添加风控名单异常：{}", JsonUtils.json(params), e);
            throw new CodeException(-1, "系统异常，请稍后重试");
        }
    }
}
```

### 7.2 降级策略

#### 7.2.1 查询降级

```java
@Service
public class RiskCustomerQueryService {
    
    public List<RiskCustomerManage> queryWithFallback(FilterParams params) {
        try {
            // 尝试从缓存查询
            List<RiskCustomerManage> cachedResult = getCachedResult(params);
            if (cachedResult != null) {
                return cachedResult;
            }
            
            // 缓存未命中，查询数据库
            return riskCustomerManageMapper.getListByValue(params, new Date());
            
        } catch (Exception e) {
            log.error("风控查询异常，启用降级策略：{}", JsonUtils.json(params), e);
            
            // 降级策略：返回空结果，不阻断业务
            return new ArrayList<>();
        }
    }
}
```

这个调用链路详细分析展示了risk_customer_manage表在整个系统中的完整调用流程，包括性能优化、监控告警和异常处理等关键环节。
