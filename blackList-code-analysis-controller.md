# 黑名单功能控制器层代码分析

## 1. BlackListApiController 分析

`BlackListApiController` 是黑名单功能的主要控制器，负责处理用户端的黑名单相关请求。

### 1.1 控制器定义

```java
@RequestMapping("blackList")
@RestController
@Slf4j
public class BlackListApiController {

    @Resource
    private BlackListService service;
    
    // 各种方法...
}
```

### 1.2 `/blackList/driver/list` 接口实现

```java
/**
 * 用户拉黑司机列表
 * riskType：1v1
 */
@RequestMapping("/driver/list")
public UiResultWrapper listDriver(@RequestBody DriverBlackListRequest request) {
    LoggerUtils.initLogMap("listDriver", "", request.getTraceId(), request.getPassengerCellphone());
    LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
    try {
        UiResult uiResult = service.listDriver(request);
        LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
        return UiResultWrapper.convert(uiResult);
    } catch (Exception e) {
        LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

该方法的主要功能：
1. 记录请求日志
2. 调用 `BlackListService.listDriver()` 方法获取黑名单列表
3. 记录响应日志
4. 返回包装后的结果
5. 异常处理

### 1.3 其他相关接口

#### 1.3.1 用户拉黑司机

```java
/**
 * 用户拉黑司机
 * riskType：1v1
 */
@RequestMapping("/driver/black")
public UiResultWrapper blackDriver(@RequestBody DriverBlackRequest request) {
    LoggerUtils.initLogMap("blackDriver", "", request.getTraceId(), request.getPassengerCellphone());
    LoggerUtils.info(log, "用户拉黑司机，req:{}", JSON.toJSONString(request));
    try {
        UiResult uiResult = service.blackDriver(request);
        LoggerUtils.info(log, "用户拉黑司机,resp:{}", JSON.toJSONString(uiResult));
        return UiResultWrapper.convert(uiResult);
    } catch (BizException e) {
        LoggerUtils.warn(log, "用户拉黑司机,业务异常", e);
        return UiResultWrapper.fail(e.getCode(), "系统异常:" + e.getMessage());
    } catch (Exception e) {
        LoggerUtils.error(log, "用户拉黑司机,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

#### 1.3.2 用户取消拉黑司机

```java
/**
 * 用户取消拉黑司机
 * riskType：1v1
 */
@RequestMapping("/driver/remove")
public UiResultWrapper removeDriver(@RequestBody DriverRemoveRequest request) {
    LoggerUtils.initLogMap("listDriver", "", request.getTraceId(), request.getPassengerCellphone());
    LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
    try {
        UiResult uiResult = service.removeDriver(request);
        LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
        return UiResultWrapper.convert(uiResult);
    } catch (Exception e) {
        LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

#### 1.3.3 检查司机是否被拉黑

```java
/**
 * 司机是否被拉黑过
 * riskType：1v1
 */
@RequestMapping("/driver/checkIn")
public UiResultWrapper checkDriverIn(@RequestBody DriverCheckInRequest request) {
    LoggerUtils.initLogMap("listDriver", "", request.getTraceId(), request.getOrderId());
    LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
    try {
        UiResult uiResult = service.checkDriverIn(request);
        LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
        return UiResultWrapper.convert(uiResult);
    } catch (Exception e) {
        LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

#### 1.3.4 批量查询车牌黑名单

```java
/**
 * 司机是否被拉黑过
 * riskType：1v1
 */
@RequestMapping("/batchQueryDriverBlack")
public UiResultWrapper batchQueryDriverBlack(@RequestBody BatchCarBlackInfoQueryRequest request) {
    String traceId = StringUtils.defaultIfBlank(request.getTraceId(), UUID.generateFormatedRandomUUID());
    LoggerUtils.initLogMap("batchQueryDriverBlack", "", traceId, "");
    LoggerUtils.info(log, "批量车牌黑名单查询, req:{}", JSON.toJSONString(request));
    try {
        List<CarBlackInfo> blackList = service.batchQueryDriverBlack(request);
        LoggerUtils.info(log, "批量车牌黑名单查询,resp:{}", JSON.toJSONString(blackList));
        return UiResultWrapper.ok(blackList);
    } catch (BizException e ){
        LoggerUtils.error(log, "批量车牌黑名单查询,业务异常", e);
        return UiResultWrapper.fail(500, "业务异常:" + e.getMessage());
    }catch (Exception e) {
        LoggerUtils.error(log, "批量车牌黑名单查询,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

#### 1.3.5 同步黑名单

```java
/**
 * 同步黑名单
 */
@RequestMapping("/sync")
public UiResultWrapper sync(@RequestBody BlackSyncRequest request) {
    String traceId = StringUtils.defaultIfBlank(request.getTraceId(), UUID.generateFormatedRandomUUID());
    LoggerUtils.initLogMap("sync", "", traceId, "");
    LoggerUtils.info(log, "批量同步外部黑名单, req:{}", JSON.toJSONString(request));
    try {
        service.syncDriverBlack(request);
        return UiResultWrapper.ok();
    } catch (Exception e) {
        LoggerUtils.error(log, "批量同步外部黑名单,未知异常", e);
        return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
    } finally {
        LoggerUtils.removeAll();
    }
}
```

## 2. BlackListController 分析

`BlackListController` 是黑名单功能的后台管理控制器，负责处理客服端的黑名单相关请求。

### 2.1 控制器定义

```java
@RequestMapping("/manage/blackList")
@RestController
public class BlackListController {

    @Resource
    private BlackListService service;
    
    // 各种方法...
}
```

### 2.2 客服拉黑司机接口

```java
/**
 * 客服拉黑司机
 * riskType：1v1、全部
 */
@RequestMapping("/driver/black")
public UiResultWrapper blackDriver(@RequestBody DriverBlackFromManageRequest request) {
    return UiResultWrapper.convert(service.blackDriverFromManage(request));
}
```

## 3. BlackDriverShieldController 分析

`BlackDriverShieldController` 是黑名单功能的另一个控制器，提供了一些旧的黑名单接口。

### 3.1 控制器定义

```java
@RequestMapping("blackDriver")
@RestController
public class BlackDriverShieldController {

    @Resource
    private BlackDriverService blackDriverService;
    
    // 各种方法...
}
```

### 3.2 相关接口

#### 3.2.1 用户拉黑司机

```java
@RequestMapping("userShield")
public UiResult userShield(@RequestBody DriverBlackParam param){
    return blackDriverService.userShield(param);
}
```

#### 3.2.2 用户取消拉黑

```java
@RequestMapping("userRemove")
public UiResult userRemove(@RequestBody DriverBlackParam param){
    return blackDriverService.userRemove(param);
}
```

#### 3.2.3 获取黑名单列表

```java
@RequestMapping("getList")
public UiResult getList(@RequestBody DriverBlackParam param){
    return this.blackDriverService.getList(param);
}
```

#### 3.2.4 订单黑名单检查

```java
@RequestMapping("orderBlack")
public UiResult orderBlack(@RequestBody DriverBlackParam param){
    return this.blackDriverService.orderBlack(param);
}
```

## 4. 控制器层总结

黑名单功能在控制器层主要由三个控制器提供服务：

1. **BlackListApiController**：提供用户端黑名单功能，包括拉黑司机、取消拉黑、查询黑名单等
2. **BlackListController**：提供客服端黑名单功能，主要是客服拉黑司机
3. **BlackDriverShieldController**：提供一些旧的黑名单接口

这些控制器共同构成了黑名单功能的完整接口层，为不同的用户角色提供了黑名单管理功能。
