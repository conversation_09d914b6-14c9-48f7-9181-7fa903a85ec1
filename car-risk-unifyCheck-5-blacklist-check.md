# car-risk项目 `/riskCheck/unifyCheck` 接口分析 - 第5部分：黑名单检查

## 1. 黑名单检查概述

黑名单检查是风控系统的第一道防线，它通过检查用户是否在黑名单中来判断是否需要拦截请求。黑名单检查的主要步骤包括：

1. 根据业务线选择不同的黑名单处理器
2. 查询黑白名单
3. 判断是否命中黑白名单
4. 返回检查结果

## 2. 黑名单检查详细流程

### 2.1 黑名单处理器选择 (BlackListHandler)

黑名单处理器选择主要是根据业务线选择不同的黑名单处理器：

```java
@Service
public class BlackListHandler implements BlackListCheck {
    @Resource
    private MTBlackListHandler mtBlackListHandler;
    @Resource
    private CommonBlackListHandler commonBlackListHandler;

    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
        beforeBlackWhiteCheck(request);
        StrategySceneEnum scene = StrategySceneEnum.of(request.getScene());

        // 黑白名单，MT的有特殊性
        if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
            return mtBlackListHandler.blackListCheck(request);
        } else if (scene == StrategySceneEnum.CANCEL_REMINDER || scene == StrategySceneEnum.FINISH_ORDER) {
            // 取消-安全提示 不校验
            return null;
        } else {
            return commonBlackListHandler.blackListCheck(request);
        }
    }
    
    private void beforeBlackWhiteCheck(UnifyCheckRequest request) {
        if (request.isDistributionFlag()) {
            // 分销单的mid是批量虚拟账号，不校验mid相关，防止误伤
            request.setMemberId(StringUtils.EMPTY);
        }
    }
}
```

### 2.2 通用黑名单检查 (CommonBlackListHandler)

通用黑名单检查主要是检查用户是否在黑名单中：

```java
@Service
public class CommonBlackListHandler implements BlackListCheck {
    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
        RiskCustomerRiskTypeEnum blackWhiteCheckResult = blackWhiteCheck(request);
        // 校验-1. 名单
        if (Objects.equals(blackWhiteCheckResult, RiskCustomerRiskTypeEnum.white_list)) {
            return RiskSceneResult.pass(blackWhiteCheckResult.getTip().getMsg());
        } else if (null != blackWhiteCheckResult) {
            RiskSceneResult fail = RiskSceneResult.fail(blackWhiteCheckResult.getTip().getMsg(), blackWhiteCheckResult.getCode());
            riskHitService.initHitRisk(request, fail);
            return fail;
        }
        return null;
    }

    private RiskCustomerRiskTypeEnum blackWhiteCheck(UnifyCheckRequest request) {
        // 全参数名单
        List<RiskCustomerManage> allCustomerList = getAllCustomerList(request);
        
        // 全参数名单经过分场景校验过滤后的名单（1v1名单不适用）
        List<RiskCustomerManage> filterCustomerList = filterByScene(request, allCustomerList);
        
        // 在普通黑名单前增加额外校验腾讯名单
        RiskCustomerRiskTypeEnum hitCustomerRiskType = specialBlackJudge(request, filterCustomerList, allCustomerList);
        if (hitCustomerRiskType != null) {
            return hitCustomerRiskType;
        }

        // 如果是司机接单，先判断1V1
        if (StringUtils.isNotBlank(request.getCarNum()) && StringUtils.isNotBlank(request.getPassengerPhone())) {
            Optional<RiskCustomerManage> optional = allCustomerList.stream().filter(p ->
                    Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                    && Objects.equals(p.getCustomerValue(), request.getCarNum())
                    && Objects.equals(p.getBindUser(), request.getPassengerPhone())).findFirst();
            if (optional.isPresent()) {
                RiskCustomerManage riskCustomerManage = optional.get();
                request.setHitType(1);
                request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
                request.setCustomerValue(riskCustomerManage.getCustomerValue());
                request.setCustomerType(riskCustomerManage.getCustomerType());
                return RiskCustomerRiskTypeEnum.ban_one_to_one_list;
            }
        }

        // 白名单
        if (filterCustomerList.stream().anyMatch(p -> Objects.equals(RiskCustomerRiskTypeEnum.white_list.getCode(), p.getRiskType()))) {
            return RiskCustomerRiskTypeEnum.white_list;
        }

        // 黑名单
        List<Integer> riskTypes = StrategySceneEnum.sceneRiskType(request.getScene(), request.getProductLine());
        List<RiskCustomerManage> matchBlackList = filterCustomerList.stream().filter(p -> riskTypes.contains(p.getRiskType())).sorted(Comparator.comparing(RiskCustomerManage::getRiskType)).collect(Collectors.toList());

        if (CollUtil.isEmpty(matchBlackList)) {
            return null;
        }

        RiskCustomerManage riskCustomerManage = matchBlackList.get(0);

        // 记录命中信息
        request.setHitType(1);
        request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
        request.setCustomerValue(riskCustomerManage.getCustomerValue());
        request.setCustomerType(riskCustomerManage.getCustomerType());

        return RiskCustomerRiskTypeEnum.of(riskCustomerManage.getRiskType());
    }
    
    private List<RiskCustomerManage> getAllCustomerList(UnifyCheckRequest request) {
        CommonCustomerParam customerParam = new CommonCustomerParam();
        customerParam.setUserPhone(request.getUserPhone());
        customerParam.setPassengerCellphone(request.getPassengerPhone());
        customerParam.setMemberId(request.getMemberId());
        customerParam.setDriverCardNo(request.getCarNum());
        customerParam.setUnionId(request.getUnionId());
        customerParam.setIdCardNos(request.getCardNos());
        customerParam.setPayAccount(request.getPayAccount());
        customerParam.setDeviceId(request.getDeviceId());
        return this.riskCustomerService.getListByValueByGroup(customerParam, new Date());
    }
    
    /**
     * 根据不同场景，返回不同适用命中的名单
     */
    private List<RiskCustomerManage> filterByScene(UnifyCheckRequest request, List<RiskCustomerManage> customerManageList) {
        if(CollectionUtils.isEmpty(customerManageList)){
            return customerManageList;
        }
        
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        if (null == sceneEnum) {
            return customerManageList;
        }
        
        Predicate<RiskCustomerManage> predicate;
        
        switch (sceneEnum) {
            case DRIVER_ACCEPT_ORDER:
            case DISPATCHER_ACCEPT_ORDER:
                predicate = customer -> Objects.equals(customer.getCustomerValue(), request.getCarNum())
                        || Objects.equals(customer.getCustomerValue(), request.getDeviceId());
                break;
            default:
                return customerManageList;
        }
        
        return customerManageList.parallelStream().filter(predicate).collect(Collectors.toList());
    }
    
    @Nullable
    private static RiskCustomerRiskTypeEnum specialBlackJudge(UnifyCheckRequest request, List<RiskCustomerManage> filterCustomerList, List<RiskCustomerManage> allCustomerList) {
        // 腾讯出行额外校验，场景5-1、5-2
        // 腾讯一对一名单->腾讯黑名单->现有名单过滤顺序->风控策略
        if (Objects.equals(request.getChannel(), MetricStrategyChannelEnum.TX_TRAVEL.code)
                && (Objects.equals(request.getScene(), DRIVER_ACCEPT_ORDER.getScene()) || Objects.equals(request.getScene(), DISPATCHER_ACCEPT_ORDER.getScene()))
        ) {
            // 1v1
            Optional<RiskCustomerManage> hit1v1Optional = allCustomerList.stream().filter(p ->
                    Objects.equals(RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list.getCode(), p.getRiskType())
                            && (Objects.equals(request.getUserPhone(), p.getBindUser()) || Objects.equals(request.getPassengerPhone(), p.getBindUser()))
                            && Objects.equals(request.getCarNum(), p.getCustomerValue())
            ).findFirst();

            if (hit1v1Optional.isPresent()) {
                RiskCustomerManage riskCustomerManage = hit1v1Optional.get();
                // 记录命中信息
                request.setHitType(1);
                request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
                request.setCustomerValue(riskCustomerManage.getCustomerValue());
                request.setCustomerType(riskCustomerManage.getCustomerType());
                return RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list;
            }

            // 黑名单
            Optional<RiskCustomerManage> hitBlackOptional = filterCustomerList.stream().filter(p ->
                    Objects.equals(RiskCustomerRiskTypeEnum.tx_black_list.getCode(), p.getRiskType()) && Objects.equals(request.getCarNum(), p.getCustomerValue())
            ).findFirst();
            if (hitBlackOptional.isPresent()) {
                RiskCustomerManage riskCustomerManage = hitBlackOptional.get();
                // 记录命中信息
                request.setHitType(1);
                request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
                request.setCustomerValue(riskCustomerManage.getCustomerValue());
                request.setCustomerType(riskCustomerManage.getCustomerType());
                return RiskCustomerRiskTypeEnum.tx_black_list;
            }
        }
        return null;
    }
}
```

### 2.3 萌艇黑名单检查 (MTBlackListHandler)

萌艇黑名单检查有其特殊性，需要查询特定的黑白名单：

```java
@Service
public class MTBlackListHandler implements BlackListCheck {
    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private HcCustomerMapper hcCustomerMapper;

    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
        HcCustomerRiskTypeEnum mtBlackWhiteCheckResult = mtBlackWhiteCheck(request);
        if (Objects.equals(mtBlackWhiteCheckResult, HcCustomerRiskTypeEnum.WHITE_LIST)) {
            return RiskSceneResult.pass(RiskCustomerRiskTipTypeEnum.white.getMsg());
        } else if (null != mtBlackWhiteCheckResult) {
            RiskSceneResult fail = RiskSceneResult.fail(mtBlackWhiteCheckResult.getTip().getMsg(), mtBlackWhiteCheckResult.getCode());
            return fail;
        }
        return null;
    }

    private HcCustomerRiskTypeEnum mtBlackWhiteCheck(UnifyCheckRequest request) {
        // 先判断是不是接单类型
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        boolean isDriverAccept = sceneEnum == StrategySceneEnum.DRIVER_ACCEPT_ORDER || sceneEnum == StrategySceneEnum.DISPATCHER_ACCEPT_ORDER;

        // MT的风控校验
        boolean hasMtWhite = false;
        boolean hasMtBlack = false;
        List<HcCustomer> hcCustomers = queryMtBlackWhiteList(request);
        List<Integer> customerRiskList = hcCustomers.stream().map(HcCustomer::getCustomerType).collect(Collectors.toList());
        List<Integer> whiteTypes = StrategySceneEnum.hcSceneWhiteType(request.getScene(), request.getProductLine());
        hasMtWhite = CollUtil.containsAny(customerRiskList, whiteTypes);
        List<Integer> blackTypes = StrategySceneEnum.hcSceneBlackType(request.getScene(), request.getProductLine());
        List<HcCustomer> matchMtBlackList = hcCustomers.stream().filter(p -> blackTypes.contains(p.getCustomerType())).sorted(Comparator.comparing(p -> p.getCustomerType())).collect(Collectors.toList());
        hasMtBlack = CollUtil.isNotEmpty(matchMtBlackList);
        // 如果有MT白名单，非司机接单则直接结束了
        if (!isDriverAccept) {
            if (hasMtWhite) {
                return HcCustomerRiskTypeEnum.WHITE_LIST;
            }
            // 如果有MT黑名单，直接卡死
            if (hasMtBlack) {
                return HcCustomerRiskTypeEnum.of(matchMtBlackList.get(0).getCustomerType());
            }
            return null;
        } else {
            // 如果有MT黑名单，直接卡死
            if (!hasMtWhite && hasMtBlack) {
                return HcCustomerRiskTypeEnum.of(matchMtBlackList.get(0).getCustomerType());
            }
            // 如果是司机接单，看下聚合名单中是否有黑名单
            boolean hasWhite = false;
            boolean hasBlack = false;
            // 黑白名单校验
            CommonCustomerParam customerParam = new CommonCustomerParam();
            customerParam.setUserPhone(request.getUserPhone());
            customerParam.setPassengerCellphone(request.getPassengerPhone());
            customerParam.setDriverCardNo(request.getCarNum());
            List<RiskCustomerManage> listByValueByGroup = this.riskCustomerService.getListByValueByGroup(customerParam, new Date());
            // 先看看是不是有黑名单
            if (StringUtils.isNotBlank(request.getPassengerPhone()) && StringUtils.isNotBlank(request.getCarNum())) {
                boolean oneToOneMatch = listByValueByGroup.stream().anyMatch(p -> Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                        && Objects.equals(p.getCustomerValue(), request.getCarNum()) && Objects.equals(p.getBindUser(), request.getPassengerPhone()));
                if (oneToOneMatch) {
                    return HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_BLACK_LIST;
                }
            }
            // 先判断是不是有白名单
            hasWhite = listByValueByGroup.stream().anyMatch(p -> Objects.equals(RiskCustomerRiskTypeEnum.white_list.getCode(), p.getRiskType()));
            if (hasWhite) {
                return HcCustomerRiskTypeEnum.WHITE_LIST;
            }
            List<Integer> riskTypes = StrategySceneEnum.sceneRiskType(StrategySceneEnum.DRIVER_ACCEPT_ORDER.getScene(), request.getProductLine());
            List<RiskCustomerManage> matchBlackList = listByValueByGroup.stream().filter(p -> riskTypes.contains(p.getRiskType())).sorted(Comparator.comparing(p -> p.getRiskType())).collect(Collectors.toList());
            hasBlack = CollUtil.isNotEmpty(matchBlackList);
            if (hasBlack) {
                return matchBlackList.stream().anyMatch(p -> Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.black_list.getCode())) ?
                        HcCustomerRiskTypeEnum.BLACK_LIST : HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_WHITE_LIST;
            }
            return hasMtWhite ? HcCustomerRiskTypeEnum.WHITE_LIST : null;
        }
    }

    private List<HcCustomer> queryMtBlackWhiteList(UnifyCheckRequest request) {
        String carNum = request.getCarNum();
        String driverId = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID);
        String toDriverId = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.TO_DRIVER_ID);
        String driverPhone = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_PHONE);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);
        if (StringUtils.isAllBlank(carNum, driverId, driverPhone, toDriverId, certNo)) {
            return new ArrayList<>();
        }

        List<HcCustomer> hcCustomers = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                .eq("line_type", 0).gt("invalid_time", new Date()).and(data -> data
                        .or().eq(StringUtils.isNotBlank(carNum), "driver_card_no", carNum)
                        .or().eq(StringUtils.isNotBlank(driverId), "driver_id", driverId)
                        .or().eq(StringUtils.isNotBlank(driverPhone), "driver_phone", driverPhone)
                        .or().eq(StringUtils.isNotBlank(toDriverId), "driver_id", toDriverId)
                        .or().eq(StringUtils.isNotBlank(certNo), "id_card_no", certNo)));
        return hcCustomers;
    }
}
```

## 3. 黑名单类型

系统中定义了多种黑名单类型，用于不同的场景：

### 3.1 通用黑名单类型 (RiskCustomerRiskTypeEnum)

```java
public enum RiskCustomerRiskTypeEnum {
    black_list(1, RiskCustomerRiskTipTypeEnum.black),
    white_list(2, RiskCustomerRiskTipTypeEnum.white),
    ban_send_list(3, RiskCustomerRiskTipTypeEnum.ban_send),
    ban_receive_list(4, RiskCustomerRiskTipTypeEnum.ban_receive),
    ban_coupon_list(5, RiskCustomerRiskTipTypeEnum.ban_coupon),
    ban_one_to_one_list(6, RiskCustomerRiskTipTypeEnum.ban_one_to_one),
    ban_ync_receive_list(7, RiskCustomerRiskTipTypeEnum.ban_ync_receive),
    tx_black_list(8, RiskCustomerRiskTipTypeEnum.tx_black),
    tx_ban_one_to_one_list(9, RiskCustomerRiskTipTypeEnum.tx_ban_one_to_one),
    ban_bus_create_order(10, RiskCustomerRiskTipTypeEnum.ban_bus_create_order),
    ban_credit_auth_list(11, RiskCustomerRiskTipTypeEnum.ban_credit_auth);

    private Integer code;
    private RiskCustomerRiskTipTypeEnum tip;

    RiskCustomerRiskTypeEnum(Integer code, RiskCustomerRiskTipTypeEnum tip) {
        this.code = code;
        this.tip = tip;
    }

    public Integer getCode() {
        return code;
    }

    public RiskCustomerRiskTipTypeEnum getTip() {
        return tip;
    }

    private static final Map<Integer, RiskCustomerRiskTypeEnum> MAP = new HashMap<>();

    static {
        for (RiskCustomerRiskTypeEnum value : RiskCustomerRiskTypeEnum.values()) {
            MAP.put(value.code, value);
        }
    }

    public static RiskCustomerRiskTypeEnum of(Integer code) {
        return MAP.get(code);
    }

    public static String getMsgByCode(Integer code) {
        RiskCustomerRiskTypeEnum riskTypeEnum = of(code);
        if (null == riskTypeEnum) {
            return "";
        }
        return riskTypeEnum.name();
    }
}
```

### 3.2 萌艇黑名单类型 (HcCustomerRiskTypeEnum)

```java
public enum HcCustomerRiskTypeEnum {
    WHITE_LIST(1, RiskCustomerRiskTipTypeEnum.white),
    BLACK_LIST(2, RiskCustomerRiskTipTypeEnum.black),
    DISABLE_REGISTER_BLACK_LIST(3, RiskCustomerRiskTipTypeEnum.disable_register),
    DISABLE_LOGIN_BLACK_LIST(4, RiskCustomerRiskTipTypeEnum.disable_login),
    DISABLE_RECEIVE_ORDER_BLACK_LIST(5, RiskCustomerRiskTipTypeEnum.disable_receive_order),
    DISABLE_WITHDRAW_BLACK_LIST(6, RiskCustomerRiskTipTypeEnum.disable_withdraw),
    DISABLE_PUBLISH_BLACK_LIST(7, RiskCustomerRiskTipTypeEnum.disable_publish),
    DISABLE_REGISTER_WHITE_LIST(8, RiskCustomerRiskTipTypeEnum.white),
    DISABLE_LOGIN_WHITE_LIST(9, RiskCustomerRiskTipTypeEnum.white),
    DISABLE_RECEIVE_ORDER_WHITE_LIST(10, RiskCustomerRiskTipTypeEnum.white),
    DISABLE_WITHDRAW_WHITE_LIST(11, RiskCustomerRiskTipTypeEnum.white),
    DISABLE_PUBLISH_WHITE_LIST(12, RiskCustomerRiskTipTypeEnum.white);

    private Integer code;
    private RiskCustomerRiskTipTypeEnum tip;

    HcCustomerRiskTypeEnum(Integer code, RiskCustomerRiskTipTypeEnum tip) {
        this.code = code;
        this.tip = tip;
    }

    public Integer getCode() {
        return code;
    }

    public RiskCustomerRiskTipTypeEnum getTip() {
        return tip;
    }

    private static final Map<Integer, HcCustomerRiskTypeEnum> MAP = new HashMap<>();

    static {
        for (HcCustomerRiskTypeEnum value : HcCustomerRiskTypeEnum.values()) {
            MAP.put(value.code, value);
        }
    }

    public static HcCustomerRiskTypeEnum of(Integer code) {
        return MAP.get(code);
    }
}
```

### 3.3 风控提示类型 (RiskCustomerRiskTipTypeEnum)

```java
public enum RiskCustomerRiskTipTypeEnum {
    white("白名单通过"),
    black("您已被列入风险名单，暂时无法使用该功能"),
    ban_send("您已被列入风险名单，暂时无法派单"),
    ban_receive("您已被列入风险名单，暂时无法接单"),
    ban_coupon("您已被列入风险名单，暂时无法领券"),
    ban_one_to_one("您已被列入风险名单，暂时无法接该乘客的订单"),
    ban_ync_receive("您已被列入风险名单，暂时无法接网约车订单"),
    tx_black("您已被列入风险名单，暂时无法接单"),
    tx_ban_one_to_one("您已被列入风险名单，暂时无法接该乘客的订单"),
    ban_bus_create_order("您已被列入风险名单，暂时无法购买汽车票"),
    ban_credit_auth("您已被列入风险名单，暂时无法授权"),
    disable_register("您已被列入风险名单，暂时无法注册"),
    disable_login("您已被列入风险名单，暂时无法登录"),
    disable_receive_order("您已被列入风险名单，暂时无法接单"),
    disable_withdraw("您已被列入风险名单，暂时无法提现"),
    disable_publish("您已被列入风险名单，暂时无法发布");

    private String msg;

    RiskCustomerRiskTipTypeEnum(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }
}
```

在下一部分中，我们将详细分析特殊场景检查的实现。
