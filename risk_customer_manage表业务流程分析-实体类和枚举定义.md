# risk_customer_manage表业务流程分析 - 实体类和枚举定义

## 1. 核心实体类定义

### 1.1 RiskCustomerManage实体类

`RiskCustomerManage`是risk_customer_manage表对应的实体类，定义了风控客户管理的核心数据结构。

```java
@Data
public class RiskCustomerManage {
    // 主键ID
    private Long id;
    
    // 客户类型（1-用户ID，2-用户手机号，6-司机车牌号等）
    private Integer customerType;
    
    // 客户值（如车牌号、手机号等）
    private String customerValue;
    
    // 风险类型（1-黑名单，2-白名单，7-一对一名单等）
    private Integer riskType;
    
    // 状态（1-有效，2-失效，3-已删除）
    private Integer status;
    
    // 有效期限（天数，-1表示永久）
    private Integer ttl;
    
    // 创建时间
    private Date createTime;
    
    // 更新时间
    private Date updateTime;
    
    // 失效时间
    private Date invalidTime;
    
    // 操作类型（1-系统操作，2-人工操作，3-客户操作）
    private Integer optionType;
    
    // 创建人
    private String createUser;
    
    // 操作人
    private String optionName;
    
    // 风险备注（拉黑原因）
    private String riskRemark;
    
    // 绑定用户（乘客手机号）
    private String bindUser;
    
    // 绑定订单
    private String bindOrder;
    
    // 供应商名称
    private String supplierName;
    
    // 上车地址
    private String startAddress;
    
    // 下车地址
    private String endAddress;
    
    // 会员ID
    private String memberId;
}
```

### 1.2 相关DTO类

#### RiskCustomerManageDto - 风控名单展示DTO
```java
@Data
public class RiskCustomerManageDto {
    private Long id;
    private String customerType;    // 客户类型描述
    private String customerValue;   // 脱敏后的客户值
    private String riskType;        // 风险类型描述
    private String status;          // 状态描述
    private String ttl;             // 有效期描述
    private Date createTime;
    private Date updateTime;
    private String optionType;      // 操作类型描述
    private String createUser;
    private String optionName;
    private String riskRemark;
    private String bindUser;        // 脱敏后的绑定用户
    private String delRemark;       // 删除备注
    private Boolean disabled;       // 是否已失效
}
```

#### RiskCustomerAddParams - 添加风控名单参数
```java
@Data
public class RiskCustomerAddParams {
    private Integer customerType;   // 客户类型
    private String customerValue;   // 客户值
    private Integer riskType;       // 风险类型
    private Integer ttl;            // 有效期
    private String riskRemark;      // 风险备注
    private String bindUser;        // 绑定用户
    private String memberId;        // 会员ID
    private String supplierName;    // 供应商名称
}
```

## 2. 核心枚举定义

### 2.1 RiskCustomerRiskTypeEnum - 风险类型枚举

```java
public enum RiskCustomerRiskTypeEnum {
    black_list(1, "黑名单"),
    white_list(2, "白名单"),
    ban_coupon_list(3, "禁止领券名单"),
    ban_reward_list(4, "禁止奖励名单"),
    ban_send_list(5, "禁止派单名单"),
    ban_receive_list(6, "禁止接单名单"),
    ban_one_to_one_list(7, "一对一名单"),
    ban_register_list(8, "禁止注册名单"),
    ban_ync_receive_list(9, "禁止网约车接单名单"),
    ban_bus_create_order(10, "禁止巴士创单名单"),
    ban_credit_auth_list(11, "禁止信用认证名单"),
    tx_ban_one_to_one_list(12, "特享一对一名单");
    
    private Integer code;
    private String msg;
    
    // 根据code获取描述信息
    public static String getMsgByCode(Integer code) {
        for (RiskCustomerRiskTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMsg();
            }
        }
        return "";
    }
}
```

### 2.2 RiskCustomerCustomerTypeEnum - 客户类型枚举

```java
public enum RiskCustomerCustomerTypeEnum {
    user_id(1, "用户ID"),
    user_phone(2, "用户手机号"),
    user_cert_no(3, "用户身份证号"),
    user_device_id(4, "用户设备ID"),
    user_union_id(5, "用户UnionID"),
    car_number(6, "司机车牌号"),
    driver_phone(7, "司机手机号"),
    driver_cert_no(8, "司机身份证号"),
    driver_id(9, "司机ID"),
    pay_account(10, "支付账号");
    
    private Integer code;
    private String msg;
}
```

### 2.3 RiskCustomerStatusEnum - 状态枚举

```java
public enum RiskCustomerStatusEnum {
    valid(1, "有效"),
    invalid(2, "失效"),
    deleted(3, "已删除");
    
    private Integer code;
    private String msg;
}
```

### 2.4 RiskCustomerOptionTypeEnum - 操作类型枚举

```java
public enum RiskCustomerOptionTypeEnum {
    system(1, "系统操作"),
    user(2, "人工操作"),
    customer(3, "客户操作");
    
    private Integer code;
    private String msg;
}
```

### 2.5 RiskCustomerTtlEnum - 有效期枚举

```java
public enum RiskCustomerTtlEnum {
    permanent(-1, "永久"),
    custom(0, "自定义"),
    one_day(1, "1天"),
    three_days(3, "3天"),
    seven_days(7, "7天"),
    thirty_days(30, "30天"),
    ninety_days(90, "90天"),
    one_year(365, "1年");
    
    private Integer code;
    private String msg;
}
```

## 3. 参数类定义

### 3.1 查询参数类

#### RiskCustomerListParams - 风控名单查询参数
```java
@Data
public class RiskCustomerListParams extends BasePageParams {
    private Integer customerType;   // 客户类型
    private String customerValue;   // 客户值
    private Integer riskType;       // 风险类型
    private Integer status;         // 状态
    private String invalidTime;     // 失效时间
    private String createTimeStart; // 创建时间开始
    private String createTimeEnd;   // 创建时间结束
}
```

#### FilterParams - 风控过滤参数
```java
@Data
public class FilterParams {
    private String memberId;        // 会员ID
    private String unionId;         // UnionID
    private String userPhone;       // 用户手机号
    private String driverCardNo;    // 司机车牌号
    private String payAccount;      // 支付账号
    private String deviceId;        // 设备ID
    private List<String> idCardNos; // 身份证号列表
    private Integer sourceId;       // 来源ID
    private Integer mainScene;      // 主场景
    private Integer childScene;     // 子场景
}
```

### 3.2 同步参数类

#### DriverSyncParams - 司机同步参数
```java
@Data
public class DriverSyncParams {
    private String driverCardNo;    // 司机车牌号
    private String driverPhone;     // 司机手机号
    private String riskRemark;      // 风险备注
    private Integer ttl;            // 有效期
    private String optionName;      // 操作人
    private String orderId;         // 订单ID
    private String passengerPhone;  // 乘客手机号
}
```

## 4. Excel导入导出Bean

### 4.1 RiskCustomerExcelBean - 风控名单Excel导入Bean
```java
@Data
public class RiskCustomerExcelBean {
    @ExcelProperty("客户类型")
    private String customerType;
    
    @ExcelProperty("客户值")
    private String customerValue;
    
    @ExcelProperty("风险类型")
    private String riskType;
    
    @ExcelProperty("有效期")
    private String ttl;
    
    @ExcelProperty("风险备注")
    private String riskRemark;
    
    @ExcelProperty("绑定用户")
    private String bindUser;
    
    @ExcelProperty("会员ID")
    private String memberId;
    
    @ExcelProperty("供应商名称")
    private String supplierName;
}
```

### 4.2 RiskCustomerExcelDeleteBean - 风控名单Excel删除Bean
```java
@Data
public class RiskCustomerExcelDeleteBean {
    @ExcelProperty("客户类型")
    private String customerType;
    
    @ExcelProperty("客户值")
    private String customerValue;
    
    @ExcelProperty("风险类型")
    private String riskType;
    
    @ExcelProperty("删除备注")
    private String delRemark;
}
```

## 5. 业务常量定义

### 5.1 风控相关常量
```java
public class RiskConstants {
    // 缓存Key前缀
    public static final String CACHE_KEY_PREFIX = "TC_CUSTOMER_";
    
    // 默认失效时间
    public static final String DEFAULT_INVALID_TIME = "2099-12-31 23:59:59";
    
    // 批量操作限制
    public static final int BATCH_SIZE_LIMIT = 1000;
    
    // Excel导出限制
    public static final int EXPORT_SIZE_LIMIT = 50000;
}
```

## 6. 数据验证规则

### 6.1 字段验证规则

1. **customerType**: 必须在枚举范围内(1-10)
2. **customerValue**: 非空，长度不超过100字符
3. **riskType**: 必须在枚举范围内(1-12)
4. **ttl**: -1表示永久，0表示自定义，正数表示天数
5. **status**: 必须在枚举范围内(1-3)
6. **bindUser**: 一对一名单时必填
7. **riskRemark**: 建议填写，长度不超过500字符

### 6.2 业务验证规则

1. **名单覆盖逻辑**: 
   - 黑名单与白名单互斥
   - 禁止类名单与黑/白名单互斥
   - 一对一名单需要绑定用户

2. **有效期逻辑**:
   - ttl=-1时，invalidTime设置为2099-12-31 23:59:59
   - ttl>0时，invalidTime=createTime+ttl天
   - ttl=0时，使用自定义的invalidTime

3. **状态流转**:
   - 新增: status=1(有效)
   - 删除: status=2(失效)
   - 清理: status=3(已删除)

## 7. 数据脱敏规则

为保护用户隐私，系统对敏感字段进行脱敏处理：

1. **手机号脱敏**: 保留前3位和后4位，中间用*替代
2. **身份证号脱敏**: 保留前4位和后4位，中间用*替代
3. **车牌号**: 不脱敏，业务需要
4. **设备ID**: 部分脱敏

脱敏处理主要在查询和导出时进行，确保敏感信息不会泄露给前端用户。
