# car-risk项目 `/riskCheck/unifyCheck` 接口分析 - 第4部分：风控策略模型与规则引擎

## 1. 风控策略数据模型

风控策略数据模型是风控系统的核心，它定义了风控策略的结构和关系。主要包括以下几个部分：

### 1.1 风控策略 (RiskStrategyDetail)

风控策略是风控系统的核心，它定义了风控规则的集合和处置动作：

```java
@Data
public class RiskStrategyDetail {
    // 策略id
    private Long strategyId;
    //策略编号
    private String strategyNo;
    // 场景
    private String scene;
    // 业务线
    private List<String> productLine;
    // 渠道(852,433)
    private List<String> channels;
    // 规则
    private List<RiskRuleDetail> rules = new ArrayList<>();
    // 策略分类 0-安全 1-风控 2-全部
    private Integer strategyType;
    //风险等级 1-低 2-中 3-高
    private Integer level;
    //风险类型 0-安全 1-风控 2-全部
    private Integer riskType;
    //城市id，空为全国
    private String cityId;
    //表达式
    private String expression;
    //执行脚本
    private String script;
    //策略返回文案
    private String strategyWord;
    //管控时间 单位天
    private Integer controlTime;
    //管控对象 0-司机 1-用户
    private Integer controlType;
    //命中字段
    private String hitField;
    //命中动作0:加全局黑 1-加1v1
    private Integer hitAction;
    //处置动作 0-禁止 1-增强校验 2-通过
    private Integer disposeAction;
    // 0-测试 1-上线运行 2-下线
    private int status;
    // 供应商
    private List<String> supplierCodes;
}
```

### 1.2 风控规则 (RiskRuleDetail)

风控规则定义了风控指标的集合和判断条件：

```java
@Data
public class RiskRuleDetail {
    // 规则id
    private Long ruleId;
    // 规则编号
    private String ruleNo;
    // 规则脚本
    private String script;
    // 规则数据 数组，实体为"left":"characteristic" "operator":">" "type":"1" "right":"characteristic"
    private String ruleJson;
    // 规则表达式
    private String expression;
    // 指标
    private List<RiskFieldDetail> riskFields = new ArrayList<>();
}
```

### 1.3 风控指标 (RiskFieldDetail)

风控指标定义了风控判断的具体指标和计算方法：

```java
@Data
public class RiskFieldDetail {
    private String fieldId;
    // 指标编号
    private String fieldNo;
    // 运算符
    private String operator;
    // 0-常量 1-特征
    private int rightType;
    // 对比阈值
    private String rightValue;
    // 特征指标动态代码
    private String script;
    // 指标分类
    private int category;
    // 指标类型
    private int type;
    // 指标主体
    private int target;
    // 排序
    private int sort;
    // 自检指标 1-基于当前订单 0-基于历史订单
    private Integer basedCurrent;
}
```

### 1.4 风控策略结果 (RiskStrategyResult)

风控策略结果记录了策略执行的结果：

```java
@Data
@Builder
public class RiskStrategyResult {
    private Long strategyId;
    private String strategyNo;
    private Boolean strategyMatched;
    private List<String> matchRules;
}
```

### 1.5 风控规则结果 (RiskRuleResult)

风控规则结果记录了规则执行的结果：

```java
@Data
@Builder
public class RiskRuleResult {
    private Long ruleId;
    private String ruleNo;
    private Boolean ruleMatched;
}
```

### 1.6 风控场景结果 (RiskSceneResult)

风控场景结果记录了整个风控检查的结果：

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskSceneResult {
    private boolean riskFlag;
    private List<String> matchedStrategy = new ArrayList<>();
    private String riskMsg;
    private Integer customerType;

    public static RiskSceneResult pass(String msg){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(false);
        result.setRiskMsg(msg);
        return result;
    }

    public static RiskSceneResult fail(String msg){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(true);
        result.setRiskMsg(msg);
        return result;
    }
    
    public static RiskSceneResult fail(String msg, Integer customerType){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(true);
        result.setRiskMsg(msg);
        result.setCustomerType(customerType);
        return result;
    }
}
```

## 2. 规则引擎实现

规则引擎是风控系统的核心，它负责执行风控规则和策略。主要包括以下几个部分：

### 2.1 策略加载 (RiskStrategyHelper)

策略加载负责从数据库加载风控策略，并定期刷新：

```java
@Service
@Slf4j
public class RiskStrategyHelper implements InitializingBean {
    @Resource
    private MetricSceneStrategyRelationMapper sceneStrategyRelationMapper;
    @Resource
    private MetricFieldMapper fieldMapper;

    private CopyOnWriteArrayList<RiskStrategyDetail> riskStrategy = new CopyOnWriteArrayList<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1);
        executorService.scheduleWithFixedDelay(() -> initRiskStrategy(), 0, 1, TimeUnit.MINUTES);
    }

    private void initRiskStrategy() {
        LoggerUtils.initLogMap("STRATEGY_INIT", "", "", "");
        List<RiskStrategyDetail> riskStrategyList = new ArrayList<>();
        try {
            //
            List<RiskSceneStrategyDTO> sceneStrategyList = sceneStrategyRelationMapper.findSceneStrategy();
            // 将策略和规则的关系查出来
            List<RiskStrategyRuleDTO> strategyRuleList = sceneStrategyRelationMapper.findStrategyRule();
            Map<Long, List<RiskStrategyRuleDTO>> strategyRuleMap = strategyRuleList.stream().collect(Collectors.groupingBy(RiskStrategyRuleDTO::getStrategyId));
            // 规则和指标的关系查出来
            Map<Long, List<RiskRuleFieldDTO>> ruleFieldMap = sceneStrategyRelationMapper.findRuleField().stream().collect(Collectors.groupingBy(RiskRuleFieldDTO::getRuleId));
            //
            Map<Long, MetricField> fieldMap = fieldMapper.selectList(new QueryWrapper<>()).stream().collect(Collectors.toMap(p -> p.getId(), v -> v, (k1, k2) -> k1));

            for (RiskSceneStrategyDTO sceneStrategy : sceneStrategyList) {
                RiskStrategyDetail riskStrategy = new RiskStrategyDetail();
                riskStrategyList.add(riskStrategy);
                // 1.策略基础内容
                BeanUtils.copyProperties(sceneStrategy, riskStrategy);
                riskStrategy.setProductLine(StrUtil.split(sceneStrategy.getProductLines(), ",", true, true));
                riskStrategy.setChannels(StrUtil.split(sceneStrategy.getChannels(), ",", true, true));
                riskStrategy.setSupplierCodes(StrUtil.split(sceneStrategy.getSupplierCodes(), ",", true, true));
                List<RiskRuleDetail> ruleDetails = new ArrayList<>();
                riskStrategy.setRules(ruleDetails);
                // 2.查出规则
                List<RiskStrategyRuleDTO> ruleList = strategyRuleMap.get(sceneStrategy.getStrategyId());
                if(CollUtil.isEmpty(ruleList)){
                    continue;
                }
                for (RiskStrategyRuleDTO strategyRule : ruleList) {
                    RiskRuleDetail riskRuleDetail = new RiskRuleDetail();
                    ruleDetails.add(riskRuleDetail);
                    BeanUtils.copyProperties(strategyRule, riskRuleDetail);
                    // 3.规则查指标
                    List<RiskRuleFieldDTO> ruleFieldList = ruleFieldMap.get(strategyRule.getRuleId());
                    if (CollUtil.isEmpty(ruleFieldList)) {
                        continue;
                    }
                    List<RiskFieldDetail> riskFields = ruleFieldList.stream().map(ruleField -> {
                        RiskFieldDetail fieldDetail = new RiskFieldDetail();
                        BeanUtils.copyProperties(ruleField, fieldDetail);
                        MetricField metricField = fieldMap.get(ruleField.getFieldId());
                        if (null != metricField) {
                            fieldDetail.setCategory(metricField.getCategory());
                            fieldDetail.setType(metricField.getType());
                            fieldDetail.setTarget(metricField.getTarget());
                            fieldDetail.setBasedCurrent(metricField.getBasedCurrent());
                        }
                        return fieldDetail;
                    }).collect(Collectors.toList());
                    riskRuleDetail.setRiskFields(riskFields);
                }
            }

            riskStrategy = new CopyOnWriteArrayList<>(riskStrategyList);

        } catch (Exception ex) {
            LoggerUtils.error(log, "策略初始化 异常", ex);
        } finally {
            LoggerUtils.info(log,"策略初始化结束，初始化策略条数：{}",riskStrategy.size());
            LoggerUtils.removeAll();
        }
    }
    
    public List<RiskStrategyDetail> findStrategy(String productLine, String channel, String scene, String supplierCode) {
        List<RiskStrategyDetail> strategyList = riskStrategy.stream().filter(p -> p.getProductLine().contains(productLine)
                        && (StringUtils.isBlank(channel) || p.getChannels().contains(channel) || p.getChannels().contains("all"))
                        && Objects.equals(p.getScene(), scene))
                .collect(Collectors.toList());
        
        // 供应商为空不过滤
        if (StringUtils.isBlank(supplierCode)) {
            return strategyList;
        }
        
        // 精准过滤出策略才执行
        List<RiskStrategyDetail> supplierStrategylist = strategyList.stream().filter(s -> s.getSupplierCodes().contains(supplierCode)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supplierStrategylist)) {
            return supplierStrategylist;
        }
        
        return strategyList;
    }
}
```

### 2.2 脚本执行 (GroovyScriptUtil)

脚本执行负责执行Groovy脚本，计算风控指标和规则：

```java
public class GroovyScriptUtil {
    private static final Map<String, Class> CLASS_CACHE = new ConcurrentHashMap<>();
    
    public static Object invokeMethod(String script, String methodName, Object[] args) {
        Class clazz = getScriptClass(script);
        if (clazz == null) {
            return null;
        }
        try {
            Object instance = clazz.newInstance();
            Method method = clazz.getMethod(methodName, getParameterTypes(args));
            return method.invoke(instance, args);
        } catch (Exception e) {
            throw new RuntimeException("执行脚本失败", e);
        }
    }
    
    private static Class getScriptClass(String script) {
        String key = DigestUtils.md5Hex(script);
        Class clazz = CLASS_CACHE.get(key);
        if (clazz != null) {
            return clazz;
        }
        
        try {
            GroovyClassLoader loader = new GroovyClassLoader();
            clazz = loader.parseClass(script);
            CLASS_CACHE.put(key, clazz);
            return clazz;
        } catch (Exception e) {
            throw new RuntimeException("解析脚本失败", e);
        }
    }
    
    private static Class[] getParameterTypes(Object[] args) {
        if (args == null) {
            return new Class[0];
        }
        
        Class[] types = new Class[args.length];
        for (int i = 0; i < args.length; i++) {
            types[i] = args[i].getClass();
        }
        return types;
    }
}
```

### 2.3 策略执行 (RiskStrategyHandler)

策略执行负责执行风控策略，判断是否命中风控规则：

```java
private RiskStrategyResult checkStrategy(RiskStrategyDetail strategyDetail, Map<String, Object> params) {
    String strategyScript = strategyDetail.getScript();
    List<RiskRuleDetail> rules = strategyDetail.getRules();
    if (CollUtil.isEmpty(rules)) {
        return RiskStrategyResult.builder().strategyMatched(false).build();
    }
    Map<String, Object> ruleDataMap = new HashMap<>();
    List<String> matchRules = new ArrayList<>();
    for (RiskRuleDetail rule : rules) {
        // 1.校验规则
        RiskRuleResult simpleRuleResult = checkRule(rule, params);
        LoggerUtils.info(log, "规则:{} 执行结果为:{}", rule.getRuleNo(), simpleRuleResult.getRuleMatched());
        // 2.收集规则结果
        ruleDataMap.put("rule" + rule.getRuleId(), simpleRuleResult.getRuleMatched());
        if (simpleRuleResult.getRuleMatched()) {
            matchRules.add(simpleRuleResult.getRuleNo());
        }
    }
    // 3.校验策略
    boolean strategyMatched = checkStrategyScript(strategyScript, ruleDataMap, strategyDetail.getStrategyNo());
    
    return RiskStrategyResult.builder()
            .strategyId(strategyDetail.getStrategyId())
            .strategyNo(strategyDetail.getStrategyNo())
            .strategyMatched(strategyMatched)
            .matchRules(matchRules).build();
}

private RiskRuleResult checkRule(RiskRuleDetail rule, Map<String, Object> params) {
    String ruleScript = rule.getScript();
    List<RiskFieldDetail> riskFields = rule.getRiskFields();
    Map<String, Object> fieldDataMap = new HashMap<>();
    for (RiskFieldDetail riskField : riskFields) {
        String fieldScript = riskField.getScript();
        // 通过groovy脚本，从策略中获取值
        HashMap<String, Object> result = getFieldScriptResult(fieldScript, params, riskField.getFieldNo());
        String num = Optional.ofNullable(result.get("num")).map(String::valueOf).orElse("");
        double value;
        if (StringUtils.isBlank(num)) {
            value = 0.0;
        } else {
            value = new BigDecimal(num).setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
        fieldDataMap.put("field" + riskField.getFieldId(), value);
    }
    boolean ruleResult = checkRuleScript(ruleScript, fieldDataMap, rule.getRuleNo());
    return RiskRuleResult.builder().ruleId(rule.getRuleId()).ruleNo(rule.getRuleNo()).ruleMatched(ruleResult).build();
}

private Boolean checkStrategyScript(String strategyScript, Map data, String strategyNo) {
    Object[] args = { data };
    Boolean ret = false;
    try {
        ret = (Boolean) GroovyScriptUtil.invokeMethod(strategyScript, "check", args);
    } catch (Exception e) {
        LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,strategyNo:{}", e, strategyNo);
    }
    return ret;
}

private Boolean checkRuleScript(String ruleScript, Map data, String ruleNo) {
    Object[] args = { data };
    Boolean ret = false;
    try {
        ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
    } catch (Exception e) {
        LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,ruleNo:{}", e, ruleNo);
    }
    return ret;
}

private HashMap<String, Object> getFieldScriptResult(String ruleScript, Map data, String fieldNo) {
    Object[] args = { data };
    HashMap<String, Object> ret = null;
    try {
        ret = (HashMap<String, Object>) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
    } catch (Exception e) {
        LoggerUtils.error(log, "执行规则,groovy脚本失败,fieldNo:{}", e, fieldNo);
        ret = new HashMap<>();
    }
    return ret;
}
```

在下一部分中，我们将详细分析黑名单检查的实现。
