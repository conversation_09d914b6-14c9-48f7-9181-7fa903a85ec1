# 黑名单功能实体类代码分析

## 1. RiskCustomerManage 实体类分析

`RiskCustomerManage` 是黑名单功能的核心实体类，对应数据库中的 `risk_customer_manage` 表。

### 1.1 实体类定义

```java
@Data
public class RiskCustomerManage {
    // 主键ID
    private Long id;
    
    // 客户类型（1-用户ID，2-用户手机号，6-司机车牌号等）
    private Integer customerType;
    
    // 客户值（如车牌号、手机号等）
    private String customerValue;
    
    // 风险类型（1-黑名单，2-白名单，7-一对一名单等）
    private Integer riskType;
    
    // 状态（1-有效，2-失效，3-已删除）
    private Integer status;
    
    // 有效期限（天数，-1表示永久）
    private Integer ttl;
    
    // 创建时间
    private Date createTime;
    
    // 更新时间
    private Date updateTime;
    
    // 失效时间
    private Date invalidTime;
    
    // 操作类型（1-系统操作，2-人工操作，3-客户操作）
    private Integer optionType;
    
    // 创建人
    private String createUser;
    
    // 操作人
    private String optionName;
    
    // 风险备注（拉黑原因）
    private String riskRemark;
    
    // 绑定用户（乘客手机号）
    private String bindUser;
    
    // 绑定订单
    private String bindOrder;
    
    // 供应商名称
    private String supplierName;
    
    // 上车地址
    private String startAddress;
    
    // 下车地址
    private String endAddress;
    
    // 用车时间
    private String useTime;
    
    // 司机名称
    private String driverName;
    
    // 司机ID
    private String driverId;
    
    // 会员ID
    private String memberId;
}
```

## 2. RiskCustomerManageListDTO 实体类分析

`RiskCustomerManageListDTO` 是用于返回黑名单列表的数据传输对象。

### 2.1 实体类定义

```java
@Data
public class RiskCustomerManageListDTO {
    // 客户值（车牌号）
    private String customerValue;
    
    // 有效期
    private String invalidTime;
    
    // 司机名称
    private String driverName;
    
    // 订单ID
    private String orderId;
    
    // 拉黑时间
    private String shieldTime;
    
    // 司机车牌号
    private String driverCardNo;
    
    // 上车点
    private String startAddress;
    
    // 下车点
    private String endAddress;
    
    // 用车时间
    private String useTime;
}
```

## 3. 请求参数实体类分析

### 3.1 DriverBlackListRequest

`DriverBlackListRequest` 是 `/blackList/driver/list` 接口的请求参数实体类。

```java
@Data
public class DriverBlackListRequest {
    // 追踪ID
    private String traceId;
    
    // 乘车人手机号
    private String passengerCellphone;
}
```

### 3.2 DriverBlackRequest

`DriverBlackRequest` 是 `/blackList/driver/black` 接口的请求参数实体类。

```java
@Data
public class DriverBlackRequest {
    // 追踪ID
    private String traceId;
    
    // 乘车人手机号
    private String passengerCellphone;
    
    // 车牌号
    private String driverCardNo;
    
    // 司机名称
    private String driverName;
    
    // 拉黑原因
    private String remark;
    
    // 绑定订单，纯记录
    private String orderId;
    
    // 上车点
    private String startAddress;
    
    // 下车点
    private String endAddress;
    
    // 用车时间
    private String useTime;
    
    // 供应商名称
    private String supplierName;
    
    // 用户memberId
    private String memberId;
}
```

### 3.3 DriverRemoveRequest

`DriverRemoveRequest` 是 `/blackList/driver/remove` 接口的请求参数实体类。

```java
@Data
public class DriverRemoveRequest {
    // 追踪ID
    private String traceId;
    
    // 乘车人手机号
    private String passengerCellphone;
    
    // 车牌号
    private String driverCardNo;
}
```

### 3.4 DriverCheckInRequest

`DriverCheckInRequest` 是 `/blackList/driver/checkIn` 接口的请求参数实体类。

```java
@Data
public class DriverCheckInRequest {
    // 追踪ID
    private String traceId;
    
    // 订单ID
    private String orderId;
    
    // 车牌号
    private String driverCardNo;
}
```

### 3.5 BatchCarBlackInfoQueryRequest

`BatchCarBlackInfoQueryRequest` 是 `/blackList/batchQueryDriverBlack` 接口的请求参数实体类。

```java
@Data
public class BatchCarBlackInfoQueryRequest extends BaseRequest {
    // 车牌号列表
    private List<String> carNums;
    
    // 是否查询无效记录
    private Boolean queryInvalidFlag;
}
```

### 3.6 BlackSyncRequest

`BlackSyncRequest` 是 `/blackList/sync` 接口的请求参数实体类。

```java
@Data
public class BlackSyncRequest extends BaseRequest {
    /**
     * 1:腾讯出行黑名单同步
     */
    private Integer fromType;
    
    /**
     * 操作类型
     * 1: 全量同步（没有）
     * 2: 新增(更新)
     * 3: 删除
     */
    private Integer operationType;
    
    private List<DriverBlackVO> driverBlacklist;
    
    @Data
    public static class DriverBlackVO {
        /**
         * 车牌
         */
        private String plateNo;
        
        /**
         * 乘客手机号
         */
        private String passengerPhone;
        
        /**
         * 拉黑原因
         */
        private String controlReason;
        
        /**
         * 拉黑时间 毫秒 -1 表示永久
         */
        private Long expireTime;
    }
}
```

### 3.7 DriverBlackFromManageRequest

`DriverBlackFromManageRequest` 是 `/manage/blackList/driver/black` 接口的请求参数实体类。

```java
@Data
public class DriverBlackFromManageRequest {
    private List<BlackInfoManageDTO> blackList;
}
```

### 3.8 BlackInfoManageDTO

`BlackInfoManageDTO` 是客服拉黑司机时使用的数据传输对象。

```java
@Data
public class BlackInfoManageDTO {
    // 车牌号
    private String driverCardNo;
    
    // 司机名称
    private String driverName;
    
    // 拉黑原因
    private String remark;
    
    // 绑定订单
    private String orderId;
    
    // 客户类型（0-一对一，1-全部）
    private Integer customerType;
    
    // 有效期（天数）
    private Integer ttl;
}
```

## 4. 响应实体类分析

### 4.1 CarBlackInfo

`CarBlackInfo` 是批量查询车牌黑名单接口的响应实体类。

```java
@Data
public class CarBlackInfo {
    // 车牌号
    private String carNum;
    
    // 风险类型
    private Integer riskType;
    
    // 风险类型名称
    private String riskTypeName;
    
    // 失效时间
    private Date invalidTime;
    
    // 绑定用户
    private String bindUser;
}
```

### 4.2 UiResult

`UiResult` 是通用的响应结果实体类。

```java
@Data
public class UiResult<T> {
    // 状态码
    private Integer code;
    
    // 消息
    private String message;
    
    // 数据
    private T data;
    
    // 是否成功
    private Boolean success;
    
    // 静态方法，创建成功结果
    public static <T> UiResult<T> ok() {
        UiResult<T> result = new UiResult<>();
        result.setCode(200);
        result.setMessage("success");
        result.setSuccess(true);
        return result;
    }
    
    // 静态方法，创建成功结果并设置数据
    public static <T> UiResult<T> ok(T data) {
        UiResult<T> result = ok();
        result.setData(data);
        return result;
    }
    
    // 静态方法，创建失败结果
    public static <T> UiResult<T> fail() {
        UiResult<T> result = new UiResult<>();
        result.setCode(500);
        result.setMessage("fail");
        result.setSuccess(false);
        return result;
    }
    
    // 静态方法，创建失败结果并设置错误码和消息
    public static <T> UiResult<T> fail(Integer code, String message) {
        UiResult<T> result = fail();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}
```

### 4.3 UiResultWrapper

`UiResultWrapper` 是对 `UiResult` 的包装，用于控制器层返回结果。

```java
@Data
public class UiResultWrapper {
    // 状态码
    private Integer code;
    
    // 消息
    private String message;
    
    // 数据
    private Object data;
    
    // 是否成功
    private Boolean success;
    
    // 静态方法，将 UiResult 转换为 UiResultWrapper
    public static UiResultWrapper convert(UiResult<?> uiResult) {
        UiResultWrapper wrapper = new UiResultWrapper();
        wrapper.setCode(uiResult.getCode());
        wrapper.setMessage(uiResult.getMessage());
        wrapper.setData(uiResult.getData());
        wrapper.setSuccess(uiResult.getSuccess());
        return wrapper;
    }
    
    // 静态方法，创建成功结果
    public static UiResultWrapper ok() {
        UiResultWrapper wrapper = new UiResultWrapper();
        wrapper.setCode(200);
        wrapper.setMessage("success");
        wrapper.setSuccess(true);
        return wrapper;
    }
    
    // 静态方法，创建成功结果并设置数据
    public static UiResultWrapper ok(Object data) {
        UiResultWrapper wrapper = ok();
        wrapper.setData(data);
        return wrapper;
    }
    
    // 静态方法，创建失败结果
    public static UiResultWrapper fail() {
        UiResultWrapper wrapper = new UiResultWrapper();
        wrapper.setCode(500);
        wrapper.setMessage("fail");
        wrapper.setSuccess(false);
        return wrapper;
    }
    
    // 静态方法，创建失败结果并设置错误码和消息
    public static UiResultWrapper fail(Integer code, String message) {
        UiResultWrapper wrapper = fail();
        wrapper.setCode(code);
        wrapper.setMessage(message);
        return wrapper;
    }
}
```

## 5. 枚举类分析

### 5.1 RiskCustomerCustomerTypeEnum

`RiskCustomerCustomerTypeEnum` 是客户类型枚举。

```java
public enum RiskCustomerCustomerTypeEnum {
    user_id(1, "用户/ID"),
    user_phone(2, "用户/手机号"),
    user_device_id(3, "用户/设备号"),
    user_unionid(4, "用户/unionid"),
    user_pay_account(5, "用户/支付账号"),
    car_number(6, "司机/车牌号"),
    hc_member_id(7,"司机/司机id"),
    hc_phone(8,"司机/手机号"),
    hc_id_card(9,"司机/身份证号"),
    user_cert_no(10, "用户/证件号");
    
    private Integer code;
    private String msg;
    
    RiskCustomerCustomerTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    // getter 和 setter 方法...
}
```

### 5.2 RiskCustomerRiskTypeEnum

`RiskCustomerRiskTypeEnum` 是风险类型枚举。

```java
public enum RiskCustomerRiskTypeEnum {
    black_list(1, "黑名单", RiskCustomerRiskTipTypeEnum.black),
    white_list(2, "白名单", RiskCustomerRiskTipTypeEnum.white),
    ban_coupon_list(3, "禁止领券名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_reward_list(4, "禁止奖励名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_send_list(5, "禁止派单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_receive_list(6, "禁止接单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_one_to_one_list(7, "一对一名单", RiskCustomerRiskTipTypeEnum.one2one),
    ban_register_list(8, "禁止认证名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_ync_receive_list(9, "禁止网约车接单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_bus_create_order(10, "禁止汽车票下单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_credit_auth_list(11, "禁止信用授权名单", RiskCustomerRiskTipTypeEnum.forbid),
    tx_black_list(21,"腾讯黑名单", RiskCustomerRiskTipTypeEnum.black),
    tx_ban_one_to_one_list(22, "腾讯一对一名单", RiskCustomerRiskTipTypeEnum.one2one);
    
    private Integer code;
    private String msg;
    private RiskCustomerRiskTipTypeEnum tip;
    
    RiskCustomerRiskTypeEnum(Integer code, String msg, RiskCustomerRiskTipTypeEnum tip) {
        this.code = code;
        this.msg = msg;
        this.tip = tip;
    }
    
    // getter 和 setter 方法...
}
```

### 5.3 RiskCustomerStatusEnum

`RiskCustomerStatusEnum` 是状态枚举。

```java
public enum RiskCustomerStatusEnum {
    valid(1, "有效"),
    invalid(2, "失效"),
    del_flag(3, "标记为已删除");
    
    private Integer code;
    private String msg;
    
    RiskCustomerStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    // getter 和 setter 方法...
}
```

### 5.4 RiskCustomerOptionTypeEnum

`RiskCustomerOptionTypeEnum` 是操作类型枚举。

```java
public enum RiskCustomerOptionTypeEnum {
    system(1, "系统操作"),
    user(2, "人工操作"),
    customer(3, "客户操作");
    
    private Integer code;
    private String msg;
    
    RiskCustomerOptionTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    // getter 和 setter 方法...
}
```

### 5.5 RiskCustomerTtlEnum

`RiskCustomerTtlEnum` 是有效期限枚举。

```java
public enum RiskCustomerTtlEnum {
    one_day(1, "1天"),
    seven_day(7, "7天"),
    one_month(30, "一个月"),
    one_year(365, "一年"),
    forever(-1, "永久");
    
    private Integer code;
    private String msg;
    
    RiskCustomerTtlEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    // getter 和 setter 方法...
}
```

### 5.6 RiskCustomerRiskTipTypeEnum

`RiskCustomerRiskTipTypeEnum` 是风险提示类型枚举。

```java
@Getter
@AllArgsConstructor
public enum RiskCustomerRiskTipTypeEnum {
    white(0, "命中白名单"),
    black(1, "账户异常，请联系供应商处理"),
    one2one(2, "当前订单无法接取，请更换"),
    forbid(3, "账户存在风险问题，操作失败");
    
    private final Integer code;
    private final String msg;
}
```

## 6. 实体类总结

黑名单功能涉及多个实体类，主要包括：

1. **核心实体类**：`RiskCustomerManage`，对应数据库中的 `risk_customer_manage` 表
2. **数据传输对象**：`RiskCustomerManageListDTO`、`BlackInfoManageDTO`、`CarBlackInfo` 等
3. **请求参数实体类**：`DriverBlackListRequest`、`DriverBlackRequest`、`DriverRemoveRequest` 等
4. **响应实体类**：`UiResult`、`UiResultWrapper` 等
5. **枚举类**：`RiskCustomerCustomerTypeEnum`、`RiskCustomerRiskTypeEnum`、`RiskCustomerStatusEnum` 等

这些实体类共同构成了黑名单功能的数据模型层，用于数据的存储、传输和展示。
