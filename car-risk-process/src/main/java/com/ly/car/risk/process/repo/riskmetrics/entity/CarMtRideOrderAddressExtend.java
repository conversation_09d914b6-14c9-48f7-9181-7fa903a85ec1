package com.ly.car.risk.process.repo.riskmetrics.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 顺风车订单地址信息扩展表
 * car_mt_ride_order_address_extend
 */
@Data
public class CarMtRideOrderAddressExtend implements Serializable {
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 实际行驶里程
     */
    private BigDecimal realMileage;
    
    /**
     * 开始行驶时间
     */
    private Date starDrivingTime;
    
    /**
     * 结束行驶时间
     */
    private Date endDrivingTime;
    
    /**
     * 预估行驶时长，单位分钟
     */
    private Long drivingTime;
    
    /**
     * 司机端订单行驶状态(0:去接乘客 1:到达接客地 2:上车 3:下车 4:乘客未上车 5:订单取消)
     */
    private Integer drivingStatus;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    private static final long serialVersionUID = 1L;
}