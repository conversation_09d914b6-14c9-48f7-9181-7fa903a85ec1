package com.ly.car.risk.process.service.rule.mtGroup;

import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class MqSendConvertService {

    @Resource(name = "mtRiskProducer")
    private MqRiskProducer mtRiskProducer;
    @Resource(name = "hitchRiskProducer")
    private MqRiskProducer hitchRiskProducer;
    @Resource(name = "htRiskProducer")
    private MqRiskProducer htRiskProducer;

    public void sendNotifyMq(String productLine,String msg) {
        if (productLine.equals("MT")) {
            this.mtRiskProducer.send(MqTagEnum.getTagName(productLine), msg, 0L);
        } else if (productLine.equals("HT")) {
            this.htRiskProducer.send(MqTagEnum.getTagName(productLine), msg, 0L);
        } else {
            log.info("[][][][]未找到相应的mq！！");
        }
    }

    public MqRiskProducer getByProductLine(String productLine){
        if(productLine.equals("MT")){
            return mtRiskProducer;
        } else if(productLine.equals("HT")){
            return htRiskProducer;
        }
        return hitchRiskProducer;
    }

}
