package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;


public enum RiskCustomerOptionTypeEnum {

    system(1, "系统操作"),
    user_server(2, "人工操作"),
    user_client(3, "用户操作"),
    ;
    private Integer code;
    private String msg;

    RiskCustomerOptionTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskCustomerOptionTypeEnum enumItem : RiskCustomerOptionTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getCodeByMsg(String msg) {
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getMsg().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
}
