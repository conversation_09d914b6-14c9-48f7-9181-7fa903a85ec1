package com.ly.car.risk.process.repo.risk.mapper.entity;

import lombok.Data;

import java.util.Date;

@Data
public class SafeCenterAppealRecord {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 申诉工单类型 1人车不符 2-110告警 3-物品申诉 4-费用申诉
     */
    private Integer appealType;

    /**
     * 乘车人订单号
     */
    private String orderId;

    /**
     * 用户id
     */
    private String memberId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * 原始车牌号
     */
    private String originalCarNo;

    /**
     * 实际车牌
     */
    private String currentCarNo;

    /**
     * 反馈工单号
     */
    private String workOrderNo;

    /**
     * 审核联系人手机号
     */
    private String appearContactPhone;

    /**
     * 申诉问题合集,逗号隔开
     */
    private String appearQuestions;
    /**
     * 反馈时间
     */
    private Date appealTime;

    /**
     * 反馈内容
     */
    private String appealContent;

    /**
     * 反馈图片(多张)
     */
    private String appealImg;

    /**
     * 反馈结果 0未反馈 1已反馈
     */
    private Integer appealResults;

    /**
     * 是否给前端展示 0未展示 1已展示
     */
    private Integer showFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}

