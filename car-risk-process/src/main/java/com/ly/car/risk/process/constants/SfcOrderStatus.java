package com.ly.car.risk.process.constants;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 拼车订单状态，和原订单状态保持同步。
 */
public enum SfcOrderStatus {
    CREATE_SUCCESS(0, "已创建", "待支付", "订单支付时间剩余"),
    @Deprecated
    PAY_SUCCESS(5, "已支付", "派车中", ""),
    DISPATCH_SUCCESS(10, "已派单", "等待车主接单", "正在派车，系统将于30分钟内返回结果"),
    REDISPATCH_SUCCESS(11, "已改派单", "等待车主接单", "正在派车，系统将于30分钟内返回结果"),
    DECISION_SUCCESS(20, "司机已接单", "待出行", "司机已接单，您可联系司机商量行程问题"),
    SETOUT(50, "司机已出发", "待接驾", "司机已出发，请根据司机位置提前到达路边等候"),
    ARRIVED(100, "司机已到达", "待上车", "司机已到达指定地点，请尽快上车"),
    SERVICE(200, "服务开始", "行程中", "行程已开始，请系好安全带"),
    FINISH(300, "服务结束", "已完成", "本次行程已结束，期待您的再次使用"),
    CANCEL(1000, "取消", "已取消", "订单已取消，期待您的再次使用");


    public int code;
    public String name;
    public String status;
    public String description;

    SfcOrderStatus(int code, String name, String status, String description) {
        this.code = code;
        this.name = name;
        this.status = status;
        this.description = description;
    }

    final static Map<Integer, SfcOrderStatus> cache = Stream.of(SfcOrderStatus.values())
            .collect(Collectors.toMap(e -> e.code, e -> e));

    public static SfcOrderStatus valueOfCode(int code) {
        return cache.get(code);
    }

    public static String orderStatusText(int code) {
        String text = "";
        if (code == 0){
            text = "等待付款";
        }else if (code == 5){
            text = "行程已发布，等待车主接单...";
        }else if (code == 10){
            text = "行程已发布，等待车主接单...";
        }else if (code == 11){
            text = "行程已发布，等待车主接单...";
        }else if (code == 300){
            text = "本次行程已结束";
        }else if (code == 1000){
            text = "订单已取消";
        }
        return text;
    }

}
