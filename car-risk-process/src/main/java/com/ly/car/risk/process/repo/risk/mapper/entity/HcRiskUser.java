package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class HcRiskUser {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String orderId;
    private String userName;
    private Integer sex;
    private Integer age;
    private String emergencyContact;
    private String userPhone;
    private String userPhonePlace;
    private String passengerPhone;
    private String activityAddress;//常活动地
    private String lastActivityAddress;//最后活动地点
    private String deviceId;//常用设备信息
    private String orderDeviceId;//订单设备信息
    private String accountPhoneRecord;//账号换绑记录
    private String memberLevel;
    private String startAddress;//开始地址
    private String endAddress;//目的地
    private String startLngLat;//开始经纬度
    private String endLngLat;//目的地经纬度
    private Date orderCreateTime;
    private Date orderServiceTime;
    private Date finishTime;
    private Date payTime;
    private String formChangeRecord;
    private String costAddRecord;
    private Integer driverEvaluation;
    private Date createTime;
    private Date updateTime;



}
