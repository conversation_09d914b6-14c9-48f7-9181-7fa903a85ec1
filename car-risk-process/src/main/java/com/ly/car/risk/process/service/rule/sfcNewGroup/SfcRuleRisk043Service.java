package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 一个memberId在24小时内对同一手机号前7位段下单>=5单，且这些手机号去重后>=4个，且80%的订单接单后取消，并且没有一笔完单
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk043Service extends FilterSfcHandler{

    private static final String ruleNo = "043";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRisk043Service][][]前置判断已通过，进入规则043判断{}", JsonUtils.json(context.getUserContextList()));
        try {
            if(!context.getSfcRiskRuleConfig().getOnOff043()){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }
            //获取取消的
            List<OrderStatusCancelDTO> orderStatusCancelDTOList = context.getMemberCancelList().stream()
                    .filter(data->data.getCancelTime().after(DateUtil.addDay(new Date(),-context.getSfcRiskRuleConfig().getTime043())))
                    .distinct()
                    .collect(Collectors.toList());
            //获取完单的
            List<OrderRiskContext> orderRiskContextList = context.getUserContextList().stream()
                    .filter(data->DateUtil.string2Date(data.getFinishTime()).after(DateUtil.addHour(new Date(),-context.getSfcRiskRuleConfig().getTime043())))
                    .distinct()
                    .collect(Collectors.toList());

            if(CollectionUtils.isEmpty(orderRiskContextList) && CollectionUtils.isNotEmpty(orderStatusCancelDTOList)){
                //判断这些手机号是否大于等于4个
                List<String> phoneList = orderStatusCancelDTOList.stream().map(OrderStatusCancelDTO::getPassengerCellphone)
                        .distinct()
                        .collect(Collectors.toList());
                Map<String,List<String>> phoneMap = new HashMap<>();
                for(String str : phoneList){
                    String splitPhone = str.substring(0,7);
                    if(phoneMap.get(splitPhone) == null){
                        List<String> insertList = new ArrayList<>();
                        insertList.add(str);
                        phoneMap.put(splitPhone,insertList);
                    } else {
                        List<String> insertList = phoneMap.get(splitPhone);
                        insertList.add(str);
                        phoneMap.put(splitPhone,insertList);
                    }
                }
                //对去掉订单这个数组里的对象手机号进行切割
                orderStatusCancelDTOList.forEach(data->data.setPassengerCellphone(data.getPassengerCellphone().substring(0,7)));
                //判断多少是接单后取消的订单
                List<OrderStatusCancelDTO> acceptAfter = orderStatusCancelDTOList.stream().filter(data->data.getType()==1).collect(Collectors.toList());
                //计算接单后取消占比
                BigDecimal rate = new BigDecimal(acceptAfter.size()).divide(new BigDecimal(orderStatusCancelDTOList.size()),2,BigDecimal.ROUND_CEILING);
                //对分割后的手机号进行分组取数
                Map<String,List<OrderStatusCancelDTO>> contextGroup = orderStatusCancelDTOList.stream()
                        .collect(Collectors.groupingBy(OrderStatusCancelDTO::getPassengerCellphone));
                for(Map.Entry<String, List<OrderStatusCancelDTO>> entry : contextGroup.entrySet()){
                    //判断这个号段对应原始手机号是否是大于阈值
                    if(phoneMap.size() < context.getSfcRiskRuleConfig().getPhoneNum043()){
                        continue;
                    }
                    if(entry.getValue().size() >= context.getSfcRiskRuleConfig().getOrderNum043()
                            && rate.compareTo(context.getSfcRiskRuleConfig().getRate043()) > 0){
                        //命中
                        List<String> orderIds = acceptAfter.stream().map(OrderStatusCancelDTO::getOrderId).collect(Collectors.toList());
                        distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                        log.info("[SfcRuleRisk043Service][doHandler][{}][{}]命中043规则{}", context.getMemberId(), context.getUnionId(), orderIds);
                        if(StringUtils.isBlank(context.getRuleNo())){
                            context.setRuleNo(ruleNo);
                        } else {
                            context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                        }
                        riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                        if(context.getSfcRiskRuleConfig().getSync043()) {
                            RiskResultDTO dto = new RiskResultDTO(405, "风控不通过043", null, null);
                            context.getUiResult().setData(dto);
                            context.getUiResult().setMsg("风控不通过");
                            break;
                        } else {
                            break;
                        }
                    }
                }
            }
        } catch (Exception e){
            log.error("[SfcRuleRisk043Service][doHandler][{}][{}]报错信息:",context.getMemberId(),context.getUnionId(),e);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }

    public static void main(String[] args) {
        String strList1 =  "{\"cancelTime\":1682501468386,\"memberId\":\"2931313563\",\"orderId\":\"SFC6448UF1D8003424BB5\",\"passengerCellphone\":\"15768240824\",\"startCityId\":82,\"type\":1}";
        String strList2 =  "{\"cancelTime\":1682510735049,\"memberId\":\"2931313563\",\"orderId\":\"SFC6449131U70034790F9\",\"passengerCellphone\":\"15768240824\",\"startCityId\":82,\"type\":0}";
        String strList3 =  "{\"cancelTime\":1682514866593,\"memberId\":\"2931313563\",\"orderId\":\"SFC6449232B90034D0CF4\",\"passengerCellphone\":\"15768240824\",\"startCityId\":82,\"type\":1}";

        String strList4 = "{\"cancelTime\":1682515150465,\"memberId\":\"2931313563\",\"orderId\":\"SFC6449242690034D2BF7\",\"passengerCellphone\":\"13256895754\",\"startCityId\":82,\"type\":1}";
        String strList5 =       "{\"cancelTime\":1682521192535,\"memberId\":\"2931313563\",\"orderId\":\"SFC64493C2170035001FU\",\"passengerCellphone\":\"15768249658\",\"startCityId\":82,\"type\":1}";
        String strList6 =         "{\"cancelTime\":1682571730206,\"memberId\":\"2931313563\",\"orderId\":\"SFC644A010U9003676756\",\"passengerCellphone\":\"15768249635\",\"startCityId\":82,\"type\":1}";
        String strList7 =       "{\"cancelTime\":1682579173143,\"memberId\":\"2931313563\",\"orderId\":\"SFC644A1UC470036BAUA6\",\"passengerCellphone\":\"15768240824\",\"startCityId\":82,\"type\":1}";
        String strList8 =        "{\"cancelTime\":1682597926675,\"memberId\":\"2931313563\",\"orderId\":\"SFC644A679690037A9AD5\",\"passengerCellphone\":\"15768248529\",\"startCityId\":82,\"type\":1}";
        String strList9 =       "{\"cancelTime\":1682600958238,\"memberId\":\"2931313563\",\"orderId\":\"SFC644A738A90037C85UB\",\"passengerCellphone\":\"15768248529\",\"startCityId\":82,\"type\":1}";

        List<String> strList = new ArrayList<>();
        strList.add(strList1);
        strList.add(strList2);
        strList.add(strList3);
        strList.add(strList4);
        strList.add(strList5);
        strList.add(strList6);
        strList.add(strList7);
        strList.add(strList8);
        strList.add(strList9);
        List<OrderStatusCancelDTO> list = new ArrayList<>();
        for(String str : strList){
            OrderStatusCancelDTO dto = new OrderStatusCancelDTO();
            dto = JSONObject.parseObject(str,OrderStatusCancelDTO.class);
            list.add(dto);
        }
        System.out.println(JsonUtils.json(list));
//获取取消的
        List<OrderStatusCancelDTO> orderStatusCancelDTOList = list.stream()
                .filter(data->data.getCancelTime().after(DateUtil.addDay(new Date(),-24)))
                .distinct()
                .collect(Collectors.toList());

        //判断这些手机号是否大于等于4个
        List<String> phoneList = orderStatusCancelDTOList.stream().map(OrderStatusCancelDTO::getPassengerCellphone)
                .distinct()
                .collect(Collectors.toList());
        if(phoneList.size() >= 3){
            //对去掉订单这个数组里的对象手机号进行切割
            orderStatusCancelDTOList.forEach(data->data.setPassengerCellphone(data.getPassengerCellphone().substring(0,7)));
            //判断多少是接单后取消的订单
            List<OrderStatusCancelDTO> acceptAfter = orderStatusCancelDTOList.stream().filter(data->data.getType()==1).collect(Collectors.toList());
            //计算接单后取消占比
            BigDecimal rate = new BigDecimal(acceptAfter.size()).divide(new BigDecimal(orderStatusCancelDTOList.size()),2,BigDecimal.ROUND_CEILING);
            //对分割后的手机号进行分组取数
            Map<String,List<OrderStatusCancelDTO>> contextGroup = orderStatusCancelDTOList.stream()
                    .collect(Collectors.groupingBy(OrderStatusCancelDTO::getPassengerCellphone));
            for(Map.Entry<String, List<OrderStatusCancelDTO>> entry : contextGroup.entrySet()){
                if(entry.getValue().size() >= 4
                        && rate.compareTo(new BigDecimal("0.8")) > 0){
                    //命中
                    List<String> orderIds = acceptAfter.stream().map(OrderStatusCancelDTO::getOrderId).collect(Collectors.toList());
                    System.out.println("命中");
                }
            }
        }
    }
}
