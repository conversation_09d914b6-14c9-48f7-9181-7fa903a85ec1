package com.ly.car.risk.process.constants;

import java.util.HashMap;
import java.util.Map;

public class CommonMap {

    public static Map<String,String> tianChuangCarMap = new HashMap<>();

    static {
        tianChuangCarMap.put("01","大型汽车");
        tianChuangCarMap.put("02","小型汽车");
        tianChuangCarMap.put("03","使馆汽车");
        tianChuangCarMap.put("04","领馆骑车");
        tianChuangCarMap.put("05","境外骑车");
        tianChuangCarMap.put("06","外籍汽车");
        tianChuangCarMap.put("07","普通摩托车");
        tianChuangCarMap.put("08","轻便摩托车");
        tianChuangCarMap.put("09","使馆摩托车");
        tianChuangCarMap.put("10","领馆摩托车");
        tianChuangCarMap.put("11","境外摩托车");
        tianChuangCarMap.put("12","外籍摩托车");
        tianChuangCarMap.put("13","低速车");
        tianChuangCarMap.put("14","拖拉机");
        tianChuangCarMap.put("15","挂车");
        tianChuangCarMap.put("16","教练汽车");
        tianChuangCarMap.put("17","教练摩托车");
        tianChuangCarMap.put("20","临时入境车");
        tianChuangCarMap.put("21","临时入境摩托车");
        tianChuangCarMap.put("22","临时行驶车");
        tianChuangCarMap.put("23","警用汽车");
        tianChuangCarMap.put("51","新能源大型车");
        tianChuangCarMap.put("52","新能源小型车");
    }

}
