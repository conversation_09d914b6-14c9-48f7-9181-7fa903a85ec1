package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Scope("prototype")
public class RuleRisk012Service extends FilterSendOrderHandler{

    private static final String ruleNo = "012";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule012onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            List<SendOrderContext> contextList = context.getPayAccountList();
            if(!CollectionUtils.isEmpty(contextList)){
                List<String> memberIds = contextList.stream().filter(orderContext -> StringUtils.isNotBlank(orderContext.getMemberId()))
                        .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.oneDay()))
                        .filter(orderContext -> StringUtils.isNotBlank(orderContext.getMemberId()))
                        .map(SendOrderContext::getMemberId).distinct().collect(Collectors.toList());

                if(memberIds.size() >= context.getSpecialCarRuleConfig().getRule012UserNum()){
                    context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
                    List<String> orderIds = contextList.stream().map(SendOrderContext::getOrderId).distinct().collect(Collectors.toList());
                    orderIds.add(context.getOrderId());
                    distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),
                            0,null,RiskLevelEnum.HIGH.getCode());

                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }

                    orderIds.remove(context.getOrderId());
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
        }
    }

}
