package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.data.SfcOrderRiskData;
import com.ly.car.risk.process.service.dto.OrderRiskDataDTO;
import com.ly.car.risk.process.service.dto.task.DriverCancelOrder;
import com.ly.car.risk.process.service.dto.task.MtTaskOrderInfo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface RiskDataMapper extends BaseMapper<Void> {
    
    @Select(
            "   SELECT "
                    + "order_id,total_amount,distance,duration,supplier_code_full "
                    + "FROM  data_risk_order_price  "
                    + "where  gmt_arrive BETWEEN #{startTime} and #{endTime} "
    )
    List<OrderRiskDataDTO> getNewOrderInfoLink(@Param("startTime") String startTime, @Param("endTime") String endTime);
    
    @Select("select driver, risk_level from data_risk_driver_cancel")
    List<DriverCancelOrder> queryAllRiskDriver();
    
    
    @Select("SELECT"
            + "            order_id,"
            + "            finish_time,"
            + "            member_id,"
            + "            union_id,"
            + "            start_lat,"
            + "            start_lng,"
            + "            end_lat,"
            + "            end_lng,"
            + "            estimate_kilo"
            + "    FROM"
            + "            data_risk_order_trip"
            + "    where"
            + "    gmt_arrive BETWEEN #{startTime} AND #{endTime}")
    List<SfcOrderRiskData> queryNewSfcRisk(@Param("startTime") Date startTime, @Param("endTime")Date endTime);
    
    @Select("select * from data_risk_mt_subsidy where finish_time between #{start} and #{end}")
    List<MtTaskOrderInfo> queryMt(Date start, Date end);
}
