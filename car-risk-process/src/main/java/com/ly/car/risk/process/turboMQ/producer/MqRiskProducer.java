package com.ly.car.risk.process.turboMQ.producer;

import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
public class MqRiskProducer {

    @Resource
    private UrlsProperties urlsProperties;

    private final MqTopicEnum mqTopicEnum;
    private List<String> consumerSuffixList;
    private DefaultMQProducer producer;

    public MqRiskProducer(MqTopicEnum mqTopicEnum){
        this.mqTopicEnum = mqTopicEnum;
    }

    /*
    应对一个topic 多个group的情况
     */
    public MqRiskProducer(MqTopicEnum mqTopic, List<String> consumerSuffixList) {
        this.mqTopicEnum = mqTopic;
        this.consumerSuffixList = consumerSuffixList;
    }

    public void send(MqTagEnum tags, String source, long executeDate) {
        Message message = new Message(mqTopicEnum.topic,
                tags.name(),
                source.getBytes(StandardCharsets.UTF_8));
        message.putUserProperty(MqUserProperty.MQ_DELAY_EXECUTE_DATE, executeDate + "");
        int delayLevel = DelayTimeLevel.calculate(executeDate);
        if (delayLevel > 0) {
            message.setDelayTimeLevel(delayLevel);
        }
        send0(message);
    }

    public static void main(String[] args) {
        int calculate = DelayTimeLevel.calculate(60000l);
        System.out.println(calculate);
    }

    public void send(String tag, String source, long executeDate) {
        Message message = new Message(mqTopicEnum.topic,
                tag,
                source.getBytes(StandardCharsets.UTF_8));
        message.putUserProperty(MqUserProperty.MQ_DELAY_EXECUTE_DATE, executeDate + "");
        int delayLevel = DelayTimeLevel.calculate(executeDate);
        if (delayLevel > 0) {
            message.setDelayTimeLevel(delayLevel);
        }
        send0(message);
    }

    public void send(MqTagEnum tags, String source, int delayTimeLevel) {
        Message message = new Message(mqTopicEnum.topic,
                tags.name(),
                source.getBytes(StandardCharsets.UTF_8));
        message.putUserProperty(MqUserProperty.MQ_DELAY_EXECUTE_DATE,  0+ "");
        message.setDelayTimeLevel(delayTimeLevel);
        send0(message);
    }

    @Retryable(value = {MQSendException.class},
            backoff = @Backoff(value = 10L, multiplier = 2))
    public void send0(Message message) {
        try {
            SendResult sendResult = producer.send(message);
            log.info("[send0] [{}] [{}] [{}] delayTimeLevel = {} sendStatus = {} {}",
                    message.getTags(),
                    sendResult.getMsgId(),
                    message.getKeys(),
                    message.getDelayTimeLevel(),
                    sendResult.getSendStatus(),
                    new String(message.getBody(), Charset.forName("UTF-8")));
        } catch (Exception e) {
            log.error("[send0] [{}] [] [{}] delayTimeLevel = {} {}",
                    message.getTags(),
                    message.getKeys(),
                    message.getDelayTimeLevel(),
                    new String(message.getBody(), Charset.forName("UTF-8")));
            throw new MQSendException("MQ发送失败", e);
        }
    }


    public void startProducer() throws MQClientException {
        producer = new DefaultMQProducer(mqTopicEnum.producer);
        producer.setNamesrvAddr(urlsProperties.getPublicMqServer());
        producer.start();
        log.info("turboMq生产链接成功:"+mqTopicEnum.topic);
    }

    private void start() throws MQClientException {
        startProducer();
    }

    public void shutdown() {
        producer.shutdown();
    }
}
