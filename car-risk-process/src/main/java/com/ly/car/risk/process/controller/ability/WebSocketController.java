package com.ly.car.risk.process.controller.ability;

import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.tencent.asr.constant.AsrConstant;
import com.tencent.asr.model.SpeechRecognitionSysConfig;
import com.tencent.core.utils.ByteUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.List;

@RestController
@RequestMapping("webSocket")
@Slf4j
public class WebSocketController {

    @Resource
    private TencentCloudApiClient tencentCloudApiClient;

    @RequestMapping("send")
    public void send() throws FileNotFoundException {
//        FileInputStream fileInputStream = new FileInputStream(new File("D://1.mp3"));
//        List<byte[]> speechData = ByteUtils.subToSmallBytes(fileInputStream,
//                SpeechRecognitionSysConfig.requestWay == AsrConstant.RequestWay.Http ? 6400 : 640);
//        tencentCloudApiClient.sendVoice(speechData);

//        tencentCloudApiClient.createRecTask("http://secret-axb-record-files.oss-cn-shanghai.aliyuncs.com/1000122027153824_ZJ_CUCC24d64e236093f6d3a_0.mp3?Expires=1697024371&OSSAccessKeyId=LTAI4G1kg5pSvJMv2rKNc7Pz&Signature=exztisACYxK0SwtF8%2B2YFFHxyr4%3D");


    }
}
