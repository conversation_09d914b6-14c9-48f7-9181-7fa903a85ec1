package com.ly.car.risk.process;


import java.util.LinkedList;
import java.util.List;

public class CacheInstance {
    private List<Node> instances = new LinkedList();
    private String name;
    private String type;

    public List<Node> getInstances() {
        return instances;
    }

    public CacheInstance setInstances(List<Node> instances) {
        this.instances = instances;
        return this;
    }

    public String getName() {
        return name;
    }

    public CacheInstance setName(String name) {
        this.name = name;
        return this;
    }

    public String getType() {
        return type;
    }

    public CacheInstance setType(String type) {
        this.type = type;
        return this;
    }

    public static class Node{
        private String ip;
        private String password;
        private String sentinel;

        public String getIp() {
            return ip;
        }

        public Node setIp(String ip) {
            this.ip = ip;
            return this;
        }

        public String getPassword() {
            return password;
        }

        public Node setPassword(String password) {
            this.password = password;
            return this;
        }

        public String getSentinel() {
            return sentinel;
        }

        public Node setSentinel(String sentinel) {
            this.sentinel = sentinel;
            return this;
        }
    }
}
