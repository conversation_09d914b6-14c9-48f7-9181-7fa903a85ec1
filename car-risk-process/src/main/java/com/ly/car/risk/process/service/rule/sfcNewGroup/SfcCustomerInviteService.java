package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@Scope("prototype")
public class SfcCustomerInviteService extends FilterSfcHandler{

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(CollectionUtils.isNotEmpty(context.getDriverList())){
            List<String> driverList = new ArrayList<>();
            List<RiskCustomerManage> riskCustomerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .in("customer_value",context.getDriverList()).gt("invalid_time",new Date())
            );
            for(RiskCustomerManage manage : riskCustomerManageList){
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode())){
                    driverList.add(manage.getCustomerValue());
                    continue;
                }
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()) &&
                        manage.getBindUser().equals(context.getUserPhone())){
                    driverList.add(manage.getCustomerValue());
                    continue;
                }
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_receive_list.getCode())){
                    driverList.add(manage.getCustomerValue());
                }
            }
            List<RiskCustomerManage> listByDriverId = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .in("driver_id",context.getDriverList()).gt("invalid_time",new Date())
            );
            //继续进行优化，会出现这一种情况，司机既是哈啰，又是一喂
            for(RiskCustomerManage manage : listByDriverId){
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode())){
                    driverList.add(manage.getDriverId());
                    continue;
                }
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()) &&
                        manage.getBindUser().equals(context.getUserPhone())){
                    driverList.add(manage.getDriverId());
                    continue;
                }
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_receive_list.getCode())){
                    driverList.add(manage.getDriverId());
                }
            }
            RiskResultDTO dto = new RiskResultDTO();
            dto.setMessage("");
            dto.setObj(driverList);
            dto.setCode(1);
            context.getUiResult().setData(dto);
        }
    }




    public static void main(String[] args) {
        String str = "{\"memberId\":\"**********\",\"unionId\":\"ohmdTt701u_ETrRcbtGN0z1eHaMQ\",\"userPhone\":null,\"passengerCellphone\":null,\"driverCardNo\":null,\"payAccount\":null,\"orderId\":null,\"deviceId\":null,\"channelType\":null,\"sourceId\":null,\"mainScene\":5,\"childScene\":1,\"isNewUser\":1,\"esAmount\":5274.51,\"productLine\":\"YNC\",\"startLat\":null,\"startLng\":null,\"endLat\":null,\"endLng\":null,\"uiResult\":null,\"driverId\":null,\"bankCard\":null,\"name\":null,\"idCard\":null,\"mobile\":null,\"plate\":null,\"plateType\":null,\"carName\":null,\"licenseNo\":null,\"invitePhone\":null,\"text\":null,\"driverList\":null}";
        FilterParams params1 = JSONObject.parseObject(str,FilterParams.class);
        Class aClass = params1.getClass();
        JSONObject jsonObject = new JSONObject();
        Field[] fs = aClass.getDeclaredFields();
        for (Field f : fs) {
            // 设置些属性是可以访问的
            f.setAccessible(true);
            try {
                if(f.get(params1) != null){
                    jsonObject.put(f.getName(),f.get(params1));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        System.out.println(JsonUtils.json(jsonObject));
    }
}
