package com.ly.car.risk.process.client.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: chenxiang
 * @Date: 2022/2/21 18:20
 * @Desc: http://wiki.17usoft.com/pages/viewpage.action?pageId=15120897
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FindCitiesRequest {

    private String domainKey;

    private String channelCode;

    private Long cityId;

    private String cityCode;

    private String cityName;

    private String provinceName;

    private String lang;

    private Boolean specialNeed;

    private String dateTime;

    private Boolean needExtendInfo;
}
