package com.ly.car.risk.process.support;

import com.ly.car.common.bean.ErrorCodeDict;
import com.ly.car.common.bean.model.UiResult;
import lombok.Data;

/**
 * Description of UiResultBuilder
 *
 * <AUTHOR>
 * @date 2024/3/20
 * @desc
 */
@Data
public class UiResultWrapper<T> extends UiResult<T>{

    private String code;

    private String message;

    public static UiResultWrapper<?> ok() {
        UiResultWrapper<?> uiResult = new UiResultWrapper<>();
        uiResult.setSuccess(true);
        uiResult.setMessage("请求成功");
        uiResult.setMsg("请求成功");
        uiResult.setCode("0");
        return uiResult;
    }

    public static <T> UiResultWrapper<T> ok(T data) {
        UiResultWrapper<T> uiResult = new UiResultWrapper<>();
        uiResult.setSuccess(true);
        uiResult.setMessage("请求成功");
        uiResult.setMsg("请求成功");
        uiResult.setData(data);
        uiResult.setCode("0");
        return uiResult;
    }

    public static UiResultWrapper<?> fail() {
        UiResultWrapper<?> uiResult = new UiResultWrapper<>();
        uiResult.setSuccess(false);
        uiResult.setCode(String.valueOf(ErrorCodeDict.INTERNAL_SERVER_ERROR.code));
        uiResult.setErrCode(ErrorCodeDict.INTERNAL_SERVER_ERROR.code);
        uiResult.setMessage("请求失败");
        uiResult.setMsg("请求失败");
        return uiResult;
    }

    public static UiResultWrapper<?> fail(int errCode) {
        UiResultWrapper<?> uiResult = new UiResultWrapper<>();
        uiResult.setSuccess(false);
        uiResult.setCode(String.valueOf(errCode));
        uiResult.setErrCode(errCode);
        uiResult.setMsg("系统异常");
        uiResult.setMessage("系统异常");
        return uiResult;
    }

    public static <T> UiResultWrapper<T> fail(int errCode, String msg) {
        UiResultWrapper<T> uiResult = new UiResultWrapper<>();
        uiResult.setSuccess(false);
        uiResult.setErrCode(errCode);
        uiResult.setCode(String.valueOf(errCode));
        uiResult.setMsg(msg);
        uiResult.setMessage(msg);
        return uiResult;
    }


    public static <T> UiResultWrapper<T> convert(UiResult<T> uiResult){
        UiResultWrapper<T> uiResultWrapper = new UiResultWrapper<>();
        uiResultWrapper.setCode(String.valueOf(uiResult.getErrCode()));
        uiResultWrapper.setErrCode(uiResult.getErrCode());
        uiResultWrapper.setMsg(uiResult.getMsg());
        uiResultWrapper.setMessage(uiResult.getMsg());
        uiResultWrapper.setSuccess(uiResult.isSuccess());
        uiResultWrapper.setData(uiResult.getData());
        return uiResultWrapper;
    }

}