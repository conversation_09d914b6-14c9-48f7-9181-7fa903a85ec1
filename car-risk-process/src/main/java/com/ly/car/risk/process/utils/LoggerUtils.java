package com.ly.car.risk.process.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * Description of LoggerUtils
 *
 * <AUTHOR>
 * @date 2024/3/19
 * @desc
 */
public class LoggerUtils {


    public static final ThreadLocal<HashMap<String, String>> logMapThreadLocal = ThreadLocal.withInitial(() -> new HashMap<>());

    public static Map<String, String> initLogMap(String module, String category, String filter1, String filter2) {
        Map<String, String> logMap = logMapThreadLocal.get();
        logMap.put("logModule", StringUtils.defaultString(module));
        logMap.put("logCategory", StringUtils.defaultString(category));
        logMap.put("filter1", filter1);
        logMap.put("filter2", filter2);
        return logMap;
    }

    public static HashMap<String, String> getLogMap() {
        return logMapThreadLocal.get();
    }

    public static Map<String, String> initLogMap(HashMap<String, String> parentLogMap) {
        Map<String, String> logMap = logMapThreadLocal.get();
        logMap.put("logModule", parentLogMap.get("logModule"));
        logMap.put("logCategory", parentLogMap.get("logCategory"));
        logMap.put("filter1", parentLogMap.get("filter1"));
        logMap.put("filter2", parentLogMap.get("filter2"));
        return logMap;
    }

    public static void removeAll() {
        logMapThreadLocal.remove();
    }

    public static void info(Logger logger,String format, Object...args) {
        Map<String, String> logMap = logMapThreadLocal.get();
        String msg = StrUtil.format(format,args);
        logger.info("[{}][{}][{}][{}]{}", logMap.get("logModule"), logMap.get("logCategory"), logMap.get("filter1"), logMap.get("filter2"), msg);
    }

    public static void warn(Logger logger, String format, Object... args) {
        Map<String, String> logMap = logMapThreadLocal.get();
        String msg = StrUtil.format(format,args);
        logger.warn("[{}][{}][{}][{}]{}", logMap.get("logModule"), logMap.get("logCategory"), logMap.get("filter1"), logMap.get("filter2"), msg);

    }

    public static void error(Logger logger, String format, Throwable e,Object... args) {
        Map<String, String> logMap = logMapThreadLocal.get();
        String errorMsg = StrUtil.format(format,args);
        String fullMsg = StrUtil.format("[{}][{}][{}][{}]{}",logMap.get("logModule"), logMap.get("logCategory"), logMap.get("filter1"), logMap.get("filter2"),errorMsg);
        logger.error(fullMsg,e);
    }

    public static void warn(Logger logger, String format, Throwable e,Object... args) {
        Map<String, String> logMap = logMapThreadLocal.get();
        String errorMsg = StrUtil.format(format,args);
        String fullMsg = StrUtil.format("[{}][{}][{}][{}]{}",logMap.get("logModule"), logMap.get("logCategory"), logMap.get("filter1"), logMap.get("filter2"),errorMsg);
        logger.warn(fullMsg,e);
    }

    public static void error(Logger logger, String format,Object... args) {
        Map<String, String> logMap = logMapThreadLocal.get();
        String errorMsg = StrUtil.format(format,args);
        String fullMsg = StrUtil.format("[{}][{}][{}][{}]{}",logMap.get("logModule"), logMap.get("logCategory"), logMap.get("filter1"), logMap.get("filter2"),errorMsg);
        logger.error(fullMsg);
    }

}