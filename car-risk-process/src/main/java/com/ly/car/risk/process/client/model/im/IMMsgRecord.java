package com.ly.car.risk.process.client.model.im;

import lombok.Data;

/**
 * Description of IMMsgRecord
 *
 * <AUTHOR>
 * @date 2024/3/18
 * @desc
 */
@Data
public class IMMsgRecord {

    /**
     * 消息记录 ID
     */
    private Long msgRecordId;

    /**
     * 会话标识（供应商 Code/子供应商 Code + 会员 ID/unionId + 司机车牌号）
     */
    private String sessionKey;

    /**
     * 用户 unionId
     */
    private String unionId;

    /**
     * 会员 ID
     */
    private Long memberId;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 供应商 Code
     */
    private String supplierCode;

    /**
     * 子供应商 Code
     */
    private String subSupplierCode;

    /**
     * 司机昵称
     */
    private String driverNickName;

    /**
     * 司机性别 0-未知 1-男 2-女
     */
    private Integer driverGender;

    /**
     * 司机 ID
     */
    private Long driverId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 消息内容
     */
    private IMMsgContent msgContent;

    /**
     * 消息发送者 1-乘客 2-司机
     */
    private Integer msgSender;

    /**
     * 场景类型（同顺风车订单状态枚举）
     */
    private Integer sceneType;

    /**
     * 消息状态 0-未读 1-已读
     */
    private Integer msgStatus;

    /**
     * 消息发送状态 0-失败 1-成功
     */
    private Integer msgSendStatus;

    /**
     * 消息发送时间
     */
    private String msgSendTime;
}