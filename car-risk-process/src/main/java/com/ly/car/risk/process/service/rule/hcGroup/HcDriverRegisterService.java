package com.ly.car.risk.process.service.rule.hcGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverInfo;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverLoginRecord;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverInfoMapper;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HcDriverRegisterService extends FilterHcAroundHandler {

    @Resource
    private DriverInfoMapper driverInfoMapper;

    @Override
    public void doHandler(FilterHcContext context) {
        List<DriverInfo> driverList = this.driverInfoMapper.selectList(
                new QueryWrapper<DriverInfo>().eq("device_id", context.getDeviceId())
                .gt("create_time", DateUtil.addHour(new Date(), -context.getSceneConfig().getScene7_1Time()))
        );
        if(CollectionUtils.isNotEmpty(driverList) && driverList.size() > context.getSceneConfig().getScene7_1Num()){
            RiskResultNewDTO dto = new RiskResultNewDTO<>(405, "司机同设备注册账户过多","");
            context.getUiResult().setData(dto);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {

    }
}
