package com.ly.car.risk.process.client.model.hellobike;

import lombok.Data;

import java.util.List;

/**
 * Description of SfcBlackDriverReqDTO
 *
 * <AUTHOR>
 * @date 2024/5/20
 * @desc
 */
@Data
public class HelloBikeSfcBlackDriverReqDTO extends HelloBikeBaseReqDTO {

    private List<BlackItem> blacklist;

    /** 1:拉黑或封禁；2:解封 */
    private Integer type;


    @Data
    public static class BlackItem {

        /** 封禁、拉黑结束时间，yyyy-MM-dd HH:mm:ss，不传表示永久封禁 */
        private String endTime;
        /**
         * 渠道用户ID，不传表示封禁，对应渠道所有乘客此车主不可见
         */
        private String partnerUserId;

        /** 封禁、拉黑原因 */
        private String reason;

        /** 封禁、拉黑开始时间，yyyy-MM-dd HH:mm:ss，不传默认当前时间 */
        private String startTime;

        /** 车牌号 */
        private String vehiclePlateNum;


    }
}