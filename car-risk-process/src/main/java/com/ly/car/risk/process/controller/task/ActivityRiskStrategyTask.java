package com.ly.car.risk.process.controller.task;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.repo.risk.mapper.ActivityRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityRelation;
import com.ly.car.risk.process.service.task.ActivityRiskStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

@RequestMapping("activityRisk")
@RestController
@Slf4j
public class ActivityRiskStrategyTask {

    @Resource
    private ActivityRiskStrategyService activityRiskStrategyService;
    @Resource
    private ActivityRelationMapper activityRelationMapper;

    @RequestMapping("computeRiskOrder")
    public UiResult computeRiskOrder(){
        this.activityRiskStrategyService.computeRiskOrder();
        return UiResult.ok();
    }

    @RequestMapping("updateActivityRelation")
    public UiResult updateActivityRelation(@RequestBody JSONObject jsonObject){
        Long id = jsonObject.getLong("id");
        String orderId = jsonObject.getString("orderId");
        ActivityRelation activityRelation = new ActivityRelation();
        activityRelation.setId(id);
        activityRelation.setOrderId(orderId);
        activityRelation.setUpdateTime(new Date());
        this.activityRelationMapper.updateById(activityRelation);
        return UiResult.ok();
    }


}
