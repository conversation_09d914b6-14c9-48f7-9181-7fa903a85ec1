package com.ly.car.risk.process.kafka.dto;

import lombok.Data;

@Data
public class RiskDataConvertDTO {

    private String chancelossmoneyamount;
    private String controldrivernum;
    private String controlusernum;
    private String date_hour;
    private String date_time;
    private String date_type;
    private String hcchancelossmoneyamount;
    private String hccontroldrivernum;
    private String hccontrolusernum;
    private String hcrecoverlossmoneyamount;
    private String hctotaloffrsikordernum;
    private String hctotalrelateriskordernum;
    private String hctotalriskordernum;
    private String mtchancelossmoneyamount;
    private String mtcontroldrivernum;
    private String mtcontrolusernum;
    private String mtrecoverlossmoneyamount;
    private String mttotaloffrsikordernum;
    private String mttotalrelateriskordernum;
    private String mttotalriskordernum;
    private String recoverlossmoneyamount;
    private String sfcchancelossmoneyamount;
    private String sfccontroldrivernum;
    private String sfccontrolusernum;
    private String sfcrecoverlossmoneyamount;
    private String sfctotaloffrsikordernum;
    private String sfctotalrelateriskordernum;
    private String sfctotalriskordernum;
    private String totaloffrsikordernum;
    private String totalrelateriskordernum;
    private String totalriskordernum;
    private String zcchancelossmoneyamount;
    private String zccontroldrivernum;
    private String zccontrolusernum;
    private String zcrecoverlossmoneyamount;
    private String zctotaloffrsikordernum;
    private String zctotalrelateriskordernum;
    private String zctotalriskordernum;

    private String realrecoverloss;
    private String limitdriverreceivenum;
    private String blackdrivernum;
    private String onetooneblackdrivernum;

    private String blackusernum;
    private String sfclimituserordernum;
    private String sfclimitdriverreceivenum;
    private String sfcblackdrivernum;
    private String sfcblackusernum;
    private String sfcrealrecoverloss;
    private String zclimituserordernum;
    private String zclimitdriverreceivenum;
    private String zcblackdrivernum;
    private String zcblackuserNum;
    private String zcrealrecoverloss;
    private String mdlimituserordernum;
    private String mdlimitdriverreceivenum;
    private String mdblackdrivernum;
    private String mdblackUserNum;
    private String mdrealrecoverloss;
    private String mtlimituserordernum;
    private String mtlimitdriverreceivenum;
    private String mtblackdrivernum;
    private String mtblackusernum;
    private String mtrealrecoverloss;
    private String limituserordernum;

}
