package com.ly.car.risk.process.model.riskJob;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description of SupplierCouponCheckResp
 *
 * <AUTHOR>
 * @date 2024/8/28
 * @desc
 */
@Data
public class SupplierCouponCheckResp {

    private String supplierCode;

    private String fullSupplierCode;

    private String supplierName;

    private String batchNo;

    private int orderType;

    private BigDecimal amount;

    private String startTime;

    private String endTime;
}