package com.ly.car.risk.process.service.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.TcSupplierWorkOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class TcSupplierWorkOrderService {

    @Resource
    private TcSupplierWorkOrderMapper tcSupplierWorkOrderMapper;


    public TcSupplierWorkOrder queryByOrderId(String orderId,String supplierOrderId){
        return tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq(StringUtils.isNotBlank(orderId),"order_id",orderId)
                .eq(StringUtils.isNotBlank(supplierOrderId),"supplier_order_id",supplierOrderId)
        );
    }

    public List<TcSupplierWorkOrder> queryByCreateTime(Date startTime,Date endTime){
        return tcSupplierWorkOrderMapper.selectList(new QueryWrapper<TcSupplierWorkOrder>()
                .between("update_time",startTime,endTime)
        );
    }

    public void updateByEntity(TcSupplierWorkOrder tcSupplierWorkOrder){
        this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);
    }

}
