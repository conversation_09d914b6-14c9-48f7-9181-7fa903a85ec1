package com.ly.car.risk.process.repo.hitchorder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class DriverCarInfo {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long memberId;
    private Integer seatNum;//座位数
    private String carImg;
    private String carLevel;
    private String carType;
    private String carNumber;
    private String carBrand;
    private String carModel;
    private String carColor;
    private Integer status;
    private Date createTime;
    private Date updateTime;
    private String createUser;
    private String updateUser;

}
