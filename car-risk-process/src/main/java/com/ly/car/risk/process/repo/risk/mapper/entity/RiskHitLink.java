package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RiskHitLink extends Model<RiskHitLink> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String parentOrderId;
    private String orderId;
    private String supplierCode;
    private String refId;
    private String refName;
    private String supplierName;
    private String passengerCellphone;
    private Date createTime;//订单创单时间
    private Date acceptTime;//接单时间
    private Date finishTime;//完单时间
    private String startAddress;
    private String endAddress;
    private String ruleNo;
    private BigDecimal actualDistance;
    private Integer actualMinute;
    private Integer cityId;
    private String cityName;
    private String couponNo;
    private BigDecimal couponAmount;
    private Date hitTime;
    private Date updateTime;
    private Integer isCheating;
    private BigDecimal totalAmount;
    private String requestId;
    private String memberId;
    private String userPhone;
    private String driverCardNo;

}
