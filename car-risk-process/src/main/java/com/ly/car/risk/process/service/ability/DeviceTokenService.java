package com.ly.car.risk.process.service.ability;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.DeviceFingerRsp;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class DeviceTokenService {

    private static String DEVICE_URL = "http://api-rc.17usoft.com/api/deviceFingerprintRisk/query";

    public String decryptDeviceId(String deviceId){
        Map<String,String> headMap = new HashMap<>();
        headMap.put("Authorization","Bearer ddaed10d-cc62-443c-935d-c42dcf93797f");
        JSONObject param = new JSONObject();
        param.put("deviceToken",deviceId);
        String result = OkHttpClientUtil.getInstance().post(DEVICE_URL,param.toJSONString(),headMap,1L);
        log.info("[][][][]解密设备token返回{}",result);
        DeviceFingerRsp rsp = JSONObject.parseObject(result,DeviceFingerRsp.class);
        return rsp.getData().getConstId();
    }

    public static void main(String[] args) {
        Map<String,String> headMap = new HashMap<>();
        headMap.put("Authorization","Bearer ddaed10d-cc62-443c-935d-c42dcf93797f");
        JSONObject param = new JSONObject();
        param.put("deviceToken","v2:zqkBUO2/o6eYmrDeioT2JhOfFO/EXzw4ZQDheUASoX2s79k6ZhA/0k7mVGuyl+miygyf4MyGhBQuqEZ3ByVJLxiyB9ZK2xXtZvY6PKDeiRyAan9xZYis3zTymByTejR7tSiz2fl5zDgvnWHJIXEboFD8wGIWPMhlPIrhMoWFZr2KJdgTRZe6myFLlMgljkK1wGQd6pN9eoUNX1lFb4FSlrH3qrajz7PPG/BZEI1nqZy9SbEJOTrBTrvz4Qs+813ShyD+zY6TQlrPsS91izCM5FO/2SSmIzs=");
        String result = OkHttpClientUtil.getInstance().post(DEVICE_URL,param.toJSONString(),headMap,1l);
        System.out.println(result);
    }

}
