package com.ly.car.risk.process.controller.params;

import lombok.Data;

import java.util.List;

@Data
public class DriverCheatOrderParams {

    private String productLine;
    private String driverId;

    //申诉用
    private String orderId;
    private String appealText;
    private List<String> appealUrl;
    private List<SoundFile> soundFile;

    //查询是否需要冻结
    private List<String> orderIds;

    //这边临时用
    private Integer appealStatus;

    private Integer pageNum=1;
    private Integer pageSize=10;


    @Data
    public static class SoundFile{
        private String url;
        private String fileName;
    }

    public Integer getCurrent(){
        return (pageNum-1)*pageSize;
    }

}
