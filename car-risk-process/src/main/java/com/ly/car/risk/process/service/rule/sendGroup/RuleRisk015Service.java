package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@Scope("prototype")
public class RuleRisk015Service extends FilterSendOrderHandler{

    private static final String ruleNo = "015";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule015onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            //获取当天完单
            List<SendOrderContext> orderContextList = context.getMemberList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.currentDay()))
                    .sorted(Comparator.comparing(SendOrderContext::getFinishTime).reversed())
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(orderContextList)) {
                //判断两笔订单之间的完单时间
                List<String> orderIds = new ArrayList<>();
                for (int i = 0; i < orderContextList.size() - 1; i++) {
                    int minute = (int) TimeUnit.MILLISECONDS.toMinutes(orderContextList.get(i).getFinishTime().getTime() - orderContextList.get(i + 1).getFinishTime().getTime());
                    if (minute < context.getSpecialCarRuleConfig().getRule015Time()) {
                        orderIds.add(orderContextList.get(i).getOrderId());
                        orderIds.add(orderContextList.get(i + 1).getOrderId());
                    }
                }
                if (orderIds.size() >= context.getSpecialCarRuleConfig().getRule015OrderNum()) {
                    context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.MEDIUM.getCode()));
                    orderIds.add(context.getOrderId());
                    distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene(), context.getChildScene(),
                            0, null, RiskLevelEnum.MEDIUM.getCode());

                    orderIds.remove(context.getOrderId());
                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
