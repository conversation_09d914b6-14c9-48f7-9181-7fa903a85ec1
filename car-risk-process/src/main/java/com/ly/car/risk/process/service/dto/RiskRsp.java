package com.ly.car.risk.process.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
@ColumnWidth(33)
public class RiskRsp {

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderId;

    /**
     * 接单时间
     */
    @ExcelProperty(value = "接单时间")
    private String 	decisionDime;

    /**
     * 完单时间
     */
    @ExcelProperty(value = "完单时间")
    private String 	finishTime;

    /**
     * 起始地
     */
    @ExcelProperty(value = "起始地")
    private String startAddress;

    @ExcelProperty(value = "目的地")
    private String endAddress;

    @ExcelProperty(value = "支付金额")
    private String payAmount;

    @ExcelProperty(value = "实际里程")
    private String actualKilo;

    @ExcelProperty(value = "是否新客")
    private String isNewUser;

    @ExcelProperty(value = "设备id")
    private String deviceId;

    @ExcelProperty(value = "分销员")
    private String distributeName;

    @ExcelProperty(value = "供应商")
    private String supplierName;

    @ExcelProperty(value = "返佣金额")
    private String commissionAmount;

    @ExcelProperty(value = "订单状态")
    private String status;

    @ExcelProperty(value = "司机车牌")
    private String driverCardNo;

    @ExcelProperty(value = "优惠券金额")
    private String discountAmount;

    @ExcelProperty(value = "支付id")
    private String payId;

    @ExcelProperty(value = "完单城市")
    private String cityName;

    @ExcelProperty(value = "收入")
    private String revenueAmount;

    @ExcelProperty(value = "渠道")
    private String channelName;

    @ExcelProperty(value = "命中规则")
    private String ruleNoList;


    @ExcelProperty(value = "分销商名称")
    private String distributorsName;




}
