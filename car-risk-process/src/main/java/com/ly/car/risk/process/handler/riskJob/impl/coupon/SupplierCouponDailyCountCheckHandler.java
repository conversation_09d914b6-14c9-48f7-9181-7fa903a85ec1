package com.ly.car.risk.process.handler.riskJob.impl.coupon;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.order.entity.SupplierInfo;
import com.ly.car.risk.common.enums.RiskAlertApproveHandleResultEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveTargetEnum;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.handler.riskJob.AbstractRiskCheckHandler;
import com.ly.car.risk.process.model.riskJob.SupplierCouponCountCheckResp;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 当日单供应商核销张数较大
 * 当日单个供应商核销优惠券张数大于等于n张
 * <AUTHOR>
 * @version Id : UserCouponDailyBatchNoCheckHandler, v 1.0  2024-11-8 14:45,ling.yang Exp $
 */
@Service
public class SupplierCouponDailyCountCheckHandler extends AbstractRiskCheckHandler<SupplierCouponCountCheckResp> {

    private Map<String, SupplierInfo> supplierMap = new HashMap<>();

    @Override
    public RiskJobTypeEnum support() {
        return RiskJobTypeEnum.SC_DAILY_COUNT;
    }

    @Override
    public List<RiskAlertApprove> doCheck() {
        String dayBegin = getDayBegin();
        String now = formatter.format(LocalDateTime.now());
        String checkThresholdVal = getCouponCheckThreshold(support());
        if (StringUtils.isBlank(checkThresholdVal)) {
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }
        BigDecimal checkThreshold = new BigDecimal(checkThresholdVal);
        if (checkThreshold.compareTo(BigDecimal.ZERO) <= 0) {
            LoggerUtils.info(logger, "预警阈值不合理，check结束");
            return null;
        }
        List<SupplierCouponCountCheckResp> checkResp = couponMapper.checkDaySupplierCountCoupon(checkThreshold, dayBegin);

        if (CollectionUtils.isEmpty(checkResp)) {
            return null;
        }

        supplierMap = supplierService.getSupplierMap();

        return checkResp.stream().peek(p -> {
            p.setStartTime(dayBegin);
            p.setEndTime(now);
        }).filter(p->!todayAlreadyAlert(p,dayBegin)).map(this::convert).collect(Collectors.toList());

    }

    private boolean todayAlreadyAlert(SupplierCouponCountCheckResp checkResp, String dayBegin) {
       List<RiskAlertApprove> approveList = approveMapper.findRecentlyAlertRecord(RiskAlertApproveTargetEnum.SUPPLIER.getCode(),checkResp.getFullSupplierCode(),
               RiskAlertApproveSceneEnum.MARKETING.getCode(), RiskJobTypeEnum.SC_DAILY_COUNT.name(),RiskAlertApproveHandleResultEnum.SAFE.getCode(),
               dayBegin);
       return CollUtil.isNotEmpty(approveList);
    }

    private RiskAlertApprove convert(SupplierCouponCountCheckResp supplierCouponCheckResp) {
        Date now = new Date();
        RiskAlertApprove alertApprove = new RiskAlertApprove();
        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
        alertApprove.setLevel(2);
        alertApprove.setTarget(RiskAlertApproveTargetEnum.SUPPLIER.getCode());
        alertApprove.setTargetValue(supplierCouponCheckResp.getFullSupplierCode());
        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.MARKETING.getCode());
        alertApprove.setAlertStrategy(RiskJobTypeEnum.SC_DAILY_COUNT.name());
        SupplierInfo supplierInfo = supplierMap.get(supplierCouponCheckResp.getFullSupplierCode());
        if (null != supplierInfo) {
            supplierCouponCheckResp.setSupplierName(supplierInfo.getCompanyName());
        }
        alertApprove.setAlertContent(JSON.toJSONString(supplierCouponCheckResp));
        alertApprove.setOrderType(supplierCouponCheckResp.getOrderType());
        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
        alertApprove.setAlertTime(now);
        alertApprove.setCreateTime(now);
        alertApprove.setUpdateTime(now);
        alertApprove.setCreateUser(support().name() + "_CHECK");
        approveMapper.insertSelective(alertApprove);

        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
        return alertApprove;
    }

    @Override
    public String getContentFormApprove(RiskAlertApprove approve) {
        String alertContent = approve.getAlertContent();
        if (StringUtils.isBlank(alertContent)) {
            return StringUtils.EMPTY;
        }
        SupplierCouponCountCheckResp resp = JSON.parseObject(alertContent, new TypeReference<SupplierCouponCountCheckResp>() {
        });
        StringBuilder sb = doGetContentFormApprove(approve,resp);
        return sb.toString();
    }

    @Override
    public String getDesc(RiskAlertApprove approve, SupplierCouponCountCheckResp resp) {
        StringBuilder sb = new StringBuilder();
        sb.append(RiskJobTypeEnum.getDescByCode(approve.getAlertStrategy()));
        sb.append(String.format("(供应商:%s,优惠券张数:%s)",resp.getSupplierName(),resp.getCount()));
        return sb.toString();
    }


}