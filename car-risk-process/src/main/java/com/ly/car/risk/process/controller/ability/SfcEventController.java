package com.ly.car.risk.process.controller.ability;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.DateUtilRisk;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/event")
public class SfcEventController {

    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer mqRiskProducer;

    @Resource(name = "binlogProducer")
    private MqRiskProducer binlogProducer;
    @Resource
    private AutoCallService autoCallService;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;

    @RequestMapping("/sendMq")
    public void sendMq(@RequestBody JSONObject jsonObject){
        mqRiskProducer.send(MqTagEnum.car_risk_self_security_task, JsonUtils.json(jsonObject),0);
    }

    @RequestMapping("/sendBinlogMq")
    public void sendBinlogMq(@RequestBody JSONObject jsonObject){
        String orderId = jsonObject.getString("orderId");
        binlogProducer.send(MqTagEnum.car_risk_sfc_user_onCar,orderId, 0);
    }

    @RequestMapping("/sendAutoCall")
    public void sendAutoCall(@RequestBody JSONObject jsonObject){
        String callCode = jsonObject.getString("code");
        String orderId = jsonObject.getString("orderId");
        String phone = jsonObject.getString("phone");
        //AO_117_1692100714966，AO_117_1692100463942

        if(callCode.equals("AO_117_1692100714966") || callCode.equals("AO_117_1692100463942")){
            SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(orderId);
            OrderAddress byOrderId = orderAddressMapper.findByOrderId(orderId);
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("@startstation",byOrderId.getStartAddress());
            paramMap.put("@endstation",byOrderId.getEndAddress());
            paramMap.put("@trainno", DateUtil.date2String(sfcOrder.getUseTime()));
        }
        autoCallService.sendCall(orderId,callCode,phone,null);
    }
}
