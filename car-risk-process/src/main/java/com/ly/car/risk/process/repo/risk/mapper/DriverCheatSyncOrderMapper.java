package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheatSyncOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DriverCheatSyncOrderMapper extends BaseMapper<DriverCheatSyncOrder> {

    List<DriverCheatSyncOrder> selectPage(@Param("driverId")String driverId,@Param("current")Integer current,@Param("pageSize")Integer pageSize);
}
