package com.ly.car.risk.process.service.dto.order;

import com.ly.travel.car.common.model.enums.ServiceType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description of OrderTripInfo
 *
 * <AUTHOR>
 * @date 2024/3/11
 * @desc
 */
@Data
public class OrderTripInfo {

    /** 出发城市code */
    private Integer departureCityCode;
    /** 起始城市名称 */
    private String departureCityName;
    /** 上车点详细地址 */
    private String departureAddress;
    /** 上车点详细地址 */
    private String departureAddressDetail;
    /** 起始纬度 */
    private BigDecimal departureLat;
    /** 起始经度 */
    private BigDecimal departureLng;
    /** 下车城市code */
    private Integer arrivalCityCode;
    /** 下车城市名称 */
    private String arrivalCityName;
    /** 下车点详细地址 */
    private String arrivalAddress;
    /** 下车点详细地址 */
    private String arrivalAddressDetail;
    /** 抵达纬度 */
    private BigDecimal arrivalLat;
    /** 抵达经度 */
    private BigDecimal arrivalLng;

    /** 拼车服务类型 */
    private ServiceType serviceType;
    /** 出发点名称 */
    private String departureName;
    /** 抵达点名称 */
    private String arrivalName;

    /** 实际运行时长（秒） */
    private int realRunTime;
    /** 实际运行里程（米） */
    private BigDecimal realRunDistance;

    /** 预估运行时长（秒） */
    private int EstimateRunTime;
    /** 预估运行里程（米） */
    private BigDecimal EstimateRunDistance;

    /** 预估公里 根据上面的米数算出来的 */
    private BigDecimal oldEstimateKilo;
    /** 预估时长(分）根据上面的秒算出来的 */
    private int oldEstimateMinute;


    /** 实际公里 根据上面的米数算出来的 */
    private BigDecimal oldRealKilo;
    /** 实际时长(分）根据上面的秒算出来的 */
    private int oldRealMinute;



}