package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderExtMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当前下单用户近30分钟内下单次数大于等于2单
 * */
@Service
@Slf4j
public class RuleRisk038Service extends FilterRuleChain {

    private static final String ruleNo = "038";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Resource
    private RuleRisk039Service ruleRisk039Service;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk038Service][][]前置判断已通过，进入规则038判断{}",orderContext.getMemberId(),orderContext.getDriverCardNo());
        this.next(ruleRisk039Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }
        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }

        //执行此规则就给个执行标记
        orderContext.getNeedRuleMap().put(ruleNo,true);
        if(orderContext.getOrderLimitList() != null && orderContext.getOrderLimitList().size() > 0){
            //写判断逻辑,下查询该用户的单子
            List<SfcRiskLimitData> cancelList = orderContext.getOrderLimitList().stream().filter(data->data.getStatus().equals(1000)
                    && StringUtils.isNotBlank(data.getCancelReason()) && data.getCancelReason().equals("车主取消")).collect(Collectors.toList());
            List<SfcRiskLimitData> acceptList = orderContext.getOrderLimitList().stream().filter(data -> data.getStatus().equals(20)).collect(Collectors.toList());

            if((cancelList != null && cancelList.size() > orderContext.getSfcRiskRuleConfig().getOrderNum038()) ||
                    acceptList != null && acceptList.size() > orderContext.getSfcRiskRuleConfig().getOrderNum038()){
                List<String> orderIds = orderContext.getOrderLimitList().stream().map(SfcRiskLimitData::getOrderId).collect(Collectors.toList());
                distributionRiskManageService.addManageCommon(orderIds,ruleNo,2,1,0,null, RiskLevelEnum.HIGH.getCode());
                    log.info("[RuleRisk038Service][doHandler][{}][{}]命中038规则",orderContext.getMemberId(),orderContext.getDriverCardNo());
                UiResult result = UiResult.ok();
                result.setMsg("风控不通过");
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过018",null,null);
                result.setData(dto);
                orderContext.setUiResult(result);
            }
        }

        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
