package com.ly.car.risk.process.repo.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.repo.data.SfcOrderRiskData;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.service.dto.task.ActivitySfcOrder;
import com.ly.car.risk.process.service.dto.task.DriverCancelOrder;
import com.ly.car.risk.process.service.dto.task.MtTaskOrderInfo;
import com.ly.car.sharding.order.entity.risk.DcdbOrderFinishRisk;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface SfcOrderMapper extends BaseMapper<SfcOrder> {

    @Select(" select " +
            "  so.order_id, " +
            "  so.member_id, " +
            "  so.finish_time, "+
            "  so.passenger_cellphone, " +
            "  so.supplier_code, " +
            "  oa.start_city_id, " +
            "  oa.start_city_name, " +
            "  sso.plate_number " +
            " from " +
            "  sfc_order so " +
            "  left join order_address oa on so.order_id = oa.order_id " +
            "  left join sfc_supplier_order sso on so.order_id = sso.order_id  and so.supplier_order_id = sso.supplier_order_id" +
            " where " +
            "  so.created between #{startTime} and #{endTime}" +
            "  and so.distributor_flag = 0" +
            "  and so.status = 300")
    List<DcdbOrderFinishRisk> getSfcFinishOrder(@Param("startTime")Date startTime,@Param("endTime")Date endTime);

    @Select("select * from sfc_order where order_id = #{orderId} limit 1")
    SfcOrder queryByOrderId(String orderId);

    List<SfcOrderRiskData> querySfcRisk(@Param("startTime")Date startTime,@Param("endTime")Date endTime);


    List<SfcRiskLimitData> querySfcRiskLimit(@Param("startTime")Date startTime,@Param("endTime")Date endTime,
                                             @Param("unionId")String unionId,@Param("memberId")String memberId);

    @Select("select * from sfc_order where supplier_order_id = #{supplierOrderId} limit 1")
    SfcOrder queryBySupplierOrderId(@Param("supplierOrderId")String supplierOrderId);


    @Select("<script>" +
            " SELECT " +
            " so.order_id, " +
            "   so.supplier_order_id, " +
            "   ss.plate_number," +
            "   so.passenger_cellphone," +
            "   so.total_amount," +
            "   oa.start_lat," +
            "   oa.start_lng," +
            "   oa.end_lat," +
            "   oa.end_lng," +
            "   so.finish_time" +
            " FROM " +
            "  sfc_order so " +
            "  left join order_address oa on so.order_id = oa.order_id " +
            "  left join sfc_supplier_order ss on so.supplier_order_id = ss.supplier_order_id " +
            "  where so.order_id in( " +
            "   <foreach collection='orderIds' item='item'  separator=','> " +
            "       #{item} " +
            "   </foreach>" +
            ")" +
            "</script>"
    )
    List<ActivitySfcOrder> queryActivitySfcOrder(@Param("orderIds") List<String> orderIds);


    @Select("<script>" +
            "select driver FROM (" +
            " select t2.driver, cast(100.0*sum(case when t2.couponAmount &gt;= 2500  then 1 else 0 end) /count( t2.order_id) as decimal(18,2)) as ratio25Coupon, " +
            "cast(100.0*sum(case when t2.ref_id = '1909555084'  then 1 else 0 end) /count(t2.order_id) as decimal(18,2)) as aliArouce, " +
            "cast(100.0*sum(case when t2.total_amount &gt; 10000  then 1 else 0 end) /count(t2.order_id) as decimal(18,2)) as  gte100Ratio, " +
            "count(distinct t2.order_id),  " +
            "cast(100.0*sum(case when t2.total_amount &lt;= 1000  then 1 else 0 end) /count(t2.order_id) as decimal(18,2)) as  lte10Ratio " +
            "from  " +
            "( " +
            "select t1.pay_amount,t1.total_amount,t1.ref_id, discount_amount as couponAmount,t1.order_id,t1.status  ,t3.plate_number as  driver from sfc_order t1  left join sfc_supplier_order  t3 on t3.order_id = t1.order_id  where t1.status =300  and  t1.created >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) and t3.created &gt;= DATE_SUB(CURDATE(), INTERVAL 1 DAY) and t1.member_id != '' and  t3.plate_number != ''  " +
            ") as t2  " +
            "group by  t2.driver " +
            "having count(distinct t2.order_id) &gt;=  3 " +
            "and   cast(100.0*sum(case when t2.couponAmount &gt; 2500  then 1 else 0 end) /count(distinct t2.order_id) as decimal(18,2)) &gt;= 90 " +
            "and    cast(100.0*sum(case when t2.total_amount &gt; 10000  then 1 else 0 end) /count(distinct t2.order_id) as decimal(18,2)) &lt; 10 " +
            "UNION " +
            "select t2.driver, cast(100.0*sum(case when t2.couponAmount &gt;= 2500  then 1 else 0 end) /count( t2.order_id) as decimal(18,2)) as ratio25Coupon, " +
            "cast(100.0*sum(case when t2.ref_id = '1909555084'  then 1 else 0 end) /count(t2.order_id) as decimal(18,2)) as aliArouce, " +
            "cast(100.0*sum(case when t2.total_amount &gt; 10000  then 1 else 0 end) /count(t2.order_id) as decimal(18,2)) as  gte100Ratio, " +
            "count(distinct t2.order_id),  " +
            "cast(100.0*sum(case when t2.total_amount &lt;= 1000  then 1 else 0 end) /count(t2.order_id) as decimal(18,2)) as  lte10Ratio " +
            "from  " +
            "( " +
            "select t1.pay_amount,t1.total_amount,t1.ref_id, discount_amount as couponAmount,t1.order_id,t1.status  ,t3.plate_number as  driver from sfc_order t1  left join sfc_supplier_order  t3 on t3.order_id = t1.order_id  where t1.status =300  and  t1.created &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and t3.created &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and t1.member_id != '' and  t3.plate_number != '' " +
            ") as t2 " +
            "group by  t2.driver " +
            "having count(distinct t2.order_id) &gt;=  5 " +
            "and   cast(100.0*sum(case when t2.couponAmount &gt; 2500  then 1 else 0 end) /count(distinct t2.order_id) as decimal(18,2)) &gt;=80 " +
            "and    cast(100.0*sum(case when t2.total_amount &gt; 10000  then 1 else 0 end) /count(distinct t2.order_id) as decimal(18,2)) &lt; 10 ) A" +
            "</script>")
    List<String> queryRiskDriver();

    @Select("<script>" +
            "  select t2.driver " +
            "from  " +
            "( " +
            "select t1.cancel_time,t3.cancel_reason, t1.status,t1.order_id, t3.plate_number as  driver from sfc_order t1  " +
            "left join sfc_supplier_order  t3 on t3.order_id = t1.order_id  " +
            " where  " +
            "t1.created &gt;= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) and " +
            "t3.created &gt;= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) " +
            "and t1.member_id != '' " +
            "and  t3.plate_number != ''  and t3.supplier_code ='HelloBike' and  t1.supplier_code ='HelloBike' " +
            "and t1.business_line = 'sfc' " +
            ") as t2 " +
            "group by  t2.driver " +
            "having  " +
            "(cast(100.0*sum(case when status = 1000 then 1 else 0 end) / count(t2.order_id)  as decimal(18,2))  &gt;= 95  and  sum(case when cancel_reason='车主取消' then 1 else 0 end) &gt;= 1 and count(t2.order_id) &gt; 20) or  " +
            "((cast(100.0*sum(case when status = 1000 then 1 else 0 end) / count(t2.order_id)  as decimal(18,2))  &gt;= 100 and count(t2.order_id) &gt; 10 and  sum(case when cancel_reason='车主取消' then 1 else 0 end) &gt;= 1 and count(t2.order_id) &gt; 10)) " +
            "or " +
            "( cast(100.0*sum(case when status = 1000 then 1 else 0 end) / count(t2.order_id) as decimal(18,2)) &gt;= 90 and sum(case when  cancel_reason ='车主要求线下交易' or cancel_reason ='车主要求加价或私下付钱' then 1 else 0 end) &gt; 1 and  count(t2.order_id) &lt;= 10 ) " +
            "order by sum(case when (status = 300 or status = 400 or status = 500) then 1 else 0 end) " +
            "</script>")
    List<String> queryHighRiskDriver();

    @Select("<script>" +
            "  select t2.driver " +
            "from  " +
            "( " +
            "select t1.cancel_time,t3.cancel_reason, t1.status,t1.order_id, t3.plate_number as  driver from sfc_order t1  left join sfc_supplier_order  t3 on t3.order_id = t1.order_id  where  t1.created >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) and t3.created >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) and t1.member_id != '' and  t3.plate_number != ''  and t3.supplier_code ='HelloBike' and  t1.supplier_code ='HelloBike' and t1.business_line = 'sfc' " +
            ") as t2 " +
            "group by  t2.driver " +
            "having " +
            "(cast(100.0*sum(case when status = 1000 then 1 else 0 end) /count(t2.order_id)  as decimal(18,2))  &gt;= 90  and  sum(case when cancel_reason='车主取消' then 1 else 0 end) &gt;= 1 and count(t2.order_id) &gt; 20) or  " +
            "(cast(100.0*sum(case when status = 1000 then 1 else 0 end) /count(t2.order_id)  as decimal(18,2))  &gt;= 95 and count(t2.order_id) &gt;  10 and  sum(case when cancel_reason='车主取消' then 1 else 0 end) &gt;= 1 and count(t2.order_id) &gt; 10) " +
            "</script>")
    List<String> queryMidRiskDriver();




}
