package com.ly.car.risk.process.controller.request.safecenter;

import com.ly.car.risk.process.controller.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/13 17:24
 **/
@Data
public class EmergencyContactRequest extends BaseRequest {

    /**
     * 用户memberId
     */
    private String memberId;

    /**
     * unionId（有就传）
     */
    private String unionId;

    private String channel;
    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;
    /**
     * 紧急联系电话
     */
    private String emergencyPhone;

    /**
     * 用户是否获取了授权 0未授权 1 授权
     */
    private Integer authorizedRecordingSwitch;


    /**
     * 行程分享开关 0关闭 1 打开
     */
    private Integer journeyShareSwitch;

    /**
     * 行程分享起始时间
     */
    private String shareBeginTime;

    /**
     * 行程分享起始时间
     */
    private String shareEndTime;

}
