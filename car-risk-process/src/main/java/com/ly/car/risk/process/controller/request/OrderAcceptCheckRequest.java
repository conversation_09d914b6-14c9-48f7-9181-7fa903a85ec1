package com.ly.car.risk.process.controller.request;

import com.ly.car.risk.process.utils.RandomUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class OrderAcceptCheckRequest {
    
    private String  traceId;
    private String  orderId;
    private String  driverCode;
    private String  driverPhone;
    private String  carNum;
    private String supplierName;
    private String refid;
    private String  productLine;

    // ******************** 内部使用 ****************************
    private Integer mainScene;
    private Integer childScene;
    private String  requestId;
    private String  sourceId;
    private String  startLat;
    private String  startLng;
    private String  endLat;
    private String  endLng;
    private String  userPhone;
    private String  passengerCellphone;
    private String  memberId;
    private String  unionId;
    private String  driverCardNo;

    public String getRequestId() {
        return StringUtils.isBlank(requestId) ? RandomUtil.getRandomString(16) + System.currentTimeMillis() : requestId;
    }

}
