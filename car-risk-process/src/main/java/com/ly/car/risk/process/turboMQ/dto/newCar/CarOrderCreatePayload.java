package com.ly.car.risk.process.turboMQ.dto.newCar;

import lombok.Data;

/**
 * Description of CarOrderCreatePayload
 *
 * <AUTHOR>
 * @date 2024/4/19
 * @desc
 */
@Data
public class CarOrderCreatePayload {

    /** 会员id */
    private String memberId;
    /** unionId */
    private String unionId;
    /** 订单号 */
    private String orderId;
    /** 渠道 */
    private int orderChannel;
    /** 订单类型 */
    private int orderType;
    /** 订单总额 */
    private String amount;
    /** 支付方式 1:在线支付/2:支付分支付 */
    private int payCategory;
    /** 乘客手机号 */
    private String passengerPhoneNum;
    /** 创单时间 */
    private String createTime;
}