package com.ly.car.risk.process.service.safecenter;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.request.safecenter.*;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.repo.risk.mapper.entity.SafeCenterShare;

/**
 * Description of SafeCenterService
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc
 */
public interface SafeCenterService {

    UiResult querySafeReminder(ReminderRequest request);

    UiResult feedBackWorkOrder(AppealOrderRequest request) throws BizException;

    UiResult queryWorkOrderFeedBack(AppealOrderRequest request);

    UiResult saveEmergencyContact(EmergencyContactRequest request);

    UiResult queryUserShareInfo(EmergencyContactRequest request);

    UiResult saveShareInfo(EmergencyContactRequest request);

    UiResult saveAlterWorkOrder(AppealOrderRequest request) throws BizException;
    
    SafeCenterShare getRecentUpdateShareInfo(String memberId, String traceId);
    
    UiResult queryFeeAppearWorkOrder(AppealOrderRequest request);

    UiResult saveItemMissingAppear(AppealOrderRequest request) throws BizException;
}