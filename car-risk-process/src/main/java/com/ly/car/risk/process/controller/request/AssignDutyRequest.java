package com.ly.car.risk.process.controller.request;

import lombok.Data;

@Data
public class AssignDutyRequest {

    private String traceId;

    /** 责任类型 0-有损 1-无损 */
    private int dutyType;

    private String productLine;

    /** 司机车牌 */
    private String driverCardNo;

    /** 订单号 */
    private String orderId;

    /** 供应商订单号 */
    private String supplierOrderId;

    /** 用户手机号 */
    private String userPhone;

    /** 乘车人手机号 */
    private String passengerCellphone;

    /** 会员id */
    private String memberId;

    private String unionId;

    /** 渠道 */
    private Integer channel;

    /** 取消原因 */
    private String cancelReason;
}
