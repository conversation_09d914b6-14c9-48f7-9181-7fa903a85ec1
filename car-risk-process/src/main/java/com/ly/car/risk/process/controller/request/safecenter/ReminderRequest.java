package com.ly.car.risk.process.controller.request.safecenter;

import com.ly.car.risk.process.controller.request.BaseRequest;
import lombok.Data;

/**
 * Description of ReminderRequest
 *
 * <AUTHOR>
 * @date 2024/3/12
 * @desc
 */
@Data
public class ReminderRequest extends BaseRequest {

    /** 订单号 */
    private String orderId;

    /** 产品线 */
    private String productLine;

    /**
     * 事件类型
     * 0-轮询 1-服务开始 2-取消 3-点击已到达 4-提示确认关闭
     */
    private int eventType;

    /**
     * 仅在eventType为4时生效
     * 确认不再需要提示的规则编号, 如有多个，以 英文逗号 分割
     */
    private String noHintRuleNos;

}