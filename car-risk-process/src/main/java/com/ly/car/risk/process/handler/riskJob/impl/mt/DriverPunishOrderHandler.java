package com.ly.car.risk.process.handler.riskJob.impl.mt;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.common.enums.RiskAlertApproveHandleResultEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveLevelEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveTargetEnum;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.handler.riskJob.MtAbstractRiskCheckHandler;
import com.ly.car.risk.process.model.riskJob.DriverPunishOrderCheckResp;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class DriverPunishOrderHandler extends MtAbstractRiskCheckHandler<DriverPunishOrderCheckResp> {
    
    @Override
    public RiskJobTypeEnum support() {
        return RiskJobTypeEnum.DRIVER_PUNISH_ORDER;
    }
    
    @Override
    public List<RiskAlertApprove> doCheck() {
        String dayBegin = getDayBegin();
        String now = formatter.format(LocalDateTime.now());
        String checkThresholdVal = getMTCheckThreshold(support());
        if (StringUtils.isBlank(checkThresholdVal)) {
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }
        
        int left;
        int right;
        if (checkThresholdVal.contains(",")) {
            String[] arr = checkThresholdVal.split(",");
            left = Integer.parseInt(arr[0]);
            right = Integer.parseInt(arr[1]);
        } else {
            left = 0;
            right = Integer.parseInt(checkThresholdVal);
        }
        
        if (left <= 0 || right <= 0) {
            LoggerUtils.info(logger, "预警阈值不合理，check结束");
            return null;
        }
        
        // 发预警只落库
        List<DriverPunishOrderCheckResp> punishResp = carMtDriverPunishRecordMapper.checkPunishCount(left, right, dayBegin);
        
        if (CollUtil.isEmpty(punishResp)) {
            return null;
        }
        
        return punishResp.stream().peek(p -> {
            p.setStartTime(dayBegin);
            p.setEndTime(now);
        }).filter(p -> !todayAlreadyAlert(p, dayBegin)).map(p -> convert(p, left)).collect(Collectors.toList());
        
    }
    
    private boolean todayAlreadyAlert(DriverPunishOrderCheckResp checkResp, String dayBegin) {
        List<RiskAlertApprove> approveList = approveMapper.findRecentlyAlertRecord(RiskAlertApproveTargetEnum.DRIVER.getCode(), checkResp.getCarNum(),
                RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND.getCode(), RiskJobTypeEnum.DRIVER_PUNISH_ORDER.name(), RiskAlertApproveHandleResultEnum.SAFE.getCode(),
                dayBegin);
        return CollUtil.isNotEmpty(approveList);
    }
    
    private RiskAlertApprove convert(DriverPunishOrderCheckResp checkResp, int left) {
        Date now = new Date();
        RiskAlertApprove alertApprove = new RiskAlertApprove();
        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
        alertApprove.setLevel(RiskAlertApproveLevelEnum.FOCUS.getCode());
        alertApprove.setTarget(RiskAlertApproveTargetEnum.DRIVER.getCode());
        alertApprove.setTargetValue(checkResp.getCarNum());
        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND.getCode());
        alertApprove.setAlertStrategy(RiskJobTypeEnum.DRIVER_PUNISH_ORDER.name());
        alertApprove.setAlertContent(JSON.toJSONString(checkResp));
        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
        alertApprove.setAlertTime(now);
        alertApprove.setCreateTime(now);
        alertApprove.setUpdateTime(now);
        alertApprove.setCreateUser(support().name() + "_CHECK");
        
        RiskAlertApprove.Ext ext = new RiskAlertApprove.Ext();
        ext.setSupplierName(checkResp.getSupplierName());
        alertApprove.setExt(JSON.toJSONString(ext));
        approveMapper.insertSelective(alertApprove);
        
        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
        return alertApprove;
    }
    
    @Override
    public String getContentFormApprove(RiskAlertApprove approve) {
        String alertContent = approve.getAlertContent();
        if (StringUtils.isBlank(alertContent)) {
            return StringUtils.EMPTY;
        }
        DriverPunishOrderCheckResp resp = JSON.parseObject(alertContent, new TypeReference<DriverPunishOrderCheckResp>() {
        });
        StringBuilder sb = doGetContentFormApprove(approve, resp);
        return sb.toString();
    }
    
    @Override
    public String getDesc(RiskAlertApprove approve, DriverPunishOrderCheckResp resp) {
        StringBuilder sb = new StringBuilder();
        sb.append(RiskJobTypeEnum.getDescByCode(approve.getAlertStrategy()));
        sb.append(String.format("(车牌号:%s，追款金额:%s，供应商:%s，追款订单数:%s)", resp.getCarNum(), resp.getRecoveryAmount(), resp.getSupplierName(), resp.getPunishOrderCount()));
        return sb.toString();
    }
    
}