package com.ly.car.risk.process.service.dto.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description of BaseOrderInfo
 *
 * <AUTHOR>
 * @date 2024/3/11
 * @desc
 */
@Data
public class BaseOrderInfo {
    /** 支付状态 */
    private int payState;

    /** 对客状态 */
    private int userState;

    /**
     * 是否拼车成功
     */
    private boolean carPooling;

    /**
     * 有过接单
     */
    private boolean hasAccepted;

    /** 联系人姓名 */
    private String contactName;

    /** 联系人电话 */
    private String contactPhone;
    /** 电话区号 */
    private String areaCode;
    /** 设备id */
    private String deviceId;

    /** 下单时间 */
    private Date gmtCreate;

    /** 用车时间（用户选择的出发时间）yyyy-MM-dd HH:mm:ss */
    private Date gmtUsage;
    /** 用车延迟时间/单位分钟（用户选择的出发时间，延迟多久，网约车为0，顺风车大于等于0） */
    private int usageDelay;
    /** 抵达时间（实际抵达时间）yyyy-MM-dd HH:mm:ss 必填 */
    private Date gmtArrive;
    /** 出发时间（实际出发时间）yyyy-MM-dd HH:mm:ss 必填 */
    private Date gmtDeparture;
    /** 派单成功时间（首次派单成功时间） yyyy-MM-dd HH:mm:ss */
    private Date gmtDispatched;
    /** 改派完成时间 yyyy-MM-dd HH:mm:ss */
    private Date gmtReassigned;
    /** 司机到达时间 yyyy-MM-dd HH:mm:ss */
    private Date gmtDriverArrived;
    /** 乘客上车时间 yyyy-MM-dd HH:mm:ss */
    private Date gmtPassengerBoard;
    /** 订单取消时间 */
    private Date gmtCanceled;
    /** 行程结束时间 yyyy-MM-dd HH:mm:ss */
    private Date gmtTripFinished;
    /** 接单时间 */
    private Date gmtAccept;
    /** 支付时间 (这个可以当作前付的时间) yyyy-MM-dd HH:mm:ss 必填 */
    private Date gmtPaid;
    /**
     * 支付分抵扣发起时间 	 yyyy-MM-dd HH:mm:ss 非必填
     */
    private Date gmtPayScoreDeduct;
    /** 取消类型 */
    private int cancelType;
    /** 订单取消原因 */
    private String cancelReason;
    /** 订单总额 */
    private String amount;
    /** 客户实际支付金额 */
    private String payAmount;
    /** 支付方式 1:在线支付/2:支付分支付 */
    private int payCategory;
    /** 订单发生改派次数 */
    private int reassignNum;

    private String refId;

    /** 感谢金 */
    private String thanksMoney;
    
    /** 附加费: attachFeeItems的type为附加费的数字之和 */
    private BigDecimal surcharge;
    
    /** 附加费明细 */
    private List<FeeItemInfo> attachFeeItems;

    /** 订单备注 */
    private String remark;

    /** 供应商结算价(只有网约车有) */
    private BigDecimal realSupplierPrice;
}