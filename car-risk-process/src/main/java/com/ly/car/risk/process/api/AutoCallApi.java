package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.rsp.CallRecordRsp;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class AutoCallApi {

    @Resource
    private UrlsProperties urlsProperties;

    public String callAction(String mobile, String code, Map<String,String> paramMap) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("Code", code);
            jsonObject.put("Mobile", mobile);
            if(paramMap != null){
                jsonObject.put("RequestStr",JsonUtils.json(paramMap));
            }
            log.info("[][][][]请求外呼接口{},code:{}",mobile,code);
            String post = OkHttpClientUtil.getInstance().post(urlsProperties.getCallApi(), JsonUtils.json(jsonObject), null);
            log.info("[][][][]请求外呼接口返回{}",post);
            JSONObject postJson = JSONObject.parseObject(post);
            if (postJson.getBoolean("IsSuccess")) {
                return postJson.getJSONObject("Data").getString("CallId");
            }
        } catch (Exception e) {
            log.error("[][][][]请求外呼报错:",e);
        }
        return null;
    }

    /**
     * 呼出后查询结果
     * */
    public CallRecordRsp getCallRecord(String callId){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("AutoCallId", callId);
        log.info("[][][][]请求外呼单查询记录接口{}",callId);
        String post = OkHttpClientUtil.getInstance().post(urlsProperties.getCallApiRecord(), JsonUtils.json(jsonObject), null);
        log.info("[][][][]请求外呼单查询记录接口返回{}",post);
        CallRecordRsp rsp = JSONObject.parseObject(post,CallRecordRsp.class);
        return rsp;
    }


    public static void main(String[] args) {
        String str = "{\"IsSuccess\":true,\"Info\":\"请求成功\",\"Data\":{\"CallId\":\"0b4b1cee-10b1-4ee1-8a43-0c677483e7b8\"}}";
        JSONObject jsonObject = JSONObject.parseObject(str);
        if (jsonObject.getBoolean("IsSuccess")) {

            System.out.println(jsonObject.getJSONObject("Data").getString("CallId"));
        }

    }


}
