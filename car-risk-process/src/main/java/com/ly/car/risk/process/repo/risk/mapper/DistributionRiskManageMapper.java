package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.entity.DistributionRiskManage;
//import com.ly.car.risk.manage.controller.dto.DistributionRiskDto;
import com.ly.car.risk.process.controller.params.FilterParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-08-15
 */
public interface DistributionRiskManageMapper extends BaseMapper<DistributionRiskManage> {


    long getListTotal(Object query);

    List<DistributionRiskManage> getList(Object query);

//    List<DistributionRiskDto> getListByOrderIds(@Param("orderIds")List<String> orderIds);

    List<DistributionRiskManage> getListExport(Object query);

    List<DistributionRiskManage> getByAllParams(@Param("params")FilterParams params);



}
