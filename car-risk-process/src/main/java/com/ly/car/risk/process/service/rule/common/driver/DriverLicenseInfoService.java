package com.ly.car.risk.process.service.rule.common.driver;

import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.rule.common.CommonFilterContext;
import com.ly.car.risk.process.service.rule.common.CommonFilterHandler;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterContext;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterHandler;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class DriverLicenseInfoService extends CommonFilterHandler {

    private static final String TAG_NAME = "_driver_notify";

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource(name = "commonRiskProducer")
    private MqRiskProducer mqRiskProducer;

    @Override
    public void doHandler(CommonFilterContext context) {
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String,Object> data = new HashMap<>();
        Map<String,Object> resultMap = new HashMap<>();
        data.put("driverId",context.getParam().get("driverId"));
        data.put("idCard",context.getParam().getString("idCard"));
        dto.setObj(data);
        if(!validParam(context.getParam())){
            dto.setCode(1);
            dto.setMessage("风控不通过-司机驾驶证参数缺失验证失败");
            mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
            return;
        }
        if(!validParam(context.getParam())){
            TianChuangCommonParam param = new TianChuangCommonParam();
            param.setName((String) context.getParam().get("name"));
            param.setLicenseNo((String) context.getParam().get("licenseNo"));
            Integer result = this.tianChuangRiskClient.verifyDriverLicenseInfo(param,null);
            if(result != 0){
                //发送mq
                dto.setCode(1);
                dto.setMessage("风控不通过-司机驾驶证验证不通过");
                resultMap.put("licenseInfo",1);
                data.put("resultMap",resultMap);
                dto.setObj(data);
                mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
                return;
            }
        }
        if(this.nextHandler == null && dto.getCode() == 0){
            mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
        } else {
            this.nextHandler.doHandler(context);
        }
    }

    public Boolean validParam(Map<String,Object> context){
        if(context.get("driverId") == null){
            return false;
        }
        if(context.get("name") == null){
            return false;
        }
        if(context.get("licenseNo") == null){
            return false;
        }
        return true;
    }
}
