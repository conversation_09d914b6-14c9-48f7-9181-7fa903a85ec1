package com.ly.car.risk.process.service.order;

import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.model.risk.IMInfo;
import com.ly.car.risk.process.model.risk.UserComplainCache;
import com.ly.car.risk.process.model.risk.UserOrCarFinishCache;
import com.ly.car.risk.process.model.risk.VirtualCallInfo;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderVirtualCallRecord;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.travel.car.ordercore.facade.model.RiskOrder;

import java.util.List;

/**
 * 用车新订单servcie
 */
public interface CarOrderService {


    CarOrderDetail queryOrderDetail(String orderId);

    String queryOrderCurrentLocal(String orderId);

    List<RiskOrderVirtualCallRecord> queryOrderVirtualRecords(String orderId);

    UserOrCarFinishCache queryUserFinishCache(String memberId);

    UserOrCarFinishCache queryCarFinishCache(String carNum) throws BizException;

    UserComplainCache queryUserComplainCache(String unionId);

    VirtualCallInfo queryVirtualCallInfo(String orderId, String driverVirtualPhone);

    IMInfo queryImInfo(String orderId, String supplierCode, String unionId, String memberId, String carNum);

    String queryPenaltyInquiry(String orderId, String memberId);

    RiskOrder queryOrderDetail(String orderId,String traceId);
}
