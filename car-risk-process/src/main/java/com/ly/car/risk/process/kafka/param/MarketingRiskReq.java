package com.ly.car.risk.process.kafka.param;

import lombok.Data;

@Data
public class MarketingRiskReq {

    /**
     * 风控组分配，访问id
     * */
    private String accessId;
    /**
     * 渠道 小程序=852
     * 公众号=501
     * app枚举=201
     * H5=301
     * */
    private String channel;
    /**
     * 请求时间 格式：yyyy-MM-dd HH:mm:ss
     * */
    private String actionTime;

    /**
     * 唯一标识 使用uuid
     * */
    private String traceId;

    /**
     * openId openId 与手机号必传其一，若都有建议都传入提高识别率（小程序端必传openId）
     * */
    private String openId;

    /**
     * unionId
     * */
    private String unionId;

    /**
     *  	有就传入，提高识别率
     * */
    private String memberId;

    /**
     * phone 手机号 openId 与手机号必传其一，若都有建议都传入提高识别率
     * */
    private String phone;

    /**
     * 设备指纹
     * */
    private String fptoken;

    /**
     * ip 不能是内网ip
     * */
    private String ip;

    /**
     *经度
     * */
    private String longitude;

    /**
     * 纬度
     * */
    private String latitude;

    /**
     * 设备id APP渠道需要填写
     * */
    private String deviceId;

    /**
     * 扩展参数 商定的一些特殊字段传入，各个业务线特有的一些参数，json字符串的格式传入
     * todo 这边既然有扩展的字段，可以用mq异步去请求
     * */
    private String extendData;


}
