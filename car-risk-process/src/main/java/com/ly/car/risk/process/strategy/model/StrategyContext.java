package com.ly.car.risk.process.strategy.model;

import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Description of StrategyContext
 *
 * <AUTHOR>
 * @date 2024/6/4
 * @desc
 */
@Data
public class StrategyContext {
    // 入参
    private Map<String, Object> params;
    // 响应
    private RiskSceneResult   result;
    // 所要用到的策略集合
    private List<RiskStrategyDetail> strategyList;
    // 每个策略执行的结果
    private Map<String, RiskStrategyResult> strategyResultMap;
    // 入参，用于收集流程的信息
    private UnifyCheckRequest request;
}
