package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.kafka.dto.PanelDataDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("panelRedis")
@Slf4j
public class DataPanelController {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @RequestMapping("queryByPattern")
    public List<PanelDataDTO> queryByPattern(@RequestBody JSONObject jsonObject){
        String key = jsonObject.getString("key");
        RList<String> list = redissonClient.getList(key+"_panel_"+ DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD));
        List<PanelDataDTO> panelDataDTOList = new ArrayList<>();
        for (String dto : list){
            PanelDataDTO panelDataDTO = JSONObject.parseObject(dto,PanelDataDTO.class);
            panelDataDTOList.add(panelDataDTO);
        }
        return panelDataDTOList;
    }

}
