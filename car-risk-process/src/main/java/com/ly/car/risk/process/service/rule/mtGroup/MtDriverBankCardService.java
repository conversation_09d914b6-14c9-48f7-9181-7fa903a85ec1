package com.ly.car.risk.process.service.rule.mtGroup;

import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.utils.StrategyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverBankCardService extends MtFilterHandler {

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private DriverCheckService driverCheckService;


    @Override
    public void doHandler(MtFilterContext context) {
        if (!validParam(context.getParam())) {
            context.getDto().setCode(1);
            context.getDto().setMessage("银行卡参数缺失，验证失败");
            return;
        }
        StringBuilder msg = new StringBuilder();
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName((String) context.getParam().get("name"));
        param.setIdCard((String) context.getParam().get("idCard"));
        param.setBankCard((String) context.getParam().get("bankCard"));
        param.setMobile((String) context.getParam().get("mobile"));
        param.setProductLine((String) context.getParam().get("productLine"));
        Integer result = this.tianChuangRiskClient.verifyBankCard3(param, msg);
        context.getDriverCheckService().insert(context.getParam(), 4, result, msg.toString());
        log.info("[][][][]验证银行卡信息返回结果文案:{}", msg);
        if (result != 0) {
            context.getDto().setCode(1);
            context.getDto().setMessage(msg.toString());
        }
        return;
    }

    public Boolean validParam(Map<String, Object> param) {
        if (param.get("name") == null) {
            return false;
        }
        if (param.get("idCard") == null) {
            return false;
        }
        if (param.get("bankCard") == null) {
            return false;
        }
        return true;
    }

    public RiskSceneResult bankCardCheck(UnifyCheckRequest request) {
        String certName = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NAME);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);
        String bankCardNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.BANK_CARD_NO);

        if (StringUtils.isAnyBlank(certName, certNo, bankCardNo)) {
            return RiskSceneResult.fail("银行卡参数缺失，验证失败");
        }

        StringBuilder msg = new StringBuilder();
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName(certName);
        param.setIdCard(certNo);
        param.setBankCard(bankCardNo);
        param.setProductLine(request.getProductLine());
        Integer result = this.tianChuangRiskClient.verifyBankCard3(param, msg);
        driverCheckService.insert(request, 4, result, msg.toString());
        log.info("[][][][]验证银行卡信息返回结果文案:{}", msg);
        if (result != 0) {
            return RiskSceneResult.fail(msg.toString());
        }
        return RiskSceneResult.pass("");
    }
}
