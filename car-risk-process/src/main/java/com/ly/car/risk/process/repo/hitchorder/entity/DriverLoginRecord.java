package com.ly.car.risk.process.repo.hitchorder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车主登录记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023/02/15 12:59
 */
@Getter
@Setter
@TableName("driver_login_record")
public class DriverLoginRecord extends Model<DriverLoginRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 初驾/准驾车型C1，C2
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 司机唯一编码
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 设备id
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 状态 
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;


}
