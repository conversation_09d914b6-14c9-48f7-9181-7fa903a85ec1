package com.ly.car.risk.process.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.model.common.DSFRequest;
import com.ly.car.risk.process.utils.DSFUtils;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.virtualphoneApi.req.CallLogSearchReq;
import com.ly.car.virtualphoneApi.rsp.CallLogSearchRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Description of VirtualPhoneApiClient
 *
 * <AUTHOR>
 * @date 2024/3/14
 * @desc
 */
@Slf4j
@Service
public class VirtualPhoneApiClient {

    @Resource
    private UrlsProperties urlProperties;

    public List<CallLogSearchRsp> search(String orderId, String virtualPhone){
        if(StringUtils.isAnyBlank(orderId,virtualPhone)){
            return new ArrayList<>();
        }
        CallLogSearchReq searchReq = new CallLogSearchReq();
        searchReq.setOrderId(orderId);
        searchReq.setVirtualPhone(virtualPhone);

        try {
            String resp = DSFUtils.sendAction(getDsfRequest("callLogApi", "search", searchReq));
            LoggerUtils.info(log,"虚拟通话记录查询,req:{},resp:{}", JSON.toJSONString(searchReq),JSON.toJSONString(resp));
            if(StringUtils.isBlank(resp) || !resp.startsWith("[") || !resp.endsWith("]")){
                return new ArrayList<>();
            }
            List<CallLogSearchRsp> result = JSON.parseObject(resp,new TypeReference<List<CallLogSearchRsp>>(){});
            return result;
        } catch (Exception e) {
            LoggerUtils.warn(log,"虚拟通话记录查询异常,req:{}", e, JSON.toJSONString(searchReq));
            return new ArrayList<>();
        }
    }

    public DSFRequest getDsfRequest(String serviceName, String actionName, Object request) {
        return new DSFRequest.Builder(
                urlProperties.getVirtualPhoneCallDsfName(),
                serviceName,
                actionName,
                urlProperties.getVirtualPhoneCallDsfVersion()
        ).postBody(request).build();
    }

}