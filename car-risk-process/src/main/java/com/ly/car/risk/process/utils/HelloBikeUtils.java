package com.ly.car.risk.process.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.car.risk.process.CacheInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description of HelloBikeUtils
 *
 * <AUTHOR>
 * @date 2024/5/20
 * @desc
 */
@Slf4j
public class HelloBikeUtils {
    private static final String MD5 = "MD5";

    public static String signTopRequest(Map<String, String> params, String secret) {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);
        for (String key : keys) {
            if (StringUtils.isNotBlank(key)) {
                String value = params.get(key);
                if (null != value) {
                    query.append(key.trim());
                    query.append(value.trim());
                }
            }
        }
        query.append(secret);
        return getMd5Str(query.toString());
    }

    public static String getStringToSign(JSONObject params, String secret) {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);

        for (String key : keys) {
            if (StringUtils.isNotBlank(key)) {
                Object value = params.get(key);
                if (null != value) {
                    query.append(key.trim());
                    if (value instanceof List || value instanceof Map) {
                        query.append(JSON.toJSONString(value).trim());
                    } else {
                        query.append(String.valueOf(value).trim());
                    }
                }
            }
        }
        query.append(secret);
        return query.toString();
    }

    private static String getListValue(List<Object> value) {
        StringBuilder sb = new StringBuilder();
        for (Object obj : value) {
            sb.append(JSON.toJSONString(obj, SerializerFeature.SortField).trim());
        }
        return sb.toString();

    }

    private static String getMapValue(Map<String, String> map) {
        StringBuilder sb = new StringBuilder();
        String[] keys = map.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        for (String key : keys) {
            if (StringUtils.isNotBlank(key)) {
                Object value = map.get(key);
                if (null != value) {
                    sb.append(key.trim());
                    sb.append(value);
                }
            }
        }
        return sb.toString();
    }

    public static String signTopRequest(JSONObject params, String secret) {
        return getMd5Str(getStringToSign(params, secret));
    }


    /**
     * 获取md5 byte数组
     */
    public static byte[] getMd5(String str) {
        MessageDigest messageDigest;
        try {
            messageDigest = MessageDigest.getInstance(MD5);
            messageDigest.reset();
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            log.error("NoSuchAlgorithmException caught!");
            return ArrayUtils.EMPTY_BYTE_ARRAY;
        }
        return messageDigest.digest();
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String str = " {\"appKey\":\"tytx-T8odQR1X\",\"blacklist\":[{\"endTime\":\"2099-01-01 00:00:00\",\"startTime\":\"2024-05-21 15:19:01\",\"vehiclePlateNum\":\"浙AN0762\"},{\"endTime\":\"2099-01-01 00:00:00\",\"startTime\":\"2024-05-20 15:19:01\",\"vehiclePlateNum\":\"浙AS7723\"},{\"endTime\":\"2099-01-01 00:00:00\",\"startTime\":\"2024-05-18 15:19:01\",\"vehiclePlateNum\":\"沪NH6853\"}],\"method\":\"sfc.black.driver\",\"timestamp\":\"1716369509\",\"type\":1}\n";
        JSONObject jsonObject = JSONObject.parseObject(str);
        String result = signTopRequest(jsonObject, "c17bdf6e53204cc9969a904f2339f363");
        System.out.println(result);
    }

    /**
     * MD5 加密
     */
    public static String getMd5Str(String str) {
        return toHex(getMd5(str));
    }


    /**
     * 转小写16进制字符串
     *
     * @param byteArray
     * @return
     */
    private static String toHex(byte[] byteArray) {
        if (null == byteArray) {
            return null;
        }

        StringBuilder md5Str = new StringBuilder();

        for (byte b : byteArray) {
            md5Str.append(String.format("%02x", b));
        }
        return md5Str.toString();

    }
}