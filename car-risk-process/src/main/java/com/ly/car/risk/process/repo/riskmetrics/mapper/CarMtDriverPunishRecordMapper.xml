<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverPunishRecordMapper">
    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverPunishRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="punish_id" jdbcType="BIGINT" property="punishId"/>
        <result column="order_serial_no" jdbcType="VARCHAR" property="orderSerialNo"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="driver_id" jdbcType="VARCHAR" property="driverId"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="full_supplier_code" jdbcType="VARCHAR" property="fullSupplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="penalty_amount" jdbcType="DECIMAL" property="penaltyAmount"/>
        <result column="recovery_amount" jdbcType="DECIMAL" property="recoveryAmount"/>
        <result column="gmt_trip_finished" jdbcType="TIMESTAMP" property="gmtTripFinished"/>
        <result column="gmt_pubish" jdbcType="TIMESTAMP" property="gmtPubish"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, punish_id, order_serial_no, order_type, driver_id, car_num, supplier_code, full_supplier_code, 
    supplier_name, penalty_amount, recovery_amount, gmt_trip_finished, gmt_pubish, gmt_create, 
    gmt_update
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from car_mt_driver_punish_record
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from car_mt_driver_punish_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverPunishRecord"
            useGeneratedKeys="true">
        insert into car_mt_driver_punish_record (punish_id, order_serial_no, order_type,
                                                 driver_id, car_num, supplier_code,
                                                 full_supplier_code, supplier_name, penalty_amount,
                                                 recovery_amount, gmt_trip_finished, gmt_pubish,
                                                 gmt_create, gmt_update)
        values (#{punishId,jdbcType=BIGINT}, #{orderSerialNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER},
                #{driverId,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR},
                #{fullSupplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR},
                #{penaltyAmount,jdbcType=DECIMAL},
                #{recoveryAmount,jdbcType=DECIMAL}, #{gmtTripFinished,jdbcType=TIMESTAMP},
                #{gmtPubish,jdbcType=TIMESTAMP},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverPunishRecord"
            useGeneratedKeys="true">
        insert into car_mt_driver_punish_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="punishId != null">
                punish_id,
            </if>
            <if test="orderSerialNo != null">
                order_serial_no,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="driverId != null">
                driver_id,
            </if>
            <if test="carNum != null">
                car_num,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
            <if test="fullSupplierCode != null">
                full_supplier_code,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
            <if test="penaltyAmount != null">
                penalty_amount,
            </if>
            <if test="recoveryAmount != null">
                recovery_amount,
            </if>
            <if test="gmtTripFinished != null">
                gmt_trip_finished,
            </if>
            <if test="gmtPubish != null">
                gmt_pubish,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="punishId != null">
                #{punishId,jdbcType=BIGINT},
            </if>
            <if test="orderSerialNo != null">
                #{orderSerialNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="driverId != null">
                #{driverId,jdbcType=VARCHAR},
            </if>
            <if test="carNum != null">
                #{carNum,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="fullSupplierCode != null">
                #{fullSupplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="penaltyAmount != null">
                #{penaltyAmount,jdbcType=DECIMAL},
            </if>
            <if test="recoveryAmount != null">
                #{recoveryAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtTripFinished != null">
                #{gmtTripFinished,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtPubish != null">
                #{gmtPubish,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverPunishRecord">
        update car_mt_driver_punish_record
        <set>
            <if test="punishId != null">
                punish_id = #{punishId,jdbcType=BIGINT},
            </if>
            <if test="orderSerialNo != null">
                order_serial_no = #{orderSerialNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="driverId != null">
                driver_id = #{driverId,jdbcType=VARCHAR},
            </if>
            <if test="carNum != null">
                car_num = #{carNum,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="fullSupplierCode != null">
                full_supplier_code = #{fullSupplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="penaltyAmount != null">
                penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
            </if>
            <if test="recoveryAmount != null">
                recovery_amount = #{recoveryAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtTripFinished != null">
                gmt_trip_finished = #{gmtTripFinished,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtPubish != null">
                gmt_pubish = #{gmtPubish,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverPunishRecord">
        update car_mt_driver_punish_record
        set punish_id          = #{punishId,jdbcType=BIGINT},
            order_serial_no    = #{orderSerialNo,jdbcType=VARCHAR},
            order_type         = #{orderType,jdbcType=INTEGER},
            driver_id          = #{driverId,jdbcType=VARCHAR},
            car_num            = #{carNum,jdbcType=VARCHAR},
            supplier_code      = #{supplierCode,jdbcType=VARCHAR},
            full_supplier_code = #{fullSupplierCode,jdbcType=VARCHAR},
            supplier_name      = #{supplierName,jdbcType=VARCHAR},
            penalty_amount     = #{penaltyAmount,jdbcType=DECIMAL},
            recovery_amount    = #{recoveryAmount,jdbcType=DECIMAL},
            gmt_trip_finished  = #{gmtTripFinished,jdbcType=TIMESTAMP},
            gmt_pubish         = #{gmtPubish,jdbcType=TIMESTAMP},
            gmt_create         = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_update         = #{gmtUpdate,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="checkPunishCount" resultType="com.ly.car.risk.process.model.riskJob.DriverPunishOrderCheckResp">
        select car_num as carNum,
        sum(ifnull(penalty_amount, 0)) as recoveryAmount,
        count(distinct order_serial_no) as punishOrderCount,
        group_concat(supplier_name) as supplierName
        from car_mt_driver_punish_record
        where penalty_amount > 0
        and car_num != ''
        and gmt_pubish >= #{dayBegin}
        and supplier_code = 'MadaSaas'
        group by car_num
        having 1 = 1
        <if test="left != null">
            and recoveryAmount >= #{left}
        </if>
        <if test="right != null">
            and #{right} > recoveryAmount
        </if>
    </select>

</mapper>