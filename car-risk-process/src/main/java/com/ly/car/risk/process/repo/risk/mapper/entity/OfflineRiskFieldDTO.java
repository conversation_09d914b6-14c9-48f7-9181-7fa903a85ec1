package com.ly.car.risk.process.repo.risk.mapper.entity;

import lombok.Data;

/**
 * Description of RiskStrategy
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Data
public class OfflineRiskFieldDTO {
    // 关联id
    private Long   relationId;
    // 指标id
    private Long   field;
    // 策略id
    private Long   strategyId;
    // 运算符
    private String operator;
    // 0-常量 1-特征
    private int    rightType;
    // 数字或任意字符串
    private String rightValue;
    private int    sort;
    private String name;
}