package com.ly.car.risk.process.service.workOrder.dto;

import lombok.Data;

import java.util.List;

@Data
public class DriverCheatListDTO {

    private String createTime;//创建时间
    private String orderId;//订单号
    private List<String> riskLabel;//违规标签
    private String riskRemark;
    private Integer appealStatus;//申诉状态
    private String appealStatusName;//0-待申诉 1-已申诉待审核 2-申诉成功 3-申诉失败
    private String 	appealTime;//申诉时间
    private String checkTime;//审核时间/处理时间
    private String refuseText;//驳回原因
    private String checkText;//审核文案
    private String appealText;//申诉文本
    private List<String> appealUrl;//申诉证据
    private List soundFile;//申诉语音·
    private String driverMemberId;


}
