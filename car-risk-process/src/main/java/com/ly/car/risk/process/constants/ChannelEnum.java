package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum ChannelEnum {

    MEITUAN("1916438563", "美团"),
    SHOUQI("1777225661", "首汽"),
    GOUPU("1563858485", "够谱"),
    HUAWEI("huawei", "华为");

    private String code;
    private String msg;

    ChannelEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static Map<String,String> getAllEnum(){
        Map<String,String> allEnums = new HashMap<>();
        for(ChannelEnum type : values()){
            allEnums.put(type.code,type.msg);
        }
        return allEnums;
    }

    public static String getMsgByCode(String code) {
        for (ChannelEnum enumItem : ChannelEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
