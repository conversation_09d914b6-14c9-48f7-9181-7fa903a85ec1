package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Scope("prototype")
public class RuleRiskxj007Service extends FilterCheckPriceHandler{


    @Override
    public UiResult doHandler(FilterSceneContext context) {
        if(!context.getSpecialCarRuleConfig().getXj007onOff()){
            if(this.nextHandler != null){
                return this.nextHandler.doHandler(context);
            } else {
                return context.getUiResult();
            }
        }
        return context.getUiResult();
    }
}
