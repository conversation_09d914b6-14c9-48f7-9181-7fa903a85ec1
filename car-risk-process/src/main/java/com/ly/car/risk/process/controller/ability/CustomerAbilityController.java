package com.ly.car.risk.process.controller.ability;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.controller.ability.dto.CustomerAbilityDTO;
import com.ly.car.risk.process.controller.ability.params.CustomerAbilityParam;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("customer")
@Slf4j
public class CustomerAbilityController {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private HcCustomerMapper hcCustomerMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @RequestMapping("query")
    public UiResult queryList(@RequestBody CustomerAbilityParam param){
        List<RiskCustomerManage> customerManageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("bind_user",param.getPassengerPhone())
                .gt("invalid_time",new Date())
        );
        List<CustomerAbilityDTO> dtoList = new ArrayList<>();
        if(param.getType() == 1){
            dtoList = customerManageList.stream()
                    .filter(data->data.getRiskType() == 7)
                    .map(data->{
                        CustomerAbilityDTO dto = new CustomerAbilityDTO();
                        dto.setPlateNumber(data.getCustomerValue());
                        dto.setType(data.getRiskType());
                        return dto;
                    })
                    .collect(Collectors.toList());
        }
        return UiResult.ok(dtoList);
    }

    /**
     * 这边查下车牌号在风控这边全部的黑名单，乘车人手机号有一对一
     * */
    @RequestMapping("queryAll")
    public UiResult queryAllList(@RequestBody CustomerAbilityParam param){
        //先查下用户端的名单
        List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("customer_value", param.getPlateNumber())
                .gt("invalid_time", new Date())
        );

        List<RiskCustomerManage> whiteTcCustomerList = customerManageList.stream()
                .filter(data->data.getRiskType()==2)
                .collect(Collectors.toList());

        List<CustomerAbilityDTO> dtoList = new ArrayList<>();
        dtoList.addAll(customerManageList.stream()
                .filter(data->data.getRiskType() == 1 || (data.getRiskType() == 7 && data.getBindUser().equals(param.getPassengerPhone())))
                .map(data->{
                    CustomerAbilityDTO dto = new CustomerAbilityDTO();
                    dto.setPlateNumber(data.getCustomerValue());
                    dto.setType(data.getRiskType());
                    return dto;
                })
                .collect(Collectors.toList()));

        //再查下司机端的名单
        List<HcCustomer> driverCustomerList = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                .eq("driver_card_no",param.getPlateNumber())
                .gt("invalid_time",new Date())
        );
        //先看下有没有白名单
        List<HcCustomer> whiteCustomerList = driverCustomerList.stream()
                .filter(data->data.getCustomerType() == 0 || data.getCustomerType() == 6)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(whiteCustomerList) && CollectionUtils.isNotEmpty(whiteTcCustomerList)){
            return UiResult.ok(new ArrayList<>());
        }
        //再看下有没有黑名单和禁止接单黑名单
        List<HcCustomer> blackCustomerList = driverCustomerList.stream()
                .filter(data->data.getCustomerType() == 1 || data.getCustomerType() == 7)
                .collect(Collectors.toList());
        dtoList.addAll(
                blackCustomerList.stream().map(data->{
                    CustomerAbilityDTO dto = new CustomerAbilityDTO();
                    dto.setPlateNumber(data.getDriverCardNo());
                    dto.setType(data.getCustomerType());
                    return dto;
                }).collect(Collectors.toList())
        );

        return UiResult.ok(dtoList);
    }

    @RequestMapping("queryBlack")
    public UiResult queryBlack(@RequestBody CustomerAbilityParam param){
        //先查下用户端的名单
        List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("customer_value", param.getPlateNumber())
                .gt("invalid_time", new Date())
        );
        //再查下司机端的名单
        //再查下司机端的名单
        List<HcCustomer> driverCustomerList = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                .gt("invalid_time",new Date()).and(data->data
                .eq(StringUtils.isNotBlank(param.getDriverId()),"driver_id",param.getDriverId())
                .eq(StringUtils.isNotBlank(param.getPlateNumber()),"driver_card_no",param.getPlateNumber())
                .eq(StringUtils.isNotBlank(param.getDriverPhone()),"driver_phone",param.getDriverPhone()))
        );
        if(CollectionUtils.isEmpty(customerManageList) && CollectionUtils.isEmpty(driverCustomerList)){
            return UiResult.ok();
        }
        RiskCustomerManage riskCustomerManage = customerManageList.stream().filter(data->data.getRiskType() == 2).findFirst().orElse(null);
        if(riskCustomerManage != null){
            return UiResult.ok();
        }
        HcCustomer hcCustomer = driverCustomerList.stream().filter(data->data.getCustomerType()==0).findFirst().orElse(null);
        if(hcCustomer != null){
            return UiResult.ok();
        }
        //到这边说明两边至少有一个是有黑名单的
        List<CustomerAbilityDTO> dtoList = new ArrayList<>();
        dtoList.addAll(customerManageList.stream().filter(data->data.getRiskType() == 1).map(data->{
            CustomerAbilityDTO dto = new CustomerAbilityDTO();
            dto.setPlateNumber(data.getCustomerValue());
            dto.setType(data.getCustomerType());
            return dto;
        }).collect(Collectors.toList()));
        dtoList.addAll(driverCustomerList.stream().map(data->{
            CustomerAbilityDTO dto = new CustomerAbilityDTO();
            dto.setPlateNumber(data.getDriverCardNo());
            dto.setType(data.getCustomerType());
            return dto;
        }).collect(Collectors.toList()));

        if(CollectionUtils.isEmpty(dtoList)){
            return UiResult.ok();
        }
        UiResult result = UiResult.ok(dtoList);
        result.setMsg("账户异常，操作失败");
        return result;
    }


    @RequestMapping("queryBlackByUser")
    public UiResult queryBlackByUser(@RequestBody CustomerAbilityParam param){
        RiskResultNewDTO riskResultNewDTO = new RiskResultNewDTO();
        //先查下用户端的名单
//        List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
//                .gt("invalid_time", new Date())
//                .and(qw-> qw.eq(StringUtils.isNotBlank(param.getPassengerPhone()), "customer_value", param.getPassengerPhone())
//                    .or().eq(StringUtils.isNotBlank(param.getMemberId()), "customer_value", param.getMemberId())
//                    .or().eq(StringUtils.isNotBlank(param.getUnionId()), "customer_value", param.getUnionId())
//        ));

        //查下白名单
        List<String> whitKeyParam = new ArrayList<>();
        List<String> blackKeyParam = new ArrayList<>();
        if(StringUtils.isNotBlank(param.getPassengerPhone())){
            whitKeyParam.add("TC_CUSTOMER_2"+"_"+param.getPassengerPhone());
            blackKeyParam.add("TC_CUSTOMER_1"+"_"+param.getPassengerPhone());
        }
        if(StringUtils.isNotBlank(param.getMemberId())){
            whitKeyParam.add("TC_CUSTOMER_2"+"_"+param.getMemberId());
            blackKeyParam.add("TC_CUSTOMER_2"+"_"+param.getMemberId());
        }
        if(StringUtils.isNotBlank(param.getUnionId())){
            whitKeyParam.add("TC_CUSTOMER_2"+"_"+param.getUnionId());
            blackKeyParam.add("TC_CUSTOMER_2"+"_"+param.getUnionId());
        }
        Integer whiteCount = 0;
        for(String key : whitKeyParam){
            RBucket<String> bucket = redissonClient.getBucket(key);
            if(bucket.isExists()){
                whiteCount = 1;
                break;
            }
        }
        log.info("[][][][]当前缓存命中白名单情况{}",whiteCount);
        if(whiteCount > 0){
            return UiResult.ok(riskResultNewDTO);
        }
        Integer blackCount = 0;
        for(String key : blackKeyParam){
            RBucket<String> bucket = redissonClient.getBucket(key);
            if(bucket.isExists()){
                blackCount = 1;
                break;
            }
        }
        log.info("[][][][]当前缓存命中黑名单情况{}",blackCount);
        if(blackCount > 0){
            riskResultNewDTO.setCode(1);
            riskResultNewDTO.setMessage("命中黑名单");
            return UiResult.ok(riskResultNewDTO);
        }

//        if(CollectionUtils.isEmpty(customerManageList)){
//            return UiResult.ok(riskResultNewDTO);
//        }
//        RiskCustomerManage riskCustomerManage = customerManageList.stream().filter(data->data.getRiskType()==2).findFirst().orElse(null);
//        if(riskCustomerManage == null){
//            riskResultNewDTO.setCode(1);
//            riskResultNewDTO.setMessage("命中黑名单");
//        }
        return UiResult.ok(riskResultNewDTO);
    }

    @RequestMapping("queryBlackBatch")
    public UiResult queryBlackBatch(@RequestBody List<CustomerAbilityParam> param){
        //先查下用户端的名单
        List<String> customerList = param.stream().map(CustomerAbilityParam::getPlateNumber).collect(Collectors.toList());
        List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .in("customer_value", customerList)
                .gt("invalid_time", new Date())
        );

        //筛选出白名单
        List<RiskCustomerManage> whiteTcCustomerList = customerManageList.stream()
                .filter(data->data.getRiskType()==2)
                .collect(Collectors.toList());
        List<String> whiteCustomerList = whiteTcCustomerList.stream().map(RiskCustomerManage::getCustomerValue).collect(Collectors.toList());

        //找出黑名单且不包含白名单的
        List<CustomerAbilityDTO> dtoList = new ArrayList<>();
        //1v1拉黑优先级最高
        dtoList.addAll(customerManageList.stream()
                .filter(data->(data.getRiskType() == 1 && !whiteCustomerList.contains(data.getCustomerValue())) || (data.getRiskType() == 7 && data.getBindUser().equals(param.get(0).getPassengerPhone())))
                .map(data->{
                    CustomerAbilityDTO dto = new CustomerAbilityDTO();
                    dto.setPlateNumber(data.getCustomerValue());
                    dto.setType(data.getRiskType());
                    return dto;
                })
                .collect(Collectors.toList()));

        //再查下司机端的名单
        List<HcCustomer> driverCustomerList = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                .in("driver_card_no",customerList)
                .gt("invalid_time",new Date())
        );
        //先看下有没有白名单
        List<HcCustomer> hcWhiteCustomerList = driverCustomerList.stream()
                .filter(data->data.getCustomerType() == 0 || data.getCustomerType() == 6)
                .collect(Collectors.toList());
        List<String> hcWhiteCustomerValue = hcWhiteCustomerList.stream().map(HcCustomer::getDriverCardNo).collect(Collectors.toList());

        //再看下有没有黑名单和禁止接单黑名单
        List<HcCustomer> blackCustomerList = driverCustomerList.stream()
                .filter(data->data.getCustomerType() == 1 || data.getCustomerType() == 7)
                .collect(Collectors.toList());


        dtoList.addAll(
                blackCustomerList.stream().filter(data->!hcWhiteCustomerValue.contains(data.getDriverCardNo())).map(data->{
                    CustomerAbilityDTO dto = new CustomerAbilityDTO();
                    dto.setPlateNumber(data.getDriverCardNo());
                    dto.setType(data.getCustomerType());
                    return dto;
                }).collect(Collectors.toList())
        );

        return UiResult.ok(dtoList);
    }
}
