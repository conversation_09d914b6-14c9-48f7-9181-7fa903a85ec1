package com.ly.car.risk.process.controller.dto;

import lombok.Data;

@Data
public class BlackInfoManageDTO {

    private String  driverCardNo;
    private String  invalidTime;
    private String  riskRemark;
    private String  orderId;
    private Integer  optionType;

    private Integer customerType;//名单类型 0-一对一拉黑 1-全部拉黑
    private String  shieldingType;//拉黑类型 1-用户要求禁止司机为其服务 2-发生严重安全风险 3-其他问题
    private String  shieldingTypeChild;
    private String  bindUser;//绑定用户的memberId
    private String  driverName;
    private String  optionName;
    // 供应商名称
    private String supplierName;
    // 下单用户mid、会员ID
    private String memberId;
}
