package com.ly.car.risk.process.handler.orderstate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.controller.request.safecenter.SafetySmsRequest;
import com.ly.car.risk.process.model.enums.SmsTypeEnum;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.service.sms.impl.SendSecuritySmsImpl;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Description of AbstractOrderStateHandler
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc
 */
@Slf4j
public abstract class AbstractOrderStateHandler implements OrderStateHandler{

    @Resource
    protected SaveScoredSortedSetService saveScoredSortedSetService;

    @Resource
    protected SendSecuritySmsImpl sendSecuritySms;

    @Resource(name = "binlogProducer")
    protected MqRiskProducer binLogProducer;

    @Resource(name = "riskSecurityProducer")
    protected MqRiskProducer riskSecurityProducer;

    @Resource(name = "riskProcessRedisson")
    protected RedissonClient redissonClient;


    @Override
    public void dealDraftState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
    }

    @Override
    public void dealDispatchingState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {

    }


    protected void virutalCallCheckTask(CarOrderDetail orderDetail) {
        // 自我扫描虚拟通话
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", orderDetail.getOrderId());
        jsonObject.put("startTime",new Date());
        riskSecurityProducer.send(MqTagEnum.car_risk_self_security_task, JsonUtils.json(jsonObject), 3);
        LoggerUtils.info(log,"发送给[riskSecurityConsumer]开启虚拟通话扫描任务，tag:{},msg:{}",MqTagEnum.car_risk_self_security_task.name(), JSON.toJSONString(jsonObject));
    }


    protected void safetyTripShare(CarOrderDetail orderDetail){
        SafetySmsRequest request = new SafetySmsRequest();
        request.setSmsTypeEnum(SmsTypeEnum.YC_SAFETY_NOTICE);
        Map<String,String> smsParams = new HashMap<>();
        smsParams.put("orderId",orderDetail.getOrderId());
        smsParams.put("memberId",orderDetail.getMemberId());
        smsParams.put("userPhone", StringUtils.defaultIfBlank(orderDetail.getBaseInfo().getContactPhone(),orderDetail.getPassengerInfo().getPassengerCellPhone()));
        smsParams.put("carNumber",orderDetail.getCarInfo().getCarNum());
        request.setSmsParams(smsParams);
        sendSecuritySms.sendSecuritySms(request,orderDetail);
    }
}