<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtRideOrderInfoMapper">
    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtRideOrderInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="pay_price" jdbcType="DECIMAL" property="payPrice"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="member_id" jdbcType="BIGINT" property="memberId"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="pay_status" jdbcType="INTEGER" property="payStatus"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, order_no, amount, pay_price, union_id, open_id, member_id, driver_id, vehicle_no, 
    order_status, pay_status, order_type, create_user, create_time, update_user, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from car_mt_ride_order_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from car_mt_ride_order_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtRideOrderInfo" useGeneratedKeys="true">
        insert into car_mt_ride_order_info (order_no, amount, pay_price,
                                            union_id, open_id, member_id,
                                            driver_id, vehicle_no, order_status,
                                            pay_status, order_type, create_user,
                                            create_time, update_user, update_time)
        values (#{orderNo,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, #{payPrice,jdbcType=DECIMAL},
                #{unionId,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, #{memberId,jdbcType=BIGINT},
                #{driverId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER},
                #{payStatus,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER}, #{createUser,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtRideOrderInfo" useGeneratedKeys="true">
        insert into car_mt_ride_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="payPrice != null">
                pay_price,
            </if>
            <if test="unionId != null">
                union_id,
            </if>
            <if test="openId != null">
                open_id,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="driverId != null">
                driver_id,
            </if>
            <if test="vehicleNo != null">
                vehicle_no,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="payStatus != null">
                pay_status,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="payPrice != null">
                #{payPrice,jdbcType=DECIMAL},
            </if>
            <if test="unionId != null">
                #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                #{openId,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                #{memberId,jdbcType=BIGINT},
            </if>
            <if test="driverId != null">
                #{driverId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                #{payStatus,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtRideOrderInfo">
        update car_mt_ride_order_info
        <set>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="payPrice != null">
                pay_price = #{payPrice,jdbcType=DECIMAL},
            </if>
            <if test="unionId != null">
                union_id = #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                open_id = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                member_id = #{memberId,jdbcType=BIGINT},
            </if>
            <if test="driverId != null">
                driver_id = #{driverId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtRideOrderInfo">
        update car_mt_ride_order_info
        set order_no     = #{orderNo,jdbcType=VARCHAR},
            amount       = #{amount,jdbcType=DECIMAL},
            pay_price    = #{payPrice,jdbcType=DECIMAL},
            union_id     = #{unionId,jdbcType=VARCHAR},
            open_id      = #{openId,jdbcType=VARCHAR},
            member_id    = #{memberId,jdbcType=BIGINT},
            driver_id    = #{driverId,jdbcType=BIGINT},
            vehicle_no   = #{vehicleNo,jdbcType=VARCHAR},
            order_status = #{orderStatus,jdbcType=INTEGER},
            pay_status   = #{payStatus,jdbcType=INTEGER},
            order_type   = #{orderType,jdbcType=INTEGER},
            create_user  = #{createUser,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_user  = #{updateUser,jdbcType=VARCHAR},
            update_time  = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <resultMap id="strategyResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail">
        <id property="id" column="id"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="memberId" column="member_id"/>
        <result property="unionId" column="union_id"/>
        <result property="productLine" column="product_line"/>
        <result property="orderType" column="order_type"/>
        <result property="orderState" column="order_state"/>
        <result property="amount" column="amount"/>
        <result property="payState" column="pay_state"/>
        <result property="orderChannel" column="order_channel"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtPaid" column="gmt_paid"/>
        <result property="gmtCanceled" column="gmt_canceled"/>
        <result property="gmtUsage" column="gmt_usage"/>
        <result property="gmtDeparture" column="gmt_departure"/>
        <result property="gmtArrive" column="gmt_arrive"/>
        <result property="gmtTripFinished" column="gmt_trip_finished"/>
        <result property="cancelType" column="cancel_type"/>
        <result property="cancelReason" column="cancel_reason"/>
        <result property="payCategory" column="pay_category"/>
        <result property="departureCityCode" column="departure_city_code"/>
        <result property="arrivalCityCode" column="arrival_city_code"/>
        <result property="departureAddress" column="departure_address"/>
        <result property="arrivalAddress" column="arrival_address"/>
        <result property="passengerPhone" column="passenger_phone"/>
        <result property="estimateDistance" column="estimate_distance"/>
        <result property="estimateTime" column="estimate_time"/>
        <result property="realDistance" column="real_distance"/>
        <result property="realTime" column="real_time"/>
        <result property="carNum" column="car_num"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierPrice" column="supplier_price"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="washExt" column="wash_ext"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="distributionFlag" column="distribution_flag"/>
        <result property="bookAmount" column="book_amount"/>
        <result property="surcharge" column="surcharge"/>
        <result property="supplementaryAmount" column="supplementary_amount"/>
        <result property="rightsOrderFlag" column="rights_order_flag"/>
    </resultMap>

    <select id="driver24hourFinishOrderLess6min" resultMap="strategyResultMap">
        SELECT roi.order_no as orderSerialNo
        FROM `car_mt_ride_order_info` roi
                 join `car_mt_ride_order_address_extend` roae on roi.order_no = roae.order_no
        where roi.vehicle_no = #{carNum}
          and roi.order_status = 6
          and roae.end_driving_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
          and 6 > TIMESTAMPDIFF(
                MINUTE,
                roae.star_driving_time,
                roae.end_driving_time
            )
    </select>
</mapper>