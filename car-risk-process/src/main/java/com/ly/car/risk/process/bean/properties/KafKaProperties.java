package com.ly.car.risk.process.bean.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties("config.kafka")
public class KafKaProperties {
    /**
     * 数据中心集群地址
     */
    private String bdsServer;

    /**
     * 苏州中心集群地址
     */
    private String szServer;

    /**
     * 消费组，不管哪个集群都使用同一消费组
     */
    private String groupName;

    /**
     * 开关，是否开启消费
     * todo为业务名称
     */
    private boolean todoConsumer;
    /**
     * 消费主题
     * todo为业务名称
     */
    private String todoTopic;

    /**
     * 司推乘 是否开启消费
     * */
    private boolean disOfflineConsumer;

    /**
     * 司推乘topic
     * */
    private String disOfflineTopic;

    /**
     * 首汽坏账 是否开启消费
     * */
    private boolean channelBadDebtsConsumer;

    /**
     * 首汽坏账topic
     * */
    private String channelBadDebtsTopic;

    /**
     * 完单司机存储
     * */
    private Boolean finishDriverConsumer;

    /**
     * 完单司机topic
     * */
    private String finishDriverTopic;

    /**
     * 指标存储
     * */
    private Boolean indexSync;

    /**
     * 指标topic
     * */
    private String indexSyncTopic;



}
