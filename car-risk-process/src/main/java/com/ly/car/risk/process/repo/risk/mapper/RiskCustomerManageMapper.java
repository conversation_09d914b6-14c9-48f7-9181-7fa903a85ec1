package com.ly.car.risk.process.repo.risk.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-10-09
 */
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {


    @Select("select "
            + "  * "
            + "from "
            + "  risk_customer_manage "
            + "where "
            + "  status = 1 "
            + "  and ( "
            + "    ( "
            + "      ttl != -1 "
            + "      and ttl != 0 "
            + "      and NOW() >= DATE_ADD(create_time, INTERVAL ttl day) "
            + "    ) "
            + "    or ( "
            + "      ttl = 0 "
            + "      and NOW() >= invalid_time "
            + "    ) "
            + "  )")
    List<RiskCustomerManage> selectInvalidData();


    List<RiskCustomerManage> getListByValue(@Param("params")FilterParams params, @Param("invalidTime")Date dateTime);

    List<RiskCustomerManage> getListByValueByGroup(@Param("params") CommonCustomerParam params, @Param("invalidTime")Date dateTime);

    long getValidCount(@Param("customerType") int customerType,
                       @Param("customerValue") String customerValue,
                       @Param("riskType") int riskType);

    List<RiskCustomerManage> queryAllValidRiskRecord(@Param("offset") int offset, @Param("limit") int limit);
}
