package com.ly.car.risk.process.repo.riskmetrics.mapper;

import cn.hutool.core.lang.Opt;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverBill;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CarMtDriverBillMapper {
    int deleteByPrimaryKey(Long id);
    
    int insert(CarMtDriverBill record);
    
    int insertSelective(CarMtDriverBill record);
    
    CarMtDriverBill selectByPrimaryKey(Long id);
    
    int updateByPrimaryKeySelective(CarMtDriverBill record);
    
    int updateByPrimaryKey(CarMtDriverBill record);
    
    List<CarRiskOrderDetail> driver24HourBillOrderCount(@Param("carNum") String carNum);
    
    List<CarRiskOrderDetail> driver24hourBillOrderAmount(@Param("carNum") String carNum);
}