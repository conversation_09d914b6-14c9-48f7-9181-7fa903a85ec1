package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
@Scope("prototype")
public class RuleRisk1003Service extends FilterSendOrderHandler{

    private static final String ruleNo = "1003";

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        //在这边先查下是否黑名单
        CommonCustomerParam params = new CommonCustomerParam();
        params.setDeviceId(context.getDeviceId());
        params.setMemberId(context.getMemberId());
        params.setUserPhone(context.getUserPhone());
        params.setPassengerCellphone(context.getPassengerCellphone());
        params.setUnionId(context.getUnionId());
        Date date = new Date();
        List<RiskCustomerManage> manageList = riskCustomerService.getListByValueByGroup(params,date);

        RiskCustomerManage whiteManage = manageList.stream().filter(e->e.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).findFirst().orElse(null);
        if(whiteManage != null){
            return;
        }
        for(RiskCustomerManage manage : manageList){
            if(manage.getRiskType() == 1 || manage.getRiskType() == 5){
                context.getRuleList().add(new RuleChain(ruleNo,RiskLevelEnum.HIGH.getCode()));
                distributionRiskManageService.addByCustomerChain(context.getOrderId(),ruleNo,context.getMainScene(),
                        context.getChildScene(), 1,manage.getCustomerValue(),RiskLevelEnum.HIGH.getCode());

                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(manage.getRiskType()),
                        RiskLevelEnum.HIGH.getCode(),1,manage.getCustomerValue(),context.getUiResult()));
                return;
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
}
