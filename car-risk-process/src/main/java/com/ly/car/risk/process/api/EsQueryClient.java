package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.dto.ShuMeiDeviceDTO;
import com.ly.car.risk.process.api.rsp.DriverInfoEsRsp;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.*;
import java.util.stream.Collectors;

@Configuration
@Slf4j
public class EsQueryClient {

    public String queryCurrentLocation(String orderId){
        try {
            String date = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            String url = "http://es.dss.17usoft.com/index/car-track-driverlocation-"+date+"/template/query_current_location/1.0.0/search";
            Map<String,String> headMap = new HashMap<>();
            headMap.put("Content-Type","application/json");
            headMap.put("Authentication","9f0c9d18-96dc-45e2-ada5-161ce387d627");
            Map<String,Object> map = new HashMap<>();
            map.put("order_id",orderId);
            map.put("status",200);
            String post = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(map), headMap);
            //先用json
            JSONObject returnJson = JSONObject.parseObject(post);
            if(returnJson.getInteger("code") != 0){
                return null;
            }
            log.info("[queryCurrentLocation][][{}][]查询当前位置返回:{}",orderId,JsonUtils.json(returnJson));
            JSONObject resultJson = (JSONObject) returnJson.get("result");
            int count = resultJson.getInteger("count");
            if(count == 0) {
                return null;
            }
            JSONArray list = resultJson.getJSONArray("list");
            if(list == null){
                return null;
            }
            JSONObject object = (JSONObject) list.get(0);
            if(DateUtil.addMinute(DateUtil.string2Date(object.getString("datetime")),10).before(new Date())){
                log.warn("[queryCurrentLocation][][{}][]当前订单已10分钟未更新轨迹，忽略监控:{}",orderId,orderId);
                return null;
            }
            return object.getString("location");
        }catch (Exception e) {
            log.error("[queryCurrentLocation][][{}][]查询当前位置返回异常:{}",orderId,e);
        }
        return null;

    }

    public String queryMtLocation(Long driverId){
        try {
            String url = "http://es.dss.17usoft.com/index/commontraffic-currdriverpos-data/template/driverLocationQuery/5.0.0/search";
            Map<String,String> headMap = new HashMap<>();
            headMap.put("Content-Type","application/json");
            headMap.put("Authentication","280e34c73fed24a3199603a60e0f7203");
            Map<String,Object> map = new HashMap<>();
            List<Long> ids = new ArrayList<>();
            ids.add(driverId);
            map.put("driverid",ids +"");
            String post = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(map), headMap);
            //先用json
            JSONObject returnJson = JSONObject.parseObject(post);
            if(returnJson.getInteger("code") != 0){
                return null;
            }
            log.info("[queryCurrentLocation][][{}][]萌艇查询当前位置返回:{}",driverId,JsonUtils.json(returnJson));
            JSONObject resultJson = (JSONObject) returnJson.get("result");
            int count = resultJson.getInteger("count");
            if(count == 0) {
                return null;
            }
            JSONArray list = resultJson.getJSONArray("list");
            if(list == null){
                return null;
            }
            List<DriverInfoEsRsp> locationRsp = JSONArray.parseArray(JSONObject.toJSONString(list),DriverInfoEsRsp.class);
            DriverInfoEsRsp driverInfoEsRsp = locationRsp.get(0);
            Date time = new Date(driverInfoEsRsp.getCreatetime());
            if(DateUtil.addMinute(time,10).before(new Date())){
                log.info("[][][][]萌艇获取当前司机位置失效，已超过10分钟未更新{}",driverId);
                return null;
            }
            return driverInfoEsRsp.getLongitude() +","+ driverInfoEsRsp.getLatitude();
        } catch (Exception e){
            return null;
        }
    }

    public void insertDevice(JSONObject jsonObject){
        log.info("[][][][]当前存入es的原始指纹数据为:{}",JsonUtils.json(jsonObject));
        List<ShuMeiDeviceDTO> deviceDTOS = new ArrayList<>();
        ShuMeiDeviceDTO shuMeiDeviceDTO = new ShuMeiDeviceDTO();
        shuMeiDeviceDTO.setId(jsonObject.getString("id"));
        shuMeiDeviceDTO.setAbtmac(jsonObject.getString("abtmac"));
        shuMeiDeviceDTO.setAdid(jsonObject.getString("adid"));
        shuMeiDeviceDTO.setAppId(jsonObject.getString("appId"));
        shuMeiDeviceDTO.setApps(jsonObject.getString("apps"));
        shuMeiDeviceDTO.setApputm(jsonObject.getString("apputm"));
        shuMeiDeviceDTO.setAppver(jsonObject.getString("appver"));
        shuMeiDeviceDTO.setAxposed(jsonObject.getString("axposed"));
        shuMeiDeviceDTO.setAvailableSpace(jsonObject.getString("availableSpace"));
        shuMeiDeviceDTO.setBand(jsonObject.getString("band"));
        shuMeiDeviceDTO.setBattery("");
        shuMeiDeviceDTO.setBinderhook(jsonObject.getString("binderhook"));
        shuMeiDeviceDTO.setBoot(jsonObject.getString("boot"));
        shuMeiDeviceDTO.setBrand(jsonObject.getJSONObject("sys").getString("brand"));
        shuMeiDeviceDTO.setBrightness(jsonObject.getString("brightness"));
        shuMeiDeviceDTO.setBssid(jsonObject.getString("bssid"));
        shuMeiDeviceDTO.setCell("");
        shuMeiDeviceDTO.setDebuggable(jsonObject.getString("debuggable"));
        shuMeiDeviceDTO.setDebugger(jsonObject.getString("debugger"));
        shuMeiDeviceDTO.setEmu("");
        shuMeiDeviceDTO.setIccid("");
        shuMeiDeviceDTO.setModel(jsonObject.getJSONObject("sys").getString("model"));
        shuMeiDeviceDTO.setName(jsonObject.getString("name"));
        shuMeiDeviceDTO.setOperator(jsonObject.getString("operator"));
        shuMeiDeviceDTO.setOs(jsonObject.getString("os"));
        shuMeiDeviceDTO.setRiskapp("");
        shuMeiDeviceDTO.setRiskdir("");
        shuMeiDeviceDTO.setSdk_flavor("");
        shuMeiDeviceDTO.setSmid(jsonObject.getString("smid"));
        shuMeiDeviceDTO.setSsid(jsonObject.getString("ssid"));
        shuMeiDeviceDTO.setVirtual(jsonObject.getString("virtualuid"));
        shuMeiDeviceDTO.setWifiip(jsonObject.getString("wifiip"));
        shuMeiDeviceDTO.setSimstate(jsonObject.getJSONObject("props").getString("gsm.sim.state"));
        shuMeiDeviceDTO.setSerial("");
        shuMeiDeviceDTO.setManufacturer(jsonObject.getJSONObject("ainfo").getJSONObject("sys_props").getString("ro.product.manufacturer"));
        shuMeiDeviceDTO.setFingerprint(jsonObject.getJSONObject("sys").getString("fingerprint"));
        shuMeiDeviceDTO.setWifi_mac("");
        shuMeiDeviceDTO.setGothk(jsonObject.getJSONObject("ainfo").getString("gothk"));
        shuMeiDeviceDTO.setHook("");
        shuMeiDeviceDTO.setHookErr("");
        shuMeiDeviceDTO.setIs_vpn(jsonObject.getJSONObject("ainfo").getString("is_vpn"));
        shuMeiDeviceDTO.setResett(jsonObject.getJSONObject("ainfo").getString("resett"));
        shuMeiDeviceDTO.setRoot(jsonObject.getJSONObject("ainfo").getString("root"));
        deviceDTOS.add(shuMeiDeviceDTO);
        log.info("[][][][]当前存入es的指纹数据为:{}",JsonUtils.json(deviceDTOS));
        String url = "http://es.dss.17usoft.com/index/car-risk-device/type/info/bulk";
        Map<String,String> headMap = new HashMap<>();
        headMap.put("Content-Type","application/json");
        headMap.put("Authentication","3637ba0d-3bb0-454e-9c68-1fcb430a810b");
        String post = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(deviceDTOS), headMap);
        log.info("[][][][]设备信息解析存入结果{}",post);
    }

    public Boolean querySfcFinish(String mobile,String startTime){
        String url = "http://es.dss.17usoft.com/index/car-data-sfc-order/template/search_sfc_order_limit/1.0.1/search";
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("passengerPhone",mobile);
        paramMap.put("startTime",startTime);
        Map<String,String> headMap = new HashMap<>();
        headMap.put("Content-Type","application/json");
        headMap.put("Authentication","35436ed2-71a0-4477-8283-f09e4be228d1");
        log.info("[][][][]查询近期完单情况请求参数{}",JsonUtils.json(paramMap));
        String post = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(paramMap), headMap);
        log.info("[][][][]查询近期完单情况{}",post);
        JSONObject returnJson = JSONObject.parseObject(post);
        JSONObject resultJson = (JSONObject) returnJson.get("result");
        int count = resultJson.getInteger("count");
        if(count == 0) {
            return false;
        }
        JSONArray list = resultJson.getJSONArray("list");
        if(list == null){
            return false;
        }
        return true;
    }

    public void insertMobileCheckRecord(String mobile,Integer result,Boolean checkTencent){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id",mobile);
        jsonObject.put("result",result);
        jsonObject.put("checkTime",new Date());
        jsonObject.put("checkTencent",checkTencent);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);
        String url = "http://es.dss.17usoft.com/index/car-risk-mobile-check/type/info/bulk";
        Map<String,String> headMap = new HashMap<>();
        headMap.put("Content-Type","application/json");
        headMap.put("Authentication","3637ba0d-3bb0-454e-9c68-1fcb430a810b");
        String post = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(jsonArray), headMap);
        log.info("[][][][]手机号验证结果{}",post);
    }

    public static void main(String[] args) {
        new EsQueryClient().queryMtLocation(35904l);
    }
}
