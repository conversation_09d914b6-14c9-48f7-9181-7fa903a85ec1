package com.ly.car.risk.process.service.rule.common;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MileagePolicyProcess implements Policy{

    @Resource
    RiskCustomerService riskCustomerService;

    @Override
    public UiResult execute(FilterParams params) {
        //获取有哪些规则
        RiskResultDTO dto = new RiskResultDTO();
        List<RiskCustomerManage> listByValue = riskCustomerService.getListByValue(params, new Date());
        if(CollectionUtils.isEmpty(listByValue)){
            return UiResult.ok(dto);
        }
        List<RiskCustomerManage> whiteRecords = listByValue.stream()
                .filter(data->data.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(whiteRecords)){
            return UiResult.ok(dto);
        }

        //走到这就是有黑名单
        dto.setCode(1);
        dto.setObj("风控不通过");
        return UiResult.ok(dto);
    }
}
