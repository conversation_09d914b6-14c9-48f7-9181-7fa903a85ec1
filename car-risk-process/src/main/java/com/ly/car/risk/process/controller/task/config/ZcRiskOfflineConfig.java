package com.ly.car.risk.process.controller.task.config;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZcRiskOfflineConfig {

    private BigDecimal realityAmount001;
    private BigDecimal totalAmount001;
    private Integer dispatchNum001;
    private Integer selectNum001;
    private BigDecimal kiloLeft002;
    private Integer minuteLeft002;
    private BigDecimal twiceNeedAmount002;
    private BigDecimal otherFee002;

    private BigDecimal realityAmountRight007;
    private BigDecimal realityAmountLeft007;
    private BigDecimal totalAmount007;



}
