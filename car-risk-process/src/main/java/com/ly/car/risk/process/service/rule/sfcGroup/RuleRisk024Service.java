package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当天完单时间差小于6分钟的订单大于2单，且完单司机一致
 *
 * 且当天完单数量大于4单或近三天完单大于8单
 * */
@Service
@Slf4j
public class RuleRisk024Service extends FilterRuleChain {

    private static final String ruleNo = "024";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Resource
    private RuleRisk038Service ruleRisk038Service;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk022Service][][]前置判断已通过，进入规则024判断{}",orderContext.getMemberId(),orderContext.getDriverCardNo());

        this.next(ruleRisk038Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }

        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }
        orderContext.getNeedRuleMap().put(ruleNo,true);

        //当天6分钟内的订单
        List<OrderRiskContext> orderRiskContextList = orderContext.getUserContextList().stream().filter(context->context.getIntervalTime() != null)
                .filter(context->context.getIntervalTime() < orderContext.getSfcRiskRuleConfig().getTime024() )
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());

        List<OrderRiskContext> orderRiskContextListCurr = orderContext.getUserContextList().stream()
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(orderRiskContextList) && orderRiskContextList.size() > orderContext.getSfcRiskRuleConfig().getOrderNum024()){
            List<String> stringList = orderRiskContextList.stream().map(OrderRiskContext::getDriverCardNo).distinct().collect(Collectors.toList());
            List<OrderRiskContext> orderRiskContextListThree = orderContext.getUserContextList().stream()
                    .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.threeDay()))
                    .collect(Collectors.toList());
            String orderIds = StringUtils.join(orderRiskContextListThree.stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList()), ",");
            if(stringList.size() == 1 && (orderRiskContextListCurr.size() > orderContext.getSfcRiskRuleConfig().getOrderNumCurrent024()
                    || orderRiskContextListThree.size() > orderContext.getSfcRiskRuleConfig().getOrderNumThreeDay024())){
                log.info("[RuleRisk018Service][doHandler][{}][{}]命中024规则，关联用户为{},关联订单为{}"
                        ,orderContext.getMemberId(),orderContext.getDriverCardNo(),orderContext.getMemberId(), JsonUtils.json(orderIds));
                distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo,2,2,0,null, RiskLevelEnum.HIGH.getCode());
                UiResult result = UiResult.ok();
                result.setMsg("风控不通过");
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过024",null,null);
                result.setData(dto);
                orderContext.setUiResult(result);
//                return result;
            }
        }
        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
//            UiResult result = UiResult.ok();
//            result.setData("0");
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
