package com.ly.car.risk.process.kafka;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.monitor.HealthCheckService;
import com.ly.car.risk.process.api.PublicServiceClient;
import com.ly.car.risk.process.bean.properties.KafKaProperties;
import com.ly.car.risk.process.kafka.dto.CarIndexDTO;
import com.ly.car.risk.process.kafka.dto.PanelDataDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskField;
import com.ly.car.risk.process.service.core.RiskFieldCache;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.redisson.api.RList;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;

@Service
@Slf4j
public class CarIndexSyncConsumer implements ApplicationListener<ApplicationStartedEvent> {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private KafKaProperties kafKaProperties;
    @Resource
    private KafkaConsumer<String, String> szKafkaConsumer;
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private RiskFieldCache riskFieldCache;
    @Resource
    private PublicServiceClient publicServiceClient;

    private static List<String> panelIndexList = Arrays.asList(
            "offline_member_3d_sfc_order_finish_num_top100",
            "offline_sfc_order_driver_self_cancel_7d_ratio_pre50_top100",
            "offline_sfc_order_supplier_self_cancel_7d_ratio_pre100_top100",
            "offline_sfc_order_driver_cancel_3d_ratio_pre30_top100",
            "offline_driver_3d_sfc_refund_money_top100"
            );


    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("[][][][]kafka配置文件{}", JsonUtils.json(kafKaProperties));
        if(!kafKaProperties.getIndexSync()){
            return;
        }
        Executors.newSingleThreadExecutor().execute(()->{
            ConsumerRecords<String, String> records;
            szKafkaConsumer.subscribe(Arrays.asList(kafKaProperties.getIndexSyncTopic()));
            while (healthCheckService.isHealth()) {
                log.info("指标同步消费开始拉取消息");
                try {
                    records = szKafkaConsumer.poll(1000);
                    if (records.count() > 0) {
                        log.info("指标同步拉取取消息{}条", records.count());
                        for (ConsumerRecord<String, String> record : records) {
                            //处理业务逻辑
                            start(record.value());
                        }
                    }
                } catch (Exception e) {
                    log.error("[][][][]指标同步消费报错:",e);
                }
            }
            log.info("指标同步消费结束...");
            szKafkaConsumer.close();
        });
    }

    public void start(String message){
        log.info("[CarIndexSyncConsumer] [info] [] [] CarIndexSyncConsumer kafka消息：{}", message);
        CarIndexDTO indexDTO = JSONObject.parseObject(message,CarIndexDTO.class);
        String indexName = indexDTO.getTarget_name();
        if(StringUtils.isNotBlank(indexDTO.getCategory_one())){
            indexName = indexName +"_" + indexDTO.getCategory_one();
        }
        if(StringUtils.isNotBlank(indexDTO.getCategory_two())){
            indexName = indexName +"_" + indexDTO.getCategory_two();
        }
        if(indexDTO.getData_type().equals("offline")){
            RMap<String, Object> map = redissonClient.getMap(indexName);
            map.put("num",indexDTO.getValue());
            map.put("orderNos", StringUtils.isNotBlank(indexDTO.getLinkData())?indexDTO.getLinkData():"");
            long endDate = DateUtil.addMinute(new Date(),1470).getTime();
            redissonClient.getKeys().expireAt(indexName, endDate);

            if(panelIndexList.contains(indexDTO.getTarget_name())){
                RList<String> list = redissonClient.getList(indexDTO.getTarget_name() +"_panel_"+DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD));
                PanelDataDTO dto = new PanelDataDTO();
                dto.setKey(indexName.replace(indexDTO.getTarget_name()+"_",""));

                if(indexDTO.getTarget_name().equals("offline_sfc_order_driver_self_cancel_7d_ratio_pre50_top100") ||
                        indexDTO.getTarget_name().equals("offline_sfc_order_driver_cancel_3d_ratio_pre30_top100") ||
                        indexDTO.getTarget_name().equals("offline_driver_3d_sfc_refund_money_top100")){
//                    String plateNumber = publicServiceClient.decrypt(6,dto.getKey());
                }
                dto.setValue(indexDTO.getValue());
                list.add(JSONObject.toJSONString(dto));
                redissonClient.getKeys().expireAt(indexDTO.getTarget_name() +"_panel_"+DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD), endDate);

            }
        }
        if(indexDTO.getData_type().equals("realtime")){
            RiskField field = riskFieldCache.loadField(indexDTO.getTarget_name());
            if(field == null){
                return;
            }
            RMap<String, Object> map = redissonClient.getMap(indexName);
            if(StringUtils.isNotBlank(indexDTO.getLinkData())){
                if(indexDTO.getLinkData().contains("=")){
                    //这种flinksql洗的linkdata是{sfc=1,sfc2=2}
                    String orderIdStr = indexDTO.getLinkData().replace("{","").replace("}","");
                    String appendOrder = "";
                    for(String str : orderIdStr.split(",")){
                        appendOrder = appendOrder + str.split("=")[0];
                    }
                    map.put("orderNos", appendOrder);
                } else {
                    map.put("orderNos", StringUtils.isNotBlank(indexDTO.getLinkData())?indexDTO.getLinkData():"");
                }
            }
            map.put("num",indexDTO.getValue());
            long endDate;
            if(field.getEffectiveDate() == -2){
                //说明是当天有效
                endDate = DateUtil.addDay(TimeUtil.currentDay(),1).getTime();
            } else {
                endDate = DateUtil.addMinute(new Date(),field.getEffectiveDate()).getTime();
            }
            redissonClient.getKeys().expireAt(indexName, endDate);
        }

    }
}
