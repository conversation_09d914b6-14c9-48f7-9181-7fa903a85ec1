package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.common.enums.ApiProviderEnum;
import com.ly.car.risk.common.enums.ChargeCallTypeEnum;
import com.ly.car.risk.process.api.rsp.DriverReviewRsp;
import com.ly.car.risk.process.api.rsp.DriverReviewV2Rsp;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.repo.risk.mapper.RiskChargeCallMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskChargeCall;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class CommonRiskClient {

    private static final String TOKEN = "0ceeb9a90d434353ba02141096410e49";

    @Resource
    private UrlsProperties       urlsProperties;
    @Resource
    private RiskChargeCallMapper riskChargeCallMapper;

    public List<String> queryDriver(Map<String,String> paramMap){
        Map<String,String> headMap = new HashMap<>();
        headMap.put("token",TOKEN);
        log.info("[CommonRiskClient][queryDriver][][]司机背审公共请求：{}",JsonUtils.json(paramMap));
        String result = OkHttpClientUtil.getInstance().get(urlsProperties.getDriverReview(), paramMap, headMap);
        //{"code":200,"data":{"retData":{"category":["0"]}},"log":{},"success":true,"message":"操作成功"}
        DriverReviewRsp driverReviewRsp = JSONObject.parseObject(result, DriverReviewRsp.class);
        log.info("[CommonRiskClient][queryDriver][][]司机背审公共返回：{}",JsonUtils.json(driverReviewRsp));
        if(driverReviewRsp.getCode() == 200){
            if(driverReviewRsp.getData() != null){
                return driverReviewRsp.getData().getRetData().getCategory();
            }
        }
        return null;
    }

    public Integer queryDriverByBds(Map<String,String> paramMap,StringBuilder msg){
        try {
            Map<String,String> headMap = new HashMap<>();
            headMap.put("token",TOKEN);
            log.info("[CommonRiskClient][queryDriver][][]司机背审公共请求：{}",JsonUtils.json(paramMap));
            String result = OkHttpClientUtil.getInstance().get(urlsProperties.getDriverReview(), paramMap, headMap);
            log.info("[CommonRiskClient][queryDriver][][]司机背审公共返回：{}",result);
            //{"code":200,"data":{"retData":{"category":["0"]}},"log":{},"success":true,"message":"操作成功"}
            DriverReviewRsp driverReviewRsp = JSONObject.parseObject(result, DriverReviewRsp.class);
            log.info("[CommonRiskClient][queryDriver][][]司机背审公共返回：{}",JsonUtils.json(driverReviewRsp));
            if(driverReviewRsp.getCode() == 200){
                if(driverReviewRsp.getData() != null && driverReviewRsp.getData().getRetData().getCategory().contains("0")){
                    return 0;
                } else {
                    msg.append(StringUtils.join(driverReviewRsp.getData().getRetData().getCategory(),","));
                    return 1;
                }
            }
            msg.append("系统异常，请联系平台");
            return 1;
        } catch (Exception e){
            log.error("[CommonRiskClient][queryDriver][][]司机背审公共返回报错",e);
        }
        return 1;
    }




    public Integer mobileCheck(String mobile){
        try {
            JSONObject param = new JSONObject();
            param.put("uuid", UUID.randomUUID()+String.valueOf(System.currentTimeMillis()));
            param.put("appKey","car.java.risk.process");
            param.put("appSecret","25d1b79a1686ce590200bfab58120cf9426f775bc26a8929bfc8d878799dfdf5");
            param.put("tel",mobile);
            param.put("type",1);
            log.info("[][][][]验证手机号-调用三方参数{}",JsonUtils.json(param));
            String post = OkHttpClientUtil.getInstance()
                    .post("http://mkcloud.17usoft.com/markettiantingapi/vacantNumber/valid", JsonUtils.json(param), null);
            log.info("[][][][]验证手机号-调用三方返回{}",post);
            JSONObject result = JSONObject.parseObject(post);
            String code = result.getString("code");
            if(!code.equals("0000")){
                return 0;//下次继续验证
            }
            JSONArray resultArray = result.getJSONArray("result");
            if(resultArray.isEmpty()){
                return 0;
            }
            /**
             *  {
             *             "tel": "18506151282",
             *             "uniqueId": "61680d5fee343364a52813b9daf2c0d7",
             *             "hit": false
             *         }
             * */
            JSONObject jsonObject = resultArray.getJSONObject(0);
            if(jsonObject != null && jsonObject.getBoolean("hit")){
                return 2;
            }
            return 1;//成功，正常的手机号
        } catch (Exception e) {
            log.error("[][][][]获取市场中心手机号错误",e);
        }
        return 0;
    }

    public DriverReviewV2Rsp queryDriverRiskV2(Map<String,String> paramMap){
        try {
            Map<String,String> headMap = new HashMap<>();
            headMap.put("token","431f86a41eb84ad1bb6f679c7c7d618e");
            log.info("[CommonRiskClient][queryDriver][][]司机背审公共请求V2：{}",JsonUtils.json(paramMap));
            String result = OkHttpClientUtil.getInstance().get("http://bds.17usoft.com/tcodpapi/risk/review", paramMap, headMap);
            log.info("[CommonRiskClient][queryDriver][][]司机背审公共返回V2：{}",result);
            
            recordScoreCall(JSON.toJSONString(paramMap), result, paramMap.get("productLine"));
            
            DriverReviewV2Rsp driverReviewRsp = JSONObject.parseObject(result, DriverReviewV2Rsp.class);
            if(driverReviewRsp.getCode() == 200){
                return driverReviewRsp;
            }
        } catch (Exception e){
            log.error("[CommonRiskClient][queryDriver][][]司机背审公共返回报错",e);
        }
        return null;
    }
    
    private void recordScoreCall(String req, String res, String productLine) {
        try {
            RiskChargeCall call = new RiskChargeCall();
            call.setType(ChargeCallTypeEnum.SCORE.getCode());
            call.setApiProvider(ApiProviderEnum.TCSHUKE.getCode());
            call.setCharge(BigDecimal.ZERO);
            
            String msg = "失败";
            if (StringUtils.isNotBlank(res)) {
                DriverReviewV2Rsp driverReviewRsp = JSONObject.parseObject(res, DriverReviewV2Rsp.class);
                if (null != driverReviewRsp && Objects.equals(driverReviewRsp.getCode(), 200)) {
                    call.setCharge(TianChuangRiskClient.getPriceConfig().getScorePrice());
                    msg = "成功";
                }
            }
            call.setProductLine(StringUtils.defaultString(productLine));
            call.setCallTime(new Date());
            call.setExt(JSON.toJSONString(new RiskChargeCall.Ext(req, res, msg)));
            call.setCreateTime(new Date());
            call.setUpdateTime(new Date());
            riskChargeCallMapper.insert(call);
        } catch (Exception e) {
            log.error("[][][][]保存调用记录异常", e);
        }
    }

    public static void main(String[] args) {
       String str = "{\"code\":200,\"success\":true,\"requestId\":\"9fdd92e0-abe2-4501-b4a3-01d98fa39ae3\",\"message\":\"操作成功\",\"data\":{\"retData\":{\"category\":[\"0\"]}}}";
        DriverReviewRsp driverReviewRsp = JSONObject.parseObject(str, DriverReviewRsp.class);
        log.info("[CommonRiskClient][queryDriver][][]司机背审公共返回：{}",JsonUtils.json(driverReviewRsp));
        if(driverReviewRsp.getCode() == 200){
            if(driverReviewRsp.getData() != null && driverReviewRsp.getData().getRetData().getCategory().contains("0")){
                System.out.println("0");
            } else {
                System.out.println("1");
            }
        }
//        if(CollectionUtils.isNotEmpty(returnList) && !result.contains("0")){
//            System.out.println("==========");
//        } else {
//            System.out.println("+++++++++");
//        }
    }
}
