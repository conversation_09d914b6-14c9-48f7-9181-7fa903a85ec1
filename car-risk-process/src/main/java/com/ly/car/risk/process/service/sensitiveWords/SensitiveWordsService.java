package com.ly.car.risk.process.service.sensitiveWords;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveWordsMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveWords;
import com.ly.car.risk.process.service.dto.SensitiveWordsBean;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SensitiveWordsService {



    @Resource
    SensitiveWordsMapper sensitiveWordsMapper;

    public static Map<String,Integer> wordTypeMap = new HashMap<>();
    static {
        wordTypeMap.put("暴恐",1);
        wordTypeMap.put("广告法",2);
        wordTypeMap.put("黄赌毒",3);
        wordTypeMap.put("迷信邪教",4);
        wordTypeMap.put("敏感词",5);
        wordTypeMap.put("跳出平台",6);
        wordTypeMap.put("违法违禁",7);
        wordTypeMap.put("政治相关和人名",8);
    }

    //初始化导入敏感词
    public void initWords() throws FileNotFoundException {
        List<SensitiveWords> list = new ArrayList<>();
        this.sensitiveWordsMapper.delete(new UpdateWrapper<SensitiveWords>().gt("id",0));
        EasyExcel.read(new File("D://最新版-敏感词.xlsx"), SensitiveWordsBean.class,new PageReadListener<SensitiveWordsBean>(dataList->{
            for(SensitiveWordsBean data : dataList){
                if(data.getWord() == null){
                    break;
                }
               System.out.println(JsonUtils.json(data));
                //插入数据库
                SensitiveWords sensitiveWords = new SensitiveWords();
                sensitiveWords.setWord(data.getWord());
                Integer typeNo = wordTypeMap.get(data.getWordType());
                sensitiveWords.setWordType(typeNo == null?0:typeNo);
                sensitiveWords.setCreateTime(new Date());
                sensitiveWords.setUpdateTime(new Date());
                sensitiveWords.setLevel(data.getLevel().equals("高")?1:0);
                sensitiveWordsMapper.insert(sensitiveWords);
            }
        })).sheet().doRead();
    }

    public List<String> queryAllWords(){
        List<SensitiveWords> words = this.sensitiveWordsMapper.selectList(
                new QueryWrapper<SensitiveWords>().gt("id",0).eq("deleted",0)
        );
        return words.stream().map(SensitiveWords::getWord).collect(Collectors.toList());
    }

    public List<SensitiveWords> queryAllWordsList(){
        List<SensitiveWords> words = this.sensitiveWordsMapper.selectList(
                new QueryWrapper<SensitiveWords>().gt("id",0).eq("deleted",0)
        );
        return words;
    }

}
