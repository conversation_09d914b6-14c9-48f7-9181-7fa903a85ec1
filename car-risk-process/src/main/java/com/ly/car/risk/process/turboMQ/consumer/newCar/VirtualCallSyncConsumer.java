package com.ly.car.risk.process.turboMQ.consumer.newCar;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.VirtualPhoneData;
import com.ly.car.risk.process.turboMQ.dto.newCar.VirtualCallSyncPayload;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class VirtualCallSyncConsumer implements MessageListenerConcurrently {

    private TencentCloudApiClient tencentCloudApiClient;
    private MqRiskProducer mqRiskProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {

        for(MessageExt messageExt : list){
            String body = null;
            LoggerUtils.initLogMap("VirtualCallSyncConsumer","",messageExt.getMsgId(),messageExt.getTags());
            try {
                List<String> careTags = StrUtil.split(ConfigCenterService.getDefault("VIRTUAL_CALL_CARE_TAGS",""),",",true,true);
                String tags = messageExt.getTags();
                if(!careTags.contains(tags) && !tags.equals("*")){
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;

                }
                body = new String(messageExt.getBody(),"utf-8");
                LoggerUtils.info(log,"收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                VirtualCallSyncPayload data = JsonUtils.json(body,VirtualCallSyncPayload.class);
                if(StringUtils.isBlank(data.getRecordUrl())){
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                tencentCloudApiClient = SpringContextUtil.getBean("tencentCloudApiClient");
                Long recTaskId = tencentCloudApiClient.createRecTask(0, data.getRecordUrl());
                JSONObject taskJsonObject = new JSONObject();
                taskJsonObject.put("taskId",recTaskId);
                taskJsonObject.put("times",1);
                taskJsonObject.put("orderId",data.getExternalId());
                taskJsonObject.put("driverCardNo",null);
                taskJsonObject.put("source",2);
                taskJsonObject.put("callId",data.getCallId());
                taskJsonObject.put("reportId",data.getCallId());
                if(recTaskId != null){
                    //发送获取结果mq
                    mqRiskProducer = SpringContextUtil.getBean("riskSecurityProducer");
                    mqRiskProducer.send(MqTagEnum.car_self_tencent_get_describe_task, JsonUtils.json(taskJsonObject), DateUtil.addMinute(new Date(),1).getTime());
                }
            } catch (Exception e) {
                LoggerUtils.error(log,"error",e);
            } finally {
                LoggerUtils.removeAll();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
