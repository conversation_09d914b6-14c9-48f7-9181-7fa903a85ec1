package com.ly.car.risk.process.supplier.Tsan;

import java.util.Optional;
import java.util.stream.Stream;

public enum TsanEnv {

    QA("qa", "https://openabilitytest.t3go.cn:11443", "ly", "M0Ti89c6uJBvyFQGhpH7w7jkkIqmyrAk"),
    STAGE("stage", "https://openabilitytest.t3go.cn", "ly", "6oijf8w1ms4up2cxb79g035tdkhzryla"),
    PRODUCT("product", "https://openability.t3go.cn", "ly", "tpumBoGrrUuLjBnXW9L13l43pfznOvkQ"),
    ;

    public String env;
    public String host;
    public String channel;
    public String token;


    TsanEnv(String env, String host, String channel, String token) {
        this.env = env;
        this.host = host;
        this.channel = channel;
        this.token = token;
    }

    public static TsanEnv get(String env) {
        return Optional.ofNullable(Stream.of(QA, STAGE, PRODUCT).filter(tsanEnv -> tsanEnv.env.equals(env)).findFirst().get())
                .orElse(null);
    }
}