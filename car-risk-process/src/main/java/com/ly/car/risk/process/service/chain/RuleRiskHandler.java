package com.ly.car.risk.process.service.chain;


import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.component.DriverSlidingWindowCounter;
import com.ly.car.risk.process.component.SfcUserOrderNumWindowCounter;
import com.ly.car.risk.process.component.UserSlidingWindowCounter;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.params.RedisParam;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.dto.*;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.sfcGroup.RuleRisk018Service;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.spat.dsf.client.utils.OKHttpUtils;
import com.ly.tcbase.config.AppProfile;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class RuleRiskHandler extends FilterChainHandler{

    @Resource
    private RiskTagHandler riskTagHandler;
    @Resource
    private UserSlidingWindowCounter userSlidingWindowCounter;
    @Resource
    private DriverSlidingWindowCounter driverSlidingWindowCounter;
    @Resource
    private RuleRisk018Service ruleRisk018Service;
    @Resource
    private SfcUserOrderNumWindowCounter sfcUserOrderNumWindowCounter;
    @Resource
    private SfcOrderMapper sfcOrderMapper;

    @Override
    public void next(FilterChainHandler nextHandler) {
        this.nextHandler = riskTagHandler;
    }

    @Override
    public UiResult doHandler(FilterParams params) {
        RiskResultDTO dto = new RiskResultDTO(0,"风控通过",null,null);
        log.info("[RuleRiskHandler][doHandler][{}][{}]进入命中规则判断{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
        //查询过滤规则需要的redis数据
        FilterOrderContext context = new FilterOrderContext();
        List<OrderRiskContext> userContextList = new ArrayList<>();
        List<OrderRiskContext> driverContextList = new ArrayList<>();
        //用户下单数
        List<SfcOrderNumDTO> userOrderNumList = new ArrayList<>();
        long startMs = TimeUtil.threeDayMs();
        if(StringUtils.isNotBlank(params.getMemberId()) && !params.getMemberId().equals("0")){
            if(!AppProfile.getEnvironment().equals("product")){
                try {
                    log.info("[RuleRiskHandler][doHandler][{}][{}]预发环境调用线上{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
                    RedisParam param = new RedisParam();
                    param.setKey(params.getMemberId());
                    param.setStartMs(startMs);
                    String result = OkHttpClientUtil.getInstance().post("http://tcwireless.17usoft.com/car_risk_process/redis/getRedisByUser",JsonUtils.json(param),null,500l);
                    userContextList = JSONObject.parseArray(result,OrderRiskContext.class);
                } catch (Exception e) {
                }
            } else {
                userContextList = userSlidingWindowCounter.getUserWindow(params.getMemberId(),startMs);

            }
        }
        if(StringUtils.isNotBlank(params.getUnionId())){
            userOrderNumList = sfcUserOrderNumWindowCounter.getCommonWindow(RedisKeyConstants.SFC_USER_ORDER_NUMBER+params.getUnionId(),startMs);
        } else  if(StringUtils.isNotBlank(params.getMemberId()) && !params.getMemberId().equals("0")){
            userOrderNumList = sfcUserOrderNumWindowCounter.getCommonWindow(RedisKeyConstants.SFC_USER_ORDER_NUMBER+params.getMemberId(),startMs);
        } else {
            //暂时啥都不做
        }
        if(StringUtils.isNotBlank(params.getDriverCardNo())){
            if(!AppProfile.getEnvironment().equals("product")){
                try {
                    log.info("[RuleRiskHandler][doHandler][{}][{}]预发环境调用线上{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
                    RedisParam param = new RedisParam();
                    param.setKey(params.getDriverCardNo());
                    param.setStartMs(startMs);
                    String result = OkHttpClientUtil.getInstance().post("http://tcwireless.17usoft.com/car_risk_process/redis/getRedisByUser",JsonUtils.json(param),null,500l);
                    userContextList = JSONObject.parseArray(result,OrderRiskContext.class);
                } catch (Exception e) {
                }
            } else {
                driverContextList = driverSlidingWindowCounter.getDriverWindow(params.getDriverCardNo(), startMs);
//                userOrderNumListByUnion = sfcUserOrderNumWindowCounter.getCommonWindow(RedisKeyConstants.SFC_USER_ORDER_NUMBER_UNION+params.getUnionId(),startMs);
            }
        }
        context.getUiResult().setData(dto);

        log.info("[RuleRiskHandler][doHandler][{}][{}]查询用户维度集合{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(userContextList));
        log.info("[RuleRiskHandler][doHandler][{}][{}]查询司机维度维度集合{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(driverContextList));


        context.setUnionId(params.getUnionId());
        context.setMemberId(params.getMemberId());
        context.setDriverCardNo(params.getDriverCardNo());
        //查询顺风车配置
        SfcRiskRuleConfig sfcRiskRuleConfig = getConfigJson();

        context.setUserContextList(userContextList);
        context.setDriverContextList(driverContextList);
        context.setSfcRiskRuleConfig(sfcRiskRuleConfig);
        context.setCancelOrderNumList(userOrderNumList);
        //查一下要跑哪些规则
        Map<String,Boolean> needRuleMap = getRuleOnOff(params.getSourceId());
        if(needRuleMap ==  null){
            return context.getUiResult();
        }
        context.setNeedRuleMap(needRuleMap);
        //查询取消或接单
        Date endDate = new Date();
        Date startDate = DateUtil.addMinute(endDate,-sfcRiskRuleConfig.getTime038());
        List<SfcRiskLimitData> sfcRiskLimitDataList = new ArrayList<>();
        if(StringUtils.isNotBlank(params.getUnionId()) || StringUtils.isNotBlank(params.getMemberId())){
            String regex=".*[a-zA-Z]+.*";
            Matcher m=Pattern.compile(regex).matcher(params.getMemberId());
            if(!m.matches()){
                sfcRiskLimitDataList = sfcOrderMapper.querySfcRiskLimit(startDate,endDate, params.getUnionId(), params.getMemberId());
            }
        }
        context.setOrderLimitList(sfcRiskLimitDataList);
        context.setFilterParams(params);

        if(CollectionUtils.isEmpty(userContextList) && CollectionUtils.isEmpty(driverContextList) && CollectionUtils.isEmpty(sfcRiskLimitDataList)){
            return context.getUiResult();
        }

        //这边先终止掉，后面可以判断返回值在再走下个大的链路
        return ruleRisk018Service.doHandler(context);
//        List<DistributionRiskManage> manageList = distributionRiskManageService.getListByAll(params);
//        if(CollectionUtils.isEmpty(manageList)){
//            return this.nextHandler.doHandler(params);
//        } else {
//            //走到这的就说明是有查到的风控相关信息
//            UiResult result = UiResult.fail();
//            result.setData("1");
//            return result;
//        }
    }

    public SfcRiskRuleConfig getConfigJson(){
        try {
            String configJson = ConfigCenterClient.get("sfc_common_risk_rule");
            log.info("获取顺风车风控规则:"+configJson);
            SfcRiskRuleConfig sfcRiskRuleConfig = JSONObject.parseObject(configJson,SfcRiskRuleConfig.class);
            return sfcRiskRuleConfig;
        } catch (Exception e) {
            log.error("获取顺风车风控规则错误:",e);
        }
        return null;
    }

    public Map<String,Boolean> getRuleOnOff(String sourceId){
        try {
            String configJson = ConfigCenterClient.get("rule_on-off_config");
            log.info("获取规则是否打开:"+configJson);
            Map<String,Map<String,Boolean>> configMap = JSONObject.parseObject(configJson, RuleOnOffConfig.class).getRuleOnOff();
            Iterator<Map.Entry<String, Boolean>> it = configMap.get(sourceId).entrySet().iterator();
            Map<String,Boolean> map = new HashMap<>();
            //规则关闭的移除规则
            while(it.hasNext()){
                Map.Entry<String, Boolean> entry = it.next();
                if(!entry.getValue()){
                    it.remove();
                    continue;
                }
                //这边的false代表的是未执行过需要执行的
                map.put(entry.getKey(),false);
            }
            map.remove("customer");
            return map;
        } catch (Exception e) {
            log.error("获取规则是否打开错误:",e);
        }
        return null;
    }

    public static void main(String[] args) {
        String str = "123-";
        String regex=".*[a-zA-Z]+.*";
        Matcher m=Pattern.compile(regex).matcher(str);
        System.out.println(m.matches());
    }

}
