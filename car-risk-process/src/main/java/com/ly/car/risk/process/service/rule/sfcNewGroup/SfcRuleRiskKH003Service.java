package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.process.component.DriverSlidingWindowCounter;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRiskKH003Service extends FilterSfcHandler{

    private static final String ruleNo = "044";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;
    @Resource
    private DriverSlidingWindowCounter driverSlidingWindowCounter;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRiskKH003Service][][]前置判断已通过，进入规则空号判断{}",
                context.getUserPhone());
        if(!context.getSfcRiskRuleConfig().getOnOffKH003()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        List<OrderRiskContext> orderRiskContextList = driverSlidingWindowCounter.getDriverWindow("sfc_user_id_place_order_context_" + context.getMemberId(), TimeUtil.threeDayMs());
        //过滤出近24h的数据
        orderRiskContextList = orderRiskContextList.stream()
                .filter(data-> DateUtil.string2Date(data.getCreateTime()).after(TimeUtil.oneDay())).collect(Collectors.toList());
        if(orderRiskContextList.size() >= 3){
            List<String> orderIds = new ArrayList<>();
            for(OrderRiskContext riskContext : orderRiskContextList){
                String phone7Number = riskContext.getPassengerCellphone().substring(riskContext.getPassengerCellphone().length()-7);
                boolean allSameDigit = true; // 是否所有数字相同的标志
                char firstDigit = phone7Number.charAt(0); // 第一个数字
                for (int i = 1; i < phone7Number.length(); i++) {
                    if (phone7Number.charAt(i) != firstDigit) { // 如果有数字和第一个数字不同
                        allSameDigit = false; // 标志设为false
                        break; // 不需要再继续比较了
                    }
                }
                if(allSameDigit){
                    orderIds.add(riskContext.getOrderId());
                }
            }
            if(orderIds.size() >= 3){
                distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过KH002",null,null);
                context.getUiResult().setData(dto);
                context.getUiResult().setMsg("风控不通过");
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
