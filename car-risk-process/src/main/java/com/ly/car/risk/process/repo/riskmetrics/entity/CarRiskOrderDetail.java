package com.ly.car.risk.process.repo.riskmetrics.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description of CarRiskOrderDetail
 *
 * <AUTHOR>
 * @date 2024/6/12
 * @desc
 */
@Data
public class CarRiskOrderDetail {

    /** 主键ID */
    @Id
    private Long id;

    /** 订单号 */
    private String orderSerialNo;

    /** 会员ID */
    private String memberId;

    /** unionId */
    private String unionId;

    /** orderId前3位 */
    private String productLine;

    /** 订单类型 */
    private int orderType;

    /** 订单状态（行程状态） */
    private int orderState;

    /** 客户下单时，订单总金额 */
    private BigDecimal amount;

    /** 支付状态 */
    private int payState;

    /** 订单渠道，公司渠道码 */
    private int orderChannel;

    /** 下单人手机号 */
    private String contactPhone;

    /** 创单时间 */
    private Date gmtCreate;

    /** 清款时间 */
    private Date gmtPaid;

    /** 订单取消时间 */
    private Date gmtCanceled;

    /** 用车时间（用户选择的出发时间） */
    private Date gmtUsage;

    /** 出发时间（实际出发时间） */
    private Date gmtDeparture;

    /** 抵达时间（实际抵达时间） */
    private Date gmtArrive;

    /** 行程结束时间 */
    private Date gmtTripFinished;

    /** 取消类型 */
    private int cancelType;

    /** 订单取消原因 */
    private String cancelReason;

    /** 支付方式 1:在线支付/2:支付分支付 */
    private int payCategory;

    /** 上车点城市code */
    private String departureCityCode;

    /** 下车点城市code */
    private String arrivalCityCode;

    /** 上车点详细地址 */
    private String departureAddress;

    /** 下车点详细地址 */
    private String arrivalAddress;

    /** 乘客手机号 */
    private String passengerPhone;

    /** 预估距离(米) */
    private int estimateDistance;

    /** 预估运行时长(秒) */
    private int estimateTime;

    /** 实际距离(米) */
    private int realDistance;

    /** 实际运行时长(秒) */
    private int realTime;

    /** 车牌号 */
    private String carNum;

    /** 供应商code */
    private String supplierCode;

    /** 供应商报价 */
    private BigDecimal supplierPrice;

    /** 退款金额 */
    private BigDecimal refundMoney;

    /** 数据清洗拓展字段 */
    private String washExt;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /**
     * 分销标志 0-普通单 1-分销单
     */
    private int distributionFlag;

    /**
     * 前付下单支付金额
     */
    private BigDecimal bookAmount;

    /**
     * 附加费
     */
    private BigDecimal surcharge;

    /**
     * 补款金额
     */
    private BigDecimal supplementaryAmount;

    /**
     * 权益单标记 0-非权益单 1-权益单
     */
    private int rightsOrderFlag;
    
    /**
     * 设备号
     */
    private String deviceId;
    
    /**
     * 用户预估金额
     */
    private BigDecimal bookUserAmount;
    
    /**
     * 上车点维度
     */
    private Double 	depLat;
    
    /**
     * 上车点经度
     */
    private Double depLon;
    
    /**
     * 下车点维度
     */
    private Double arrLat;
    
    /**
     * 下车点经度
     */
    private Double arrLon;
}