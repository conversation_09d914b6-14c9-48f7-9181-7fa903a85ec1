package com.ly.car.risk.process.handler.selfbin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.CarOrderApi;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.TrafficClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.MqSendService;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.order.BaseOrderInfo;
import com.ly.car.risk.process.service.dto.order.CarInfo;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.redis.OrderIdScoredSortedSetService;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.task.SfcFinishOrCancelData;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.DateUtilRisk;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description of NewOrerSelfBinHandler
 *
 * <AUTHOR>
 * @date 2024/3/14
 * @desc
 */
@Service
@Slf4j
public class NewOrderSelfBinHandler extends AbstractSelfBinHandler {

    @Resource(name = "saveScoredSortedSetService")
    private SaveScoredSortedSetService saveScoredSortedSetService;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private TrafficClient trafficClient;
    @Resource
    private OrderIdScoredSortedSetService orderIdScoredSortedSetService;
    @Resource(name = "szKafkaProducer")
    private KafkaProducer<String, String> szKafkaProducer;
    @Resource(name = "mqSendService")
    private MqSendService mqSendService;
    @Resource(name = "carOrderApi")
    private CarOrderApi carOrderApi;
    @Resource(name = "labelClient")
    private LabelClient labelClient;
    @Resource(name = "autoCallService")
    private AutoCallService autoCallService;
    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer riskSecurityProducer;

    @Override
    public String supportOrderType() {
        return "new";
    }

    @Override
    public void doHandler(String body, String tag) {
        if (tag.equals(MqTagEnum.car_risk_binlog_sfc_cancel.name())) {
            // 顺风车取消
            dealSfcCancelLog(body);
        } else if (tag.equals(MqTagEnum.car_risk_safe_warning.name())) {
            // 行程开始的时候，去处理预估路径
            dealPlaceOrder(body);
        } else if (tag.equals(MqTagEnum.car_risk_place_order_phone.name())) {
            // 顺风车用户下单时,同样也是5秒延迟消息发到这里处理
            dealCreateOrder(body);
        } else if (tag.equals(MqTagEnum.car_risk_convert_sfc_data.name())) {
            // 顺风车数据同步 取消和完单时
            dealFlinkData(body);
        } else if (tag.equals(MqTagEnum.car_risk_order_event_topic.name())) {
            dealFlinkDataByJson(JSONObject.parseObject(body));
        } else if (tag.equals(MqTagEnum.car_risk_driver_ex_notify.name())) {
            dealDriverNotify(body);
        } else if (tag.equals(MqTagEnum.car_risk_driver_mini_warn_notify.name())) {
            //直接调用顺风车发送短信接口,这流程应该是走不到的
            dealMiniSms(body);
        } else if (tag.equals(MqTagEnum.car_risk_sfc_user_onCar.name())) {
            // 这是顺风车用户上车(或者说行程开始)时触发的，判断敏感时间+女性时的一个外呼事件
            dealUserPassengerOnCar(body);
        }
    }

    private void dealDriverNotify(String body) {
        LoggerUtils.info(log,"开始处理 行程安全订单 司机通知");
        // 顺风车行车中 检测到安全问题，发送司机端通知用
        mqSendService = SpringContextUtil.getBean("mqSendService");
        mqSendService.driverWarnNotify(body, null, 0L);
    }

    /**
     * 用户确认上车事件
     */
    public void dealUserPassengerOnCar(String orderId) {

        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        if (null == orderDetail) {
            return;
        }
        // 这应该是行程中状态，才会触发的
        if (orderDetail.getOrderState() != OrderState.IN_TRIP.getCode()) {
            LoggerUtils.info(log,"订单当前状态不处于行程中，跳过处理，orderId:{}",orderId);
            return;
        }
        //获取敏感时间
        String currentDay = DateUtil.date2String(new Date(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
        if (new Date().after(DateUtil.string2Date(currentDay + " 05:00:00"))
                && new Date().before(DateUtil.string2Date(currentDay + " 20:00:00:00"))) {
            LoggerUtils.info(log,"当前订单不为敏感时间订单，跳过处理。orderId:{}", orderId);
            return;
        }

        LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(orderDetail.getOrderChannel(), String.valueOf(orderDetail.getMemberId()), orderDetail.getUnionId());
        if (detailRsp != null && detailRsp.getGender() == 2) {
            //外呼
            LoggerUtils.info(log,"女性用户，于敏感时间上车，触发外呼事件. orderId:{}",orderId);
            String callId = autoCallService.sendCall(orderId, "AO_117_1692337222554", orderDetail.getPassengerInfo().getPassengerCellPhone(), null);
            //失败 发送短信
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("callId", callId);
            jsonObject.put("orderId", orderId);
            jsonObject.put("mobile", orderDetail.getPassengerInfo().getPassengerCellPhone());
            jsonObject.put("plateId", orderDetail.getOrderChannel());
            riskSecurityProducer.send(MqTagEnum.car_risk_Self_on_car_sms_send, JsonUtils.json(jsonObject), DateUtil.addMinute(new Date(), 15).getTime());
            LoggerUtils.info(log, "发送给[riskSecurityConsumer],tag:{},msg:{}", MqTagEnum.car_risk_Self_on_car_sms_send.name(), JSON.toJSONString(jsonObject));
        }
    }


    public void dealMiniSms(String body) {
        LoggerUtils.info(log,"开始处理顺风车短信发送");
        RBucket<String> bucket = redissonClient.getBucket("program:order:warn:sms:" + body);
        if (bucket.isExists() || StringUtils.isNotBlank(bucket.get())) {
            return;
        } else {
            bucket.set("1", 10, TimeUnit.MINUTES);
        }
        //直接请求顺风车接口，发送短信
        carOrderApi = SpringContextUtil.getBean("carOrderApi");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", body);
        carOrderApi.sendSms(jsonObject);
    }


    /**
     * json数据
     */
    public void dealFlinkDataByJson(JSONObject jsonObject) {
        String orderId = jsonObject.getString("orderId");
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        if (null == orderDetail) {
            return;
        }
        BaseOrderInfo baseInfo = orderDetail.getBaseInfo();
        CarInfo carInfo = orderDetail.getCarInfo();
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();

        SfcFinishOrCancelData data = new SfcFinishOrCancelData();
        data.setOrderId(orderId);
        data.setMemberId(orderDetail.getMemberId());
        data.setFinishTime(baseInfo.getGmtTripFinished());
        data.setCreateTime(baseInfo.getGmtCreate());
        data.setCancelTime(baseInfo.getGmtCanceled());
        data.setStatus(OrderUtils.convertStatus(orderDetail.getOrderState()));
        data.setTotalAmount(new BigDecimal(orderDetail.getBaseInfo().getAmount()));
        data.setEventTime(jsonObject.getDate("eventTime"));
        data.setEventType(jsonObject.getInteger("eventType"));

        data.setEstimateKilo(orderTrip.getOldEstimateKilo());
        data.setCityId(orderTrip.getDepartureCityCode());
        data.setStartLat(orderTrip.getDepartureLat());
        data.setStartLng(orderTrip.getDepartureLng());
        data.setEndLat(orderTrip.getArrivalLat());
        data.setEndLng(orderTrip.getArrivalLng());

        if (orderDetail.getOrderState() == OrderState.CANCELED.getCode()) {
            data.setCancelType(StringUtils.isBlank(orderDetail.getCarInfo().getCarNum()) ? 0 : 1);
            data.setAcceptTime(orderDetail.getBaseInfo().getGmtAccept());
            data.setDriverCardNo(orderDetail.getCarInfo().getCarNum());

        } else {
            data.setAcceptTime(orderDetail.getBaseInfo().getGmtAccept());
            data.setDriverCardNo(orderDetail.getCarInfo().getCarNum());
            data.setIntervalTime((int) TimeUnit.MILLISECONDS
                    .toMinutes(orderDetail.getBaseInfo().getGmtTripFinished().getTime() - orderDetail.getBaseInfo().getGmtAccept().getTime()));

        }
        String passengerCellPhone = orderDetail.getPassengerInfo().getPassengerCellPhone();
        data.setPhone7(passengerCellPhone.substring(0, 7));
        data.setPassengerCellphone(passengerCellPhone);
        ProducerRecord<String, String> record = new ProducerRecord<>("car_flink_stream_order_create_topic_risk", JsonUtils.json(data));
        szKafkaProducer.send(record);
        LoggerUtils.info(log,"将订单信息发送kafka, topic:car_flink_stream_order_create_topic_risk");

    }


    /**
     * 处理顺风车用户完单和取消的时候组装数据
     */
    public void dealFlinkData(String orderId) {
        SfcFinishOrCancelData data = convertFlinkData(orderId);
        ProducerRecord<String, String> record = new ProducerRecord<>("car_flink_stream_order_finish_cancel_topic_risk", JsonUtils.json(data));
        szKafkaProducer.send(record);
        LoggerUtils.info(log,"将订单信息发送kafka, topic:car_flink_stream_order_finish_cancel_topic_risk");
    }

    public SfcFinishOrCancelData convertFlinkData(String orderId) {
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);

        SfcFinishOrCancelData data = new SfcFinishOrCancelData();
        data.setOrderId(orderId);
        data.setMemberId(orderDetail.getMemberId());
        data.setUnionId(orderDetail.getUnionId());
        data.setFinishTime(orderDetail.getBaseInfo().getGmtTripFinished());
        data.setCreateTime(orderDetail.getBaseInfo().getGmtCreate());
        data.setCancelTime(orderDetail.getBaseInfo().getGmtCanceled());
        data.setStatus(OrderUtils.convertStatus(orderDetail.getOrderState()));  // 转换一下
        data.setDistributorFlag(0);

        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        data.setEstimateKilo(orderTrip.getOldEstimateKilo());
        data.setCityId(orderTrip.getDepartureCityCode());
        data.setStartLat(orderTrip.getDepartureLat());
        data.setStartLng(orderTrip.getDepartureLng());
        data.setEndLat(orderTrip.getArrivalLat());
        data.setEndLng(orderTrip.getArrivalLng());

        if (orderDetail.getOrderState() == OrderState.CANCELED.getCode()) {
            data.setCancelType(StringUtils.isBlank(orderDetail.getCarInfo().getCarNum()) ? 0 : 1);
            data.setAcceptTime(orderDetail.getBaseInfo().getGmtAccept());
            data.setDriverCardNo(orderDetail.getCarInfo().getCarNum());

        } else {
            data.setAcceptTime(orderDetail.getBaseInfo().getGmtAccept());
            data.setDriverCardNo(orderDetail.getCarInfo().getCarNum());
            data.setIntervalTime((int) TimeUnit.MILLISECONDS
                    .toMinutes(orderDetail.getBaseInfo().getGmtTripFinished().getTime() - orderDetail.getBaseInfo().getGmtAccept().getTime()));

        }
        String passengerCellPhone = orderDetail.getPassengerInfo().getPassengerCellPhone();
        data.setPhone7(passengerCellPhone.substring(0, 7));
        data.setPassengerCellphone(passengerCellPhone);
        return data;
    }


    /**
     * 处理用户全部下单
     */
    public void dealCreateOrder(String orderId) {

        String key = "ORDER_CREATE_" + orderId;
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (bucket != null && bucket.get() != null) {
            return;
        } else {
            bucket.set(key, 10, TimeUnit.SECONDS);
        }

        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        Date orderCreateTime = orderDetail.getBaseInfo().getGmtCreate();

        OrderPassengerCellPhone orderPassengerCellPhone = new OrderPassengerCellPhone();
        orderPassengerCellPhone.setMemberId(orderDetail.getMemberId());
        orderPassengerCellPhone.setOrderId(orderId);
        orderPassengerCellPhone.setCreateTime(orderCreateTime);
        orderPassengerCellPhone.setStartCityId(orderDetail.getOrderTrip().getDepartureCityCode());
        saveScoredSortedSetService.save(RedisKeyConstants.SFC_PHONE_ORDER_NUMBER + orderDetail.getPassengerInfo().getPassengerCellPhone(),
                3 * 24 * 60 * 60L, orderPassengerCellPhone, orderCreateTime.getTime());

        //记录创单
        OrderRiskContext context = new OrderRiskContext();
        context.setOrderId(orderDetail.getOrderId());
        context.setPassengerCellphone(orderDetail.getPassengerInfo().getPassengerCellPhone());
        context.setMemberId(orderDetail.getMemberId());
        context.setUnionId(orderDetail.getUnionId());
        context.setCreateTime(DateUtil.date2String(orderCreateTime));
        saveScoredSortedSetService.save("sfc_user_id_place_order_context_" + orderDetail.getMemberId(),
                3 * 24 * 60 * 60L, context, orderCreateTime.getTime());

        String userId = StringUtils.defaultIfBlank(orderDetail.getUnionId(), orderDetail.getMemberId());

        //下单存入
        saveScoredSortedSetService.save(RedisKeyConstants.SFC_USER_ID_PLACE_ORDER + userId,
                3 * 24 * 60 * 60L, orderDetail.getOrderId(), orderCreateTime.getTime());

        List<String> orderIds = orderIdScoredSortedSetService.get(RedisKeyConstants.SFC_USER_ID_PLACE_ORDER + userId);
        //用户下单指标
        RMap<String, Object> map = redissonClient.getMap("sfc_user_id_24h_order_num_" + userId);
        map.put("num", 1);
        map.put("orderNos", StringUtils.join(orderIds, ","));
        long endDate = DateUtil.addDay(new Date(), 1).getTime();
        redissonClient.getKeys().expireAt("sfc_user_id_24h_order_num_" + userId, endDate);

    }

    public void dealPlaceOrder(String orderId) {
        String key = "ORDER_STARTING_" + orderId;
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (bucket != null && bucket.get() != null) {
            return;
        } else {
            bucket.set(key, 10, TimeUnit.SECONDS);
        }
        LoggerUtils.info(log, "开始为订单:{}，处理预估路径", orderId);
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        //行程中的订单处理
        saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
        //存储预估路径
        String estimatePath = trafficClient.queryPoint(orderId);
//        String estimatePath = trafficClient.queryPoint(orderTrip.getDepartureLng().toString() + "," + orderTrip.getDepartureLat().toString()
//                , orderTrip.getArrivalLng() + "," + orderTrip.getArrivalLat());
        LoggerUtils.info(log, "根据订单起始经纬，获取到的预估路径:{}", estimatePath);
        //先缓存起来，给个1天的过期时间,这个时间每次会重置，权重给个5分钟开始计算
        if (OrderUtils.isSFC(orderId)) {
            //这边放入行程中队列
            saveScoredSortedSetService.save(RedisKeyConstants.SFC_MOVING_ORDER,
                    3 * 24 * 60 * 60L, orderId, DateUtil.addMinute(new Date(), 20).getTime());
            redissonClient.getBucket(RedisKeyConstants.SFC_ESTIMATE_PATH + orderId).set(estimatePath, 1, TimeUnit.DAYS);
            LoggerUtils.info(log,"将订单加入顺风车SFC_MOVING_ORDER缓存中，将订单预估路径加入SFC_ESTIMATE_PATH缓存中");
        } else {
            saveScoredSortedSetService.save(RedisKeyConstants.YNC_MOVING_ORDER,
                    3 * 24 * 60 * 60L, orderId, DateUtil.addMinute(new Date(), 5).getTime());
            redissonClient.getBucket(RedisKeyConstants.YNC_ESTIMATE_PATH + orderId).set(estimatePath, 1, TimeUnit.DAYS);
            LoggerUtils.info(log,"将订单加入网约车YNC_MOVING_ORDER缓存中，将订单预估路径加入YNC_ESTIMATE_PATH缓存中");
        }
    }

    private void dealSfcCancelLog(String orderId) {
        saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
        String key = "sfc_cancel_order_" + orderId;
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (bucket != null && bucket.get() != null) {
            return;
        } else {
            bucket.set(key, 10, TimeUnit.SECONDS);
        }
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        //说明是接单后取消的
        OrderStatusCancelDTO dto = new OrderStatusCancelDTO();
        dto.setOrderId(orderId);
        dto.setMemberId(orderDetail.getMemberId());
        dto.setPassengerCellphone(orderDetail.getPassengerInfo().getPassengerCellPhone());
        dto.setCancelTime(DateUtilRisk.addSeconds(new Date(), -5));
        dto.setStartCityId(orderDetail.getOrderTrip().getDepartureCityCode());
        dto.setType(orderDetail.getBaseInfo().isHasAccepted() ? 1 : 0);
        saveScoredSortedSetService.save(RedisKeyConstants.SFC_CANCEL_ORDER_MEMBER + dto.getMemberId(),
                3 * 24 * 60 * 60L, dto, new Date().getTime());
        LoggerUtils.info(log, "顺风车取消添加redis成功:{}", orderId);
    }
}