package com.ly.car.risk.process.client;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.client.model.WordOrderRes;
import com.ly.car.risk.process.controller.request.safecenter.AppealOrderRequest;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.workOrder.dto.FormItems;
import com.ly.car.risk.process.service.workOrder.dto.OrderWorkDTO;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/13 16:05
 **/
@Slf4j
@Service
public class WorkOrderClient {
    @Resource
    private UrlsProperties urlProperties;

    public WordOrderRes saveWorkOrder(String tempId,
                                      String createBy,
                                      String creatorId,
                                      List<FormItems> formItems,
                                      String orderId) throws BizException {
        OrderWorkDTO orderWorkDTO = OrderWorkDTO.builder()
                .authTocken("car.java.risk.process")
                .tempId(tempId)
                .creatorId(creatorId)
                .createBy(StringUtils.defaultIfBlank(createBy,"风控系统"))
                .formItems(formItems)
                .poid(orderId)
                .build();
        LoggerUtils.info(log,"请求工单系统,req:{}",JSON.toJSONString(orderWorkDTO));
        String workResult = OkHttpClientUtil.getInstance()
                .post(urlProperties.getWorkOrderSaveUrl(), JsonUtils.json(orderWorkDTO), null, 1l);
        log.info("[][][][]请求工单系统,resp:{}",workResult);

        JSONObject jsonObject = JSON.parseObject(workResult);
        // 之所以这么写，是因为客服那边接口返回和文档不一样，返回的data 正常情况下
        if(jsonObject.getInteger("code") == 10000){
            WordOrderRes wordOrderRes = JSON.parseObject(workResult, WordOrderRes.class);
            return wordOrderRes;
        }else{
            WordOrderRes wordOrderRes = new WordOrderRes();
            wordOrderRes.setCode(String.valueOf(jsonObject.getInteger("code")));
            wordOrderRes.setMsg(StringUtils.defaultString(jsonObject.getString("msg")) + StringUtils.defaultString(jsonObject.getString("data")));
            throw new BizException(-1,wordOrderRes.getMsg());
        }
    }
}
