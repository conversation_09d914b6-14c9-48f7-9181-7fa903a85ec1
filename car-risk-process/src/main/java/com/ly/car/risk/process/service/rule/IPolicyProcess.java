package com.ly.car.risk.process.service.rule;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.rule.common.MileagePolicyProcess;
import com.ly.car.risk.process.service.rule.common.Policy;
import com.ly.car.risk.process.service.rule.common.SafeWarningProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

//todo 策略模式优化
@Service
@Slf4j
public class IPolicyProcess implements InitializingBean {

    @Resource
    private ApplicationContext applicationContext;

    private final Map<String, Class<? extends Policy>> handlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        handlerMap.put("1-2",MileagePolicyProcess.class);
        handlerMap.put("9-1",SafeWarningProcess.class);
    }

    public Policy getProcessHandler(String code){
        Class<? extends Policy> aClass = handlerMap.get(code);
        if(aClass == null){
            throw new RuntimeException("获取处理类为空");
        }
        return applicationContext.getBean(aClass);
    }

    public UiResult dispatchByScene(FilterParams params){
        return getProcessHandler(params.getMainScene()+"-"+ params.getChildScene()).execute(params);
    }


}
