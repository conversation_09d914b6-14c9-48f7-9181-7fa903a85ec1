package com.ly.car.risk.process.utils;

import com.ly.car.risk.process.model.exception.BizException;
import org.apache.commons.lang3.StringUtils;

/**
 * Description of CheckUtil
 *
 * <AUTHOR>
 * @date 2024/3/24
 * @desc
 */
public class CheckUtil {


    public static void checkNotBlank(String val, String errMsg) throws BizException {
        if (StringUtils.isNotBlank(val)) {
            return;
        }
        throw new BizException(-1, errMsg);
    }

}