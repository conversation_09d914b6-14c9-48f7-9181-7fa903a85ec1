package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.OrderVirtualPhone;
import com.ly.car.risk.process.repo.order.mapper.OrderVirtualPhoneMapper;
import com.ly.car.risk.process.repo.risk.mapper.VirtualPhoneRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.VirtualPhoneRecord;
import com.ly.car.risk.process.service.dto.VirtualPhoneRecordRsp;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VirtualPhoneRecordService{

    //获取通话记录
    private static final String url = "http://tourwirelessapi.17usoft.com/pnpservice/getReport";
    //获取通话记录-录音文件id
    private static final String callUrl = "http://tourwirelessapi.17usoft.com/pnpservice/report/virtualnum/call";
    //获取录音地址
    private static final String downloadUrl = "http://tourwirelessapi.17usoft.com/pnpservice/report/virtualnum/call/download";

    private static final String axnId = "2a9d2054173c16343c052adfdfe9be4e";
    private static final String axnSecret = "202e697df5bf2ae0c6bc7d48da067cbd";
    private static final String axbId = "0424a7abcfcb4d67b8b770b3f53c6bcd";
    private static final String axbSecret = "d1e84a8b84e9484391cbf2893a68484f";

    @Resource
    private VirtualPhoneRecordMapper virtualPhoneRecordMapper;
    @Resource
    private OrderVirtualPhoneMapper orderVirtualPhoneMapper;


    /**
     * 顺风车获取通话记录,获取录音文件id
     * */
    public List<VirtualPhoneRecordRsp.PhoneData> getRecordId(String phoneX, String orderId){
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("AccessKeyId",axbId);
        headerMap.put("AccessKeySecret",axbSecret);
        Map<String,String> param = new HashMap<>();
        param.put("phoneNoX",phoneX);
        param.put("externalId",orderId);
        log.info("[][][][{}]获取通话记录录音文件id请求{}",orderId,JsonUtils.json(param));
        String result = OkHttpClientUtil.getInstance().post(callUrl, JsonUtils.json(param),headerMap,5L);
        log.info("[][][][{}]获取通话记录录音文件id返回{}",orderId,result);
        VirtualPhoneRecordRsp rsp = JSONObject.parseObject(result,VirtualPhoneRecordRsp.class);
        if(rsp.getCode().equals("1000") && CollectionUtils.isNotEmpty(rsp.getData())){
            return rsp.getData();
        }
        return null;
    }

    public Map<String,String> queryDownLoadUrl(List<VirtualPhoneRecordRsp.PhoneData> phoneData){
        Map<String,String> videoIdAndUrlMap = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("AccessKeyId",axbId);
        headerMap.put("AccessKeySecret",axbSecret);
        List<String> stringList = new ArrayList<>();
        for(VirtualPhoneRecordRsp.PhoneData data : phoneData){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("reportId",data.getReportId());
            jsonObject.put("callId",data.getCallId());
            log.info("[][][][]获取通话记录录音文件下载地址请求{}",JsonUtils.json(data));
            String result = OkHttpClientUtil.getInstance().post(downloadUrl, JsonUtils.json(jsonObject),headerMap,5L);
            log.info("[][][][]获取通话记录录音文件下载地址返回{}",result);
            JSONObject object = JSONObject.parseObject(result);
            if(object.getString("Code") != null && object.getString("Code").equals("1000")){
                stringList.add(object.getString("data"));
                videoIdAndUrlMap.put(data.getCallId(),object.getString("data"));
            }
        }
        return videoIdAndUrlMap;
    }

    /**
     * 插入记录只需要一个订单号就可以了
     * */
    public void savePhoneRecord(String orderId){

        //先查下当前订单是否有虚拟号
        OrderVirtualPhone orderVirtualPhone = this.orderVirtualPhoneMapper.selectOne(new QueryWrapper<OrderVirtualPhone>()
            .eq("order_id",orderId).last("limit 1")
        );
        if(orderVirtualPhone == null){
            return;
        }
        //开始请求虚拟号记录
        JSONArray array = new JSONArray();
        array.add(orderId);
        Map<String,JSONArray> param = new HashMap<>();
        param.put("ExternalId",array);
        Map<String, String> headerMap = new HashMap<>();
        if(orderVirtualPhone.getVirtualType().equals("AXB")){
            headerMap.put("AccessKeyId",axbId);
            headerMap.put("AccessKeySecret",axbSecret);
        } else {
            headerMap.put("AccessKeyId",axnId);
            headerMap.put("AccessKeySecret",axnSecret);
        }
        String result = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(param),headerMap,5L);
        JSONObject resultJson = JSONObject.parseObject(result);
        if(resultJson.getString("Code").equals("1000")){
            JSONArray resultArray = resultJson.getJSONArray("Body");
            for(Object data : resultArray){
                JSONObject convertData = JSONObject.parseObject(JsonUtils.json(data));
                VirtualPhoneRecord record = new VirtualPhoneRecord();
                record.setOrderId(orderId);
                record.setCallingNo(convertData.getString("CallingNo"));
                record.setCalledNo(convertData.getString("CalledNo"));
                record.setReleaseTime(convertData.getString("ReleaseTime"));
                record.setCallSecond(convertData.getInteger("CallSeconds"));
                record.setCallingType(convertData.getString("CallingType"));
                record.setCreateTime(new Date());
                this.virtualPhoneRecordMapper.insert(record);
            }
        }
    }

    public static void main(String[] args) {
        String str = "{\"Code\":\"1000\",\"Message\":\"请求成功\",\"data\":[{\"callingNo\":\"15996789005\",\"calledNo\":\"18915498937\",\"releaseTime\":\"2023-09-11 19:34:22\",\"startTime\":\"2023-09-11 19:34:09\",\"callTime\":\"2023-09-11 19:34:02\",\"releaseCause\":16,\"callType\":0,\"callId\":\"ZJ_CUCC24d64fefb2a011939\",\"id\":null,\"reportId\":56876905,\"poolKey\":null,\"callMinutes\":null}]}";
        VirtualPhoneRecordRsp rsp = JSONObject.parseObject(str,VirtualPhoneRecordRsp.class);
        System.out.println(JsonUtils.json(rsp));
        if(rsp.getCode().equals("1000") && CollectionUtils.isNotEmpty(rsp.getData())){
            System.out.println(rsp.getCode());
        }
    }
}
