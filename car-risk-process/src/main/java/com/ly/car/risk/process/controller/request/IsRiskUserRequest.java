package com.ly.car.risk.process.controller.request;

import com.ly.car.risk.process.utils.RandomUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class IsRiskUserRequest {
    
    private String  traceId;
    private String  productLine;//YNC,SFC
    private String  orderId;
    private String  userPhone;
    private String  passengerCellphone;
    private String  memberId;
    private String  unionId;
    private Integer channel;
    
    // ******************** 内部使用 ****************************
    private Integer mainScene;
    private Integer childScene;
    private String  requestId;
    private String  sourceId;
    private String  startLat;
    private String  startLng;
    private String  endLat;
    private String  endLng;

    public String getRequestId() {
        return StringUtils.isBlank(requestId) ? RandomUtil.getRandomString(16) + System.currentTimeMillis() : requestId;
    }

}
