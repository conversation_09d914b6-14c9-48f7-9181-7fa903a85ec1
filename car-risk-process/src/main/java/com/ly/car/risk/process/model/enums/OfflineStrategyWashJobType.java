package com.ly.car.risk.process.model.enums;

import com.ly.car.risk.process.constants.NewRiskOrderTypeEnum;
import lombok.Getter;

@Getter
public enum OfflineStrategyWashJobType {

    ZC_011("zc-011", "异常大额", NewRiskOrderTypeEnum.AMOUNT_EX),

    R_025("025", "订单距离过近", NewRiskOrderTypeEnum.TRACK_EX),

    R_026("026", "订单时长过短", NewRiskOrderTypeEnum.QUICK_ORDER_EX),

    R_027("027", "订单金额异常", NewRiskOrderTypeEnum.AMOUNT_EX),

    R_028("028", "订单金额异常", NewRiskOrderTypeEnum.AMOUNT_EX),

    R_029("029", "位置异常", NewRiskOrderTypeEnum.TRACK_EX),

    R_032("032", "订单里程异常", NewRiskOrderTypeEnum.TRACK_EX),

    R_034("034", "订单金额异常", NewRiskOrderTypeEnum.AMOUNT_EX),


    ;


    private String type;

    private String desc;

    private NewRiskOrderTypeEnum riskType;

    OfflineStrategyWashJobType(String type, String desc, NewRiskOrderTypeEnum riskType) {
        this.type = type;
        this.desc = desc;
        this.riskType = riskType;
    }
}
