package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class Check<PERSON><PERSON><PERSON>hain implements CheckPriceHandler{

    private int size = 0;

    private List<CheckPriceHandler> checkPriceHandlerList;

    public void addChain(CheckPriceHandler checkPriceHandler){
        if(checkPriceHandlerList == null){
            checkPriceHandlerList = new ArrayList<>();
        }
        checkPriceHandlerList.add(checkPriceHandler);
    }



    @Override
    public UiResult doFilter(FilterSceneContext context) {
        return null;
    }
}
