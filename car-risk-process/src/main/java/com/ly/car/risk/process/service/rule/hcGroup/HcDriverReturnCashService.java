package com.ly.car.risk.process.service.rule.hcGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 汇川订单命中
 * */
@Service
@Slf4j
public class HcDriverReturnCashService extends FilterHcAroundHandler{

    private static final List<String> ruleNoList =  Stream.of("015", "034", "035" ,"036" ,"037").collect(Collectors.toList());

    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {

    }

    @Override
    public void doHandler(FilterHcContext context) {
        log.info("[][][][]司机返现过滤{}", JsonUtils.json(context.getOrderIds()));
        List<String> hcOrderIds = context.getOrderIds();
        //在风险订单表查询这些订单
        List<RiskOrderManage> manageList = this.riskOrderManageMapper.selectList(new QueryWrapper<RiskOrderManage>()
                .in("supplier_order_id",hcOrderIds)
        );
        log.info("[][][][]司机返现查询风险单{}", JsonUtils.json(context.getOrderIds()));
        //过滤当前列表是否包含上面的规则
        manageList.forEach(data->{
            List<String> ruleSplits = new ArrayList<>(Arrays.asList(data.getRuleNo().split(",")));
            ruleSplits.retainAll(ruleNoList);
            data.setRuleNo(StringUtils.join(ruleSplits,","));
        });
        log.info("[][][][]司机返现过滤风险规则编号{}", JsonUtils.json(manageList));
        manageList = manageList.stream().filter(data->StringUtils.isNotBlank(data.getRuleNo())).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(manageList)){
            List<String> orderIds = manageList.stream().map(RiskOrderManage::getSupplierOrderId).collect(Collectors.toList());
            RiskResultNewDTO resultDTO = (RiskResultNewDTO) context.getUiResult().getData();
            resultDTO.setCode(1);
            resultDTO.setMessage("风控不通过");
            resultDTO.setObj(orderIds);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }

    }

    public static void main(String[] args) {
        List<String> list1 = new ArrayList<>();

        list1.add("01");

        List<String> list2 = new ArrayList<>();
        list2.add("02");
        list2.add("03");

        // list1 只保留在 lists2 中的元素
        list1.retainAll(list2);

        System.out.println(list1);
    }
}
