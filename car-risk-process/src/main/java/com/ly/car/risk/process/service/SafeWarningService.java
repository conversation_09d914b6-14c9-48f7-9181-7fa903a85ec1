package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcOrderExt;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderExtMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.dto.CommonRuleConfig;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.SensitiveWordsSimple;
import com.ly.car.risk.process.service.rule.common.SafeWarningResult;
import com.ly.car.risk.process.service.sensitiveWords.SensitiveWordsService;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.SensitiveWordUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 安全预警 高中低
 * */
@Service
@Slf4j
public class SafeWarningService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private LabelClient labelClient;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private SfcOrderExtMapper sfcOrderExtMapper;
    @Resource
    private EsQueryClient esQueryClient;

    public UiResult getSafeByOrderId(String orderId,Integer childScene,String text){
        //判断当前订单状态
        List<SafeWarningResult> returnResult = new ArrayList<>();
//        if(StringUtils.isNotBlank(text)){
//            Map<String, SensitiveWordsSimple> wordsNumMap = SensitiveWordUtil.matchWords(text);
//            if(!wordsNumMap.isEmpty()){
//                //到这边说明是命中的,获取所有关键字
//                SafeWarningResult safeWarningResult = new SafeWarningResult();
//
//                safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
//                safeWarningResult.setText("IM场景");
//                safeWarningResult.setSort(2);
//                for(Map.Entry<String,SensitiveWordsSimple> entry : wordsNumMap.entrySet()){
//                    if(entry.getValue().getWordType() == 5){
//                        safeWarningResult.setRuleNo("aq006");
//                    } else if(entry.getValue().getWordType() == 7 ){
//                        safeWarningResult.setRuleNo("aq007");
//                    }
//                }
//                returnResult.add(safeWarningResult);
//            }
//        }
        //长时间未上车
        SfcOrder sfcOrder = null;
        OrderInfo orderInfo = null;
        if(orderId.startsWith("SFC")){
            sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
        } else {
            orderInfo = orderInfoMapper.findByOrderId(orderId);
        }
        //aq009当前里程过半，查询位置
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
        if(childScene == 1) {
            String date = DateUtil.date2String(new Date(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
            RMap<String, String> rMap = redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderId);
            log.info("[][][][]命中安全预警获取缓存:{}", JsonUtils.json(rMap));
            if (rMap != null && !rMap.isEmpty()) {
                for (Map.Entry<String, String> entry : rMap.entrySet()) {
                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo(entry.getKey());
                    safeWarningResult.setHitTime(entry.getValue().split(",")[0]);
                    safeWarningResult.setText(entry.getValue().split(",")[1]);
                    Date sixDate = DateUtil.string2Date(date + " 06:00:00");
                    Date twentyDate = DateUtil.string2Date(date + " 20:00:00");
                    if (entry.getKey().equals("aq002")) {
                        if (new Date().after(sixDate) && new Date().before(twentyDate)) {
                            safeWarningResult.setSort(1);
                        } else {
                            safeWarningResult.setSort(2);
                        }
                    } else if (entry.getKey().equals("aq001")) {
                        if (new Date().after(sixDate) && new Date().before(twentyDate)) {
                            safeWarningResult.setSort(2);
                        } else {
                            safeWarningResult.setSort(1);
                        }
                    }else if (entry.getKey().equals("aq003")) {
                        safeWarningResult.setSort(3);
                    }else if (entry.getKey().equals("aq004")) {
                        safeWarningResult.setSort(4);
                    } else {
                        safeWarningResult.setSort(2);
                    }
                    returnResult.add(safeWarningResult);
                }
            }
            //aq010 暂时没有预估结束时间

            //aq011长时间未结束
            String memberId = "";
            String unionId = "";
            //司机接单
            Date now = new Date();
            Date sixDate = DateUtil.string2Date(date+" 06:00:00");
            Date twentyDate = DateUtil.string2Date(date + " 20:00:00");
            if(orderInfo != null){
                memberId = orderInfo.getMemberId();
                unionId = orderInfo.getUniId();
                if(DateUtil.addMinute(DateUtil.addMinute(orderInfo.getUseTime(),orderAddress.getEstimateMinute()),30).before(new Date())
                    && orderInfo.getStatus() == 200
                ){
                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo("aq011");
                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                    safeWarningResult.setText("长时间未结束");
                    safeWarningResult.setSort(3);
                    returnResult.add(safeWarningResult);
                }
                if(DateUtil.addMinute(orderInfo.getUseTime(),10).before(new Date())
                        && orderInfo.getStatus()>=20 && orderInfo.getStatus()<200){
                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo("aq008");
                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                    safeWarningResult.setText("长时间未上车");
                    safeWarningResult.setSort(1);
                    returnResult.add(safeWarningResult);
                }
                //状态是否是待出行
                if(orderInfo.getStatus() == 20){
                    if(orderInfo.getUseTime().after(sixDate) && orderInfo.getUseTime().before(twentyDate)){
                        //返回aq014
                        SafeWarningResult safeWarningResult = new SafeWarningResult();
                        safeWarningResult.setRuleNo("aq014");
                        safeWarningResult.setHitTime(DateUtil.date2String(now));
                        safeWarningResult.setText("");
                        safeWarningResult.setSort(1);
                        returnResult.add(safeWarningResult);
                    } else {
                        SafeWarningResult safeWarningResult = new SafeWarningResult();
                        safeWarningResult.setRuleNo("aq013");
                        safeWarningResult.setHitTime(DateUtil.date2String(now));
                        safeWarningResult.setText("");
                        safeWarningResult.setSort(3);
                        returnResult.add(safeWarningResult);
                    }
                }
            } else {
                memberId = sfcOrder.getMemberId()==0?null:String.valueOf(sfcOrder.getMemberId());
                unionId = sfcOrder.getUnionId();
//                if(DateUtil.addMinute(sfcOrder.getUseTime(),30).before(new Date()) && sfcOrder.getStatus() < 300){
//                    SafeWarningResult safeWarningResult = new SafeWarningResult();
//                    safeWarningResult.setRuleNo("aq011");
//                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
//                    safeWarningResult.setText("长时间未结束");
//                    safeWarningResult.setSort(3);
//                    returnResult.add(safeWarningResult);
//                }
                //状态是否是待出行
                SfcOrderExt ext = sfcOrderExtMapper.queryByOrderId(orderId);
                if(sfcOrder.getStatus() == 20){
                    if(sfcOrder.getUseTime().after(sixDate) && sfcOrder.getUseTime().before(twentyDate)){
                        //返回aq014
                        SafeWarningResult safeWarningResult = new SafeWarningResult();
                        safeWarningResult.setRuleNo("aq014");
                        safeWarningResult.setHitTime(DateUtil.date2String(now));
                        safeWarningResult.setText("");
                        safeWarningResult.setSort(1);
                        returnResult.add(safeWarningResult);
                    } else {
                        SafeWarningResult safeWarningResult = new SafeWarningResult();
                        safeWarningResult.setRuleNo("aq013");
                        safeWarningResult.setHitTime(DateUtil.date2String(now));
                        safeWarningResult.setText("");
                        safeWarningResult.setSort(3);
                        returnResult.add(safeWarningResult);
                    }
                }
                if(DateUtil.addMinute(sfcOrder.getUseTime(),10).before(new Date())
                        && sfcOrder.getStatus()>=20 && sfcOrder.getStatus()<100){
                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo("aq008");
                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                    safeWarningResult.setText("长时间未上车");
                    safeWarningResult.setSort(1);
                    returnResult.add(safeWarningResult);
                }
                if(DateUtil.addMinute(sfcOrder.getUseTime(),10).before(new Date())
                        && sfcOrder.getStatus() == 100 && sfcOrder.getPassengerOnCarTime().before(DateUtil.string2Date("2020-01-01 00:00:00"))){
                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo("aq015");
                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                    safeWarningResult.setText("长时间未上车进入订单详情");
                    safeWarningResult.setSort(1);
                    returnResult.add(safeWarningResult);
                }

                if(sfcOrder.getStatus() == 200){
                    LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(sfcOrder.getPlatId(),memberId, unionId);

                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo("aq016");
                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                    safeWarningResult.setText("确认上车");
                    safeWarningResult.setSort(4);
                    returnResult.add(safeWarningResult);
                    if(StringUtils.isNotBlank(ext.getLeaveMessage()) && (ext.getLeaveMessage().contains("孕妇") ||
                            ext.getLeaveMessage().contains("老人"))){
                        SafeWarningResult resultOld = new SafeWarningResult();
                        resultOld.setRuleNo("aq018");
                        resultOld.setHitTime(DateUtil.date2String(new Date()));
                        resultOld.setText("点击确认上车，孕妇、老年人乘车");
                        resultOld.setSort(2);
                        returnResult.add(resultOld);
                    }
                    if(detailRsp != null && (detailRsp.getGender() == 2 || detailRsp.getStudent() == 1)){
                        SafeWarningResult resultStu = new SafeWarningResult();
                        resultStu.setRuleNo("aq017");
                        resultStu.setHitTime(DateUtil.date2String(new Date()));
                        resultStu.setText("点击确认上车，学生、女性乘车");
                        resultStu.setSort(1);
                        returnResult.add(resultStu);
                    }
                    if(sfcOrder.getCarpoolStatus() != 0){
                        if(StringUtils.isNotBlank(ext.getLeaveMessage()) && ext.getLeaveMessage().contains("宠物")){
                            SafeWarningResult resultAloneAnimal = new SafeWarningResult();
                            resultAloneAnimal.setRuleNo("aq019");
                            resultAloneAnimal.setHitTime(DateUtil.date2String(new Date()));
                            resultAloneAnimal.setText("点击确认上车，有宠物乘车且为拼车");
                            resultAloneAnimal.setSort(4);
                            returnResult.add(resultAloneAnimal);
                        }
                    }
                    if(sfcOrder.getCarpoolStatus() == 0){
                        if(StringUtils.isNotBlank(ext.getLeaveMessage()) && ext.getLeaveMessage().contains("宠物")){
                            SafeWarningResult resultShareAnimal = new SafeWarningResult();
                            resultShareAnimal.setRuleNo("aq020");
                            resultShareAnimal.setHitTime(DateUtil.date2String(new Date()));
                            resultShareAnimal.setText("点击确认上车，有宠物乘车且为独享");
                            resultShareAnimal.setSort(4);
                            returnResult.add(resultShareAnimal);
                        }
                    }
                    if(orderAddress.getEstimateKilo().compareTo(new BigDecimal("100")) > 0){
                        SafeWarningResult resultFar = new SafeWarningResult();
                        resultFar.setRuleNo("aq012");
                        resultFar.setHitTime(DateUtil.date2String(new Date()));
                        resultFar.setText("确认上车，路线较远");
                        resultFar.setSort(3);
                        returnResult.add(resultFar);
                    }
                    double originalDistance = CoordUtil.getDistance(orderAddress.getStartLng(), orderAddress.getStartLat(), orderAddress.getEndLng(), orderAddress.getEndLat());
                    String locationPoint = esQueryClient.queryCurrentLocation(orderId);
                    if(locationPoint != null){
                        String lng = locationPoint.split(",")[0];
                        String lat = locationPoint.split(",")[1];
                        double nowDistance = CoordUtil.getDistance(orderAddress.getStartLng(), orderAddress.getStartLat(), new BigDecimal(lng), new BigDecimal(lat));
                        if(new BigDecimal(nowDistance).divide(new BigDecimal(originalDistance),4,BigDecimal.ROUND_DOWN).compareTo(new BigDecimal("0.5")) > 0){
                            SafeWarningResult resultHalf = new SafeWarningResult();
                            resultHalf.setRuleNo("aq009");
                            resultHalf.setHitTime(DateUtil.date2String(new Date()));
                            resultHalf.setText("行程过半");
                            resultHalf.setSort(3);
                            returnResult.add(resultHalf);
                        }
                    }
                }
            }
        }
        if(childScene == 4){
            //确认到达目的地
            String locationPoint = esQueryClient.queryCurrentLocation(orderId);
            if(locationPoint != null){
                String lng = locationPoint.split(",")[0];
                String lat = locationPoint.split(",")[1];
                double distance = CoordUtil.getDistance(new BigDecimal(lng), new BigDecimal(lat), orderAddress.getEndLng(), orderAddress.getEndLat());
                if(new BigDecimal(distance).compareTo(new BigDecimal("3000")) > 0){
                    SafeWarningResult safeWarningResult = new SafeWarningResult();
                    safeWarningResult.setRuleNo("aq010");
                    safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                    safeWarningResult.setText("确认到达目的地");
                    safeWarningResult.setSort(3);
                    returnResult.add(safeWarningResult);
                }
            }
        }
        if(returnResult.size() > 0){
            Map<String,Boolean> commonRuleConfig = queryConfig();
            List<SafeWarningResult> finalResult = new ArrayList<>();
            for(SafeWarningResult result : returnResult){
                if(commonRuleConfig.get(result.getRuleNo()) != null
                        && commonRuleConfig.get(result.getRuleNo())){
                    finalResult.add(result);
                }
            }
            if(finalResult.size() > 0){
                RiskResultDTO dto = new RiskResultDTO(1,"风控不通过",null,null);
                dto.setObj(finalResult);
                return UiResult.ok(dto);
            }
            return UiResult.ok();
        } else {
            return UiResult.ok();
        }

    }

    public static void main(String[] args) {
        String str = "{\"aq003\":\"2023-06-09 17:10:01,车辆长时间停留\",\"aq001\":\"2023-06-09 17:10:01,车辆行驶路线偏移\"}";
        Map<String, String> map = JSONObject.parseObject(str, new TypeReference<Map<String, String>>() {});
        for(Map.Entry<String,String> entry : map.entrySet()){
            SafeWarningResult safeWarningResult = new SafeWarningResult();
            safeWarningResult.setRuleNo(entry.getKey());
            safeWarningResult.setHitTime(entry.getValue().split(",")[0]);
            safeWarningResult.setText(entry.getValue().split(",")[1]);
            String date = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            Date sixDate = DateUtil.string2Date(date+" 06:00:00");
            Date twentyDate = DateUtil.string2Date(date + " 20:00:00");
            if(entry.getKey().equals("aq002")){
                if(new Date().after(sixDate) && new Date().before(twentyDate)){
                    safeWarningResult.setSort(1);
                } else {
                    safeWarningResult.setSort(2);
                }
            }
            if(entry.getKey().equals("aq001")){
                if(new Date().after(sixDate) && new Date().before(twentyDate)){
                    safeWarningResult.setSort(2);
                } else {
                    safeWarningResult.setSort(1);
                }
            }
        }
    }

    private Map<String,Boolean> queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("warn_rule_config");
            Map<String,Boolean> config = JSONObject.parseObject(configJson,Map.class);
            log.info("[][][][]获取预警规则:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取预警规则配置错误:",e);
        }
        return null;
    }

}
