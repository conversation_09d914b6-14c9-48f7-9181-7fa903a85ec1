package com.ly.car.risk.process.bean.properties;

import com.alibaba.fastjson2.JSONObject;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class BaseUrlsProperties {
    
    protected String getVersionFromConfig(String key, String defaultValue) {
        String value = null;
        try {
            String config = ConfigCenterClient.get("dsf_service_version");
            if (StringUtils.isNotBlank(config)) {
                JSONObject jsonObject = JSONObject.parseObject(config);
                value = jsonObject.getString(key);
            }
        } catch (Exception e) {
            LoggerUtils.warn(log, "[获取版本号失败]  key: {}", key, e);
        }
        return StringUtils.isBlank(value) ? defaultValue : value;
    }
    
}
