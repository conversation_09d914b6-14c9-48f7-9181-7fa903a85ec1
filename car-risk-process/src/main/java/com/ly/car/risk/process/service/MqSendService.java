package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.constants.DriverWarnNotifyEnum;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.dto.DriverWarningDTO;
import com.ly.car.risk.process.service.rule.mtGroup.MqSendConvertService;
import com.ly.car.risk.process.turboMQ.DelayTimeLevel;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MqSendService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private MqSendConvertService mqSendConvertService;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource(name = "binlogProducer")
    private MqRiskProducer mqRiskProducer;

    @Resource
    private OrderClient orderClient;


    public void driverWarnNotify(String body,String productLine,Long time){
        LoggerUtils.info(log,"发送信息:{}",body);
        DriverWarningDTO dto = JSONObject.parseObject(body,DriverWarningDTO.class);
        String key = "sfc:driver:warn:notify:"+dto.getOrderId()+":"+dto.getRuleNo();
        RBucket<String> bucket = redissonClient.getBucket(key);
        if(bucket != null && StringUtils.isNotBlank(bucket.get())){
            LoggerUtils.info(log, "获取当前发送语音播报订单：{},规则编号{},已重复发送", dto.getOrderId(), dto.getRuleNo());
            return;
        }
        String ruleNo = dto.getRuleNo();
        if(productLine == null){
            String supplierCode;
            SfcOrder sfcOrder = sfcOrderMapper.queryBySupplierOrderId(dto.getOrderId());
            if(null != sfcOrder){
                supplierCode = sfcOrder.getSupplierCode();
            }else {
                // 根据供应商编号，查询下是否是新订单
                QueryDriverInfoResponse supplierOrderResponse = orderClient.queryDriverInfo(dto.getOrderId());
                if (null == supplierOrderResponse) {
                    return;
                }
                supplierCode = supplierOrderResponse.getSupplierCode();
            }

            if(supplierCode.startsWith("MadaSaas")){
                productLine = "MT";
                if(!ruleNo.contains("mt")){
                    ruleNo = ruleNo + "_mt";
                }
            } else {
                productLine = "HC";
                ruleNo = ruleNo + "_hc";
            }
        } else {
            if(productLine.equals("HC")){
                ruleNo = ruleNo + "_hc";
            }
        }
        DriverWarnNotifyEnum notifyEnum = DriverWarnNotifyEnum.getByRuleNo(ruleNo);
        if(notifyEnum == null){
            return;
        }
        LoggerUtils.info(log,"获取当前发送语音播报订单：{},规则编号{},发送内容{}",dto.getOrderId(),ruleNo,notifyEnum.getVoice());
        DriverWarningDTO sendDto = new DriverWarningDTO(dto.getOrderId(),notifyEnum.getTitle(),notifyEnum.getVoice(),dto.getRuleNo());
        mqSendConvertService.getByProductLine(productLine).send(MqTagEnum.getTagWarnNotify(productLine),JsonUtils.json(sendDto),time);
        redissonClient.getBucket(key).set("1",3, TimeUnit.DAYS);
    }

    public void dealMiniProgram(String body,String productLine,Long time){
        String key = "sfc:laluola:warn:sms:"+body;
        RBucket<String> bucket = redissonClient.getBucket(key);
        if(bucket != null && StringUtils.isNotBlank(bucket.get())){
            log.info("卡罗拉司机管控短信：{}已重复发送",body);
            return;
        }
        log.info("[][][][]小程序端发送30分钟后消费mq{}",body);
        mqRiskProducer.send(MqTagEnum.car_risk_driver_mini_warn_notify,body, DelayTimeLevel.MINUTE_30);//30分钟后发送
    }

    public static void main(String[] args) {
    }
}
