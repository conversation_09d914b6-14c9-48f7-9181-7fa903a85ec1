package com.ly.car.risk.process.controller.request.safecenter;

import com.ly.car.risk.process.controller.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description 人车不符工单反馈
 * <AUTHOR>
 * @Date 2024/3/13 15:21
 **/
@Data
public class FeedBackRequest extends BaseRequest {

    /** 订单号 */
    private String orderId;

    /**
     * 用户memberId
     */
    private String memberId;

    /**
     * unionId（有就传）
     */
    private String unionId;
    /**
     * 描述问题
     */
    private String questionDesc;

    /**
     * 问题描述图片
     */
    private List<String> descImage;

    /**
     * 用户实际乘车车牌
     */
    private String currentCarNo;
}
