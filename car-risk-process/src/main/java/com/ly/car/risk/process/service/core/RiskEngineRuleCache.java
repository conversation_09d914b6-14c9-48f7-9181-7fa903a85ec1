package com.ly.car.risk.process.service.core;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskEngineRuleMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskFieldMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskEngineRule;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskField;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RiskEngineRuleCache {
    private static final String KEY = "risk_rule_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskEngineRuleMapper riskEngineRuleMapper;

    @Scheduled(fixedRate = 1000L * 60L,initialDelay = 0)
    public void init(){
        List<RiskEngineRule> ruleList = this.riskEngineRuleMapper.selectList(new QueryWrapper<RiskEngineRule>()
                .eq("is_deleted", 0)
        );
        for(RiskEngineRule rule : ruleList){
            redissonClient.getBucket(KEY+rule.getId()).set(JSONObject.toJSONString(rule),1,TimeUnit.DAYS);
        }
    }

    public RiskEngineRule loadRule(String key){
        RBucket<String> riskEngineRuleRBucket = redissonClient.getBucket(KEY + key);
        if(riskEngineRuleRBucket == null){
            return null;
        }
        return JSONObject.parseObject(riskEngineRuleRBucket.get(),RiskEngineRule.class);
    }
}
