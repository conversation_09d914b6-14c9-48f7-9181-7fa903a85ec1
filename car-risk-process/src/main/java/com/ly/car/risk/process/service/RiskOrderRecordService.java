package com.ly.car.risk.process.service;

import com.ly.car.risk.entity.RiskOrderRecord;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class RiskOrderRecordService {

    @Resource
    private RiskOrderRecordMapper riskOrderRecordMapper;

    public void insertRecord(String orderId,String ruleNo,Integer isRisk,String operateUser){
        RiskOrderRecord record = new RiskOrderRecord();
        record.setCreateTime(new Date());
        record.setOperateTime(new Date());
        record.setOrderId(orderId);
        record.setRuleNo(ruleNo);
        record.setIsRisk(isRisk);
        record.setOperateUser(operateUser);
        riskOrderRecordMapper.insert(record);
    }
}
