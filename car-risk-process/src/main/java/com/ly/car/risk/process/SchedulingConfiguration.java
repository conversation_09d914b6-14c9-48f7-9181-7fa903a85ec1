package com.ly.car.risk.process;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.context.annotation.AdviceMode;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@EnableScheduling
@EnableAsync(mode = AdviceMode.ASPECTJ)
public class SchedulingConfiguration implements SchedulingConfigurer, AsyncConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(getTaskScheduler());
    }

    @Bean(name = "taskScheduler", initMethod = "initialize", destroyMethod = "shutdown")
    public ThreadPoolTaskScheduler getTaskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        BasicThreadFactory threadFactory = new BasicThreadFactory.Builder().namingPattern("process-%d").build();
        taskScheduler.setThreadFactory(threadFactory);
        taskScheduler.setPoolSize(8);
        return taskScheduler;
    }

    @Override
    public Executor getAsyncExecutor() {
        return getExecutorService();
    }

    @Bean(value = "executorService",destroyMethod = "shutdown")
    public ExecutorService getExecutorService(){
        return Executors.newFixedThreadPool(10);
    }

    @Bean(value = "riskExecutorService",destroyMethod = "shutdown")
    public ExecutorService getRiskExecutorService(){
        return Executors.newFixedThreadPool(64);
    }

}
