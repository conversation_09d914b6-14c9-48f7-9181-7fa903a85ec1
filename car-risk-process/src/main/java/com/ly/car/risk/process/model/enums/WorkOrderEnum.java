package com.ly.car.risk.process.model.enums;

import lombok.Getter;

@Getter
public enum WorkOrderEnum {
    SAFETY_POLICE(2,"110告警工单"),
    FEE_APPEAL(4,"费用申诉工单"),
    ITEM_MISSING(3,"物品遗失工单"),
    YC_CAR_NUMBER_NOT_MATCH(1,"人车不符工单"),
    ;


    private Integer type;

    private String sceneName;

    WorkOrderEnum(Integer type, String sceneName) {
        this.type = type;
        this.sceneName = sceneName;
    }


    public static WorkOrderEnum findByType(int type){
        for(WorkOrderEnum orderEnum : values()){
            if(orderEnum.getType() == type){
                return orderEnum;
            }
        }
        return YC_CAR_NUMBER_NOT_MATCH;
    }
}
