package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ly.car.risk.common.enums.ApiProviderEnum;
import com.ly.car.risk.common.enums.ChargeCallTypeEnum;
import com.ly.car.risk.process.api.dto.CallChargeConfig;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.api.rsp.TianChuangRsp;
import com.ly.car.risk.process.repo.risk.mapper.RiskChargeCallMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskChargeCall;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.risk.process.utils.TianChuangUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 天创API收口
 * */
@Component
@Slf4j
public class TianChuangRiskClient {
    
    @Resource
    private RiskChargeCallMapper riskChargeCallMapper;

    private final String APP_ID="c1fbcd100aeb45749e0d032ac2b1bb11";
    private final String TOKEN_ID = "594e9e96e0c74c619fb98e0b0211834a";
    private final String ID_CARD_URL = "http://api.ypcredit.com/identity/verifyIdcardC";
    private final String BANK_CARD_URL = "http://api.ypcredit.com/bankcard/bankcardInfo4";
    private final String MOBILE_INFO_URL = "http://api.ypcredit.com/mobile/cmcc/verifyMobileInfo3C";
    private final String SCORE_INFO_URL = "http://api.tcredit.com/integration/getScoreInfoProV3_A";
    private final String CAR_INFO_URL = "https://api.tcredit.com/driver/drivingpermitStandard";
    private final String DRIVER_LICENSE_URL = "http://api.ypcredit.com/driver/driverslicenseV2";
    private final String CAR_OWNER_URL = "https://api.tcredit.com/driver/xszOwnersVerifyName";
    private final String BACK_CARD_INFO_3 = "http://api.ypcredit.com/bankcard/bankcardInfo3";

    /**
     *  身份信息验证
     *  返回0认证成功
     * */
    public Integer verifyIdCard(TianChuangCommonParam param,StringBuilder msg){
        try {
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("name", param.getName());
            paramMap.put("idcard", param.getIdCard());
            paramMap.put("authId", param.getAuthId());
            paramMap.put("isCpt", param.getIsCpt());
            paramMap.put("tmcName", param.getTmcName());

            String tokenKey = TianChuangUtil.getTokenKey(ID_CARD_URL,TOKEN_ID,paramMap);
            paramMap.put("tokenKey", tokenKey);
            paramMap.put("appId", APP_ID);
            log.info("[TianChuangRiskClient][verifyIdCard][][]身份信息验证请求："+JsonUtils.json(paramMap));
            String result = OkHttpClientUtil.getInstance().post(ID_CARD_URL, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
            log.info("[TianChuangRiskClient][verifyIdCard][][]身份信息验证返回："+result);
            
            recordCertCall(JSON.toJSONString(paramMap), result, param.getProductLine());
            
            TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
            if(rsp != null && rsp.getStatus() == 0){
                JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
                if(jsonObject != null && jsonObject.getInteger("result") == 1){
                    msg.append(jsonObject.getString("resultMsg"));
                    return 0;
                } else {
                    if(jsonObject != null && jsonObject.getInteger("result") != 1){
                        msg.append(jsonObject.getString("resultMsg"));
                    }
                    return 1;
                }
            } else {
                msg.append("认证失败");
            }
        } catch (Exception e){
            log.error("[][][][]验证报错",e);
            msg.append("认证失败");
        }
        log.info("[][][][]身份验证最后");
        return 1;
    }
    
    private void recordCertCall(String req, String res, String productLine) {
        try {
            RiskChargeCall call = new RiskChargeCall();
            call.setType(ChargeCallTypeEnum.CERT.getCode());
            call.setApiProvider(ApiProviderEnum.TIANCHUANG.getCode());
            call.setCharge(BigDecimal.ZERO);
            
            String msg = "失败";
            if (StringUtils.isNotBlank(res)) {
                TianChuangRsp rsp = JSONObject.parseObject(res, TianChuangRsp.class);
                if (null != rsp && Objects.equals(rsp.getStatus(), 0) && null != rsp.getData()) {
                    JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
                    if (jsonObject != null) {
                        msg = jsonObject.getString("resultMsg");
                        Integer result = jsonObject.getInteger("result");
                        if (Objects.equals(result, 1) || Objects.equals(result, 2)) {
                            call.setCharge(getPriceConfig().getCertPrice());
                        }
                    }
                }
            }
            call.setProductLine(StringUtils.defaultString(productLine));
            call.setCallTime(new Date());
            call.setExt(JSON.toJSONString(new RiskChargeCall.Ext(req, res, msg)));
            call.setCreateTime(new Date());
            call.setUpdateTime(new Date());
            riskChargeCallMapper.insert(call);
        } catch (Exception e) {
            log.error("[][][][]保存调用记录异常", e);
        }
    }

    /**
     * 银行卡验证
     * */
    public Integer verifyBankCard(TianChuangCommonParam param){
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("authId", param.getAuthId());
        paramMap.put("isCpt", param.getIsCpt());
        paramMap.put("tmcName", param.getTmcName());

        paramMap.put("name", param.getName());
        paramMap.put("idcard", param.getIdCard());
        paramMap.put("bankcard", param.getBankCard());
        paramMap.put("mobile", param.getMobile());

        String tokenKey = TianChuangUtil.getTokenKey(BANK_CARD_URL,TOKEN_ID,paramMap);
        paramMap.put("tokenKey", tokenKey);
        paramMap.put("appId", APP_ID);
        log.info("[TianChuangRiskClient][verifyBankCard][][]银行卡信息验证请求："+JsonUtils.json(paramMap));
        String result = OkHttpClientUtil.getInstance().post(BANK_CARD_URL, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
        log.info("[TianChuangRiskClient][verifyBankCard][][]银行卡信息验证返回："+result);
        TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
        if(rsp != null && rsp.getStatus() == 0){
            JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
            if(jsonObject != null && jsonObject.get("result").equals("000")){
                return 0;
            } else {
                return 1;
            }
        }
        return 1;
    }

    /**
     * 运营商信息验证
     * */
    public Integer verifyMobileInfo(TianChuangCommonParam param){
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("authId", param.getAuthId());
        paramMap.put("isCpt", param.getIsCpt());
        paramMap.put("tmcName", param.getTmcName());

        paramMap.put("name", param.getName());
        paramMap.put("idcard", param.getIdCard());
        paramMap.put("mobile", param.getMobile());

        String tokenKey = TianChuangUtil.getTokenKey(MOBILE_INFO_URL,TOKEN_ID,paramMap);
        paramMap.put("tokenKey", tokenKey);
        paramMap.put("appId", APP_ID);
        log.info("[TianChuangRiskClient][verifyMobileInfo][][]运营商信息验证请求："+JsonUtils.json(paramMap));
        String result = OkHttpClientUtil.getInstance().post(MOBILE_INFO_URL, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
        log.info("[TianChuangRiskClient][verifyMobileInfo][][]运营商信息验证返回："+result);
        TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
        if(rsp != null && rsp.getStatus() == 0){
            JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
            if(jsonObject != null && jsonObject.get("result").equals(1)){
                return 0;
            } else {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 风险评分增强
     * */
    public Integer verifyScoreInfo(TianChuangCommonParam param,StringBuilder msg){
        try {
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("name", param.getName());
            paramMap.put("idcard", param.getIdCard());

            String tokenKey = TianChuangUtil.getTokenKey(SCORE_INFO_URL,TOKEN_ID,paramMap);
            paramMap.put("tokenKey", tokenKey);
            paramMap.put("appId", APP_ID);
            log.info("[TianChuangRiskClient][verifyScoreInfo][][]用户风险信息验证请求："+JsonUtils.json(paramMap));
            String result = OkHttpClientUtil.getInstance().post(SCORE_INFO_URL, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
            log.info("[TianChuangRiskClient][verifyScoreInfo][][]用户风险信息验证返回："+result);
            TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
            if(rsp != null && rsp.getStatus() == 0){
                JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
                if(jsonObject != null && jsonObject.get("hit").equals("0")){
                    return 0;
                } else {
                    if(jsonObject != null && jsonObject.get("hit").equals("1")){
                        Map<String,String> map = JSONObject.toJavaObject(jsonObject,Map.class);
                        for(Map.Entry<String,String> entry : map.entrySet()){
                            if(!entry.getValue().equals("0")){
                                msg.append(entry.getKey());
                            }
                        }
                    }
                    return 1;
                }
            }
            msg.append(rsp.getMessage());
        } catch (Exception e){
        }
        return 1;
    }

    /**
     * 车辆信息
     * */
    public Integer verifyCarInfo(TianChuangCommonParam param,StringBuilder msg){
        try {
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("name", param.getName());
            paramMap.put("plate", param.getPlate());
            paramMap.put("plateType", param.getPlateType());

            String tokenKey = TianChuangUtil.getTokenKey(CAR_INFO_URL,TOKEN_ID,paramMap);
            paramMap.put("tokenKey", tokenKey);
            paramMap.put("appId", APP_ID);
            log.info("[TianChuangRiskClient][verifyCarInfo][][]行驶证第一版验证请求："+JsonUtils.json(paramMap));
            String result = OkHttpClientUtil.getInstance().post(CAR_INFO_URL, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
            log.info("[TianChuangRiskClient][verifyCarInfo][][]行驶证第一版验证返回："+result);
            
            recordCarInfoCall(JSON.toJSONString(param), result, param.getProductLine());
            
            TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
            // 请求成功
            if(rsp != null && rsp.getStatus() == 0){
                JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
                if(jsonObject != null && jsonObject.get("code").equals("0")){
                    if(jsonObject.get("checkResult") != null && !jsonObject.get("checkResult").equals("00")){
                        msg.append("车牌号和持有人姓名不符，请确认");
                        return 1;
                    }
                    if(jsonObject.getString("vehicleStatus") != null && !jsonObject.getString("vehicleStatus").equals("正常")){
                        if(jsonObject.getString("vehicleStatus").equals("违法未处理") || jsonObject.getString("vehicleStatus").equals("事故未处理")){
                            msg.append("存在事故或违法未处理，请处理后再试");
                            return 1;
                        } else {
                            msg.append("行驶证异常，请更换");
                            return 1;
                        }
                    }
                    return 0;
                } else {
                    if(jsonObject != null && jsonObject.get("code").equals("1")){
                        // 这里是查询成功，天创无数据
                        msg.append("证件信息有误，请重新上传");
                    }
                    return 0;
                }
            }
            // 数据异常
            if(rsp != null && rsp.getStatus() == 3){
                msg.append(rsp.getMessage());
                return 0;
            }
        } catch (Exception e){
        }
        msg.append("证件信息有误，请重新上传");
        return 1;
    }
    
    private void recordCarInfoCall(String req, String res, String productLine) {
        try {
            RiskChargeCall call = new RiskChargeCall();
            call.setType(ChargeCallTypeEnum.CAR.getCode());
            call.setApiProvider(ApiProviderEnum.TIANCHUANG.getCode());
            call.setCharge(BigDecimal.ZERO);
            
            String msg = "失败";
            if (StringUtils.isNotBlank(res)) {
                TianChuangRsp rsp = JSONObject.parseObject(res, TianChuangRsp.class);
                if (null != rsp && Objects.equals(rsp.getStatus(), 0) && null != rsp.getData()) {
                    JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
                    if (jsonObject != null) {
                        msg = jsonObject.getString("checkResult");
                        if (Objects.equals(msg, "00")) {
                            msg = "姓名一致";
                        } else if (Objects.equals(msg, "01")) {
                            msg = "姓名不一致";
                        }
                        Integer code = jsonObject.getInteger("code");
                        if (Objects.equals(code, 0) || Objects.equals(code, 1)) {
                            call.setCharge(getPriceConfig().getCarPrice());
                        }
                    }
                }
            }
            call.setProductLine(StringUtils.defaultString(productLine));
            call.setCallTime(new Date());
            call.setExt(JSON.toJSONString(new RiskChargeCall.Ext(req, res, msg)));
            call.setCreateTime(new Date());
            call.setUpdateTime(new Date());
            riskChargeCallMapper.insert(call);
        } catch (Exception e) {
            log.error("[][][][]保存调用记录异常", e);
        }
    }

    /**
     * 驾驶证核验
     * */
    public Integer verifyDriverLicenseInfo(TianChuangCommonParam param,String msg){
        Map<String,String> paramMap = new HashMap<>();
//        paramMap.put("authId", param.getAuthId());
//        paramMap.put("isCpt", param.getIsCpt());
//        paramMap.put("tmcName", param.getTmcName());

        paramMap.put("name", param.getName());
        paramMap.put("licenseNo", param.getLicenseNo());

//        String tokenKey = TianChuangUtil.getTokenKey(DRIVER_LICENSE_URL,TOKEN_ID,paramMap);
//        paramMap.put("tokenKey", tokenKey);
//        paramMap.put("appId", APP_ID);
//        log.info("[TianChuangRiskClient][verifyDriverLicenseInfo][][]驾驶证信息验证请求："+JsonUtils.json(paramMap));
//        String result = OkHttpClientUtil.getInstance().post(DRIVER_LICENSE_URL, JsonUtils.json(paramMap),80L, TimeUnit.SECONDS);
//        log.info("[TianChuangRiskClient][verifyDriverLicenseInfo][][]驾驶证信息验证返回："+result);
//        TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
//        if(rsp != null && rsp.getStatus() == 0){
//            JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
//            if(jsonObject != null && jsonObject.getInteger("charge") == 0){
//                log.info("[TianChuangRiskClient][verifyDriverLicenseInfo][][]驾驶证信息验证通过："+result);
//                return 0;
//            } else {
//                return 1;
//            }
//        }
        return 0;
    }

    /**
     * 行驶证所有人验证
     * */
    public Integer verifyCarOwner(TianChuangCommonParam param,StringBuilder msg){
        if(1==1){
            return 0;
        }
        Map<String,String> paramMap = new HashMap<>();

        paramMap.put("name", param.getName());
        paramMap.put("plate", param.getPlate());
        if(param.getPlate().length() == 8){
            param.setPlateType("52");
        }
        paramMap.put("plateType", param.getPlateType());

        String tokenKey = TianChuangUtil.getTokenKey(CAR_OWNER_URL,TOKEN_ID,paramMap);
        paramMap.put("tokenKey", tokenKey);
        paramMap.put("appId", APP_ID);
        log.info("[TianChuangRiskClient][verifyCarOwner][][]行驶证第二版验证请求："+JsonUtils.json(paramMap));
        String result = OkHttpClientUtil.getInstance().post(CAR_OWNER_URL, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
        log.info("[TianChuangRiskClient][verifyCarOwner][][]行驶证第二版验证返回："+result);
        TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
        if(rsp != null && rsp.getStatus() == 0){
            JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
            if(jsonObject != null && jsonObject.get("result").equals("1")){//1-z一致 -1-无数据 0-不一致
                return 0;
            } else {
                if(jsonObject != null && jsonObject.get("result").equals("0")){//1-z一致 -1-无数据 0-不一致
                    msg.append("验证不一致");
                }
                if(jsonObject != null && jsonObject.get("result").equals("-1")){//1-z一致 -1-无数据 0-不一致
                    msg.append("未查到证件信息");
                }
                return 1;
            }
        }
        return 1;
    }

    /**
     * 银行卡验证
     * */
    public Integer verifyBankCard3(TianChuangCommonParam param,StringBuilder msg){
        String finalMsg = "";
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("authId", param.getAuthId());
        paramMap.put("isCpt", param.getIsCpt());
        paramMap.put("tmcName", param.getTmcName());

        paramMap.put("name", param.getName());
        paramMap.put("idcard", param.getIdCard());
        paramMap.put("bankcard", param.getBankCard().replace(" ",""));
//        paramMap.put("mobile", param.getMobile());

        String tokenKey = TianChuangUtil.getTokenKey(BACK_CARD_INFO_3,TOKEN_ID,paramMap);
        paramMap.put("tokenKey", tokenKey);
        paramMap.put("appId", APP_ID);
        log.info("[TianChuangRiskClient][verifyBankCard][][]银行卡三要素信息验证请求："+JsonUtils.json(paramMap));
        String result = OkHttpClientUtil.getInstance().post(BACK_CARD_INFO_3, JsonUtils.json(paramMap),50l, TimeUnit.SECONDS);
        log.info("[TianChuangRiskClient][verifyBankCard][][]银行卡三要素信息验证返回："+result);
        
        recordCardCall(JSON.toJSONString(paramMap), result, param.getProductLine());
        
        TianChuangRsp rsp = JSONObject.parseObject(result,TianChuangRsp.class);
        if(rsp != null && rsp.getStatus() == 0){
            JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
            if(jsonObject != null && jsonObject.get("result").equals("000")){
                return 0;
            } else {
                if(jsonObject != null && !jsonObject.get("result").equals("000")){
                    String code = jsonObject.getString("result");
                    if(code.equals("202") || code.equals("204")){
                        msg.append("身份信息验证失败，请重试");
                    } else if(code.equals("205") || code.equals("206") || code.equals("300") || code.equals("301")
                        || code.equals("302") || code.equals("305") || code.equals("308")
                    ){
                        msg.append("银行卡无效，请尝试更换其他卡片");
                    } else if(code.equals("307")){
                        msg.append("银行卡验证次数超限，请明日重试");
                    } else {
                        msg.append(jsonObject.getString("resultMsg"));
                    }
                }
                return 1;
            }
        }
        return 1;
    }
    
    private final List<String> CARD_CHARGE_CODE_LIST = Lists.newArrayList("000", "100", "200", "201", "202", "203", "204", "205", "206", "300", "305");
    
    private void recordCardCall(String req, String res, String productLine) {
        try {
            RiskChargeCall call = new RiskChargeCall();
            call.setType(ChargeCallTypeEnum.CARD.getCode());
            call.setApiProvider(ApiProviderEnum.TIANCHUANG.getCode());
            call.setCharge(BigDecimal.ZERO);
            
            String msg = "失败";
            if (StringUtils.isNotBlank(res)) {
                TianChuangRsp rsp = JSONObject.parseObject(res, TianChuangRsp.class);
                if (null != rsp && Objects.equals(rsp.getStatus(), 0) && null != rsp.getData()) {
                    JSONObject jsonObject = JSONObject.parseObject(JsonUtils.json(rsp.getData()));
                    if (jsonObject != null) {
                        msg = jsonObject.getString("resultMsg");
                        String result = jsonObject.getString("result");
                        if (CARD_CHARGE_CODE_LIST.contains(result)) {
                            call.setCharge(getPriceConfig().getCardPrice());
                        }
                    }
                }
            }
            call.setProductLine(StringUtils.defaultString(productLine));
            call.setCallTime(new Date());
            call.setExt(JSON.toJSONString(new RiskChargeCall.Ext(req, res, msg)));
            call.setCreateTime(new Date());
            call.setUpdateTime(new Date());
            riskChargeCallMapper.insert(call);
        } catch (Exception e) {
            log.error("[][][][]保存调用记录异常", e);
        }
    }
    
    public static CallChargeConfig getPriceConfig() {
        try {
            String str = ConfigCenterClient.get("call_charge_config");
            if (StringUtils.isNotBlank(str)) {
                return JSONObject.parseObject(str, CallChargeConfig.class);
            }
        } catch (Exception e) {
            LoggerUtils.warn(log, "[获取收费接口价格配置] 失败", e);
        }
        
        // 使用默认
        CallChargeConfig config = new CallChargeConfig();
        config.setCertPrice(new BigDecimal("0.06"));
        config.setCardPrice(new BigDecimal("0.20"));
        config.setCarPrice(new BigDecimal("1.30"));
        config.setScorePrice(new BigDecimal("0.30"));
        return config;
    }
}
