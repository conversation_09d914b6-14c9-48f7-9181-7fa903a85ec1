package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
public class SupplierAppealRecord extends Model<SupplierAppealRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String orderId;
    private String tcAppealId;
    private String supplierAppealId;
    private String appealText;
    private String appealPic;
    private String appealSound;
    private Integer auditStatus;
    private String auditRemark;
    private Date createTime;
    private Date updateTime;
}
