package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RiskHit extends Model<RiskHit> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String orderId;
    private Integer mainScene;
    private Integer childScene;
    private String mainSceneNo;
    private String childSceneNo;
    private String memberId;
    private String reqParam;
    private String passengerCellphone;
    private Integer riskLevel;
    private Integer hitType;
    private String hitRule;
    private String customerValue;
    private Integer customerType;
    private Integer isCheating;
    private String productLine;
    private Integer cityId;
    private String cityName;
    private String startAddress;
    private String endAddress;
    private String mainSceneName;
    private String childSceneName;
    private String env;
    private Date createTime;
    private Date updateTime;
    private BigDecimal totalAmount;
    private String couponNo;
    private BigDecimal orderAmount;//优惠券金额赛这里
    private String resResult;
    private String requestId;

    private String userPhone;//账户手机号
    private String driverCardNo;
    private String unionId;
    private String hitStrategy;
    private String hitField;
    private String controlTarget;
    private String disposeAction;

}
