package com.ly.car.risk.process.constants;

public enum RuleNoMapRiskTypeEnum {

    MILEAGE_SHORT("025",3,"行驶里程过短",6,"清零"),
    FINISH_TIME_SHORT("026",5,"短时间快速完单",6,"清零"),
    AMOUNT_ABNORMAL("027",21,"订单金额异常",6,"清零"),
    AMOUNT_ABNORMAL_028("028",21,"订单金额异常",6,"清零"),
    MILEAGE_SHORT_029("029",3,"行驶里程过短",6,"清零"),
    MILEAGE_SHORT_032("032",3,"行驶里程过短",6,"清零"),
    AMOUNT_ABNORMAL_034("034",21,"订单金额异常",6,"清零"),
    ZC_AMOUNT_001("zc-001",21,"行程费用异常",6,"清零"),
    ZC_AMOUNT_002("zc-002",22,"添加附加费异常",6,"清零"),
    ZC_AMOUNT_007("zc-007",21,"行程费用异常2",6,"清零"),
    ZC_AMOUNT_011("zc-011",21,"行程费用异常2",6,"清零"),
    ZC_AMOUNT_012("zc-012",22,"行程费用异常2",6,"清零"),
    ZC_AMOUNT_013("zc-013",21,"行程费用异常2",6,"清零"),
    ZC_AMOUNT_014("zc-014",22,"行程费用异常2",6,"清零"),
    LXCL_025("lxcl-025",3,"行驶里程过短",6,"清零"),
    LXCL_026("lxcl-026",5,"短时间快速完单",6,"清零"),
    ;
    public String ruleNo;
    public Integer riskType;
    public String riskDesc;
    public Integer disposeType;
    public String disposeTypeDesc;

    RuleNoMapRiskTypeEnum(String ruleNo,Integer riskType,String riskDesc,Integer disposeType,String disposeTypeDesc){
        this.ruleNo = ruleNo;
        this.riskType = riskType;
        this.riskDesc = riskDesc;
        this.disposeType = disposeType;
        this.disposeTypeDesc = disposeTypeDesc;
    }

    public static RuleNoMapRiskTypeEnum getDescByRuleNo(String ruleNo){
            for (RuleNoMapRiskTypeEnum enumItem : RuleNoMapRiskTypeEnum.values()) {
                if (enumItem.getRuleNo().equals(ruleNo)) {
                    return enumItem;
                }
            }
            return null;
    }

    public static RuleNoMapRiskTypeEnum getDescByRiskType(Integer riskType){
        for (RuleNoMapRiskTypeEnum enumItem : RuleNoMapRiskTypeEnum.values()) {
            if (enumItem.getRiskType().equals(riskType)) {
                return enumItem;
            }
        }
        return null;
    }

    public String getRuleNo() {
        return ruleNo;
    }

    public void setRuleNo(String ruleNo) {
        this.ruleNo = ruleNo;
    }

    public Integer getRiskType() {
        return riskType;
    }

    public void setRiskType(Integer riskType) {
        this.riskType = riskType;
    }

    public String getRiskDesc() {
        return riskDesc;
    }

    public void setRiskDesc(String riskDesc) {
        this.riskDesc = riskDesc;
    }

    public Integer getDisposeType() {
        return disposeType;
    }

    public void setDisposeType(Integer disposeType) {
        this.disposeType = disposeType;
    }

    public String getDisposeTypeDesc() {
        return disposeTypeDesc;
    }

    public void setDisposeTypeDesc(String disposeTypeDesc) {
        this.disposeTypeDesc = disposeTypeDesc;
    }
}
