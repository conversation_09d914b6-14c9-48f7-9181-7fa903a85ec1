package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.AutoCallRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.AutoCallRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class AutoCallRecordService {

    @Resource
    private AutoCallRecordMapper autoCallRecordMapper;

    public void insertRecord(AutoCallRecord autoCallRecord){
        this.autoCallRecordMapper.insert(autoCallRecord);
    }

    public AutoCallRecord getEntity(String orderId,String callId){
        return this.autoCallRecordMapper.selectOne(new QueryWrapper<AutoCallRecord>()
                .eq("order_id", orderId)
                .eq("call_id", callId)
                .last("limit 1")
        );
    }

    public void updateRecord(AutoCallRecord autoCallRecord){
        this.autoCallRecordMapper.updateById(autoCallRecord);
    }

}
