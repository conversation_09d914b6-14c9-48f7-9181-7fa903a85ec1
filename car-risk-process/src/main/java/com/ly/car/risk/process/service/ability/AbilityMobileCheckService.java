package com.ly.car.risk.process.service.ability;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.CommonRiskClient;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.controller.ability.params.MobileCheckParam;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AbilityMobileCheckService {

    @Resource
    private TencentCloudApiClient tencentCloudApiClient;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private EsQueryClient esQueryClient;
    @Resource
    private CommonRiskClient commonRiskClient;

    public void initMobileBloom(){
        RBloomFilter<String> normalMobileBloom = redissonClient.getBloomFilter("bloom:check:mobile:normal");
        normalMobileBloom.tryInit(10000000,0.02);
        RBloomFilter<String> abNormalMobileBloom = redissonClient.getBloomFilter("bloom:check:mobile:abnormal");
        abNormalMobileBloom.tryInit(1000000,0.02);
    }

    public UiResult mobileCheck(MobileCheckParam param){
        log.info("[][][][]验证手机号参数：{}", JsonUtils.json(param));
        //验证格式等信息
        if(param.getMobile().length() != 11 || !param.getMobile().startsWith("1")){
            log.info("[][][][]验证手机号参数格式不对：{}", JsonUtils.json(param));
            UiResult result = UiResult.fail();
            result.setErrCode(1);
            result.setMsg("手机号格式不正确");
            return result;
        }
        //用户第二次提交和之前一样的手机号则让过
        RBucket<String> bucket = redissonClient.getBucket("mobile:check:status:" + param.getMobile());
        //查下正常号码布隆过滤器
        RBloomFilter<String> normalMobileBloom = redissonClient.getBloomFilter("bloom:check:mobile:normal");
        if(bucket != null && bucket.isExists()){
            normalMobileBloom.add(param.getMobile());
            log.info("[][][][]验证手机号参数格式第二次提交：{}", param.getMobile());
            return UiResult.ok();
        }


        try {
            log.info("[][][][]验证手机号布隆过滤器");
            if(normalMobileBloom.contains(param.getMobile())){
                log.info("[][][][]验证手机号已存在正常布隆过滤器：{}", param.getMobile());
                return UiResult.ok();
            }
            //查下异常号码布隆过滤器
            RBloomFilter<String> abNormalMobileBloom = redissonClient.getBloomFilter("bloom:check:mobile:abnormal");
            if(abNormalMobileBloom.contains(param.getMobile())){
                log.info("[][][][]验证手机号已存在不正常布隆过滤器：{}", param.getMobile());
                UiResult result = UiResult.fail();
                result.setErrCode(1);
                result.setMsg("手机号历史已验证问题");
                if(param.getIsClick() == null){
                    bucket.set("1",10, TimeUnit.MINUTES);
                }
                return result;
            }
            //http://es.dss.17usoft.com/index/car-data-sfc-order/template/search_sfc_order_limit/1.0.1/search
            Boolean finishRecord = esQueryClient.querySfcFinish(param.getMobile(), DateUtil.date2String(TimeUtil.thirtyDay()));
            log.info("[][][][]验证手机号查询近3天是否有完单：{}", finishRecord);
            if(finishRecord){
                //有的话直接放到缓存
                normalMobileBloom.add(param.getMobile());
                //插入记录es
                esQueryClient.insertMobileCheckRecord(param.getMobile(), 1,false);
                //返回验证成功
                log.info("[][][][]验证手机号查询近3天确认有完单：{}", param.getMobile());
                return UiResult.ok();
            }
            //查下三方
            Integer status = commonRiskClient.mobileCheck(param.getMobile());
            log.info("[][][][]验证手机号-三方查询订单状态：{}", status);
            if(status == 0){
                //下次继续验证
                return UiResult.ok();
            } else if(status == 1){
                normalMobileBloom.add(param.getMobile());
                esQueryClient.insertMobileCheckRecord(param.getMobile(), 1,true);
                //返回验证成功
                return UiResult.ok();
            } else if(status == 2){
                if(param.getIsClick() != null && param.getIsClick() == 0){
                    UiResult result = UiResult.fail();
                    result.setErrCode(1);
                    result.setMsg("三方验证不正确");
                    esQueryClient.insertMobileCheckRecord(param.getMobile(), 0,true);
                    return result;
                } else {
                    if(param.getIsClick() == null){
                        //说明是用户确认的
                        bucket.set("1",10, TimeUnit.MINUTES);
                        UiResult result = UiResult.fail();
                        result.setErrCode(1);
                        result.setMsg("三方验证不正确");
                        return result;
                    }
                }

            }
        } catch (Exception e) {
            log.error("[][][][]验证手机号错误:",e);
        }
        return UiResult.ok();
    }

    public UiResult checkTest(List<String> mobileList){
        int i = 0;
        for(String str : mobileList) {
            tencentCloudApiClient.checkMobileStatus(str, false);
            i = i+1;
            log.info("[][][][]验证手机号==="+i);
        }
        return UiResult.ok();
    }


    public static void main(String[] args) {
        System.out.println("18506151282".startsWith("1"));
    }
}
