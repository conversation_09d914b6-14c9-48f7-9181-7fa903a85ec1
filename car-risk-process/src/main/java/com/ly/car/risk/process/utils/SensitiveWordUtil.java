package com.ly.car.risk.process.utils;

import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveWords;
import com.ly.car.risk.process.service.dto.SensitiveWordsSimple;
import com.ly.car.risk.process.service.sensitiveWords.SensitiveWordsService;
import com.ly.car.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class SensitiveWordUtil {

    public static Map<String, Object> dictionaryMap = new HashMap<>();
    public static Map<String, SensitiveWordsSimple> simpleMap = new HashMap<>();

    private final static String IGNORE_SPECIAL_CHAR_REGEX = "[`~!@#$%^&*()+=|{}';',\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}【】‘；”“’。，、？]|\\s*";
    private final static Matcher IGNORE_MATCHER = Pattern.compile(IGNORE_SPECIAL_CHAR_REGEX).matcher("");

    @Resource
    private SensitiveWordsService sensitiveWordsService;

    /**
     * 生成关键词字典库
     * @param
     * @return
     */
    @PostConstruct
    @Scheduled(cron = "0 */30 * * * ?")
    public void initMap() {
        Collection<SensitiveWords> wordsList = this.sensitiveWordsService.queryAllWordsList();
        Collection<String> words = wordsList.stream().map(SensitiveWords::getWord).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(words)) {
            System.out.println("敏感词列表不能为空");
            return ;
        }
        // map初始长度words.size()，整个字典库的入口字数(小于words.size()，因为不同的词可能会有相同的首字)
        Map<String, Object> map = new HashMap<>(wordsList.size());
        // 遍历过程中当前层次的数据
        Map<String, Object> curMap = null;
        Iterator<SensitiveWords> iterator = wordsList.iterator();
        while (iterator.hasNext()) {
            String wordStr = "";
            String word = iterator.next().getWord();
            curMap = map;
            int len = word.length();
            for (int i =0; i < len; i++) {
                // 遍历每个词的字
                String key = String.valueOf(word.charAt(i));
                if(isIgnore(word.charAt(i))){
                    continue;
                }
                // 当前字在当前层是否存在, 不存在则新建, 当前层数据指向下一个节点, 继续判断是否存在数据
                Map<String, Object> wordMap = (Map<String, Object>) curMap.get(key);
                if (wordMap == null) {
                    // 每个节点存在两个数据: 下一个节点和isEnd(是否结束标志)
                    wordMap = new HashMap<>(2);
                    wordMap.put("isEnd", "0");
                    curMap.put(key, wordMap);
                }
                curMap = wordMap;
                // 如果当前字是词的最后一个字，则将isEnd标志置1
                if (i == len -1) {
                    curMap.put("isEnd", "1");
                }
            }
        }
        dictionaryMap = map;
    }

    @PostConstruct
    @Scheduled(cron = "0 */30 * * * ?")
    public void initOriMap(){
        Collection<SensitiveWords> wordsList = this.sensitiveWordsService.queryAllWordsList();
        if (CollectionUtils.isEmpty(wordsList)) {
            System.out.println("敏感词列表不能为空");
            return ;
        }
        for(SensitiveWords word : wordsList){
            String str = "";
            for (int i =0; i < word.getWord().length(); i++) {
                if(isIgnore(word.getWord().charAt(i))){
                    continue;
                }
                str = str + word.getWord().charAt(i);
            }
            SensitiveWordsSimple sensitiveWordsSimple = new SensitiveWordsSimple();
            sensitiveWordsSimple.setId(word.getId());
            sensitiveWordsSimple.setWords(word.getWord());
            sensitiveWordsSimple.setWordType(word.getWordType());
            simpleMap.put(str,sensitiveWordsSimple);
        }
    }




    /**
     * 搜索文本中某个文字是否匹配关键词
     * @param text
     * @param beginIndex
     * @return
     */
    public static int checkWord(String text, int beginIndex) {
        if (dictionaryMap == null) {
            throw new RuntimeException("字典不能为空");
        }
        boolean isEnd = false;
        int wordLength = 0;
        Map<String, Object> curMap = dictionaryMap;
        int len = text.length();
        // 从文本的第beginIndex开始匹配
        for (int i = beginIndex; i < len; i++) {
            String key = String.valueOf(text.charAt(i));
//            if(isIgnore(text.charAt(i))){
//                wordLength ++;
//                isEnd = true;
//            }
            // 获取当前key的下一个节点
            curMap = (Map<String, Object>) curMap.get(key);
            if (curMap == null) {
                break;
            } else {
                wordLength ++;
                if ("1".equals(curMap.get("isEnd"))) {
                    isEnd = true;
                }
            }
        }
        if (!isEnd) {
            wordLength = 0;
        }
        return wordLength;
    }

    /**
     * 获取匹配的关键词和命中次数
     * @param text
     * @return
     */
    public static Map<String, SensitiveWordsSimple> matchWords(String text) {
        //去掉斜杠这些特殊字符
        Map<String, Integer> wordMap = new HashMap<>();
        //在开头就要先过滤掉各种字符
        String key = "";
        for(int i= 0;i<text.length();i++){
            if(!isIgnore(text.charAt(i))){
                key = key + text.charAt(i)+"";
            }
        }
        int len = key.length();
//        StringBuilder sb = new StringBuilder(text);
        for (int i = 0; i < len; i++) {
            int wordLength = checkWord(key, i);
            if (wordLength > 0) {
                String word = key.substring(i, i + wordLength);
                // 添加关键词匹配次数
                if (wordMap.containsKey(word)) {
                    wordMap.put(word, wordMap.get(word) + 1);
                } else {
                    wordMap.put(word, 1);
                }
                i += wordLength - 1;
                i += len;
            }
        }
        Map<String,SensitiveWordsSimple> originalMap = new HashMap<>();
        for(Map.Entry<String,Integer> entry : wordMap.entrySet()){
//            if(entry.getKey().matches("[0-9]+")){
//                //把当前命中的key往后算8位，都为数字则认定为手机号，不为数字或不满足8位则放过，从map移除
//                int index = key.indexOf(entry.getKey());
//                //后面所有的加起来小于11位或者后面11位不是纯数字
//                if(key.substring(index,key.length()).length() < 11 || !key.substring(index,index+11).matches("[0-9]+")){
//                    continue;
//                }
//            }
            SensitiveWordsSimple sensitiveWordsSimple = simpleMap.get(entry.getKey());
            if(sensitiveWordsSimple != null){
                originalMap.put(sensitiveWordsSimple.getWords(), sensitiveWordsSimple);
            }

        }
        return originalMap;
    }


    private static boolean isIgnore(char specificChar){
        Pattern pattern = Pattern.compile(IGNORE_SPECIAL_CHAR_REGEX);
        Matcher matcher = pattern.matcher(String.valueOf(specificChar));
        return matcher.matches();
    }

    public static void main(String[] args) {

    }

}
