package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@Scope("prototype")
public class DriverFirstService extends MtFilterHandler{

    private static final List<String> ruleNoList =  Stream.of("015", "034", "035" ,"036" ,"037").collect(Collectors.toList());

    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;

    @Override
    public void doHandler(MtFilterContext context) {
        log.info("[][][][]司机返现过滤{}", JsonUtils.json(context.getParam().getJSONArray("orderIds")));
        List<RiskOrderManage> manageList = new ArrayList<>();
        if(context.getParam().getString("productLine").equals("MT")){

        } else {
            List<String> hcOrderIds = JSONObject.parseArray(context.getParam().getJSONArray("orderIds").toJSONString(),String.class);
            //在风险订单表查询这些订单
            manageList = this.riskOrderManageMapper.selectList(new QueryWrapper<RiskOrderManage>()
                    .in("supplier_order_id",hcOrderIds)
            );
            log.info("[][][][]司机返现查询风险单{}", JsonUtils.json(hcOrderIds));
            //过滤当前列表是否包含上面的规则
            manageList.forEach(data->{
                List<String> ruleSplits = new ArrayList<>(Arrays.asList(data.getRuleNo().split(",")));
                ruleSplits.retainAll(ruleNoList);
                data.setRuleNo(StringUtils.join(ruleSplits,","));
            });
            log.info("[][][][]司机返现过滤风险规则编号{}", JsonUtils.json(manageList));
            manageList = manageList.stream().filter(data->StringUtils.isNotBlank(data.getRuleNo())).collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(manageList)){
            List<String> orderIds = manageList.stream().map(RiskOrderManage::getSupplierOrderId).collect(Collectors.toList());
            context.getDto().setCode(1);
            context.getDto().setMessage("司机首单奖不通过");
            context.getDto().setObj(orderIds);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
}
