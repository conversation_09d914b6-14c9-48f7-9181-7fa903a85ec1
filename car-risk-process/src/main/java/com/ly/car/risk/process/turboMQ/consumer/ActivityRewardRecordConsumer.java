package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.ActivityRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityRelation;
import com.ly.car.risk.process.service.ActivityRelationService;
import com.ly.car.risk.process.turboMQ.dto.ActivityRewardRecord;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.utils.BingLogUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class ActivityRewardRecordConsumer implements MessageListenerConcurrently {

    private ActivityRelationService activityRelationService;
    private SfcOrderMapper sfcOrderMapper;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][ActivityRewardRecordConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                BingLogData bingLogData = JSON.parseObject(body, BingLogData.class);
                String eventType = bingLogData.getEventType();
                ActivityRewardRecord beforeActivityRewardRecord = BingLogUtil.buildBefore(bingLogData,ActivityRewardRecord.class);
                ActivityRewardRecord afterActivityRewardRecord = BingLogUtil.buildSource(bingLogData,ActivityRewardRecord.class);
                log.info("[][][][]同步发奖订单before{}", JsonUtils.json(beforeActivityRewardRecord));
                log.info("[][][][]同步发奖订单after{}", JsonUtils.json(afterActivityRewardRecord));
                if(eventType.equals("INSERT") && afterActivityRewardRecord.getRoleType() == 1 && afterActivityRewardRecord.getStatus() == 2
                    && !afterActivityRewardRecord.getThirdId().equals("[]")){

                    List<String> orderIds = JSONObject.parseArray(afterActivityRewardRecord.getThirdId(),String.class);
                    if(CollectionUtils.isEmpty(orderIds)){
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    activityRelationService = SpringContextUtil.getBean("activityRelationService");
                    ActivityRelation relation = activityRelationService.queryOneRelation(afterActivityRewardRecord.getActivityId(), afterActivityRewardRecord.getInvitedUserId(), afterActivityRewardRecord.getUserId());

                    relation.setOrderId(orderIds.get(orderIds.size()-1));
                    relation.setUpdateTime(new Date());
                    activityRelationService.updateActivityRelation(relation);
                }
            } catch (Exception e){
                log.error("[][][][]同步发奖订单错误",e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
