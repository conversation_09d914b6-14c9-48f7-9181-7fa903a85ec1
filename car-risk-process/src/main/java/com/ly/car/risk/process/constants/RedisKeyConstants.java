package com.ly.car.risk.process.constants;

public class RedisKeyConstants {

    public static final String USER_SLIDING_WINDOW = "MEMBER.sliding.window_";
    public static final String DRIVER_SLIDING_WINDOW = "DRIVER.sliding.window_";
    public static final String SFC_USER_ORDER_NUMBER = "SFC_USER_ORDER_NUMBER_MEMBER_";
    public static final String SFC_CANCEL_ORDER_MEMBER = "SFC_CANCEL_ORDER_MEMBER_";
    public static final String SFC_MOVING_ORDER = "SFC_MOVING_ORDER";
    public static final String SFC_ESTIMATE_PATH = "SFC_ESTIMATE_PATH_";//顺风车存储预估距离，使用Map eviction，利用key的过期时间
    public static final String SFC_PHONE_ORDER_NUMBER = "SFC_PHONE_ORDER_NUMBER_";//乘客手机号
    public static final String SFC_USER_ID_PLACE_ORDER = "SFC_USER_ID_PLACE_ORDER_";
    public static final String SFC_USER_ID_CANCEL_ORDER = "SFC_USER_ID_CANCEL_ORDER_";
    public static final String SFC_USER_ID_CANCEL_24h_RATE= "sfc_user_id_cancel_24h_rate_";

    public static final String SFC_NO_NORMAL_STOPPING = "SFC_NO_NORMAL_STOPPING";
    public static final String SFC_NO_NORMAL_DEVIATION = "SFC_NO_NORMAL_DEVIATION";
    public static final String SFC_NO_NORMAL_SPEED = "SFC_NO_NORMAL_SPEED";
    public static final String SFC_SERVICE_TIME = "SFC_SERVICE_TIME_";


    //专车redis key
    public static final String USER_ORDER_NUMBER_MEMBER = "USER_ORDER_NUMBER_MEMBER_";//memberId订单数
    public static final String USER_ORDER_NUMBER_UNION = "USER_ORDER_NUMBER_UNION_";//UNION_ID订单数
    public static final String PAY_ACCOUNT_WINDOW = "PAY.ACCOUNT.WINDOW_";//支付账号
    public static final String DEVICE_ID_WINDOW = "DEVICE.ID.WINDOW_";//设备号
    public static final String USER_MEMBER_WINDOW = "USER_MEMBER_WINDOW_";//memberId完单
    public static final String USER_UNION_ID_WINDOW = "USER_UNION_ID_WINDOW_";//unionId完单
    public static final String DRIVER_CARD_WINDOW = "DRIVER_CARD_WINDOW_";//司机车牌号
    public static final String YNC_MOVING_ORDER = "YNC_MOVING_ORDER";
    public static final String YNC_ESTIMATE_PATH = "YNC_ESTIMATE_PATH_";//专车存储预估距离，使用Map eviction，利用key的过期时间

    public static final String YNC_NO_NORMAL_STOPPING = "YNC_NO_NORMAL_STOPPING";
    public static final String YNC_NO_NORMAL_DEVIATION = "YNC_NO_NORMAL_DEVIATION";
    public static final String YNC_NO_NORMAL_SPEED = "YNC_NO_NORMAL_SPEED";

    //COMMON
    public static final String HIT_SAFE_WARNING = "HIT_SAFE_WARNING_ORDER_";//命中安全预警,存储5分钟的
    public static final String ORDER_DRIVER_LOCATION_MINUTES = "ORDER_DRIVER_LOCATION_MINUTES";//存储司机当前位置
    public static final String ORDER_DRIVER_LOCATION_MINUTES_NEW = "ORDER_DRIVER_LOCATION_MINUTES_NEW";//存储司机当前位置
    public static final String ESTIMATE_START_END_INFO = "ESTIMATE_START_END_INFO_";

    /** 安全中心订单无需提示规则hash */
    public static final String SAFE_REMINDER_ORDER_NO_HINT_RULE_CACHE = "safe:reminder:nohint:rule:%s";
    public static final String BIGDATA_USER_CREATE_ORDER_WINDOW = "bigdata:user:create:window:%s";

}
