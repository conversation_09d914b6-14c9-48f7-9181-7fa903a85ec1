package com.ly.car.risk.process.scene;

import cn.hutool.core.collection.CollUtil;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.handler.riskSecurity.AbstractRiskSecurityHandler;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description of SpecialSceneHandlerFactory
 *
 * <AUTHOR>
 * @date 2024/11/20
 * @desc
 */
@Service
public class SpecialSceneHandlerFactory implements ApplicationContextAware, InitializingBean {

    private ApplicationContext ctx;

    private ConcurrentHashMap<StrategySceneEnum, List<SpecialSceneHandler>> handlerMaps = new ConcurrentHashMap<>();


    public RiskSceneResult handlerCheck(UnifyCheckRequest request){
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        List<SpecialSceneHandler> specialSceneHandlers = handlerMaps.get(sceneEnum);
        if(CollUtil.isEmpty(specialSceneHandlers)){
            return null;
        }
        for(SpecialSceneHandler handler : specialSceneHandlers){
            RiskSceneResult result = handler.check(request);
            if(null != result && result.isRiskFlag()){
                return result;
            }
        }
        return null;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, SpecialSceneHandler> handlers = ctx.getBeansOfType(SpecialSceneHandler.class, false, true);
        ConcurrentHashMap<StrategySceneEnum, List<SpecialSceneHandler>> map = new ConcurrentHashMap<>();
        for (SpecialSceneHandler handler : handlers.values()) {
            List<StrategySceneEnum> strategySceneEnums = handler.supportScene();
            for(StrategySceneEnum sceneEnum : strategySceneEnums){
                if(map.contains(sceneEnum)){
                    map.get(sceneEnum).add(handler);
                }else{
                    List<SpecialSceneHandler> list = new ArrayList<>();
                    list.add(handler);
                    map.put(sceneEnum,list);
                }
            }
        }
        this.handlerMaps = map;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }


}