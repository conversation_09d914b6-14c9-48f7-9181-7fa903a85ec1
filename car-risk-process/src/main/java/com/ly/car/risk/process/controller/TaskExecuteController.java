package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.repo.mtticket.mapper.DriverInfoMtMapper;
import com.ly.car.risk.process.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.SfcRiskService;
import com.ly.car.risk.process.service.rule.safe.SfcSafetyWarningService;
import com.ly.car.risk.process.service.rule.safe.YncSafeWarningService;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/task")
@Slf4j
public class TaskExecuteController {

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private SfcSafetyWarningService sfcSafetyWarningService;
    @Resource
    private YncSafeWarningService yncSafeWarningService;
    @Resource
    private SfcRiskService sfcRiskService;
    @Resource
    private DriverInfoMtMapper driverInfoMtMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @RequestMapping("upgradeDriver")
    public String upgradeDriver(){
        riskCustomerService.upgradeDriver();
        return "success";
    }

    @RequestMapping("delManage")
    public void delManage(){
        //初始化删除时间，每小时删一次
        Date startDate = DateUtil.addHour(new Date(),-1);
        Date endDate = new Date();
//        List<DistributionRiskManage> manageList = distributionRiskManageMapper.selectList(
//                new QueryWrapper<DistributionRiskManage>().gt("create_time",startDate).lt("create_time",endDate).eq("env","STAGE")
//        );
//        if(manageList != null && manageList.size() > 0){
//            List<Long> ids = manageList.stream().map(DistributionRiskManage::getId).collect(Collectors.toList());
//            distributionRiskManageMapper.deleteBatchIds(ids);
//        }
//        List<RiskOrderManage> riskOrderManages = riskOrderManageMapper.selectList(
//                new QueryWrapper<RiskOrderManage>().gt("create_time",startDate).lt("create_time",endDate).eq("env","STAGE")
//        );
//        if(riskOrderManages != null && riskOrderManages.size() > 0){
//            List<Long> ids = riskOrderManages.stream().map(RiskOrderManage::getId).collect(Collectors.toList());
//            riskOrderManageMapper.deleteBatchIds(ids);
//        }
    }

    @RequestMapping("sfcSafeWarning")
    public void dealSfcSafeWarning(){
        long startTime = System.currentTimeMillis();
        LoggerUtils.initLogMap("顺风车行程安全","computeMoving",String.valueOf(startTime),"");
        try {
            LoggerUtils.info(log,"顺风车行程安全定时任务开始,批次:{}",startTime);
            sfcSafetyWarningService.computeMoving();
            LoggerUtils.info(log,"顺风车行程安全定时任务结束,批次:{}",startTime);
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping("yncSafeWarning")
    public void dealYncSafeWarning(){
        long startTime = System.currentTimeMillis();
        LoggerUtils.initLogMap("网约车行程安全", "computeMoving", String.valueOf(startTime), "");
        try {
            LoggerUtils.info(log, "网约车行程安全定时任务开始,批次:{}", startTime);
            yncSafeWarningService.computeMoving();
            LoggerUtils.info(log, "网约车行程安全定时任务结束,批次:{}", startTime);
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping("lastedCurrentLocation")
    public void dealCurrentLocation(){

    }

    @RequestMapping("mtSubsidyTask")
    public void mtSubsidyTask(){
        this.sfcRiskService.mtSubsidyTask();
    }

    @RequestMapping("initRiskDriver")
    public void queryDriverRisk(){
        List<String> stringList = this.driverInfoMtMapper.queryRiskDriver();
        String result = "";
        if(CollectionUtils.isNotEmpty(stringList)){
            result = StringUtils.join(stringList,",");
        }
        redissonClient.getBucket("risk:driver:0913:plate").set(result,1, TimeUnit.DAYS);
    }



}
