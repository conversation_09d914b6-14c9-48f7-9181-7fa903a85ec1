package com.ly.car.risk.process.utils;


import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;

import java.util.HashMap;
import java.util.Map;

public class GroovyScriptUtil {

    public static Map<String, GroovyObject> passedClassMap = new HashMap<>();

    public static GroovyClassLoader groovyClassLoader;

    //初始化GroovyClassLoader
    static {
        ClassLoader parent = AutowiredAnnotationBeanPostProcessor.class.getClassLoader();
        groovyClassLoader = new GroovyClassLoader(parent);
    }

    //加载groovy script,放到map里面去，这样减少生成class的数量，要不然回OOM
    public static GroovyObject loadScript(String script){
        GroovyObject groovyObject = passedClassMap.get(CryptUtils.md5(script));
        if (groovyObject == null) {
            synchronized (GroovyScriptUtil.class){
                groovyObject = passedClassMap.get(CryptUtils.md5(script));
                if(groovyObject == null){
                    Class groovyClass = groovyClassLoader.parseClass(script);
                    try {
                        groovyObject = (GroovyObject) groovyClass.newInstance();
                        passedClassMap.put(CryptUtils.md5(script), groovyObject);
                    } catch (InstantiationException e) {
                        e.printStackTrace();
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return groovyObject;
    }

    public static Object invokeMethod(GroovyObject object, String method, Object[] args) {
        return object.invokeMethod(method, args);
    }

    public static Object invokeMethod(String script, String method, Object[] args) {
        GroovyObject groovy = loadScript(script);
        if (groovy != null) {
            return invokeMethod(groovy, method, args);
        } else {
            return null;
        }
    }

    /**
     * 删除不在使用的脚本关联的groovy object, 不然内存有溢出风险。
     */
    //这个一定要做，每一次做更新的时候
    public static void removeInactiveScript(String  script){
        passedClassMap.remove(CryptUtils.md5(script));
    }

    public static Integer getSize(){
        return passedClassMap.size();
    }
}
