package com.ly.car.risk.process.repo.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.OrderCoupon;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OrderCouponMapper extends BaseMapper<OrderCoupon> {

    @Select("<script>" +
            "   select * from order_coupon where order_id = #{orderId}}" +
            "</script>")
    List<OrderCoupon> getListByOrderId(@Param("orderId")String orderId);
}
