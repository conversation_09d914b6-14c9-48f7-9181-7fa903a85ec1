<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper">

    <resultMap id="baseResultMap" type="com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice">
        <id property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="source" column="source"/>
        <result property="productLine" column="product_line"/>
        <result property="apiProvider" column="api_provider"/>
        <result property="productType" column="product_type"/>
        <result property="charge" column="charge"/>
        <result property="voiceDuring" column="voice_during"/>
        <result property="ext" column="ext"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getTotal" resultType="long">
        select count(*) from risk_charge_voice
        <include refid="getCondition"/>
    </select>

    <select id="queryList" resultType="com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice">
        select * from risk_charge_voice
        <include refid="getCondition"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="queryAllList" resultType="com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice">
        select *
        from risk_charge_voice
    </select>


    <sql id="getCondition">
        where 1 = 1
        <if test="name != null and name != '' ">
            and `name` like CONCAT('%',#{name},'%')
        </if>
        <if test="ruleNo != null and ruleNo != '' ">
            and rule_no like CONCAT('%',#{ruleNo},'%')
        </if>
    </sql>


    <select id="queryCallDuration" resultType="java.lang.Long">
        select sum(ifnull(voice_during,0)) as duration
        from risk_charge_voice
        where api_provider = #{apiProvider}
          and product_type = #{productType}
          and create_time between #{startTime} and #{endTime}

    </select>

    <select id="queryCallCount" resultType="java.lang.Long">
        select count(*)
        from risk_charge_voice
        where api_provider = #{apiProvider}
          and product_type = #{productType}
          and create_time between #{startTime} and #{endTime}
    </select>

</mapper>