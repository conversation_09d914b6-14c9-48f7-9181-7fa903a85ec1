package com.ly.car.risk.process.controller;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.HitchRiskParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/hitch/risk")
@RestController
public class DriverRiskValidController {

    /**
     * 此接口聚合 高危风险核查，身份证实名，银行卡三要素，银行卡四要素
     * */
    @RequestMapping("/verify")
    public UiResult verify(@RequestBody HitchRiskParam param){

        return null;
    }


}
