package com.ly.car.risk.process.aop;

import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategy;
import com.ly.car.risk.process.strategy.RiskStrategyHandler;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * Description of RiskMetricsAOP
 *
 * <AUTHOR>
 * @date 2024/6/18
 * @desc 风控策略SQL  AOP
 */
@Configuration
public class RiskMetricsAOP {

    @Resource
    private RiskStrategyHandler riskStrategyHandler;


    @Pointcut("@annotation(com.ly.car.risk.process.aop.RiskMetricsCache)")
    public void cachePointCut() {

    }

    @Around("cachePointCut")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();

        Object proceed = joinPoint.proceed();

        long executionTime = System.currentTimeMillis() - start;

        System.out.println(joinPoint.getSignature() + " executed in " + executionTime + "ms");

        return proceed;
    }
}