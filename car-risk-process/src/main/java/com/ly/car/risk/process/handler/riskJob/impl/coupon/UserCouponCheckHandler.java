package com.ly.car.risk.process.handler.riskJob.impl.coupon;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.common.enums.RiskAlertApproveHandleResultEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveTargetEnum;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.handler.riskJob.AbstractRiskCheckHandler;
import com.ly.car.risk.process.model.riskJob.UserCouponCheckResp;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;

/**
 * Description of SupplierCouponCheckHandler
 *
 * <AUTHOR>
 * @date 2024/8/28
 * @desc
 */
@Service
public class UserCouponCheckHandler extends AbstractRiskCheckHandler<UserCouponCheckResp> {

    @Override
    public RiskJobTypeEnum support() {
        return RiskJobTypeEnum.USER_COUPON;
    }


    @Override
    public List<RiskAlertApprove> doCheck() {
        Pair<String, String> lastHour = getLastHour();
        String startTime = lastHour.getKey();
        String endTime = lastHour.getValue();
        String checkThresholdVal = getCouponCheckThreshold(support());
        if (StringUtils.isBlank(checkThresholdVal)) {
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }
        int checkThreshold = Integer.parseInt(checkThresholdVal);
        if (checkThreshold <= 0) {
            LoggerUtils.info(logger, "预警阈值不合理，check结束");
            return null;
        }
        List<UserCouponCheckResp> checkResp = couponMapper.checkHourUserCoupon(checkThreshold, startTime, endTime);

        if (CollUtil.isEmpty(checkResp)) {
            return null;
        }

        return checkResp.stream().peek(p -> {
            p.setStartTime(startTime);
            p.setEndTime(endTime);
        }).map(this::convert).collect(Collectors.toList());

    }

    private RiskAlertApprove convert(UserCouponCheckResp checkResp) {
        Date now = new Date();
        RiskAlertApprove alertApprove = new RiskAlertApprove();
        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
        alertApprove.setLevel(2);
        alertApprove.setTarget(RiskAlertApproveTargetEnum.USER.getCode());
        alertApprove.setTargetValue(checkResp.getMemberId());
        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.MARKETING.getCode());
        alertApprove.setAlertStrategy(RiskJobTypeEnum.USER_COUPON.name());
        alertApprove.setOrderType(checkResp.getOrderType());
        alertApprove.setAlertContent(JSON.toJSONString(checkResp));
        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
        alertApprove.setAlertTime(now);
        alertApprove.setCreateTime(now);
        alertApprove.setUpdateTime(now);
        alertApprove.setCreateUser(support().name() + "_CHECK");
        approveMapper.insertSelective(alertApprove);

        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
        return alertApprove;
    }

    @Override
    public String getContentFormApprove(RiskAlertApprove approve) {
        String alertContent = approve.getAlertContent();
        if (StringUtils.isBlank(alertContent)) {
            return StringUtils.EMPTY;
        }
        UserCouponCheckResp resp = JSON.parseObject(alertContent, new TypeReference<UserCouponCheckResp>() {
        });
        StringBuilder sb = doGetContentFormApprove(approve,resp);
        return sb.toString();
    }

    @Override
    public String getDesc(RiskAlertApprove approve, UserCouponCheckResp resp) {
        StringBuilder sb = new StringBuilder();
        sb.append(RiskJobTypeEnum.getDescByCode(approve.getAlertStrategy()));
        sb.append(String.format("(用户:%s,用券量:%s,用券金额:%s,时间:%s-%s)", resp.getMemberId(), resp.getCount(), resp.getAmount(), resp.getStartTime(), resp.getEndTime()));
        return sb.toString();
    }
}