package com.ly.car.risk.process.service.rule.hcGroup;

import com.ly.car.risk.process.service.DriverHistoryService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.HitchBlackSendData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class HcDriverHistoryService extends FilterHcAroundHandler {

    @Resource
    private DriverHistoryService driverHistoryService;
    @Resource(name = "hitchRiskProducer")
    private MqRiskProducer mqRiskProducer;

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {
        //doSomething
    }

    @Override
    public void doHandler(FilterHcContext context) {
        //对车牌进行加密
        Integer count = driverHistoryService.queryDriver(DigestUtils.md5Hex(context.getPlate()));
        log.info("[HcDriverHistoryService][][][]司机认证查询完单司机库:{}",count);
        if(count > 0){
            RiskResultNewDTO resultDTO = (RiskResultNewDTO) context.getUiResult().getData();
            HitchBlackSendData data = new HitchBlackSendData();
            data.setDriverId(context.getDriverId());
            resultDTO.setObj(data);
            mqRiskProducer.send(MqTagEnum.car_risk_hitch_notify, JsonUtils.json(resultDTO),0L);
            return;
        }
        if (this.nextHandler != null) {
            this.nextHandler.doHandler(context);
        }
    }
}
