package com.ly.car.risk.process.api.param;

import lombok.Data;

import java.util.List;

@Data
public class PublicEncryptParam {

    private String app = "car.java.risk.process";
    private String securityKey = "73c89bf1d5a0ae6288471a7a0c74ec94";
    private List<EncryptChildParam> data;
    private String jobNumber = "1207024";

    @Data
    public static class EncryptChildParam{
        private Integer encryptType;
        private String data;
    }
}
