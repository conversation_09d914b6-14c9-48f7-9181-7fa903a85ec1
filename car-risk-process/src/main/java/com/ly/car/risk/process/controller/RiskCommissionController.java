package com.ly.car.risk.process.controller;

import com.ly.car.risk.entity.DistributionOfflineRisk;
import com.ly.car.risk.process.controller.params.JobReq;
import com.ly.car.risk.process.repo.risk.mapper.DistributionOfflineRiskMapper;
import com.ly.car.risk.process.service.RiskCommissionService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/distribute/risk")
public class RiskCommissionController {

    @Resource
    private RiskCommissionService riskCommissionService;

    @Resource
    private DistributionOfflineRiskMapper distributionOfflineRiskMapper;

    @RequestMapping("/commission")
    public String saveCommission(@RequestBody JobReq req){
        riskCommissionService.action(req.getOrderIds());
        return "success";
    }

    @RequestMapping("/insertRisk")
    public String insertRisk(@RequestBody DistributionOfflineRisk risk){
        distributionOfflineRiskMapper.insert(risk);
        return "success";
    }
}
