package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.constants.MainSceneEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.repo.data.SfcOrderRiskData;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskDataMapper;
import com.ly.car.risk.process.service.context.SaveRiskManageContext;
import com.ly.car.risk.process.service.dto.SfcRiskRuleConfig;
import com.ly.car.risk.process.service.dto.task.MtOrderSubsidyDTO;
import com.ly.car.risk.process.service.dto.task.MtSubsidyConfig;
import com.ly.car.risk.process.service.dto.task.MtTaskOrderInfo;
import com.ly.car.risk.process.utils.AmountUtil;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SfcRiskService {

    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private RiskRuleService riskRuleService;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private RiskDataMapper     riskDataMapper;

    public void sfcRuleRisk(CommonParams commonParams){
        Date now = new Date();
        Date date = DateUtil.addDay(now, -1);
        List<SfcOrderRiskData> riskDataList;
        List<SfcOrderRiskData> newRiskDataList;
        SfcRiskRuleConfig configJson = getConfigJson();
        if(StringUtils.isNotBlank(commonParams.getStartTime())){
            //同步历史数据
            //开始对时间区间进行分组
            Date startDate = DateUtil.string2Date(commonParams.getStartTime(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            Date endDate = DateUtil.string2Date(commonParams.getEndTime(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            while (!startDate.after(endDate)){
                log.info("[][][][]当前循环日期:{}",startDate);
                String startDateTime = DateUtil.date2String(startDate).split(" ")[0] + " 00:00:00";
                String endDateTime = DateUtil.date2String(startDate).split(" ")[0]+" 23:59:59";
                log.info("[][][][]当前循环查询订单日期:{},{}",startDateTime,endDateTime);
                riskDataList = sfcOrderMapper.querySfcRisk(DateUtil.string2Date(startDateTime),DateUtil.string2Date(endDateTime));
                newRiskDataList = riskDataMapper.queryNewSfcRisk(DateUtil.string2Date(startDateTime),DateUtil.string2Date(endDateTime));
                riskDataList.addAll(newRiskDataList);
                log.info("[][][][]当前循环查询订单:{}",riskDataList.size());
                if(CollectionUtils.isEmpty(riskDataList)){
                    return;
                }
                setManage(riskDataList,configJson,now);
                startDate = DateUtil.addDay(startDate,1);
                log.info("[][][][]当前循环查询订单加一天:{}",DateUtil.date2String(startDate));
            }
        } else {
            riskDataList = sfcOrderMapper.querySfcRisk(date,now);
            newRiskDataList = riskDataMapper.queryNewSfcRisk(date,now);
            riskDataList.addAll(newRiskDataList);
            setManage(riskDataList,configJson,now);
        }
    }

    public static void main(String[] args) {
        Date startDate = DateUtil.string2Date("2022-11-10",DateUtil.DATE_PATTERN_YYYY_MM_DD);
        Date endDate = DateUtil.string2Date("2022-11-15",DateUtil.DATE_PATTERN_YYYY_MM_DD);
        while (!startDate.after(endDate)){
            System.out.println("当前日期："+DateUtil.date2String(startDate));
            startDate = DateUtil.addDay(startDate,1);
        }
    }

    public void setManage(List<SfcOrderRiskData> riskDataList,SfcRiskRuleConfig configJson,Date now){
        //获取当天0点时间
        String zeroDate = DateUtil.date2String(now,DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 00:00:00";
        //获取近一小时时间
        Date hourDate = DateUtil.addHour(now,-1);
        //当天完单
        List<SfcOrderRiskData> zeroOrderList = riskDataList.stream().
                filter(order->order.getFinishTime().after(DateUtil.string2Date(zeroDate))).collect(Collectors.toList());
        //近一小时完单
        List<SfcOrderRiskData> hourOrderList = riskDataList.stream().
                filter(order->order.getFinishTime().after(hourDate)).collect(Collectors.toList());
        //对订单以用户维度分组
        Map<Long,List<SfcOrderRiskData>> allSfcOrderMap = riskDataList.stream()
                .filter(e->e.getMemberId() != null && e.getMemberId() > 0)
                .collect(Collectors.groupingBy(risk-> risk.getMemberId()));
        Map<Long,List<SfcOrderRiskData>> zeroSfcOrderMap = zeroOrderList.stream()
                .filter(e->e.getMemberId() != null && e.getMemberId() > 0)
                .collect(Collectors.groupingBy(risk-> risk.getMemberId()));
        Map<Long,List<SfcOrderRiskData>> hourSfcOrderMap = hourOrderList.stream()
                .filter(e->e.getMemberId() != null && e.getMemberId() > 0)
                .collect(Collectors.groupingBy(risk->risk.getMemberId()));
        //给个池子放命中信息管理
        Map<String, DistributionRiskManage> distributionRiskManageMap = new HashMap<>();
        if(riskRuleService.isEnable("015")){
            //对当天订单和24小时订单排序且计算时间间隔
            for(Map.Entry<Long,List<SfcOrderRiskData>> entry : allSfcOrderMap.entrySet()){
                //对当天订单进行排序，有一组是小于6分钟的间隔
                List<SfcOrderRiskData> sortAllList = entry.getValue().stream().sorted(
                        Comparator.comparing(SfcOrderRiskData::getFinishTime,Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList()
                );

                for(int i = 0;i<sortAllList.size()-1;i++){
                    if(sortAllList.get(i).getFinishTime().before(DateUtil.addMinute(sortAllList.get(i+1).getFinishTime(),configJson.getTime015()))){
                        //因为是倒序，所以当前完单与下一个单子加6分钟后还是在后面，说明不满足条件,在前面了说明是满足小于6分钟的
                        //这个时候要再判断下近24H内间隔6分钟内的订单
                        Set<String> stringSet = new HashSet<>();
                        for(SfcOrderRiskData beforeData : sortAllList){
                            for(SfcOrderRiskData afterData : sortAllList){
                                if(beforeData.getOrderId().equals(afterData.getOrderId())){
                                    continue;
                                }
                                //大于6分钟或者小于6分钟,后面写个工具类计算时间差
                                if(beforeData.getFinishTime().after(afterData.getFinishTime())){
                                    if(beforeData.getFinishTime().before(DateUtil.addMinute(afterData.getFinishTime(),configJson.getTime015()))){
                                        stringSet.add(beforeData.getOrderId());
                                        stringSet.add(afterData.getOrderId());
                                    }
                                } else {
                                    if(beforeData.getFinishTime().after(DateUtil.addMinute(afterData.getFinishTime(),-configJson.getTime015()))){
                                        stringSet.add(beforeData.getOrderId());
                                        stringSet.add(afterData.getOrderId());
                                    }
                                }
                            }
                        }
                        if(stringSet.size() >= configJson.getOrderNum015()){
                            stringSet.forEach(orderId->{
                                List<String> linkOrderIdList = stringSet.stream()
                                        .filter(e-> !orderId.equals(e))
                                        .collect(Collectors.toList());
                                if(distributionRiskManageMap.get(orderId) == null){
                                    DistributionRiskManage manage = new DistributionRiskManage();
                                    manage.setOrderId(orderId);
                                    manage.setLinkOrder(StringUtils.join(linkOrderIdList,","));
                                    manage.setRuleNoList("015");
                                    distributionRiskManageMap.put(manage.getOrderId(),manage);
                                } else {
                                    DistributionRiskManage manage = distributionRiskManageMap.get(orderId);
                                    manage.setRuleNoList(manage.getRuleNoList()+",015");
                                    manage.setLinkOrder(manage.getLinkOrder()+","+StringUtils.join(linkOrderIdList,","));
                                    distributionRiskManageMap.put(manage.getOrderId(),manage);
                                }
                            });
                        }
                        //只要有一个命中的,跳出本次循，进行下个用户的分析
                        if(stringSet.size() >= 3){
                            break;
                        }
                    }
                }
            }
        }
        if(riskRuleService.isEnable("034")){
            for(Map.Entry<Long,List<SfcOrderRiskData>> entry : allSfcOrderMap.entrySet()){
                if(entry.getValue().size() >= configJson.getOrderNum034()){
                    entry.getValue().forEach(order->{
                        List<String> linkOrderIdList = entry.getValue().stream()
                                .map(SfcOrderRiskData::getOrderId)
                                .filter(e-> !order.getOrderId().equals(e))
                                .collect(Collectors.toList());
                        if(distributionRiskManageMap.get(order.getOrderId()) == null){
                            DistributionRiskManage manage = new DistributionRiskManage();
                            manage.setOrderId(order.getOrderId());
                            manage.setLinkOrder(StringUtils.join(linkOrderIdList,","));
                            manage.setRuleNoList("034");
                            distributionRiskManageMap.put(manage.getOrderId(),manage);
                        } else {
                            DistributionRiskManage manage = distributionRiskManageMap.get(order.getOrderId());
                            manage.setRuleNoList(manage.getRuleNoList()+",034");
                            manage.setLinkOrder(manage.getLinkOrder()+","+StringUtils.join(linkOrderIdList,","));
                            distributionRiskManageMap.put(manage.getOrderId(),manage);
                        }
                    });
                }
            }
        }

        if(riskRuleService.isEnable("035")){
            for(Map.Entry<Long,List<SfcOrderRiskData>> entry : allSfcOrderMap.entrySet()){
                if(entry.getKey().equals(310427169)){
                    log.info("[][][][]当前用户step1:{}",JsonUtils.json(entry));
                }
                if(entry.getValue().size() >= configJson.getOrderNum035()){
                    Set<String> stringSet = new HashSet<>();
                    for(SfcOrderRiskData beforeData : entry.getValue()){
                        for(SfcOrderRiskData afterData : entry.getValue()){
                            if(beforeData.getOrderId().equals(afterData.getOrderId())){
                                continue;
                            }
                            //计算出发点与到达点距离
                            double distanceEnd = CoordUtil.getDistance(String.valueOf(beforeData.getEndLng()),String.valueOf(beforeData.getEndLat()),
                                    String.valueOf(afterData.getEndLng()),String.valueOf(afterData.getEndLat()));
                            double distanceStart = CoordUtil.getDistance(String.valueOf(beforeData.getStartLng()),String.valueOf(beforeData.getStartLat()),
                                    String.valueOf(afterData.getStartLng()),String.valueOf(afterData.getStartLat()));

                            if(new BigDecimal(distanceEnd).compareTo(new BigDecimal(configJson.getDistance035())) < 0 &&
                                    new BigDecimal(distanceStart).compareTo(new BigDecimal(configJson.getDistance035())) < 0){
                                stringSet.add(beforeData.getOrderId());
                                stringSet.add(afterData.getOrderId());
                            }
                        }
                    }
                    if(entry.getKey().equals(310427169)){
                        log.info("[][][][]当前用户step2:{}",JsonUtils.json(stringSet));
                        log.info("[][][][]当前用户step3:{}",new BigDecimal(stringSet.size()).
                                divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING));
                    }
                    if(stringSet.size() > 0 && new BigDecimal(stringSet.size()).
                            divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING).
                            compareTo(configJson.getRate035()) > 0 ){
                        entry.getValue().forEach(order->{
                            List<String> linkOrderIdList = entry.getValue().stream()
                                    .map(SfcOrderRiskData::getOrderId)
                                    .filter(e-> !order.getOrderId().equals(e))
                                    .collect(Collectors.toList());
                            if(distributionRiskManageMap.get(order.getOrderId()) == null){
                                DistributionRiskManage manage = new DistributionRiskManage();
                                manage.setOrderId(order.getOrderId());
                                manage.setLinkOrder(StringUtils.join(linkOrderIdList,","));
                                manage.setRuleNoList("035");
                                distributionRiskManageMap.put(manage.getOrderId(),manage);
                            } else {
                                DistributionRiskManage manage = distributionRiskManageMap.get(order.getOrderId());
                                manage.setRuleNoList(manage.getRuleNoList()+",035");
                                manage.setLinkOrder(manage.getLinkOrder()+","+StringUtils.join(linkOrderIdList,","));
                                distributionRiskManageMap.put(manage.getOrderId(),manage);
                            }
                        });
                    }
                }
            }
        }

        if(riskRuleService.isEnable("036")){
            for(Map.Entry<Long,List<SfcOrderRiskData>> entry : hourSfcOrderMap.entrySet()){
                if(entry.getValue().size() >= configJson.getOrderNum036()){
                    entry.getValue().forEach(order->{
                        List<String> linkOrderIdList = entry.getValue().stream()
                                .map(SfcOrderRiskData::getOrderId)
                                .filter(e-> !order.getOrderId().equals(e))
                                .collect(Collectors.toList());
                        if(distributionRiskManageMap.get(order.getOrderId()) == null){
                            DistributionRiskManage manage = new DistributionRiskManage();
                            manage.setOrderId(order.getOrderId());
                            manage.setLinkOrder(StringUtils.join(linkOrderIdList,","));
                            manage.setRuleNoList("036");
                            distributionRiskManageMap.put(manage.getOrderId(),manage);
                        } else {
                            DistributionRiskManage manage = distributionRiskManageMap.get(order.getOrderId());
                            manage.setRuleNoList(manage.getRuleNoList()+",036");
                            manage.setLinkOrder(manage.getLinkOrder()+","+StringUtils.join(linkOrderIdList,","));
                            distributionRiskManageMap.put(manage.getOrderId(),manage);
                        }
                    });
                }
            }
        }
        if(riskRuleService.isEnable("037")){
            for(Map.Entry<Long,List<SfcOrderRiskData>> entry : allSfcOrderMap.entrySet()){
                if(entry.getValue().size() >= configJson.getOrderNum037()){
                    int kiloNumber = 0;
                    for(SfcOrderRiskData data : entry.getValue()){
                        if(data.getEstimateKilo().compareTo(new BigDecimal(configJson.getEsKilo037())) < 0){
                            kiloNumber = kiloNumber + 1;
                        }
                    }
                    if(new BigDecimal(kiloNumber).divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING).compareTo(configJson.getRate037()) > 0){
                        entry.getValue().forEach(order->{
                            List<String> linkOrderIdList = entry.getValue().stream()
                                    .map(SfcOrderRiskData::getOrderId)
                                    .filter(e-> !order.getOrderId().equals(e))
                                    .collect(Collectors.toList());
                            if(distributionRiskManageMap.get(order.getOrderId()) == null){
                                DistributionRiskManage manage = new DistributionRiskManage();
                                manage.setOrderId(order.getOrderId());
                                manage.setLinkOrder(StringUtils.join(linkOrderIdList,","));
                                manage.setRuleNoList("037");
                                distributionRiskManageMap.put(manage.getOrderId(),manage);
                            } else {
                                DistributionRiskManage manage = distributionRiskManageMap.get(order.getOrderId());
                                manage.setRuleNoList(manage.getRuleNoList()+",037");
                                manage.setLinkOrder(manage.getLinkOrder()+","+StringUtils.join(linkOrderIdList,","));
                                distributionRiskManageMap.put(manage.getOrderId(),manage);
                            }
                        });
                    }
                }

            }
        }
        for(Map.Entry<String,DistributionRiskManage> entry : distributionRiskManageMap.entrySet()){
            //对关联订单去个重
            if(StringUtils.isNotBlank(entry.getValue().getLinkOrder())){
                List<String> strList = Arrays.asList(entry.getValue().getLinkOrder().split(","));
                Set<String> noRepeat = new HashSet<>(strList);
                strList = new ArrayList<>(noRepeat);
                entry.getValue().setLinkOrder(StringUtils.join(strList,","));
            }
            log.info("[][][][]顺风车策略添加:{}", JsonUtils.json(entry));
            DistributionRiskManage manage = entry.getValue();
            SaveRiskManageContext context = new SaveRiskManageContext();
            context.setOrderId(manage.getOrderId());
            context.setRuleNoList(manage.getRuleNoList());
            context.setMainScene(MainSceneEnum.OFF_LINE.getCode());
            context.setChildScene(5);
            context.setLinkOrder(manage.getLinkOrder());
            context.setLevel(RiskLevelEnum.HIGH.getCode());
            context.setFlag(0);
            distributionRiskManageService.saveRiskManage(context);
//            RiskOrderManage riskOrderManage = new RiskOrderManage();
//            riskOrderManage.setRuleNo(manage.getRuleNoList());
//            riskOrderManage.setOrderId(manage.getOrderId());
//            riskOrderManageService.addRiskOrder(riskOrderManage);
        }
    }


    public SfcRiskRuleConfig getConfigJson(){
        try {
            String configJson = ConfigCenterClient.get("sfc_common_risk_rule");
            log.info("获取顺风车风控规则:"+configJson);
            SfcRiskRuleConfig sfcRiskRuleConfig = JSONObject.parseObject(configJson,SfcRiskRuleConfig.class);
            return sfcRiskRuleConfig;
        } catch (Exception e) {
            log.error("获取顺风车风控规则错误:",e);
        }
        return null;
    }

    public void mtSubsidyTask(){
        List<SfcOrder> sfcOrderList = this.sfcOrderMapper.selectList(new QueryWrapper<SfcOrder>()
            .between("finish_time", TimeUtil.oneDay(),new Date())
            .eq("status",300)
            .like("supplier_code","MadaSaas%")
        );

        List<MtTaskOrderInfo> newMtOrders = riskDataMapper.queryMt(TimeUtil.oneDay(), new Date());

        log.info("[][][][]萌艇B补策略查询老订单数{},新订单数",sfcOrderList.size(),newMtOrders.size());
        Map<String,SfcOrder> sfcOrderMap = sfcOrderList.stream().collect(Collectors.toMap(SfcOrder::getOrderId,v->v,(old,cur)->old));
        List<String> supplierOrderIds = sfcOrderList.stream().map(SfcOrder::getSupplierOrderId).collect(Collectors.toList());
        List<String> orderIds = sfcOrderList.stream().map(SfcOrder::getOrderId).collect(Collectors.toList());
        List<List<String>> splitSupplierOrderIds = Lists.partition(supplierOrderIds,500);
        List<SfcSupplierOrder> sfcSupplierOrders = new ArrayList<>();
        for(List<String> supplierList : splitSupplierOrderIds){
            List<SfcSupplierOrder> ids = this.sfcSupplierOrderMapper.selectList(new QueryWrapper<SfcSupplierOrder>()
                    .in("supplier_order_id", supplierList)
            );
            sfcSupplierOrders.addAll(ids);
        }
        Map<String,SfcSupplierOrder> sfcSupplierOrderMap = sfcSupplierOrders.stream()
                .collect(Collectors.toMap(SfcSupplierOrder::getOrderId,v->v,(old,cur)->old));

        List<OrderAddress> orderAddressList = new ArrayList<>();
        List<List<String>> splitOrderIds = Lists.partition(orderIds,500);
        for(List<String> sod : splitOrderIds){
            List<OrderAddress> byOrderIds = orderAddressMapper.findByOrderIds(sod);
            orderAddressList.addAll(byOrderIds);
        }
        Map<String,OrderAddress> orderAddressMap = orderAddressList.stream()
                .collect(Collectors.toMap(OrderAddress::getOrderId,v->v,(old,cur)->old));

        List<MtOrderSubsidyDTO> dtoList = new ArrayList<>();
        for(String orderId : orderIds){
            SfcOrder sfcOrder = sfcOrderMap.get(orderId);
            if(sfcOrder.getDistributorFlag() == 1){
                continue;
            }
            MtOrderSubsidyDTO dto = new MtOrderSubsidyDTO();
            SfcSupplierOrder supplierOrder = sfcSupplierOrderMap.get(orderId);
            if(supplierOrder == null){
                continue;
            }
            OrderAddress orderAddress = orderAddressMap.get(orderId);
            if(orderAddress == null){
                continue;
            }
            dto.setOrderId(orderId);
            dto.setSupplierOrderId(supplierOrder.getSupplierOrderId());
            dto.setEsKilo(orderAddress.getEstimateKilo());
            dto.setStartLng(orderAddress.getStartLng());
            dto.setStarLat(orderAddress.getStartLat());
            dto.setEndLng(orderAddress.getEndLng());
            dto.setEndLat(orderAddress.getEndLat());
            dto.setPlate(supplierOrder.getPlateNumber());
            dto.setMemberId(String.valueOf(sfcOrder.getMemberId()));
            dto.setTotalAmount(AmountUtil.changeF2Y(sfcOrder.getTotalAmount()));
            dtoList.add(dto);
        }
        List<MtOrderSubsidyDTO> newMtDTOs = newMtOrders.stream().map(p -> {
            MtOrderSubsidyDTO dto = new MtOrderSubsidyDTO();
            dto.setOrderId(p.getOrderId());
            dto.setSupplierOrderId(p.getSupplierOrderId());
            dto.setEsKilo(p.getEstimateKilo());
            dto.setStartLng(p.getStartLng());
            dto.setStarLat(p.getStartLat());
            dto.setEndLng(p.getEndLng());
            dto.setEndLat(p.getEndLat());
            dto.setPlate(p.getPlateNumber());
            dto.setMemberId(String.valueOf(p.getMemberId()));
            dto.setTotalAmount(p.getTotalAmount());
            return dto;
        }).collect(Collectors.toList());
        dtoList.addAll(newMtDTOs);

        Map<String,List<MtOrderSubsidyDTO>> subsidyGroupMap = dtoList.stream().collect(Collectors.groupingBy(MtOrderSubsidyDTO::getPlate));
        Map<String,RiskOrderManage> orderManageMap = new HashMap<>();
        List<String> virtualDriverList = driverList();
        MtSubsidyConfig mtSubsidyConfig = getConfig();
        log.info("[][][][]萌艇B补策略查询司机数{}",subsidyGroupMap.size());
        for(Map.Entry<String,List<MtOrderSubsidyDTO>> entry : subsidyGroupMap.entrySet()){
            List<MtOrderSubsidyDTO> values = entry.getValue();
            if(values.size() >= mtSubsidyConfig.getMt001OrderNum()){
                if(!virtualDriverList.contains(entry.getKey())){
                    List<MtOrderSubsidyDTO> filterValues = values.stream()
                            .filter(data->data.getEsKilo().compareTo(new BigDecimal(mtSubsidyConfig.getMt001Kilo())) < 0).collect(Collectors.toList());
                    if(filterValues.size() == values.size()){
                        for(MtOrderSubsidyDTO dto : filterValues){
                            RiskOrderManage riskOrderManage = new RiskOrderManage();
                            riskOrderManage.setIsRisk(1);
                            riskOrderManage.setRuleNo("mt-001");
                            riskOrderManage.setOrderId(dto.getOrderId());
                            RiskOrderManage haveManage = orderManageMap.get(dto.getOrderId());
                            if(haveManage == null){
                                orderManageMap.put(dto.getOrderId(),riskOrderManage);
                            } else {
                                haveManage.setRuleNo(haveManage.getRuleNo()+","+"mt-001");
                                orderManageMap.put(dto.getOrderId(),haveManage);
                            }
                        }

                    }
                }
            }
            if(entry.getValue().size() >= mtSubsidyConfig.getMt002OrderNum()){
                if(!virtualDriverList.contains(entry.getKey())){
                    //判断起始地和目的地都是在一定的范围
                    boolean flag = true;
                    for(int i=0;i< values.size()-1;i++){
                        double distance = CoordUtil.getDistance(values.get(i).getStartLng(), values.get(i).getStarLat(), values.get(i + 1).getStartLng(), values.get(i + 1).getStarLat());
                        if(new BigDecimal(distance).compareTo(new BigDecimal(mtSubsidyConfig.getMt002Distance())) > 0){
                            //有一个小于500米的就说明不满足规则
                            flag = false;
                            break;
                        }
                    }
                    if(flag){
                        for(MtOrderSubsidyDTO dto : values){
                            RiskOrderManage riskOrderManage = new RiskOrderManage();
                            riskOrderManage.setIsRisk(1);
                            riskOrderManage.setRuleNo("mt-002");
                            riskOrderManage.setOrderId(dto.getOrderId());
                            RiskOrderManage haveManage = orderManageMap.get(dto.getOrderId());
                            if(haveManage == null){
                                orderManageMap.put(dto.getOrderId(),riskOrderManage);
                            } else {
                                haveManage.setRuleNo(haveManage.getRuleNo()+","+"mt-002");
                                orderManageMap.put(dto.getOrderId(),haveManage);
                            }
                        }
                    }

                }
            }

            Map<String,List<MtOrderSubsidyDTO>> sameUserAndDriverGroup = values.stream().collect(Collectors.groupingBy(MtOrderSubsidyDTO::getMemberId));
            for(Map.Entry<String,List<MtOrderSubsidyDTO>> listEntry : sameUserAndDriverGroup.entrySet()){
                if(listEntry.getValue().size() >= mtSubsidyConfig.getMt002OrderNum()){
                    //判断全部金额是不是都小于M元
                    List<MtOrderSubsidyDTO> filterList = listEntry.getValue().stream()
                            .filter(data->data.getTotalAmount().compareTo(new BigDecimal(mtSubsidyConfig.getMt003Amount())) < 0)
                            .collect(Collectors.toList());
                    if(filterList.size() == listEntry.getValue().size()){
                        for(MtOrderSubsidyDTO dto : listEntry.getValue()){
                            RiskOrderManage riskOrderManage = new RiskOrderManage();
                            riskOrderManage.setIsRisk(1);
                            riskOrderManage.setRuleNo("mt-003");
                            riskOrderManage.setOrderId(dto.getOrderId());
                            RiskOrderManage haveManage = orderManageMap.get(dto.getOrderId());
                            if(haveManage == null){
                                orderManageMap.put(dto.getOrderId(),riskOrderManage);
                            } else {
                                haveManage.setRuleNo(haveManage.getRuleNo()+","+"mt-003");
                                orderManageMap.put(dto.getOrderId(),haveManage);
                            }
                        }
                    }
                }
            }

            for(Map.Entry<String,List<MtOrderSubsidyDTO>> listEntry : sameUserAndDriverGroup.entrySet()) {
                if (listEntry.getValue().size() >= 3) {
                    for(MtOrderSubsidyDTO dto : listEntry.getValue()){
                        RiskOrderManage riskOrderManage = new RiskOrderManage();
                        riskOrderManage.setIsRisk(1);
                        riskOrderManage.setRuleNo("mt-004");
                        riskOrderManage.setOrderId(dto.getOrderId());
                        RiskOrderManage haveManage = orderManageMap.get(dto.getOrderId());
                        if(haveManage == null){
                            orderManageMap.put(dto.getOrderId(),riskOrderManage);
                        } else {
                            haveManage.setRuleNo(haveManage.getRuleNo()+","+"mt-004");
                            orderManageMap.put(dto.getOrderId(),haveManage);
                        }
                    }
                }
            }
        }

        for(Map.Entry<String,RiskOrderManage> entry : orderManageMap.entrySet()){
            log.info("当前命中萌艇B补离线策略{}",JsonUtils.json(entry.getValue()));
            this.riskOrderManageService.addRiskOrder(entry.getValue());
        }

    }


    public List<String> driverList(){
        List<String> driverList = new ArrayList<>();
        try {
            String configJson = ConfigCenterClient.get("virtual_driver_list");
            log.info("获取车头列表:{}",configJson);
            if(StringUtils.isNotBlank(configJson)){
                driverList = Arrays.asList(configJson.split(","));
            }
        } catch (Exception e) {
        }
        return driverList;
    }

    public MtSubsidyConfig getConfig(){
        try {
            String configJson = ConfigCenterClient.get("mt_subsidy_config");
            MtSubsidyConfig config = JSONObject.parseObject(configJson,MtSubsidyConfig.class);
            return config;
        } catch (Exception e) {
        }
        return null;
    }



}
