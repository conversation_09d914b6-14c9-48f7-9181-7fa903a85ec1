package com.ly.car.risk.process.repo.risk.mapper.entity;

import lombok.Data;

/**
 * Description of RiskStrategy
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Data
public class OfflineRiskSceneStrategyDTO {
    // 策略id
    private Long strategyId;
    // 业务线
    private String productLines;
    // 策略编号
    private String strategyNo;
    // 风险类型 0-安全 1-风控 2-全部
    private int riskType;
    // 表达式
    private String expression;
    // 策略返回文案
    private String strategyWord;
    // 管控时间 单位天
    private int controlTime;
    // 管控对象 0-司机 1-用户
    private int controlType;
    // 命中字段
    private String hitField;
    // 命中动作0:加全局黑 1-加1v1
    private int hitAction;
    // 处置动作 0-禁止 1-增强校验 2-通过
    private int disposeAction;
    // 0-测试 1-上线运行 2-下线
    private int status;
    // 运行周期
    private String during;
    // 策略脚本
    private String script;
    // 供应商
    private String supplierCodes;
    // 城市
    private Integer cityId;
    // 渠道
    private String channels;
}