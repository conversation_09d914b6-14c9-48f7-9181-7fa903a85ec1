package com.ly.car.risk.process.service.context;

import com.ly.car.risk.entity.DistributionRiskManage;
import lombok.Data;

@Data
public class SaveRiskManageContext {

    private String orderId;
    private String ruleNoList;
    private Integer mainScene;
    private Integer childScene;
    private String linkOrder;
    private String hitValue;
    private Integer flag;
    private Integer level;
    private String payAccount;
    private String memberId;
    private String phone;//乘车人手机号
    private String userPhone;//用户手机号
    private String unionId;
    private String productLine;//业务先

    private DistributionRiskManage distributionRiskManage;
}
