package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.param.DriverLocationParams;
import com.ly.car.risk.process.api.rsp.DriverLocationRsp;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class CarOrderApi {

    private static final String DRIVER_LOCATION_URL = "sfcClient/driverLocation";
    private static final String SEND_SMS = "riskNotify/sendSms";

    @Value("${config.urls.sfcUrl}")
    private String sfcUrl;

    //获取司机位置
    public DriverLocationRsp queryLocation(DriverLocationParams params){
        String result = OkHttpClientUtil.getInstance()
                .post(sfcUrl + DRIVER_LOCATION_URL, JsonUtils.json(params), 5l, TimeUnit.SECONDS);
        if(result == null){
            return null;
        }
        DriverLocationRsp rsp = JSONObject.parseObject(result,DriverLocationRsp.class);
        return rsp;
    }

    public void sendSms(JSONObject jsonObject){
        OkHttpClientUtil.getInstance()
                .post(sfcUrl+SEND_SMS,JsonUtils.json(jsonObject),5l,TimeUnit.SECONDS);
    }
}
