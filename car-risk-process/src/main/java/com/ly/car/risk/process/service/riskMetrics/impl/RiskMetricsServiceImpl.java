package com.ly.car.risk.process.service.riskMetrics.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverAccountRecordMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverBillMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverInfoMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtRideOrderInfoMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderCouponMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService;
import com.ly.car.risk.process.strategy.RiskStrategyHandler;
import com.ly.car.risk.process.utils.CoordUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.catalina.util.ServerInfo;
import org.springframework.stereotype.Service;

/**
 * Description of RiskMetricsServiceImpl
 *
 * <AUTHOR>
 * @date 2024/6/18
 * @desc
 */
@Service(value = "riskMetricsService")
public class RiskMetricsServiceImpl implements RiskMetricsService {

    @Resource
    private RiskStrategyHandler riskStrategyHandler;

    @Resource
    private CarRiskOrderDetailMapper orderDetailMapper;

    @Resource
    private CarRiskOrderCouponMapper couponMapper;
    
    @Resource
    private CarMtDriverInfoMapper carMtDriverInfoMapper;
    
    @Resource
    private CarMtRideOrderInfoMapper carMtRideOrderInfoMapper;
    
    @Resource
    private CarMtDriverAccountRecordMapper carMtDriverAccountRecordMapper;
    
    @Resource
    private CarMtDriverBillMapper carMtDriverBillMapper;

    @Resource
    private SensitiveRecordMapper sensitiveRecordMapper;

    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;

    @Override
    public void cleanRiskMetricOldData() {
        orderDetailMapper.cleanRiskMetricOldData();
    }

    @Override
    public void cleanRiskMetricOldCouponData() {
        couponMapper.cleanRiskMetricOldCouponData();
    }

    @Override
    public List<Map<String, Object>> executeSql(String sql) {
        return orderDetailMapper.executeSql(sql);
    }

    @Override
    public List<CarRiskOrderDetail> user1HourFinishOrder(String memberId,String productLine) {
        String methodName = "user1HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser1HourFinishOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user24HourFinishOrder(String memberId,String productLine) {
        String methodName = "user24HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser24HourFinishOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car1HourFinishOrder(String carNum,String productLine) {
        String methodName = "car1HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar1HourFinishOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car24HourFinishOrder(String carNum,String productLine) {
        String methodName = "car24HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar24HourFinishOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user24HourTotalOrder(String memberId,String productLine) {
        String methodName = "user24HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser24HourAllOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car24HourTotalOrder(String carNum, String productLine) {
        String methodName = "car24HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar24HourTotalOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car1HourTotalOrder(String carNum, String productLine) {
        String methodName = "car1HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar1HourTotalOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user1HourTotalOrder(String memberId, String productLine) {
        String methodName = "user1HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser1HourAllOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user30MinTotalOrder(String memberId, String productLine) {
        String methodName = "user30MinTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser30MinTotalOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> phone24HourFinishOrder(String phone, String productLine) {
        String methodName = "phone24HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryPhone24HourFinishOrder(phone,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> phone24HourTotalOrder(String phone, String productLine) {
        String methodName = "phone24HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryPhone24HourTotalOrder(phone,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }


    @Override
    public List<CarRiskOrderDetail> user30MinCancelOrder(String memberId, String productLine) {
        String methodName = "user30MinCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser30MinCancelOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user1HourCancelOrder(String memberId, String productLine) {
        String methodName = "user1HourCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser1HourCancelOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user24HourCancelOrder(String memberId, String productLine) {
        String methodName = "user24HourCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser24HourCancelOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }


    @Override
    public List<CarRiskOrderDetail> phone24HourCancelOrder(String phone, String productLine) {
        String methodName = "phone24HourCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryPhone24HourCancelOrder(phone,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car24HourFinishRightsOrder(String carNum, String productLine) {
        String methodName = "car24HourFinishRightsOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.car24HourFinishRightsOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前设备近24小时注册司机账户数量
     */
    @Override
    public List<CarRiskOrderDetail> driver24HourRegisterCountOnSameDevice(String deviceId) {
        String methodName = "mtDriver24HourRegisterCountOnSameDevice";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverInfoMapper.driver24HourRegisterCountOnSameDevice(deviceId);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前设备近24小时登录司机账户数量
     */
    @Override
    public List<CarRiskOrderDetail> driver24HourLoginCountOnSameDevice(String deviceId) {
        String methodName = "mtDriver24HourLoginCountOnSameDevice";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverAccountRecordMapper.driver24HourLoginCountOnSameDevice(deviceId);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前设备近24小时关联提现司机账户数量
     */
//    @Override
//    public List<CarRiskOrderDetail> driver24HourWithdrawCountSameDevice(String deviceId) {
//        String methodName = "driver24HourWithdrawCountSameDevice";
//        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
//        if (null != orderList) {
//            return orderList;
//        }
//        orderList = driverWithdrawalRecordMapper.driver24HourWithdrawCountSameDevice(deviceId);
//        riskStrategyHandler.setLocal(methodName,orderList);
//        return orderList;
//    }
    
    /**
     * 萌艇-当前设备近24小时关联提现金额
     */
//    @Override
//    public List<CarRiskOrderDetail> driver24HourWithdrawAmountSameDevice(String deviceId) {
//        String methodName = "driver24HourWithdrawAmountSameDevice";
//        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
//        if (null != orderList) {
//            return orderList;
//        }
//        orderList = driverWithdrawalRecordMapper.driver24HourWithdrawAmountSameDevice(deviceId);
//        riskStrategyHandler.setLocal(methodName,orderList);
//        return orderList;
//    }
    
    /**
     * 萌艇-当前设备敏感时间注册司机账户数量（手机号）0-5
     */
    @Override
    public List<CarRiskOrderDetail> driver05RegisterSameDevice(String deviceId) {
        String methodName = "mtDriver05RegisterSameDevice";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverInfoMapper.driver05RegisterSameDevice(deviceId);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前司机近24小时获取补贴订单数
     */
    @Override
    public List<CarRiskOrderDetail> driver24HourBillOrderCount(String carNum) {
        String methodName = "mtDriver24HourBillOrderCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverBillMapper.driver24HourBillOrderCount(carNum);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前近24小时司机完单小于6分钟的订单数
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourFinishOrderLess6min(String carNum) {
        String methodName = "mtDriver24hourFinishOrderLess6min";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtRideOrderInfoMapper.driver24hourFinishOrderLess6min(carNum);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 当前近24小时司机获取补贴、奖励金额
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourBillOrderAmount(String carNum) {
        String methodName = "driver24hourBillOrderAmount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverBillMapper.driver24hourBillOrderAmount(carNum);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时取消单个供应商名称的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourCancelSupplierOrderCount(String memberId, String productLine) {
        String methodName = "user24hourCancelSupplierOrderCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        Map<String, List<CarRiskOrderDetail>> map = orderDetailMapper.queryUser24hourCancelSupplierOrderCount(memberId, productLine)
                .stream()
                .collect(Collectors.groupingBy(CarRiskOrderDetail::getSupplierCode));
        
        orderList = map.values().stream().max(Comparator.comparing(List::size)).orElse(Collections.emptyList());
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当天用户订单完单时间与上笔订单完单时间间隔小于6分钟的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishNearOrderLess6MinCount(String memberId, String productLine) {
        String methodName = "user24hourFinishNearOrderLess6MinCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = new ArrayList<>();
        
        List<CarRiskOrderDetail> list = orderDetailMapper.queryUser24hourFinishNearOrderLess6MinCount(memberId, productLine);
        
        for (int i = 1; i < list.size(); i++) {
            CarRiskOrderDetail before = list.get(i - 1);
            CarRiskOrderDetail cur = list.get(i);
            if (DateUtil.between(before.getGmtTripFinished(), cur.getGmtTripFinished(), DateUnit.MINUTE) < 6) {
                orderList.add(cur);
            }
        }
        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时同设备号完单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishOrderSameDeviceCount(String memberId, String productLine) {
        String methodName = "user24hourFinishOrderSameDeviceCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        Map<String, List<CarRiskOrderDetail>> map = orderDetailMapper.queryUser24hourFinishOrderSameDeviceCount(memberId, productLine)
                .stream()
                .collect(Collectors.groupingBy(CarRiskOrderDetail::getDeviceId));
        
        orderList = map.values().stream().max(Comparator.comparing(List::size)).orElse(Collections.emptyList());
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时订单的起点与上笔订单的终点距离小于500米的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishNearOrderLess500mCount(String memberId, String productLine) {
        String methodName = "user24hourFinishNearOrderLess500mCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = new ArrayList<>();
        
        List<CarRiskOrderDetail> list = orderDetailMapper.queryUser24hourFinishNearOrderLess500mCount(memberId, productLine);
        
        for (int i = 1; i < list.size(); i++) {
            CarRiskOrderDetail before = list.get(i - 1);
            CarRiskOrderDetail cur = list.get(i);
            if (CoordUtil.getDistance(before.getArrLon(), before.getArrLat(), cur.getDepLon(), cur.getDepLat()) < 500) {
                orderList.add(cur);
            }
        }
        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时完单小于2公里的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishOrderLess2kmCount(String memberId, String productLine) {
        String methodName = "user24hourFinishOrderLess2kmCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = orderDetailMapper.queryUser24hourFinishOrderLess2kmCount(memberId, productLine);
        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前司机近24小时完单时间小于6分钟的订单
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourFinishOrderLess6minCount(String carNum, String productLine) {
        String methodName = "driver24hourFinishOrderLess6minCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = orderDetailMapper.queryDriver24hourFinishOrderLess6minCount(carNum, productLine);

        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前司机近24小时完单时间小于6分钟的订单
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourFinishOrderCount(String carNum, String productLine) {
        String methodName = "driver24hourFinishOrderCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = orderDetailMapper.queryDriver24hourFinishOrderCount(carNum, productLine);
        

        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }

    /**
     * 订单24小时
     */
    @Override
    public List<SensitiveRecord> order24hourSensitiveMatchRecord(String orderId) {
        List<SensitiveRecord> recordList  = sensitiveRecordMapper.queryOrder24hourSensitiveMatchRecord(orderId);
        return recordList;
    }

    /**
     * 当前订单手机号指定时间内待补款订单
     *
     * @param passengerPhone 订单手机号
     * @param startTime    行程结束时间范围-开始
     * @param endTime      行程结束时间范围-结束
     * @return 订单
     */
    @Override
    public List<CarRiskOrderDetail> phonePendingOrderNum(String passengerPhone, List<String> productLineList, Date startTime, Date endTime) {
        String methodName = "phonePendingOrderNum";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }

        orderList = orderDetailMapper.queryPhoneSupplementaryAmountOrderCount(passengerPhone,productLineList, startTime, endTime);

        riskStrategyHandler.setLocal(methodName, orderList);

        return orderList;
    }

    @Override
    public BigDecimal phonePendingOrderAmount(String passengerPhone, List<String> productLineList, Date startTime, Date endTime) {
        String methodName = "phonePendingOrderAmount";

        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return new BigDecimal(object.toString());
        }

        BigDecimal  amount = orderDetailMapper.queryPhoneOrderSupplementaryAmount(passengerPhone,productLineList, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, amount);

        return amount;
    }

    @Override
    public long phonePendingDriverOrderCount(String passengerPhone, List<String> productLineList, Date startTime, Date endTime) {
        String methodName = "phonePendingDriverOrderCount";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Long.parseLong(object.toString());
        }

        long count = orderDetailMapper.queryPhonePendingDriverOrderCount(passengerPhone,productLineList, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, count);

        return count;
    }

    /**
     * 司机指定时间内命中规则次数
     *
     * @param ruleNo    规则编号
     * @param startTime 行程结束时间范围-开始
     * @param endTime   行程结束时间范围-结束
     * @return 命中规则次数
     */
    @Override
    public long driverFitRuleNum(String carNum,String ruleNo, Date startTime, Date endTime) {
        String methodName = "driverFitRuleNum";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Long.parseLong(object.toString());
        }

        long count = riskOrderManageMapper.queryDriverFitRuleCount(carNum, ruleNo, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, count);
        return count;
    }

    /**
     * 司机单月添加附加费订单数
     */
    @Override
    public List<CarRiskOrderDetail> driverSurchargeMonthNum(String carNum, Integer orderNum, List<String> productLineList) {
        String methodName = "driverSurchargeMonthNum";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        Date lastMonth = DateUtil.lastMonth();
        Date startTime = DateUtil.beginOfMonth(lastMonth);
        Date endTime = DateUtil.endOfMonth(lastMonth);
        orderList = orderDetailMapper.driverSurchargeMonthNum(carNum, productLineList, startTime, endTime);
        orderList = orderList.stream().skip(orderNum).collect(Collectors.toList());
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }

    /**
     *  司机每月（上个自然月）有责风险单数量
     *
     * @param startTime 行程结束时间范围-开始
     * @param endTime   行程结束时间范围-结束
     * @return 命中规则次数
     */
    @Override
    public long driverResponsibleOrderCount(String carNum, Date startTime, Date endTime) {
        String methodName = "driverResponsibleOrderCount";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Long.parseLong(object.toString());
        }

        long count = riskOrderManageMapper.queryDriverResponsibleOrderCount(carNum, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, count);
        return count;
    }
    
    /**
     * 当前订单附加费金额＞预估金额X%
     */
    @Override
    public List<CarRiskOrderDetail> driverSurchargeGtBookRate(CarOrderDetail orderDetail) {
        String methodName = "driverSurchargeGtBookRate";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        if (null == orderDetail || null == orderDetail.getBaseInfo()) {
            return Collections.emptyList();
        }
        
        CarRiskOrderDetail order = orderDetailMapper.getByOrderSerialNo(orderDetail.getOrderId());
        if (null == order) {
            return Collections.emptyList();
        }
        
        order.setSurcharge(orderDetail.getBaseInfo().getSurcharge());
                
                orderList = Collections.singletonList(order);
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }

    public static void main(String[] args) {
        System.out.println("Tomcat Version: " + ServerInfo.getServerInfo());
    }
}