package com.ly.car.risk.process.controller;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.request.DriverBlackFromManageRequest;
import com.ly.car.risk.process.service.BlackListService;
import com.ly.car.risk.process.support.UiResultWrapper;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后台-黑名单操作
 * <AUTHOR>
 * @since 2024/3/5 14:42
 **/
@RequestMapping("/manage/blackList")
@RestController
public class BlackListController {

    @Resource
    private BlackListService service;

    /**
     * 客服拉黑司机
     * riskType：1v1、全部
     */
    @RequestMapping("/driver/black")
    public UiResultWrapper blackDriver(@RequestBody DriverBlackFromManageRequest request) {
        return UiResultWrapper.convert(service.blackDriverFromManage(request));
    }
}
