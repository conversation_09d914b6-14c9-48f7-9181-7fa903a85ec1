package com.ly.car.risk.process.service.ability;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.common.enums.VoiceApiProviderEnum;
import com.ly.car.risk.common.enums.VoiceProductTypeEnum;
import com.ly.car.risk.process.api.AutoCallApi;
import com.ly.car.risk.process.api.rsp.CallRecordRsp;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice.Ext;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.AutoCallRecord;
import com.ly.car.risk.process.service.AutoCallRecordService;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.workOrder.dto.FormItems;
import com.ly.car.risk.process.service.workOrder.dto.OrderWorkDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AutoCallService {

    @Resource
    private AutoCallApi autoCallApi;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer mqRiskProducer;
    @Resource
    private AutoCallRecordService autoCallRecordService;
    @Resource
    private UrlsProperties        urlsProperties;
    @Resource
    private RiskChargeVoiceMapper riskChargeVoiceMapper;
    @Resource
    private CarOrderService carOrderService;


    public String sendCall(String orderId, String callCode, String phone, Map<String,String> paramMap){
        log.info("[][][][]发送外呼{},code={},phone={}",orderId,callCode,phone);
        RBucket<String> bucket = redissonClient.getBucket("im:notify:repeat:" + phone+":"+callCode+":"+orderId);
        if(bucket.isExists() && StringUtils.isNotBlank(bucket.get())){
            log.info("[][][][]已发送过外呼{},code={},phone={}",orderId,callCode,phone);
            return null;
        }
        String callId = autoCallApi.callAction(phone, callCode,paramMap);
        if(StringUtils.isBlank(callId)){
            log.info("[][][{}][{}]发送外呼错误{}",phone,callCode,orderId);
            return null;
        }
        //成功发送mq 5分钟后检查
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId",orderId);
        jsonObject.put("callId",callId);
        mqRiskProducer.send(MqTagEnum.car_risk_self_auto_call_check_task,JsonUtils.json(jsonObject), DateUtil.addMinute(new Date(),5).getTime());
        //持久化记录
        AutoCallRecord autoCallRecord = new AutoCallRecord();
        autoCallRecord.setOrderId(orderId);
        autoCallRecord.setCallId(callId);
        autoCallRecord.setMobile(phone);
        autoCallRecord.setCallCode(callCode);
        autoCallRecord.setUserPhone(phone);
        autoCallRecord.setCreateTime(new Date());
        autoCallRecordService.insertRecord(autoCallRecord);
        bucket.set("1",1,TimeUnit.DAYS);
        return callId;
    }

    public Boolean checkAutoCall(String callId,String orderId){
        AutoCallRecord record = autoCallRecordService.getEntity(orderId, callId);
        if(record == null){
            LoggerUtils.info(log,"未查询到外呼记录，不再处理。callId:{},orderId",callId,orderId);
            return true;
        }
        CallRecordRsp callRecord = autoCallApi.getCallRecord(callId);
        log.info("[][][][{}]自动外呼检查结果{}", callId,JsonUtils.json(callRecord));
        
        // 保存调用统计
        RiskChargeVoice riskChargeVoice = build(orderId, callId);
        
        if(callRecord != null && callRecord.getData() != null){
            record.setRequestTime(callRecord.getData().getRequestTime());
            record.setCallTime(callRecord.getData().getCallTime());
            record.setConnectTime(callRecord.getData().getConnectTime());
            record.setTalkTimeSpan(callRecord.getData().getTalkTimeSpan());
            record.setIsConnect(callRecord.getData().getIsConnect());
            record.setUpdateTime(new Date());
            this.autoCallRecordService.updateRecord(record);
            
            if (callRecord.getData().getTalkTimeSpan() != null) {
                riskChargeVoice.setVoiceDuring(BigDecimal.valueOf(callRecord.getData().getTalkTimeSpan()));
                BigDecimal minutes = riskChargeVoice.getVoiceDuring().divide(new BigDecimal("60"), 0, RoundingMode.CEILING);
                riskChargeVoice.setCharge(minutes.multiply(new BigDecimal("0.07")).setScale(6, RoundingMode.HALF_UP));
            }
            
            if(CollectionUtils.isNotEmpty(callRecord.getData().getFeedBackKeyArray()) && record.getCallCode().equals("AO_117_1692337222554")){
                //生成安全工单
                try {
                    log.info("[][][][]查询外呼结果用户按键值{}",JsonUtils.json(callRecord));
                    List<FormItems> formItemList = new ArrayList<>();
                    formItemList.add(FormItems.builder().ctrlCode("level").valueCode("hurry").build());
                    formItemList.add(FormItems.builder().ctrlCode("orderId").value(record.getOrderId()).build());
                    OrderWorkDTO orderWorkDTO = OrderWorkDTO.builder()
                            .authTocken("car.java.risk.process")
                            .tempId("255")
                            .creatorId("风控系统")
                            .createBy("风控系统")
                            .formItems(formItemList)
                            .poid(record.getOrderId())
                            .build();
                    String workResult = OkHttpClientUtil.getInstance().post("http://tcwlservice.17usoft.com/workorder/managerapi/service/saveOrder", JsonUtils.json(orderWorkDTO), null, 1l);
                    log.info("[][][][]请求紧急工单系统{}",workResult);
                    //微信报警
                    JSONObject requestBody = new JSONObject();
                    requestBody.put("msgtype","text");
                    JSONObject contentMap = new JSONObject();
                    String allMsg = "订单号：" +record.getOrderId()+"\n"+
                                    "安全类型：对女性敏感时间外呼后用户寻求帮助(用户按'1')";
                    contentMap.put("content","当前报警信息:\n" + allMsg);
                    requestBody.put("text",contentMap);
                    String notifyUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a2051347-60d4-48ca-9fcb-ee7a866a2113";
                    String post = OkHttpClientUtil.getInstance().post(notifyUrl, requestBody.toJSONString(), null);
                    log.info("[][][][]请求企微机器人工单{}",post);
                } catch (Exception e){
                    log.error("[][][][]企微通知或工单生产错误",e);
                }
            }
        }
        
        riskChargeVoiceMapper.insert(riskChargeVoice);
        return callRecord.getIsSuccess() && callRecord.getData() != null && callRecord.getData().getIsConnect() > 0;
    }
    
    private RiskChargeVoice build(String orderId, String callId) {
        
        RiskChargeVoice riskChargeVoice = new RiskChargeVoice();
        riskChargeVoice.setOrderNo(orderId);
        riskChargeVoice.setApiProvider(VoiceApiProviderEnum.IVR_CALL.getCode());
        riskChargeVoice.setProductType(VoiceProductTypeEnum.OUTER_CALL.getCode());
        riskChargeVoice.setVoiceDuring(BigDecimal.ZERO);
        riskChargeVoice.setCharge(BigDecimal.ZERO);
        RiskChargeVoice.Ext ext = new Ext();
        ext.setReq(String.valueOf(callId));
        riskChargeVoice.setExt(JSON.toJSONString(ext));
        riskChargeVoice.setCreateTime(new Date());
        riskChargeVoice.setUpdateTime(new Date());
        
        if (OrderUtils.isNewOrder(orderId)) {
            CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
            if (null == orderDetail) {
                return riskChargeVoice;
            }
            
            riskChargeVoice.setSource(String.valueOf(orderDetail.getOrderChannel()));
            riskChargeVoice.setProductLine(orderDetail.getProductLine());
            
        } else {
            riskChargeVoice.setSource("");
            riskChargeVoice.setProductLine("");
        }
        
        return riskChargeVoice;
    }
    
    public void checkRecord(String callId,String orderId,String phone,Integer plateId){
        AutoCallRecord record = autoCallRecordService.getEntity(orderId, callId);
        if(record != null && StringUtils.isNotBlank(record.getCallTime()) && record.getIsConnect() != 0 && record.getTalkTimeSpan() == 0){
            LoggerUtils.info(log,"检查到外呼失败，准备开始发送短信。外呼记录:{}", JSON.toJSONString(record));
            //发送短信
            if(isMadaApp(plateId)){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("phoneNumber",phone);
                jsonObject.put("nodeId","170573");
                jsonObject.put("isHidePrefix",true);
                log.info("[][][][{}]外呼失败发送短信请求{}",orderId,JsonUtils.json(jsonObject));
                String post = OkHttpClientUtil.getInstance().post(urlsProperties.getSfcUrl() + "sms/callFailedMsg", JsonUtils.json(jsonObject), null);
                log.info("[][][][{}]外呼失败发送短信返回{}",orderId,post);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("phoneNumber",phone);
                jsonObject.put("nodeId","170550");
                log.info("[][][][{}]外呼失败发送短信请求{}",orderId,JsonUtils.json(jsonObject));
                String post = OkHttpClientUtil.getInstance().post(urlsProperties.getSfcUrl() + "sms/callFailedMsg", JsonUtils.json(jsonObject), null);
                log.info("[][][][{}]外呼失败发送短信返回{}",orderId,post);
            }
        }else{
            LoggerUtils.info(log,"外呼成功，不再额外进行短信发送");
        }
    }

    /**
     * 是否马达app
     * @return
     */
    public static boolean isMadaApp(Integer platId){
        return StringUtils.equalsAnyIgnoreCase(platId+"", "10178", "10177");
    }
}
