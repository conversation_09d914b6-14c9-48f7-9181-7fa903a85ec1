package com.ly.car.risk.process.service.groovy;

import java.math.BigDecimal;

public enum FieldType {

    STRING(1, "字符串", String.class),
    INTEGER(2, "整数", Integer.class),
    LONG(3, "长整数", Long.class),
    DECIMAL(4, "浮点数", BigDecimal.class);

    private int key;
    private String desc;

    private Class<?> clazz;

    FieldType(int key,String desc, Class<?> clazz) {
        this.key = key;
        this.desc = desc;
        this.clazz = clazz;
    }

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }
}
