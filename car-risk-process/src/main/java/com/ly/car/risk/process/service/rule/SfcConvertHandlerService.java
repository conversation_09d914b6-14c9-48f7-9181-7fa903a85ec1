package com.ly.car.risk.process.service.rule;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.component.DriverSlidingWindowCounter;
import com.ly.car.risk.process.component.OrderStatusSlidingWindowCounter;
import com.ly.car.risk.process.component.SfcRuleServiceContextUtil;
import com.ly.car.risk.process.component.SfcUserOrderNumWindowCounter;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.component.UserSlidingWindowCounter;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.params.RedisParam;
import com.ly.car.risk.process.controller.request.IsRiskUserRequest;
import com.ly.car.risk.process.controller.request.OrderAcceptCheckRequest;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RuleOnOffConfig;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.dto.SfcRiskRuleConfig;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.risk.process.service.rule.sfcNewGroup.FilterSfcHandler;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.AppProfile;
import com.ly.tcbase.config.ConfigCenterClient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SfcConvertHandlerService {

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private DriverSlidingWindowCounter driverSlidingWindowCounter;
    @Resource
    private UserSlidingWindowCounter userSlidingWindowCounter;
    @Resource
    private SfcUserOrderNumWindowCounter sfcUserOrderNumWindowCounter;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private OrderStatusSlidingWindowCounter orderStatusSlidingWindowCounter;
    @Resource
    private CarOrderService                 carOrderService;
    @Resource
    private OrderAddressMapper              orderAddressMapper;

    public List<FilterSfcHandler> getSfcHandlerList(Integer mainScene,Integer childScene){
        List<FilterSfcHandler> serviceNameList = new ArrayList<>();
        List<String> childSceneStr = new ArrayList<>();
        if(mainScene == null && childScene == null){
            return null;
        }
//        if(childScene == null){
//            //先取出主场景下所有子场景
//            childSceneStr.addAll(ChildSceneEnum.getAllChildStr(mainScene));
//        } else {
//            childSceneStr.add(mainScene+"-"+childScene);
//        }
        String str = mainScene+"-"+childScene;
        //名单类肯定放第一个
        List<String> mapService = SfcRuleServiceContextUtil.serviceNameMap.get(str);
        if(CollectionUtils.isEmpty(mapService)){
            return null;
        }
        log.info("[][][][]顺风车流程获取执行器{}",JsonUtils.json(mapService));
        for(String serviceStr : mapService){
            serviceNameList.add(SpringContextUtil.getBean(serviceStr));
        }

        //串联链路
        if(!CollectionUtils.isEmpty(serviceNameList)){
            if(serviceNameList.size() > 1) {
                for (int i = 1; i < serviceNameList.size(); i++) {
                    serviceNameList.get(i - 1).next(serviceNameList.get(i));
                }
            }
        }
        return serviceNameList;
    }

    public List<FilterSfcHandler> getSfcHandlerList(List<String> scenes){
        List<FilterSfcHandler> serviceNameList = new ArrayList<>();

        for (String str : scenes){
            List<String> mapService = SfcRuleServiceContextUtil.serviceNameMap.get(str);
            if(CollectionUtils.isEmpty(mapService)){
                continue;
            }
            for(String serviceStr : mapService){
                serviceNameList.add(SpringContextUtil.getBean(serviceStr));
            }
        }
        //串联链路
        if(!CollectionUtils.isEmpty(serviceNameList)){
            if(serviceNameList.size() > 1) {
                for (int i = 1; i < serviceNameList.size(); i++) {
                    serviceNameList.get(i - 1).next(serviceNameList.get(i));
                }
            }
        }
        return serviceNameList;
    }

    public FilterSfcHandler getHandler(List<String> scenes) {
        List<FilterSfcHandler> handlers = getSfcHandlerList(scenes);
        Assert.isTrue(CollectionUtils.isNotEmpty(handlers), () -> new RuntimeException("执行引擎不存在"));
        return handlers.get(0);
    }

    public FilterSfcContext convertParams(FilterParams params){
        FilterSfcContext filterSfcContext = new FilterSfcContext();
        filterSfcContext.setRiskCustomerManageList(getCustomerList(params));//名单先查
        filterSfcContext.setMainScene(params.getMainScene());
        filterSfcContext.setChildScene(params.getChildScene());
        filterSfcContext.setOrderId(params.getOrderId());
        filterSfcContext.setUnionId(params.getUnionId());
        filterSfcContext.setMemberId(params.getMemberId());
        filterSfcContext.setUserPhone(params.getUserPhone());
        filterSfcContext.setPassengerCellphone(params.getPassengerCellphone());
        filterSfcContext.setDriverCardNo(params.getDriverCardNo());
        filterSfcContext.setDriverContextList(getDriverContextList(params));
        filterSfcContext.setUserContextList(getUserContextList(params));
        filterSfcContext.setCancelOrderNumList(getUserOrderNumList(params));
        filterSfcContext.setMemberCancelList(getCancelOrder(params));
        SfcRiskRuleConfig configJson = getConfigJson();
        filterSfcContext.setSfcRiskRuleConfig(configJson);
        filterSfcContext.setOrderLimitList(getRiskLimitList(params,configJson));
        filterSfcContext.setRuleOnOff(getRuleOnOff(params.getSourceId()));//有sourceId用sourceID获取，没有就获取默认的
        filterSfcContext.setPhoneContextList(getPhoneOrderList(params));
        filterSfcContext.setDriverList(params.getDriverList());
        filterSfcContext.setParams(params);
        return filterSfcContext;
    }

    public FilterSfcContext convertParams(IsRiskUserRequest request){
        FilterSfcContext filterSfcContext = new FilterSfcContext();

        if (StringUtils.isNotBlank(request.getOrderId())) {
            // 新订单
            if (request.getOrderId().startsWith("YC")) {
                CarOrderDetail order = carOrderService.queryOrderDetail(request.getOrderId());
                if (null != order && null != order.getOrderTrip()) {
                    request.setStartLng(null == order.getOrderTrip().getDepartureLng() ? "" : order.getOrderTrip().getDepartureLng().toString());
                    request.setStartLat(null == order.getOrderTrip().getDepartureLat() ? "" : order.getOrderTrip().getDepartureLat().toString());
                    request.setEndLng(null == order.getOrderTrip().getArrivalLng() ? "" : order.getOrderTrip().getArrivalLng().toString());
                    request.setEndLat(null == order.getOrderTrip().getArrivalLat() ? "" : order.getOrderTrip().getArrivalLat().toString());
                }
            } else {
                // 旧订单
                if (request.getOrderId().startsWith("SFC")) {
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(request.getOrderId());
                    if (orderAddress != null) {
                        request.setStartLng(null == orderAddress.getStartLng() ? "" : orderAddress.getStartLng().toString());
                        request.setStartLat(null == orderAddress.getStartLat() ? "" : orderAddress.getStartLat().toString());
                        request.setEndLng(null == orderAddress.getEndLng() ? "" : orderAddress.getEndLng().toString());
                        request.setEndLat(null == orderAddress.getEndLat() ? "" : orderAddress.getEndLat().toString());
                    }
                }
            }
        }

        FilterParams filterParams = new FilterParams();
        BeanUtils.copyProperties(request, filterParams);

        filterSfcContext.setRiskCustomerManageList(getCustomerList(filterParams));//名单先查
        filterSfcContext.setMainScene(request.getMainScene());
        filterSfcContext.setChildScene(request.getChildScene());
        filterSfcContext.setOrderId(request.getOrderId());
        filterSfcContext.setUnionId(request.getUnionId());
        filterSfcContext.setMemberId(request.getMemberId());
        filterSfcContext.setUserPhone(request.getUserPhone());
        filterSfcContext.setPassengerCellphone(request.getPassengerCellphone());
        filterSfcContext.setDriverCardNo("");
        filterSfcContext.setDriverContextList(getDriverContextList(filterParams));
        filterSfcContext.setUserContextList(getUserContextList(filterParams));
        filterSfcContext.setCancelOrderNumList(getUserOrderNumList(filterParams));
        filterSfcContext.setMemberCancelList(getCancelOrder(filterParams));
        SfcRiskRuleConfig configJson = getConfigJson();
        filterSfcContext.setSfcRiskRuleConfig(configJson);
        filterSfcContext.setOrderLimitList(getRiskLimitList(filterParams, configJson));
        filterSfcContext.setRuleOnOff(getRuleOnOff(request.getSourceId()));//有sourceId用sourceID获取，没有就获取默认的
        filterSfcContext.setPhoneContextList(getPhoneOrderList(filterParams));
        filterSfcContext.setDriverList(new ArrayList<>());
        filterSfcContext.setParams(filterParams);
        return filterSfcContext;
    }

    public FilterSfcContext convertParams(OrderAcceptCheckRequest request,boolean isDistributionOrder){
        FilterSfcContext filterSfcContext = new FilterSfcContext();

        if (StringUtils.isNotBlank(request.getOrderId())) {
            // 新订单
            if (request.getOrderId().startsWith("YC")) {
                CarOrderDetail order = carOrderService.queryOrderDetail(request.getOrderId());
                if (null != order && null != order.getOrderTrip()) {
                    request.setStartLng(null == order.getOrderTrip().getDepartureLng() ? "" : order.getOrderTrip().getDepartureLng().toString());
                    request.setStartLat(null == order.getOrderTrip().getDepartureLat() ? "" : order.getOrderTrip().getDepartureLat().toString());
                    request.setEndLng(null == order.getOrderTrip().getArrivalLng() ? "" : order.getOrderTrip().getArrivalLng().toString());
                    request.setEndLat(null == order.getOrderTrip().getArrivalLat() ? "" : order.getOrderTrip().getArrivalLat().toString());
                }
            } else {
                // 旧订单
                if (request.getOrderId().startsWith("SFC")) {
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(request.getOrderId());
                    if (orderAddress != null) {
                        request.setStartLng(null == orderAddress.getStartLng() ? "" : orderAddress.getStartLng().toString());
                        request.setStartLat(null == orderAddress.getStartLat() ? "" : orderAddress.getStartLat().toString());
                        request.setEndLng(null == orderAddress.getEndLng() ? "" : orderAddress.getEndLng().toString());
                        request.setEndLat(null == orderAddress.getEndLat() ? "" : orderAddress.getEndLat().toString());
                    }
                }
            }
        }

        FilterParams filterParams = new FilterParams();
        BeanUtils.copyProperties(request, filterParams);
        filterParams.setDistributionOrder(isDistributionOrder);

        filterSfcContext.setRiskCustomerManageList(getCustomerList(filterParams));//名单先查
        filterSfcContext.setMainScene(request.getMainScene());
        filterSfcContext.setChildScene(request.getChildScene());
        filterSfcContext.setOrderId(request.getOrderId());
        filterSfcContext.setUnionId(request.getUnionId());
        filterSfcContext.setMemberId(request.getMemberId());
        filterSfcContext.setUserPhone(request.getUserPhone());
        filterSfcContext.setPassengerCellphone(request.getPassengerCellphone());
        filterSfcContext.setDriverCardNo(request.getCarNum());
        filterSfcContext.setDriverContextList(getDriverContextList(filterParams));
        filterSfcContext.setUserContextList(getUserContextList(filterParams));
        filterSfcContext.setCancelOrderNumList(getUserOrderNumList(filterParams));
        filterSfcContext.setMemberCancelList(getCancelOrder(filterParams));
        SfcRiskRuleConfig configJson = getConfigJson();
        filterSfcContext.setSfcRiskRuleConfig(configJson);
        filterSfcContext.setOrderLimitList(getRiskLimitList(filterParams, configJson));
        filterSfcContext.setRuleOnOff(getRuleOnOff(request.getSourceId()));//有sourceId用sourceID获取，没有就获取默认的
        filterSfcContext.setPhoneContextList(getPhoneOrderList(filterParams));
        filterSfcContext.setDriverList(new ArrayList<>());
        filterSfcContext.setParams(filterParams);
        return filterSfcContext;
    }

    //第一版规则是否只打开
    public Map<String,Boolean> getRuleOnOff(String sourceId){
        if(StringUtils.isBlank(sourceId)){
            sourceId = "default";
        }
        try {
            String configJson = ConfigCenterClient.get("rule_on-off_config");
            log.info("获取规则是否打开:"+configJson);
            Map<String,Map<String,Boolean>> configMap = JSONObject.parseObject(configJson, RuleOnOffConfig.class).getRuleOnOff();
            return configMap.get(sourceId);
        } catch (Exception e) {
            log.error("获取规则是否打开错误:",e);
        }
        return null;
    }

    //查询名单
    public List<RiskCustomerManage> getCustomerList(FilterParams params){
        Date date = new Date();
        FilterParams filterParams = new FilterParams();
        BeanUtils.copyProperties(params,filterParams);
        if(StringUtils.isNotBlank(params.getUserId())){
            filterParams.setUnionId(params.getUserId());
        }
        if(params.getMainScene() == 2 && params.getChildScene() == 1){
            filterParams.setMemberId(null);
            filterParams.setUnionId(null);
            filterParams.setUserPhone(null);
            filterParams.setPassengerCellphone(null);
        }
        if(StringUtils.isBlank(filterParams.getMemberId()) && StringUtils.isBlank(filterParams.getUnionId())
                && StringUtils.isBlank(filterParams.getUserPhone()) && StringUtils.isBlank(filterParams.getPassengerCellphone())
                && StringUtils.isBlank(filterParams.getDriverCardNo()) && StringUtils.isBlank(filterParams.getPlate())
        ){
            return new ArrayList<>();
        }
        return riskCustomerService.getListByValue(filterParams,date);
    }

    //查询取消或接单
    public List<SfcRiskLimitData> getRiskLimitList(FilterParams params,SfcRiskRuleConfig configJson){
        //查询取消或接单
        Date endDate = new Date();
        Date startDate = DateUtil.addMinute(endDate,-configJson.getTime038());
        List<SfcRiskLimitData> sfcRiskLimitDataList = new ArrayList<>();
        if(StringUtils.isNotBlank(params.getMemberId())){
            String regex=".*[a-zA-Z]+.*";
            Matcher m= Pattern.compile(regex).matcher(params.getMemberId());
            if(m.matches()){
                params.setMemberId(null);
            }
        }
        if(StringUtils.isNotBlank(params.getUnionId()) || StringUtils.isNotBlank(params.getMemberId())){
            if(StringUtils.isNotBlank(params.getMemberId()) && params.getMemberId().equals("0")){
                return sfcRiskLimitDataList;
            }
            if(params.isDistributionOrder()){
                return sfcRiskLimitDataList;
            }

            sfcRiskLimitDataList = sfcOrderMapper.querySfcRiskLimit(startDate,endDate, params.getUnionId(), params.getMemberId());
        }
        return sfcRiskLimitDataList;
    }

    //获取用户取消
    public List<OrderPassengerCellPhone> getPhoneOrderList(FilterParams params){
        List<OrderPassengerCellPhone> phoneOrderList = new ArrayList<>();
        long startMs = TimeUtil.threeDayMs();
        if(params.getMemberId() != null && !params.getMemberId().equals("0") && StringUtils.isNotBlank(params.getUserPhone())){
            phoneOrderList = orderStatusSlidingWindowCounter.getPhoneOrderList(params.getUserPhone(),startMs);
        }
        return phoneOrderList;
    }

    //获取用户下单数
    public List<SfcOrderNumDTO> getUserOrderNumList(FilterParams params){
        List<SfcOrderNumDTO> userOrderNumList = new ArrayList<>();
        // 如果是分销单，直接返回
        if(params.isDistributionOrder()){
            return userOrderNumList;
        }
        long startMs = TimeUtil.threeDayMs();
        if(StringUtils.isNotBlank(params.getUnionId())){
            userOrderNumList = sfcUserOrderNumWindowCounter.getCommonWindow(RedisKeyConstants.SFC_USER_ORDER_NUMBER+params.getUnionId(),startMs);
        } else  if(StringUtils.isNotBlank(params.getMemberId()) && !params.getMemberId().equals("0")){
            userOrderNumList = sfcUserOrderNumWindowCounter.getCommonWindow(RedisKeyConstants.SFC_USER_ORDER_NUMBER+params.getMemberId(),startMs);
        } else {
            //暂时啥都不做
        }
        return userOrderNumList;
    }

    //获取用户下单数
    public List<OrderStatusCancelDTO> getCancelOrder(FilterParams params){
        List<OrderStatusCancelDTO> cancelOrderList = new ArrayList<>();
        long startMs = TimeUtil.threeDayMs();
        if(StringUtils.isNotBlank(params.getMemberId()) && !params.getMemberId().equals("0") && !params.isDistributionOrder()){
            cancelOrderList = orderStatusSlidingWindowCounter.getCancelList(
                    RedisKeyConstants.SFC_CANCEL_ORDER_MEMBER+params.getMemberId(),startMs);
        }
        return cancelOrderList;
    }




    //获取各种订单完单数据
    public List<OrderRiskContext> getDriverContextList(FilterParams params){
        List<OrderRiskContext> driverContextList = new ArrayList<>();
        long startMs = TimeUtil.threeDayMs();
        if(StringUtils.isNotBlank(params.getDriverCardNo())){
            if(!AppProfile.getEnvironment().equals("product")){
                try {
                    log.info("[RuleRiskHandler][doHandler][{}][{}]预发环境调用线上{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
                    RedisParam param = new RedisParam();
                    param.setKey(params.getDriverCardNo());
                    param.setStartMs(startMs);
                    String result = OkHttpClientUtil.getInstance().post("http://tcwireless.17usoft.com/car_risk_process/redis/getRedisByUser",JsonUtils.json(param),null,500l);
                    driverContextList = JSONObject.parseArray(result,OrderRiskContext.class);
                } catch (Exception e) {
                }
            } else {
                driverContextList = driverSlidingWindowCounter.getDriverWindow(params.getDriverCardNo(), startMs);
            }
        }
        return driverContextList;
    }

    public List<OrderRiskContext> getUserContextList(FilterParams params){
        List<OrderRiskContext> userContextList = new ArrayList<>();
        long startMs = TimeUtil.threeDayMs();
        if(StringUtils.isNotBlank(params.getMemberId()) && !params.getMemberId().equals("0") && !params.isDistributionOrder()){
            if(!AppProfile.getEnvironment().equals("product")){
                try {
                    log.info("[RuleRiskHandler][doHandler][{}][{}]预发环境调用线上{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
                    RedisParam param = new RedisParam();
                    param.setKey(params.getMemberId());
                    param.setStartMs(startMs);
                    String result = OkHttpClientUtil.getInstance().post("http://tcwireless.17usoft.com/car_risk_process/redis/getRedisByUser",JsonUtils.json(param),null,500l);
                    userContextList = JSONObject.parseArray(result,OrderRiskContext.class);
                } catch (Exception e) {
                }
            } else {
                userContextList = userSlidingWindowCounter.getUserWindow(params.getMemberId(),startMs);
            }
        }
        return userContextList;
    }


    //获取顺风车配置文件
    public SfcRiskRuleConfig getConfigJson(){
        try {
            String configJson = ConfigCenterClient.get("sfc_common_risk_rule");
            log.info("获取顺风车风控规则:"+configJson);
            SfcRiskRuleConfig sfcRiskRuleConfig = JSONObject.parseObject(configJson,SfcRiskRuleConfig.class);
            return sfcRiskRuleConfig;
        } catch (Exception e) {
            log.error("获取顺风车风控规则错误:",e);
        }
        return null;
    }


}
