package com.ly.car.risk.process.controller.ability;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.controller.ability.params.RealTimeVoiceParam;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

@RequestMapping("asr")
@RestController
@Slf4j
public class AsrController {

    @Resource
    private TencentCloudApiClient tencentCloudApiClient;
    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer mqRiskProducer;

    @RequestMapping("sendRealTime")
    public UiResult sendRealTimeVoice(@RequestBody RealTimeVoiceParam param){
        if(!queryAbilityOnOff()){
            log.info("[][][][]实时语音转文字开关暂未开放");
            return UiResult.ok("开关暂未开放，请联系风控平台");
        }
        Long recTaskId = tencentCloudApiClient.createRecTask(1, param.getBase64Str());
        JSONObject taskJsonObject = new JSONObject();
        taskJsonObject.put("taskId",recTaskId);
        taskJsonObject.put("times",1);
        taskJsonObject.put("orderId",param.getOrderId());
        taskJsonObject.put("driverCardNo",param.getPlateNumber());
        taskJsonObject.put("source",3);
        if(recTaskId != null){
            //发送获取结果mq
            mqRiskProducer.send(MqTagEnum.car_self_tencent_get_describe_task, JsonUtils.json(taskJsonObject), DateUtil.addMinute(new Date(),1).getTime());
        }
        return UiResult.ok();
    }


    public Boolean queryAbilityOnOff(){
        try {
            String thirdAbilityConfig = ConfigCenterClient.get("third_ability_config");
            if(StringUtils.isBlank(thirdAbilityConfig)){
                return false;
            }
            JSONObject jsonObject = JSONObject.parseObject(thirdAbilityConfig);
            return jsonObject.getBoolean("realTimeOnOff");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

}
