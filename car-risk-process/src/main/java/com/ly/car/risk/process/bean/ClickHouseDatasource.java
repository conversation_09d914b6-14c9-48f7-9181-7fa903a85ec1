package com.ly.car.risk.process.bean;

import com.alibaba.druid.pool.DruidDataSource;
import com.ly.car.risk.process.bean.properties.ClickHouseProperties;
import com.ly.car.utils.JsonUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.annotation.Resource;
import javax.sql.DataSource;

@Configuration
public class ClickHouseDatasource {

    @Resource
    private ClickHouseProperties properties;

//    @Bean(name = "clickDataSource")
//    @Primary
//    public DataSource dataSource() throws Exception {
//        DruidDataSource dataSource = new DruidDataSource();
//        dataSource.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
//        dataSource.setUrl("jdbc:clickhouse://"+properties.getInsertUrl());
//        dataSource.setUsername(properties.getUser());
//        dataSource.setPassword(properties.getPwd());
//        System.out.println("clickhouse参数等信息:"+ JsonUtils.json(properties));
//        dataSource.setTestOnBorrow(false);
//        dataSource.setTestOnReturn(false);
//        dataSource.setTestWhileIdle(false);
//        dataSource.setInitialSize(1);
//        dataSource.setMaxActive(4);
//        dataSource.setValidationQuery("select 2");
//        dataSource.init();
//        return dataSource;
//    }
//
//    @Bean(name = "clickHouseSqlSessionFactory")
//    public SqlSessionFactory clickHouseSqlSessionFactoryBean() throws Exception {
//        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
//        factory.setDataSource(dataSource());
//        // 实体 model的 路径 比如 com.order.model
//        factory.setTypeAliasesPackage("com.ly.car.risk.process.repo.clickhouse");
//        //添加XML目录
//        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
//        factory.setMapperLocations(resolver.getResources("classpath*:clickhousemapper/*.xml"));
//        //开启驼峰命名转换
//        factory.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
//        return factory.getObject();
//    }
//
//    @Bean(name = "sqlSessionTemplateCK")
//    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("clickHouseSqlSessionFactory") SqlSessionFactory sessionFactory) {
//        return  new SqlSessionTemplate(sessionFactory);
//    }
}
