package com.ly.car.risk.process.event.listener;

import com.ly.car.risk.process.event.OrderCreateEvent;
import com.ly.car.risk.process.turboMQ.dto.newCar.CarOrderCreatePayload;
import com.ly.travel.car.tradecore.model.enums.OrderType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description of SfcOrderCreateOneHourListener
 *
 * <AUTHOR>
 * @date 2024/4/23
 * @desc 顺风车用户近1小时内创单数
 */
@Component
public class SfcOrderCreateOneHourListener extends AbstractOrderCreateListener {

    private static final String SFC_ONE_HOUR_CREATE_INDEX = "sfc_member_1h_create_order_num";


    @Override
    public String dealDataIndex() {
        return SFC_ONE_HOUR_CREATE_INDEX;
    }

    public void doHandleOrderCreateEvent(OrderCreateEvent orderCreateEvent) {
        CarOrderCreatePayload order = orderCreateEvent.getOrder();
        String productLine = OrderType.getByCrmType(order.getOrderType()).getRiskType();
        String memberId = order.getMemberId();
        if (!"SFC".equalsIgnoreCase(productLine)) {
            return;
        }
        // 获取近1小时的订单数据
        List<CarOrderCreatePayload> rangeOrders = getOneHourRangeOrders(memberId);
        if (CollectionUtils.isEmpty(rangeOrders)) {
            return;
        }
        String indexName = String.format(SFC_ONE_HOUR_CREATE_INDEX + "_%s", memberId);
        RMap<String, Object> map = redissonClient.getMap(indexName);

        String orderNos = rangeOrders.stream().map(p -> p.getOrderId()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        map.put("num", rangeOrders.size());
        map.put("orderNos", orderNos);

        map.expire(TTL_ONE_HOUR);
    }

}