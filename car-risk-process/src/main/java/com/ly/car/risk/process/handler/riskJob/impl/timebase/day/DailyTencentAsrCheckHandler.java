package com.ly.car.risk.process.handler.riskJob.impl.timebase.day;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.common.enums.*;
import com.ly.car.risk.process.handler.riskJob.MtAbstractRiskCheckHandler;
import com.ly.car.risk.process.model.riskJob.TencentAsrCheckResp;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class DailyTencentAsrCheckHandler extends MtAbstractRiskCheckHandler<TencentAsrCheckResp> {

    @Resource
    private RiskChargeVoiceMapper riskChargeVoiceMapper;

    
    @Override
    public RiskJobTypeEnum support() {
        return RiskJobTypeEnum.DAILY_TENCENT_ASR_COUNT;
    }
    
    @Override
    public List<RiskAlertApprove> doCheck() {
        String dayBegin = getYesterdayBegin();
        String dayEnd = getDayBegin();
        String checkThresholdVal = getMTCheckThreshold(support());
        if (StringUtils.isBlank(checkThresholdVal)) {
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }

        Long duration = riskChargeVoiceMapper.queryCallDuration(VoiceApiProviderEnum.TX_VOICE.getCode(),VoiceProductTypeEnum.VIRTUAL_PHONE.getCode(),dayBegin,dayEnd);
        if(null == duration){
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }

        BigDecimal callHour = new BigDecimal(duration).divide(new BigDecimal(3600), 2, RoundingMode.HALF_UP);
        BigDecimal checkLimit = new BigDecimal(checkThresholdVal);
        LoggerUtils.info(logger,"当日腾讯语音转文本调用小时数：{}, 阈值：{}", callHour , checkLimit);
        if(callHour.compareTo(checkLimit) <= 0){
            return null;
        }
        TencentAsrCheckResp checkResp = TencentAsrCheckResp.builder().callDuration(callHour).checkLimit(checkLimit).build();
        return Collections.singletonList(convert(checkResp));

    }
    
    private RiskAlertApprove convert(TencentAsrCheckResp checkResp) {
        Date now = new Date();
        RiskAlertApprove alertApprove = new RiskAlertApprove();
        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
        alertApprove.setLevel(RiskAlertApproveLevelEnum.FOCUS.getCode());
        alertApprove.setTarget(RiskAlertApproveTargetEnum.ORDER.getCode());
        alertApprove.setTargetValue("");
        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.CALL_STATISTICS.getCode());
        alertApprove.setAlertStrategy(support().name());
        alertApprove.setAlertContent(JSON.toJSONString(checkResp));
        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
        alertApprove.setAlertTime(now);
        alertApprove.setCreateTime(now);
        alertApprove.setUpdateTime(now);
        alertApprove.setCreateUser(support().name() + "_CHECK");
        
        RiskAlertApprove.Ext ext = new RiskAlertApprove.Ext();
        alertApprove.setExt(JSON.toJSONString(ext));
        approveMapper.insertSelective(alertApprove);
        
        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
        return alertApprove;
    }
    
    @Override
    public String getContentFormApprove(RiskAlertApprove approve) {
        String alertContent = approve.getAlertContent();
        if (StringUtils.isBlank(alertContent)) {
            return StringUtils.EMPTY;
        }
        TencentAsrCheckResp resp = JSON.parseObject(alertContent, new TypeReference<TencentAsrCheckResp>() {
        });
        StringBuilder sb = doGetContentFormApprove(approve, resp);
        return sb.toString();
    }
    
    @Override
    public String getDesc(RiskAlertApprove approve, TencentAsrCheckResp resp) {
        StringBuilder sb = new StringBuilder();
        sb.append(RiskJobTypeEnum.getDescByCode(approve.getAlertStrategy()));
        sb.append(String.format("(当日调用时长：%s，阈值：%s)",resp.getCallDuration().toPlainString(),resp.getCheckLimit().toPlainString()));
        return sb.toString();
    }
    
}