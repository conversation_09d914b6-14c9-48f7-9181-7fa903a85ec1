package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.TrafficApi;
import com.ly.car.risk.process.api.param.RiskCheckOrderParam;
import com.ly.car.risk.process.api.rsp.TrafficCheckOrderRsp;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 当前用户手机号、设备号、乘车人手机号下有待补款订单
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk1002Service extends FilterCheckPriceHandler{

    private static final String ruleNo = "1002";

    @Resource
    private TrafficApi trafficApi;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterSceneContext context) {
        UiResult uiResult = context.getUiResult();
        RiskCheckOrderParam param = new RiskCheckOrderParam();
        param.setPayAccount(context.getPayAccount());
        param.setUnionId(context.getUnionId());
        param.setMemberId(context.getMemberId());
        TrafficCheckOrderRsp trafficCheckOrderRsp = trafficApi.riskCheckOrder(param);
        if(trafficCheckOrderRsp != null && trafficCheckOrderRsp.getCode().equals(200) && !CollectionUtils.isEmpty(trafficCheckOrderRsp.getOrder_list())){
            distributionRiskManageService.addByCustomerNoOrder(ruleNo,2,1,0,null);
            RiskResultDTO resultDTO = new RiskResultDTO(405,"风控不通过",ruleNo,null, RiskLevelEnum.HIGH.getCode());
            resultDTO.setObj(trafficCheckOrderRsp.getOrder_list());
            uiResult.setMsg("风控不通过");
            uiResult.setData(resultDTO);
            return uiResult;
        }

        //当前可以判断是否有下个节点

        return context.getUiResult();
    }
}
