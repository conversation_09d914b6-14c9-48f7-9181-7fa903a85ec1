package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskWarnMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskWarn;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class RiskWarnService {

    public static Map<Integer,String> warnTypeMap;

    @Resource
    private RiskWarnMapper riskWarnMapper;

    static {
        warnTypeMap = new HashMap<>();
        warnTypeMap.put(0,"路线偏移");
        warnTypeMap.put(1,"异常停留");
        warnTypeMap.put(2,"车速过快");
        warnTypeMap.put(3,"中途下车");
    }

    public void saveRiskWarn(String orderId,Integer warnType,String productLine){
        RiskWarn haveRiskWarn = this.riskWarnMapper.selectOne(new QueryWrapper<RiskWarn>()
                .eq("order_id",orderId)
                .eq("warn_type",warnType)
        );
        if(haveRiskWarn != null){
            return;
        }
        RiskWarn riskWarn = new RiskWarn();
        riskWarn.setOrderId(orderId);
        riskWarn.setWarnType(warnType);
        riskWarn.setWarnName(warnTypeMap.get(warnType));
        riskWarn.setCreateTime(new Date());
        riskWarn.setUpdateTime(new Date());
        riskWarn.setProductLine(productLine);
        this.riskWarnMapper.insert(riskWarn);

    }
}
