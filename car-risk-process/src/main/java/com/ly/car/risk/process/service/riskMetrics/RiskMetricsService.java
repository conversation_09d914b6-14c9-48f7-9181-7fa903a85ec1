package com.ly.car.risk.process.service.riskMetrics;

import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;

import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description of RiskMetricsService
 *
 * <AUTHOR>
 * @date 2024/6/18
 * @desc
 */
public interface RiskMetricsService {


    void cleanRiskMetricOldData();

    void cleanRiskMetricOldCouponData();
    List<Map<String, Object>> executeSql(String sql);
    List<CarRiskOrderDetail> user1HourFinishOrder(String memberId,String productLine);

    List<CarRiskOrderDetail> user24HourFinishOrder(String memberId,String productLine);

    List<CarRiskOrderDetail> car1HourFinishOrder(String carNum,String productLine);

    List<CarRiskOrderDetail> car24HourFinishOrder(String carNum,String productLine);

    List<CarRiskOrderDetail> user24HourTotalOrder(String memberId,String productLine);


    List<CarRiskOrderDetail> car24HourTotalOrder(String carNum,String productLine);

    List<CarRiskOrderDetail> car1HourTotalOrder(String carNum,String productLine);

    List<CarRiskOrderDetail> user1HourTotalOrder(String memberId,String productLine);

    List<CarRiskOrderDetail> user30MinTotalOrder(String memberId,String productLine);

    List<CarRiskOrderDetail> phone24HourFinishOrder(String phone,String productLine);
    List<CarRiskOrderDetail> phone24HourTotalOrder(String phone,String productLine);

    List<CarRiskOrderDetail> user30MinCancelOrder(String memberId,String productLine);
    List<CarRiskOrderDetail> user1HourCancelOrder(String memberId,String productLine);
    List<CarRiskOrderDetail> user24HourCancelOrder(String memberId,String productLine);

    List<CarRiskOrderDetail> phone24HourCancelOrder(String phone,String productLine);

    List<CarRiskOrderDetail> car24HourFinishRightsOrder(String carNum,String productLine);
    
    List<CarRiskOrderDetail> driver24HourRegisterCountOnSameDevice(String deviceId);
    
    List<CarRiskOrderDetail> driver24HourLoginCountOnSameDevice(String deviceId);
    
//    List<CarRiskOrderDetail> driver24HourWithdrawCountSameDevice(String deviceId);
//
//    List<CarRiskOrderDetail> driver24HourWithdrawAmountSameDevice(String deviceId);
    
    List<CarRiskOrderDetail> driver05RegisterSameDevice(String deviceId);
    
    List<CarRiskOrderDetail> driver24HourBillOrderCount(String carNum);
    
    List<CarRiskOrderDetail> driver24hourFinishOrderLess6min(String carNum);
    
    List<CarRiskOrderDetail> driver24hourBillOrderAmount(String carNum);
    
    List<CarRiskOrderDetail> user24hourCancelSupplierOrderCount(String memberId, String productLine);
    
    List<CarRiskOrderDetail> user24hourFinishNearOrderLess6MinCount(String memberId, String productLine);
    
    List<CarRiskOrderDetail> user24hourFinishOrderSameDeviceCount(String memberId, String productLine);
    
    List<CarRiskOrderDetail> user24hourFinishNearOrderLess500mCount(String memberId, String productLine);
    
    List<CarRiskOrderDetail> user24hourFinishOrderLess2kmCount(String memberId, String productLine);
    
    List<CarRiskOrderDetail> driver24hourFinishOrderLess6minCount(String carNum, String productLine);
    
    List<CarRiskOrderDetail> driver24hourFinishOrderCount(String carNum, String productLine);

    List<SensitiveRecord> order24hourSensitiveMatchRecord(String orderId);

    /**
     * 当前订单手机号指定时间内待补款订单
     *
     * @param passengerPhone 乘机人手机号
     * @param startTime    行程结束时间范围-开始
     * @param endTime      行程结束时间范围-结束
     * @return 订单
     */
    List<CarRiskOrderDetail> phonePendingOrderNum( String passengerPhone, List<String> productLineList,  Date startTime,Date endTime);

    /**
     * 当前订单手机号指定时间内待补款订单关联未付金额数
     *
     * @param passengerPhone 乘机人手机号
     * @param startTime    行程结束时间范围-开始
     * @param endTime      行程结束时间范围-结束
     * @return 未支付金额
     */
    BigDecimal phonePendingOrderAmount(String passengerPhone, List<String> productLineList, Date startTime, Date endTime);
    /**
     * 当前订单手机号指定时间内待补款订单关联司机车牌数
     *
     * @param passengerPhone 乘机人手机号
     * @param startTime    行程结束时间范围-开始
     * @param endTime      行程结束时间范围-结束
     * @return 未支付金额
     */
    long phonePendingDriverOrderCount( String passengerPhone, List<String> productLineList,  Date startTime,Date endTime);
    
    List<CarRiskOrderDetail> driverSurchargeMonthNum(String carNum, Integer orderNum, List<String> productLineList);

    /**
     * 司机指定时间内命中规则次数
     *
     * @param ruleNo    规则编号
     * @param startTime 行程结束时间范围-开始
     * @param endTime   行程结束时间范围-结束
     * @return 命中规则次数
     */
    long driverFitRuleNum(String carNum,String ruleNo, Date startTime, Date endTime);


    /**
     *  司机每月（上个自然月）有责风险单数量
     *
     * @param ruleNo    规则编号
     * @param startTime 行程结束时间范围-开始
     * @param endTime   行程结束时间范围-结束
     * @return 命中规则次数
     */
    long driverResponsibleOrderCount(String carNum,Date startTime, Date endTime);
    
    List<CarRiskOrderDetail> driverSurchargeGtBookRate(CarOrderDetail orderDetail);
}