package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;


public enum RiskCustomerCustomerTypeEnum {

    user_id(1, "用户/ID"),
    user_phone(2, "用户/手机号"),
    user_device_id(3, "用户/设备号"),
    user_unionid(4, "用户/unionid"),
    user_pay_account(5, "用户/支付账号"),
    car_number(6, "司机/车牌号"),
    hc_member_id(7,"司机/司机id"),
    hc_phone(8,"司机/手机号"),
    hc_id_card(9,"司机/身份证号"),
    user_cert_no(10, "用户/证件号"),
    ;
    private Integer code;
    private String msg;

    RiskCustomerCustomerTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskCustomerCustomerTypeEnum enumItem : RiskCustomerCustomerTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getCodeByMsg(String msg) {
        for (RiskCustomerCustomerTypeEnum enumItem : RiskCustomerCustomerTypeEnum.values()) {
            if (enumItem.getMsg().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
}
