package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
public class VirtualPhoneRecord extends Model<VirtualPhoneRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String orderId;

    private String callingNo;

    private String calledNo;

    private String releaseTime;

    private Integer callSecond;

    private String callingType;

    private Date createTime;
}
