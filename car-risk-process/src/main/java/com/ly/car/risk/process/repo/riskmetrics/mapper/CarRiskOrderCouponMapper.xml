<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderCouponMapper">
    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderCoupon">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_serial_no" jdbcType="VARCHAR" property="orderSerialNo"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
        <result column="real_price" jdbcType="DECIMAL" property="realPrice"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="subCategory" jdbcType="VARCHAR" property="subcategory"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="order_channel" jdbcType="INTEGER" property="orderChannel"/>
        <result column="departure_city_code" jdbcType="VARCHAR" property="departureCityCode"/>
        <result column="gmt_trip_finished" jdbcType="TIMESTAMP" property="gmtTripFinished"/>
    </resultMap>

    <resultMap id="supplierCouponCheckResultMap" type="com.ly.car.risk.process.model.riskJob.SupplierCouponCheckResp">
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="full_supplier_code" jdbcType="VARCHAR" property="fullSupplierCode"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
    </resultMap>

    <resultMap id="supplierCouponCountCheckResultMap" type="com.ly.car.risk.process.model.riskJob.SupplierCouponCountCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="full_supplier_code" jdbcType="VARCHAR" property="fullSupplierCode"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>

    <resultMap id="supplierCouponBatchCountCheckResultMap" type="com.ly.car.risk.process.model.riskJob.SupplierCouponBatchCountCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="full_supplier_code" jdbcType="VARCHAR" property="fullSupplierCode"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>

    <resultMap id="driverCouponCheckResultMap" type="com.ly.car.risk.process.model.riskJob.DriverCouponCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
    </resultMap>

    <resultMap id="driverCouponCountCheckResultMap" type="com.ly.car.risk.process.model.riskJob.DriverCouponCountCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>

    <resultMap id="driverCouponBatchCountCheckResultMap" type="com.ly.car.risk.process.model.riskJob.DriverCouponBatchCountCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>

    <resultMap id="userCouponCheckResultMap" type="com.ly.car.risk.process.model.riskJob.UserCouponCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
    </resultMap>

    <resultMap id="userCouponBatchCountCheckResultMap" type="com.ly.car.risk.process.model.riskJob.UserCouponBatchCountCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
    </resultMap>

    <resultMap id="userDriverCouponCheckResultMap" type="com.ly.car.risk.process.model.riskJob.UserDriverCouponCheckResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="car_nums" jdbcType="VARCHAR" property="carNums"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , order_serial_no, member_id, business_type, sale_price, real_price, batch_no,
    category, subCategory, `type`, create_time, update_time, item_id, supplier_code, 
    car_num, order_channel, departure_city_code, gmt_trip_finished
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from car_risk_order_coupon
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from car_risk_order_coupon
        where id = #{id,jdbcType=BIGINT}
    </delete>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderCoupon"
            useGeneratedKeys="true">
        insert into car_risk_order_coupon (order_serial_no, member_id, business_type,
                                           sale_price, real_price, batch_no,
                                           category, subCategory, `type`,
                                           create_time, update_time, item_id,
                                           supplier_code, car_num, order_channel,
                                           departure_city_code, gmt_trip_finished)
        values (#{orderSerialNo,jdbcType=VARCHAR}, #{memberId,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER},
                #{salePrice,jdbcType=DECIMAL}, #{realPrice,jdbcType=DECIMAL}, #{batchNo,jdbcType=VARCHAR},
                #{category,jdbcType=VARCHAR}, #{subcategory,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{itemId,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR}, #{orderChannel,jdbcType=INTEGER},
                #{departureCityCode,jdbcType=VARCHAR}, #{gmtTripFinished,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderCoupon"
            useGeneratedKeys="true">
        insert into car_risk_order_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderSerialNo != null">
                order_serial_no,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="salePrice != null">
                sale_price,
            </if>
            <if test="realPrice != null">
                real_price,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="subcategory != null">
                subCategory,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
            <if test="carNum != null">
                car_num,
            </if>
            <if test="orderChannel != null">
                order_channel,
            </if>
            <if test="departureCityCode != null">
                departure_city_code,
            </if>
            <if test="gmtTripFinished != null">
                gmt_trip_finished,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderSerialNo != null">
                #{orderSerialNo,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                #{memberId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="salePrice != null">
                #{salePrice,jdbcType=DECIMAL},
            </if>
            <if test="realPrice != null">
                #{realPrice,jdbcType=DECIMAL},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="subcategory != null">
                #{subcategory,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="carNum != null">
                #{carNum,jdbcType=VARCHAR},
            </if>
            <if test="orderChannel != null">
                #{orderChannel,jdbcType=INTEGER},
            </if>
            <if test="departureCityCode != null">
                #{departureCityCode,jdbcType=VARCHAR},
            </if>
            <if test="gmtTripFinished != null">
                #{gmtTripFinished,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderCoupon">
        update car_risk_order_coupon
        <set>
            <if test="orderSerialNo != null">
                order_serial_no = #{orderSerialNo,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                member_id = #{memberId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="salePrice != null">
                sale_price = #{salePrice,jdbcType=DECIMAL},
            </if>
            <if test="realPrice != null">
                real_price = #{realPrice,jdbcType=DECIMAL},
            </if>
            <if test="batchNo != null">
                batch_no = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="subcategory != null">
                subCategory = #{subcategory,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="carNum != null">
                car_num = #{carNum,jdbcType=VARCHAR},
            </if>
            <if test="orderChannel != null">
                order_channel = #{orderChannel,jdbcType=INTEGER},
            </if>
            <if test="departureCityCode != null">
                departure_city_code = #{departureCityCode,jdbcType=VARCHAR},
            </if>
            <if test="gmtTripFinished != null">
                gmt_trip_finished = #{gmtTripFinished,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderCoupon">
        update car_risk_order_coupon
        set order_serial_no     = #{orderSerialNo,jdbcType=VARCHAR},
            member_id           = #{memberId,jdbcType=VARCHAR},
            business_type       = #{businessType,jdbcType=INTEGER},
            sale_price          = #{salePrice,jdbcType=DECIMAL},
            real_price          = #{realPrice,jdbcType=DECIMAL},
            batch_no            = #{batchNo,jdbcType=VARCHAR},
            category            = #{category,jdbcType=VARCHAR},
            subCategory         = #{subcategory,jdbcType=VARCHAR},
            `type`              = #{type,jdbcType=VARCHAR},
            create_time         = #{createTime,jdbcType=TIMESTAMP},
            update_time         = #{updateTime,jdbcType=TIMESTAMP},
            item_id             = #{itemId,jdbcType=VARCHAR},
            supplier_code       = #{supplierCode,jdbcType=VARCHAR},
            car_num             = #{carNum,jdbcType=VARCHAR},
            order_channel       = #{orderChannel,jdbcType=INTEGER},
            departure_city_code = #{departureCityCode,jdbcType=VARCHAR},
            gmt_trip_finished   = #{gmtTripFinished,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="checkDaySupplierCoupon" resultMap="supplierCouponCheckResultMap">
        select full_supplier_code,
               batch_no,
               order_type,
               sum(ifnull(real_price, 0)) as amount
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin}
          and full_supplier_code != '' and  batch_no != '' and type!='timeLimited'
          and order_type in (19,11,80,85,86)
        group by full_supplier_code, batch_no
        having sum(ifnull( real_price, 0)) >= #{threshold}
    </select>

    <select id="checkDaySupplierCountCoupon" resultMap="supplierCouponCountCheckResultMap">
        select full_supplier_code,
               order_type,
               count(*) as `count`
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin}
          and full_supplier_code != '' and  batch_no != '' and type!='timeLimited'
         and order_type in (19,11,80,85,86)
        group by full_supplier_code
        having count(*) >= #{threshold}
    </select>

    <select id="checkDaySupplierBatchCountCoupon" resultMap="supplierCouponBatchCountCheckResultMap">
        select full_supplier_code,
               order_type,
               batch_no,
               count(*) as `count`
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin}
          and full_supplier_code != '' and  batch_no != '' and type!='timeLimited'
          and order_type in (19,11,80,85,86)
        group by full_supplier_code, batch_no
        having count(*) >= #{threshold}
    </select>

    <select id="checkDayDriverCoupon" resultMap="driverCouponCheckResultMap">
        select car_num,
               batch_no,
               order_type,
               sum(ifnull(real_price, 0)) as amount
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin}
          and car_num != '' and batch_no != '' and type!='timeLimited'
        group by car_num, batch_no
        having sum(ifnull(real_price, 0)) >= #{threshold}
    </select>

    <select id="checkDayDriverCouponCount" resultMap="driverCouponCountCheckResultMap">
        select car_num,
               order_type,
               count(*) as `count`
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin}
          and car_num != '' and batch_no != '' and type!='timeLimited'
          and order_type in (19,11,80,85,86)
        group by car_num
        having count(*) >= #{threshold}
    </select>

    <select id="checkDayDriverCouponBatchCount" resultMap="driverCouponBatchCountCheckResultMap">
        select car_num,
               order_type,
               batch_no,
               count(*) as `count`
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin}
          and car_num != '' and batch_no != '' and type!='timeLimited'
          and order_type in (19,11,80,85,86)
        group by car_num, batch_no
        having count(*) >= #{threshold}
    </select>

    <select id="checkHourUserCoupon" resultMap="userCouponCheckResultMap">
        select member_id,
               order_type,
               count(*) as `count`,
               sum(ifnull(real_price, 0)) as amount
        from car_risk_order_coupon
        where gmt_trip_finished between #{startTime} and #{endTime}  and type!='timeLimited'
        group by member_id
        having count(*) >= #{threshold}
    </select>

    <select id="checkDailyBatchNoUserCoupon" resultMap="userCouponBatchCountCheckResultMap">
        select member_id,
               count(*) as `count`,
               order_type,
               batch_no
        from car_risk_order_coupon
        where gmt_trip_finished >= #{dayBegin} and type!='timeLimited'
        and order_type in (19,11,80,85,86)
        group by member_id, 	batch_no
        having count(*) >= #{threshold}
    </select>

    <select id="checkUserDriverCoupon" resultMap="userDriverCouponCheckResultMap">
        select member_id,
               order_type,
        GROUP_CONCAT(car_num ORDER BY car_num ASC) as car_nums,
        count(*)                                   as `count`,
        sum(ifnull(real_price, 0))                 as amount
        from car_risk_order_coupon
        where gmt_trip_finished between #{startTime} and #{endTime}   and type!='timeLimited'
        group by member_id
        having count(*) >= #{couponCount}
        and count(DISTINCT car_num) &lt;= #{driverCount}
    </select>

    <delete id="cleanRiskMetricOldCouponData">
        delete from car_risk_order_coupon
        where DATE_SUB(now(), INTERVAL 20 DAY) > gmt_trip_finished
    </delete>

</mapper>