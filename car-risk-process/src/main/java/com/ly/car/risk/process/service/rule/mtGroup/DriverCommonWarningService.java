package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.DriverOrderInfoMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverOrderInfo;
import com.ly.car.risk.process.service.MqSendService;
import com.ly.car.risk.process.service.dto.DriverWarningDTO;
import com.ly.car.risk.process.service.dto.MtDriverConfig;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.common.SafeWarningResult;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@Scope("prototype")
public class DriverCommonWarningService extends MtFilterHandler{

    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private OrderClient     orderClient;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private LabelClient     labelClient;
    @Resource
    private OrderAddressMapper    orderAddressMapper;
    @Resource
    private MqSendService         mqSendService;
    @Resource
    private DriverOrderInfoMapper driverOrderInfoMapper;

    @Override
    public void doHandler(MtFilterContext context) {
        if(!validParam(context.getParam())){
            context.getDto().setCode(1);
            context.getDto().setMessage("司机安全预警错误-参数缺失");
            return;
        }
        List<SafeWarningResult> returnResult = new ArrayList<>();
        List<SafeWarningResult> forceResult = new ArrayList<>();
        //当前订单号为供应商订单号，所以要转为同程订单号
        String supplierOrderId = context.getParam().getString("orderId");
        String currentLng = context.getParam().getString("currentLng");
        String currentLat = context.getParam().getString("currentLat");
        Integer isMini = context.getParam().getInteger("isMini");
        String tcOrderId = "";
        //行程开始 start
        //行程中 underway
        //确认到达 arrive
        String eventType = context.getParam().getString("eventType");
        Date useTime = new Date();
        Integer gender = 0;
        String memberId = "";
        String unionId = "";
        BigDecimal esKilo = BigDecimal.ZERO;
        String addressLng = "";
        String addressLat = "";
        Integer orderChannel = null;
        MtDriverConfig config = queryConfig();
        if (context.getParam().getString("productLine").equals("MT") || context.getParam().getString("productLine").equals("HC")) {

            SfcOrder sfcOrder = this.sfcOrderMapper.queryBySupplierOrderId(supplierOrderId);

            if (sfcOrder != null) {
                OrderAddress orderAddress = this.orderAddressMapper.findByOrderId(sfcOrder.getOrderId());

                if (orderAddress == null) {
                    return;
                }

                useTime = sfcOrder.getUseTime();
                tcOrderId = sfcOrder.getOrderId();
                memberId = sfcOrder.getMemberId() == 0 ? "" : String.valueOf(sfcOrder.getMemberId());
                unionId = StringUtils.isBlank(sfcOrder.getUnionId()) ? "" : sfcOrder.getUnionId();
                orderChannel = sfcOrder.getPlatId();
                esKilo = orderAddress.getEstimateKilo();
                addressLng = String.valueOf(orderAddress.getEndLng());
                addressLat = String.valueOf(orderAddress.getEndLat());

            } else {

                QueryDriverInfoResponse queryDriverInfoResponse = orderClient.queryDriverInfo(supplierOrderId);

                if (null == queryDriverInfoResponse) {
                    return;
                }

                CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(queryDriverInfoResponse.getOrderSerialNo());

                if (null == carOrderDetail) {
                    return;
                }

                tcOrderId = carOrderDetail.getOrderId();
                memberId = carOrderDetail.getMemberId();
                unionId = carOrderDetail.getUnionId();
                orderChannel = carOrderDetail.getOrderChannel();

                if (null != carOrderDetail.getBaseInfo()) {
                    useTime = carOrderDetail.getBaseInfo().getGmtUsage();
                }

                if (null != carOrderDetail.getOrderTrip()) {
                    addressLng = String.valueOf(carOrderDetail.getOrderTrip().getArrivalLng());
                    addressLat = String.valueOf(carOrderDetail.getOrderTrip().getArrivalLat());
                    esKilo = carOrderDetail.getOrderTrip().getOldEstimateKilo();
                }
            }
        }

        if(eventType.equals("start")){
            //司机端小程序通知
            if(isMini != null && isMini == 1){
                mqSendService.dealMiniProgram(tcOrderId,context.getParam().getString("productLine"),0L);
            }
            if(context.getParam().getString("productLine").equals("MT")){
                SafeWarningResult result = new SafeWarningResult();
                result.setRuleNo("sjaq011");
                result.setHitTime(DateUtil.date2String(new Date()));
                result.setText("乘客已上车");
                result.setSort(99);
                result.setGender(0);
                returnResult.add(result);
            }
            LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(orderChannel,memberId, unionId);
            //获取敏感时间点
            if(config.getTimeStart() != null){
                if(!(useTime.getHours() > config.getTimeStart() && useTime.getHours() < config.getTimeEnd())){
                    //敏感时间
                    if((detailRsp != null && detailRsp.getGender() != null && detailRsp.getGender() == 2) || config.getGender() == 2){
                        SafeWarningResult result = new SafeWarningResult();
                        result.setRuleNo("sjaq003");
                        result.setHitTime(DateUtil.date2String(new Date()));
                        result.setText("敏感时间且为女性出行");
                        result.setSort(1);
                        result.setGender(detailRsp==null?0:detailRsp.getGender());
                        returnResult.add(result);
                    }
                    if(esKilo.compareTo(new BigDecimal(config.getEsKilo())) > 0){
                        SafeWarningResult result = new SafeWarningResult();
                        result.setRuleNo("sjaq002");
                        result.setHitTime(DateUtil.date2String(new Date()));
                        result.setText("敏感时间且行程较长");
                        result.setSort(2);
                        result.setGender(0);
                        returnResult.add(result);
                    }
                    SafeWarningResult result = new SafeWarningResult();
                    result.setRuleNo("sjaq001");
                    result.setHitTime(DateUtil.date2String(new Date()));
                    result.setText("敏感时间出行");
                    result.setSort(3);
                    result.setGender(0);
                    returnResult.add(result);
                }
            }

            if(context.getParam().getString("productLine").equals("HC")){
                //先插入该订单开始
                DriverOrderInfo driverOrderInfo = this.driverOrderInfoMapper.selectOne(new QueryWrapper<DriverOrderInfo>()
                        .eq("order_id", tcOrderId)
                );
                if(driverOrderInfo == null){
                    DriverOrderInfo orderInfo = new DriverOrderInfo();
                    orderInfo.setOrderId(tcOrderId);
                    orderInfo.setDriverId(context.getParam().getString("driverId"));
                    orderInfo.setDriverCardNo(context.getParam().getString("plate"));
                    orderInfo.setOrderCreated(new Date());
                    orderInfo.setCreateTime(new Date());
                    this.driverOrderInfoMapper.insert(orderInfo);

                    //处理高峰期语音
                    //上下班高峰,获取当前时间小时
                    Integer currentHour = new Date().getHours();
                    String morningHour = config.getRushHour1();
                    String nightHour = config.getRushHour2();
                    String[] morningArray = morningHour.split(",");
                    String[] nightArray = nightHour.split(",");
                    if((currentHour >= Integer.valueOf(morningArray[0]) && Integer.valueOf(morningArray[1]) >= currentHour) ||
                            (currentHour >= Integer.valueOf(nightArray[0]) && Integer.valueOf(nightArray[1]) >= currentHour)){
                        SafeWarningResult result = new SafeWarningResult();
                        result.setRuleNo("sjaq010");
                        result.setHitTime(DateUtil.date2String(new Date()));
                        result.setText("上下班高峰");
                        result.setSort(1);
                        result.setType(1);
                        forceResult.add(result);
                    }

                    //疲劳驾驶，实现方案，存入redis实体类
                    String date = DateUtil.date2String(new Date(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
                    Date startTime = DateUtil.string2Date(date + " 00:00:00");
                    List<DriverOrderInfo> driverOrderInfos = driverOrderInfoMapper.selectList(new QueryWrapper<DriverOrderInfo>()
                            .between("order_finished", startTime, new Date())
                            .eq("driver_id", context.getParam().getString("driverId"))
                    );
                    long minutes = 0;
                    for(DriverOrderInfo info : driverOrderInfos){
                        //计算分钟
                        LocalDateTime sTime = info.getOrderCreated().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        LocalDateTime eTime = info.getOrderFinished().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        minutes = Duration.between(sTime,eTime).toMinutes();
                    }
                    if(minutes > config.getTiredTime()){
                        SafeWarningResult result = new SafeWarningResult();
                        result.setRuleNo("sjaq009");
                        result.setHitTime(DateUtil.date2String(new Date()));
                        result.setText("疲劳驾驶");
                        result.setSort(1);
                        result.setType(1);
                        forceResult.add(result);
                    }
                }
            }
        }
        if(eventType.equals("arrive")){
            if(StringUtils.isNotBlank(currentLat) && StringUtils.isBlank(currentLng)){
                if(BigDecimal.valueOf(CoordUtil.getDistance(currentLng,currentLat,addressLng,addressLat)).compareTo(new BigDecimal("3000")) > 0){
                    SafeWarningResult result = new SafeWarningResult();
                    result.setRuleNo("sjaq005");
                    result.setHitTime(DateUtil.date2String(new Date()));
                    result.setText("提前下车");
                    result.setSort(1);
                    result.setGender(0);
                    returnResult.add(result);
                }
            }

            SafeWarningResult result = new SafeWarningResult();
            result.setRuleNo("sjaq008");
            result.setHitTime(DateUtil.date2String(new Date()));
            result.setText("司机确认到达目的地");
            result.setSort(2);
            result.setGender(0);
            returnResult.add(result);

            //先插入该订单开始
            DriverOrderInfo orderInfo = this.driverOrderInfoMapper.selectOne(new QueryWrapper<DriverOrderInfo>().eq("order_id", tcOrderId).last("limit 1"));
            if(orderInfo != null){
                if(orderInfo.getOrderFinished().after(DateUtil.string2Date("2022-01-01 00:00:01"))){
                } else {
                    orderInfo.setOrderFinished(new Date());
                    this.driverOrderInfoMapper.updateById(orderInfo);
                }
            }
        }
        if(returnResult.size() > 0){
            returnResult.sort(Comparator.comparing(data->data.getSort()));
            context.getDto().setCode(1);
            context.getDto().setObj(returnResult.get(0));
            //调用萌艇接口
            DriverWarningDTO dto = new DriverWarningDTO(supplierOrderId,returnResult.get(0).getRuleNo());
            mqSendService.driverWarnNotify(JsonUtils.json(dto),context.getParam().getString("productLine"),0L);
        }
        if(forceResult.size() > 0){
            int i = 1;
            for(SafeWarningResult result : forceResult){
                context.getDto().setCode(1);
                context.getDto().setObj(result);
                DriverWarningDTO dto = new DriverWarningDTO(supplierOrderId,result.getRuleNo());
                mqSendService.driverWarnNotify(JsonUtils.json(dto),context.getParam().getString("productLine"),returnResult.size()==0?0L:(DateUtil.addMinute(new Date(),i).getTime()));
                i = i+1;
            }
        }
    }

    public Boolean validParam(Map<String,Object> param){
        if(param.get("productLine") == null){
            return false;
        }
        if(param.get("orderId") == null){
            return false;
        }
        return true;
    }

    public MtDriverConfig queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("mt_driver_config");
            if(StringUtils.isNotBlank(configJson)){
                MtDriverConfig config = JSONObject.parseObject(configJson, MtDriverConfig.class);
                return config;
            }
        } catch (Exception e) {
        }
        return null;
    }

}
