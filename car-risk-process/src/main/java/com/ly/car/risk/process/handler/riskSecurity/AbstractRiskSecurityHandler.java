package com.ly.car.risk.process.handler.riskSecurity;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.client.VirtualPhoneApiClient;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderVirtualCallRecordMapper;
import com.ly.car.risk.process.service.RiskWarnService;
import com.ly.car.risk.process.service.VirtualPhoneRecordService;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.imGrooup.SensitiveTextService;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Description of AbstractRiskSecurityHandler
 *
 * <AUTHOR>
 * @date 2024/3/14
 * @desc
 */
@Slf4j
public abstract class AbstractRiskSecurityHandler implements RiskSecurityHandler {


    protected Map<String, Boolean> queryConfig() {
        try {
            String configJson = ConfigCenterClient.get("tencent_ability");
            Map<String, Boolean> config = JSONObject.parseObject(configJson, Map.class);
            log.info("[][][][]获取腾讯能力包开关:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取腾讯能力包开关:", e);
        }
        return null;
    }
}