package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.dcdborder.mapper.OrderDataMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskDataMapper;
import com.ly.car.risk.process.service.dto.OrderRiskDataDTO;
import com.ly.car.risk.process.service.dto.RiskOrderConfig;
import com.ly.car.sharding.order.entity.OrderSupplierBill;
import com.ly.car.sharding.order.mapper.OrderSupplierBillMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderDataService {

    @Resource
    private OrderDataMapper orderDataMapper;
    @Resource
    private RiskOrderManageService  riskOrderManageService;
    @Resource
    private RiskDataMapper              riskDataMapper;
    @Resource
    private OrderSupplierBillMapper orderSupplierBillMapper;

    public void dealOrderRisk(String startTime,String endTime){
        log.info("[][][][]当前查询时间:{},{}", startTime,endTime);
        List<OrderRiskDataDTO> riskDataDTOS = orderDataMapper.getOrderInfoLink(startTime,endTime);
        List<OrderRiskDataDTO> riskNewDataDTOS = riskDataMapper.getNewOrderInfoLink(startTime,endTime);
        riskDataDTOS.addAll(riskNewDataDTOS);
        if(CollectionUtils.isEmpty(riskDataDTOS)){
            return;
        }
        Map<String, RiskOrderManage> riskOrderManageMap = new HashMap<>();
        RiskOrderConfig config = getConfig();
        if(config == null){
            return;
        }
        //过滤掉曹操供应商金额为0 的
        List<String> orderIds = riskDataDTOS.stream().filter(data->data.getSupplierCodeFull().equals("CaoCao") || data.getSupplierCodeFull().equals("T3")).map(OrderRiskDataDTO::getOrderId).collect(Collectors.toList());
        //查下供应商单据表
        // todo 新订单如何排除金额为0
        List<OrderSupplierBill> supplierBills = orderSupplierBillMapper.findByOrderIds(orderIds);
        // 曹操、T3总金额为0的订单
        List<String> filterOrderIds = supplierBills.stream().filter(data->data.getTotalFee().compareTo(BigDecimal.ZERO)==0).map(OrderSupplierBill::getOrderId).collect(Collectors.toList());
        riskDataDTOS.forEach(data->{
            if(data.getSupplierCodeFull().startsWith("DidiPlatform") && !data.getSupplierCodeFull().equals("DidiPlatform_didi")){
                //非滴滴自营不走
                return;
            }
            if(filterOrderIds.contains(data.getOrderId())){
                //曹操总金额为0 的不走
                return;
            }
            log.info("[][][][]当前完单:{}", JsonUtils.json(data));
            boolean flag = false;
            try {
                RiskOrderManage manage = new RiskOrderManage();
                manage.setOrderId(data.getOrderId());
                manage.setIsRisk(1);
                //如祺暂时过滤单独规则的，先兼容一下，后面删掉
                if(data.getSupplierCodeFull().equals("T3")){
                    if(data.getDuration() < 3){
                        riskOrderManageMap.put(data.getOrderId(), manage);
                        manage.setRiskType(5);
                        if (StringUtils.isBlank(manage.getRuleNo())) {
                            manage.setRuleNo("026");
                        } else {
                            manage.setRuleNo(manage.getRuleNo() + ",026");
                        }
                        flag = true;
                    }
                } else {
                    if (data.getDuration() < config.getOrderActTime()) {
                        riskOrderManageMap.put(data.getOrderId(), manage);
                        manage.setRiskType(5);
                        if (StringUtils.isBlank(manage.getRuleNo())) {
                            manage.setRuleNo("026");
                        } else {
                            manage.setRuleNo(manage.getRuleNo() + ",026");
                        }
                        flag = true;
                    }
                }
                if(data.getSupplierCodeFull().equals("T3")){
                    if(data.getDistance().compareTo(new BigDecimal("0.4"))<0 && StringUtils.isNotBlank(manage.getRuleNo())){
                        riskOrderManageMap.put(data.getOrderId(), manage);
                        manage.setRiskType(3);
                        if (StringUtils.isBlank(manage.getRuleNo())) {
                            manage.setRuleNo("025");
                        } else {
                            manage.setRuleNo(manage.getRuleNo() + ",025");
                            manage.setRiskType(5);
                        }
                        flag = true;
                    } else {
                        flag = false;
                    }
                } else {
                    if (data.getDistance().compareTo(config.getOrderKilo()) < 0) {
                        riskOrderManageMap.put(data.getOrderId(), manage);
                        manage.setRiskType(3);
                        if (StringUtils.isBlank(manage.getRuleNo())) {
                            manage.setRuleNo("025");
                        } else {
                            manage.setRuleNo(manage.getRuleNo() + ",025");
                            manage.setRiskType(5);
                        }
                        flag = true;
                    }
                }

                if(data.getTotalAmount().compareTo(config.getOrderAmount()) > 0){
                    riskOrderManageMap.put(data.getOrderId(),manage);
                    manage.setRiskType(21);
                    if(StringUtils.isBlank(manage.getRuleNo())){
                        manage.setRuleNo("027");
                    } else {
                        manage.setRuleNo(manage.getRuleNo()+",027");
                    }
                    flag = true;
                }
                if(flag){
                    riskOrderManageService.addRiskOrder(manage);
                }
//                if(StringUtils.isNotBlank(manage.getRuleNo())){
//                    distributionRiskManageService.addByRule(manage.getOrderId(), manage.getRuleNo(),3,4,null,null,5 );
//                }
            } catch (Exception e){
                log.warn("[][][][]命中风险规则完单:{}",JsonUtils.json(data));
                log.error("[][][][]命中风险规则报错:{}",e);
            }
        });
    }

    public RiskOrderConfig getConfig(){
        //订单行驶时间 单位 分钟
        Integer orderActTime = 2;
        //订单形式公里数
        BigDecimal orderKilo = BigDecimal.ONE;
        //订单金额
        BigDecimal orderAmount = new BigDecimal("20");
        try {
            String orderConfig = ConfigCenterClient.get("common.risk.only.order");
            if(StringUtils.isNotBlank(orderConfig)){
                JSONObject jsonObject = JSON.parseObject(orderConfig);
                orderActTime = jsonObject.getInteger("orderActTime");
                orderKilo = jsonObject.getBigDecimal("orderKilo");
                orderAmount = jsonObject.getBigDecimal("orderAmount");
            }
            RiskOrderConfig config = new RiskOrderConfig();
            config.setOrderAmount(orderAmount);
            config.setOrderKilo(orderKilo);
            config.setOrderActTime(orderActTime);
            return config;
        } catch (Exception e) {
            log.error("[][][][]获取单笔订单配置报错:",e);
        }
        return null;
    }


}
