package com.ly.car.risk.process.handler.job;

import com.alibaba.fastjson.JSON;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.utils.LoggerUtils;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Description of AbstractWashJobHandler
 *
 * <AUTHOR>
 * @date 2024/12/17
 * @desc
 */
@Slf4j
public abstract class AbstractWashJobHandler implements WashJobHandler {

    @Resource
    protected CarRiskOrderDetailMapper orderDetailMapper;


    @Override
    public List<RiskOrderManage> doJob(String startTime, String endTime) {
        List<RiskOrderManage> orderManages = new ArrayList<>();
        try {
            List<CarRiskOrderDetail> riskOrders = findRiskOrders(startTime, endTime);
            for (CarRiskOrderDetail riskOrder : riskOrders) {
                RiskOrderManage manage = new RiskOrderManage();
                manage.setOrderId(riskOrder.getOrderSerialNo());
                manage.setIsRisk(1);
                manage.setRiskType(support().getRiskType().getCode());
                manage.setRuleNo(support().getType());
                orderManages.add(manage);
            }
            return orderManages;
        } catch (Exception e) {
            LoggerUtils.error(log, "job-{} 异常", e, support().getType());
            return orderManages;
        }
    }

    public abstract List<CarRiskOrderDetail> findRiskOrders(String startTime, String endTime);
    
    protected String getName(){
        return this.getClass().getSimpleName();
    }
    
    @Override
    public Boolean exeAble() {
        String washJobConfig = ConfigCenterService.getDefault("WASH_JOB_CONFIG", "{}");
        Object object = JSON.parseObject(washJobConfig).get(getName());
        if (object == null) {
            return true;
        }
        return (boolean) object;
    }
}