package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MtFilterContext {

    public JSONObject param;
    public RiskResultNewDTO dto;
    public List<Integer> whiteType;
    public List<Integer> blackType;
    public DriverCheckService driverCheckService;
    public HcSceneConfig hcSceneConfig;

    public MtFilterContext(){
        this.dto = new RiskResultNewDTO();
    }
}
