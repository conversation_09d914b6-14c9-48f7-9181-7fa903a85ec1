package com.ly.car.risk.process.repo.dcdborder.mapper;

import com.ly.car.risk.process.service.dto.OrderRiskDataDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface OrderDataMapper {

    @Select(
            "   SELECT  " +
            "       oi.order_id,oi.total_amount,osb.distance,osb.duration,oi.supplier_code_full" +
            "   FROM    " +
            "       order_info oi   " +
            "       left join order_supplier_bill osb on oi.order_id = osb.order_id " +
            "   where   " +
            "       oi.finish_time BETWEEN #{startTime} and #{endTime} " +
            "       and  oi.status in (300, 400)  " +
            "       and oi.product_id in (11,19) "
    )
    List<OrderRiskDataDTO> getOrderInfoLink(@Param("startTime") String startTime, @Param("endTime") String endTime);

    @Select("<script>" +
            "   SELECT " +
            "  oi.order_id, " +
            "  oi.finish_time, " +
            "  oi.status, " +
            "  oi.supplier_code, " +
            "  oi.amount_type, " +
            "  oi.total_amount, " +
            "  op.reality_amount, " +
            "  op.twice_need_amount, " +
            "  oa.estimate_kilo, " +
            "  oa.estimate_minute, " +
            "  oa.actual_kilo, " +
            "  oa.actual_minute, " +
            "  osb.total_fee, " +
            "  osb.park_fee, " +
            "  osb.bridge_fee, " +
            "  osb.other_fee, " +
            "  osb.high_way_fee, " +
            "  osb.wait_fee " +
            "FROM " +
            "  order_info oi " +
            "  left join order_price op on oi.order_id = op.order_id " +
            "  left join order_address oa on oi.order_id = oa.order_id " +
            "  left join order_supplier_bill osb on oi.order_id = osb.order_id " +
            "where " +
            "  oi.finish_time BETWEEN #{startTime} and #{endTime}" +
            "  and oi.product_id in (11,19) " +
            "</script>")
    List<DcdbOrderAmountData> queryByFinishTime(@Param("startTime") Date startTime, @Param("endTime")Date endTime);

}
