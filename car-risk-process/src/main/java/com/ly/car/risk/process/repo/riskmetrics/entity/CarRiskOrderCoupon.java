package com.ly.car.risk.process.repo.riskmetrics.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * car_risk_order_coupon
 * <AUTHOR>
@Data
public class CarRiskOrderCoupon implements Serializable {
    private Long id;

    /**
     * 订单号
     */
    private String orderSerialNo;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 业务类型 3-优惠券
     */
    private Integer businessType;

    /**
     * 销售金额
     */
    private BigDecimal salePrice;


    /**
     * 实际金额
     */
    private BigDecimal realPrice;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 类别
     */
    private String category;

    /**
     * 小类
     */
    private String subcategory;

    /**
     * 类型
     */
    private String type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * item流水号
     */
    private String itemId;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 车牌号
     */
    private String carNum;

    /**
     * 订单渠道，公司渠道码
     */
    private Integer orderChannel;

    /**
     * 上车点城市code
     */
    private String departureCityCode;

    /**
     * 行程结束时间
     */
    private Date gmtTripFinished;

    private static final long serialVersionUID = 1L;
}