package com.ly.car.risk.process.client.model.hellobike;

import lombok.Data;

/**
 * Description of BaseReqDTO
 *
 * <AUTHOR>
 * @date 2024/5/20
 * @desc
 */
@Data
public class HelloBikeBaseReqDTO {

    /** API接口名称 */
    private String method;

    /** 开放平台分配给app的AppKey */
    private String appKey;

    /** unix时间戳 */
    private String timestamp;

    /** 合作伙伴身份标识 */
    private String partnerId;

    /** API输入参数签名结果，签名算法参照下面的介绍 */
    private String sign;

    /** 用户鉴权信息，需要鉴权的接口必填 */
    private String token;


}