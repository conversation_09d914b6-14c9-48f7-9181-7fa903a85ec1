package com.ly.car.risk.process.utils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 经纬度计算工具类
 */
public class CoordUtil {
    // 地球半径 单位米
    private static final double EARTH_RADIUS = 6371004.0;

    // 最大精度(double 有效位 15位)
    private static final double MAX_PRECISION = 0.0000000000000001;

    // 180度
    private static final int RADIUS_180 = 180;

    private static final double DOUBLE_ZERO = 0D;
    private static final double DOUBLE_ONE = 1.0D;

    // 预先计算1 避免不必要的实时计算 浪费性能
    private static final double PRE_CAL1 = Math.PI / RADIUS_180;

    // 判断两个坐标是否相同的精确度
    private static final double DELEA_PRECISION = 0.0000001;

    public static void main(String[] args) {
        double distance = getDistance("109.44958", "36.6168", "109.45112", "36.617");
        System.out.println(distance);
    }

    /**
     * 计算两点距离(优化版本) 经纬度表示
     *
     * @param lon1
     *            点1的经度
     * @param lat1
     *            点1的纬度
     * @param lon2
     *            点2的经度
     * @param lat2
     *            点1的纬度
     * @return 两点的弧度距离 单位:米
     */
    public static double getDistance(double lon1, double lat1, double lon2, double lat2) {
        /**
         * 当两个经纬度的坐标相等时，判断为同一个地点，直接返回距离为零
         */
        if (Math.abs(lat1 - lat2) < DELEA_PRECISION && Math.abs(lon1 - lon2) < DELEA_PRECISION) {
            return 0;
        }

        double ax = lat1 * PRE_CAL1;
        double bx = lat2 * PRE_CAL1;

        /**
         * 由于double精度的问题 同样的坐标 计算出来的tempValueBeforeAcos
         * 可能会大于1(如1.0000000000000002 正确值应该是1.0)
         * 此时如果执行Math.acos(tempValueBeforeAcos) 会出现NaN情况 这边的判断 就是避免这种情况 但误差大于1时
         * 就强制等于1 避免出现NaN问题
         */
        double tempValueBeforeAcos = Math.sin(ax) * Math.sin(bx)
                + Math.cos(ax) * Math.cos(bx) * Math.cos((lon2 - lon1) * PRE_CAL1);
        return (tempValueBeforeAcos - DOUBLE_ONE) < MAX_PRECISION ? EARTH_RADIUS * Math.acos(tempValueBeforeAcos)
                : DOUBLE_ZERO;
    }

    public static double getDistance(String lon1, String lat1, String lon2, String lat2) {
        double startLongitude = Double.parseDouble(lon1);
        double startLatitude = Double.parseDouble(lat1);
        double endLongitude = Double.parseDouble(lon2);
        double endLatitude = Double.parseDouble(lat2);
        return getDistance(startLongitude, startLatitude, endLongitude, endLatitude);
    }

    public static double getDistance(BigDecimal lon1, BigDecimal lat1, BigDecimal lon2, BigDecimal lat2) {
        double startLongitude = lon1.doubleValue();
        double startLatitude = lat1.doubleValue();
        double endLongitude = lon2.doubleValue();
        double endLatitude = lat2.doubleValue();
        return getDistance(startLongitude, startLatitude, endLongitude, endLatitude);
    }


    /*
     * 计算两点对于正北方向的朝向角度 [0,360]
     * @param {*} start format:{'latitude': 30, 'longitude': 120 }
     * @param {*} end
     */
    public static double bearing(double startLat,double startLng, double endLat,double endLng) {
        double rad = Math.PI / 180,
                lat1 = startLat * rad,
                lat2 = endLat * rad,
                lon1 = startLng * rad,
                lon2 = endLng * rad;
        double a = Math.sin(lon2 - lon1) * Math.cos(lat2);
        double b = Math.cos(lat1) * Math.sin(lat2) -
                Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
        double angle = radiansToDegrees(Math.atan2(a, b));
        if (angle < 0) {
            angle = 360 + angle;
        }
        return angle;
    }

    /*
     * 弧度转换为角度
     */
    private static double radiansToDegrees(double radians) {
        double degrees = radians % (2 * Math.PI);
        return degrees * 180 / Math.PI;
    }

    public static double getAngle(String lat1,String lng1,String lat2,String lng2,String lat3,String lng3){
        // 计算三维坐标向量
        double[] OA = { Math.cos(Double.parseDouble(lat1)) * Math.cos(Double.parseDouble(lng1)),
                Math.sin(Double.parseDouble(lat1)) * Math.cos(Double.parseDouble(lng1)), Math.sin(Double.parseDouble(lng1)) };
        double[] OB = { Math.cos(Double.parseDouble(lat2)) * Math.cos(Double.parseDouble(lng2)),
                Math.sin(Double.parseDouble(lat2)) * Math.cos(Double.parseDouble(lng2)), Math.sin(Double.parseDouble(lng2)) };
        double[] OC = { Math.cos(Double.parseDouble(lat3)) * Math.cos(Double.parseDouble(lng3)),
                Math.sin(Double.parseDouble(lat3)) * Math.cos(Double.parseDouble(lng3)), Math.sin(Double.parseDouble(lng3)) };
        // 计算向量 AB 和 AC 的点积
        double[] AB = { OB[0] - OA[0], OB[1] - OA[1], OB[2] - OA[2] };
        double[] AC = { OC[0] - OA[0], OC[1] - OA[1], OC[2] - OA[2] };
        double dotProduct = AB[0] * AC[0] + AB[1] * AC[1] + AB[2] * AC[2];
        // 计算夹角
        double cos = dotProduct / (Math.sqrt(AB[0] * AB[0] + AB[1] * AB[1] + AB[2] * AB[2]) * Math.sqrt(AC[0] * AC[0] + AC[1] * AC[1] + AC[2] * AC[2]));
        double angle = Math.acos(cos);
        // 转换为角度值
        return Math.toDegrees(angle);
    }

    public static double getPlaneAngle(String lat1,String lng1,String lat2,String lng2,String lat3,String lng3){
        // 经纬度点A
        double ax = Double.parseDouble(lng1);
        double ay = Double.parseDouble(lat1);

        // 经纬度点B
        double bx = Double.parseDouble(lng2);
        double by = Double.parseDouble(lat2);

        // 经纬度点C
        double cx = Double.parseDouble(lng3);
        double cy = Double.parseDouble(lat3);

        // 将经纬度转换成平面坐标系中的坐标
        double[] a = lonLat2Mercator(ax, ay);
        double[] b = lonLat2Mercator(bx, by);
        double[] c = lonLat2Mercator(cx, cy);

        // 计算向量AB和向量AC的夹角(弧度制)
        double angle_rad = Math.atan2(a[1] - b[1], a[0] - b[0]) - Math.atan2(a[1] - c[1], a[0] - c[0]);

        // 将弧度制转换为角度制
        double angle_deg = Math.toDegrees(angle_rad);

        // 取绝对值，保证角度在0到180度之间
        if (angle_deg < 0) {
            angle_deg += 180;
        }

       return angle_deg;
    }

    /**
     * 将经纬度转换成平面坐标系中的坐标
     *
     * @param lon 经度
     * @param lat 纬度
     * @return 平面坐标系中的坐标
     */
    private static double[] lonLat2Mercator(double lon, double lat) {
        double x = lon * 20037508.34 / 180;
        double y = Math.log(Math.tan((90 + lat) * Math.PI / 360)) / (Math.PI / 180);
        y = y * 20037508.34 / 180;
        return new double[]{x, y};
    }

//    public static void main(String[] args) {
//        System.out.println(getDegree(
//                Double.parseDouble("113.0112130"),
//                Double.parseDouble("28.1959460"),
//
//                Double.parseDouble("113.1661830"),
//                Double.parseDouble("27.8427350"),
//
//                Double.parseDouble("113.128427"),
//                Double.parseDouble("27.999248")));
//    }

    public static int getDegree(double vertexPointX, double vertexPointY, double point0X, double point0Y, double point1X, double point1Y) {
        //向量的点乘
        double vector = (point0X - vertexPointX) * (point1X - vertexPointX) + (point0Y - vertexPointY) * (point1Y - vertexPointY);
        //向量的模乘
        double sqrt = Math.sqrt(
                (Math.abs((point0X - vertexPointX) * (point0X - vertexPointX)) + Math.abs((point0Y - vertexPointY) * (point0Y - vertexPointY)))
                        * (Math.abs((point1X - vertexPointX) * (point1X - vertexPointX)) + Math.abs((point1Y - vertexPointY) * (point1Y - vertexPointY)))
        );
        //反余弦计算弧度
        double radian = Math.acos(vector / sqrt);
        //弧度转角度制
        return (int) (180 * radian / Math.PI);
    }


}
