package com.ly.car.risk.process;

import com.ly.car.configcenter.ConfigCenterConfiguration;
import com.ly.car.fantasy.client.EnableFantasyClient;
import com.ly.car.fantasy.client.FantasyClient;
import com.ly.car.monitor.EnableMonitor;
import com.ly.car.risk.process.bean.properties.TurboMqProperties;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.EnableLoadTimeWeaving;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;


@Import({ConfigCenterConfiguration.class})
@SpringBootApplication(scanBasePackages = "com.ly.car.risk.process",
        exclude = {DataSourceAutoConfiguration.class})
@EnableFantasyClient({@FantasyClient("com.ly.car.risk.process.api")})
@EnableRetry
@EnableMonitor
@EnableLoadTimeWeaving
@EnableAspectJAutoProxy
@EnableConfigurationProperties({UrlsProperties.class, TurboMqProperties.class})
public class RiskProcessApplication {
    public static void main(String[] args) {
        SpringApplication.run(RiskProcessApplication.class);
    }
}
