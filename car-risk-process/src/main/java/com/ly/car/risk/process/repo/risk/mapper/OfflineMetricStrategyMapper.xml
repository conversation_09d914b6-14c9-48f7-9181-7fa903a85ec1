<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.risk.mapper.OfflineMetricStrategyMapper">
    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.risk.mapper.entity.OfflineMetricStrategy">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="strategy_no" jdbcType="VARCHAR" property="strategyNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="risk_type" jdbcType="INTEGER" property="riskType"/>
        <result column="during" jdbcType="VARCHAR" property="during"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="product_lines" jdbcType="VARCHAR" property="productLines"/>
        <result column="strategy_word" jdbcType="VARCHAR" property="strategyWord"/>
        <result column="control_type" jdbcType="INTEGER" property="controlType"/>
        <result column="hit_field" jdbcType="VARCHAR" property="hitField"/>
        <result column="hit_action" jdbcType="INTEGER" property="hitAction"/>
        <result column="control_time" jdbcType="INTEGER" property="controlTime"/>
        <result column="expression" jdbcType="VARCHAR" property="expression"/>
        <result column="dispose_action" jdbcType="INTEGER" property="disposeAction"/>
        <result column="notice_type" jdbcType="INTEGER" property="noticeType"/>
        <result column="channels" jdbcType="VARCHAR" property="channels"/>
        <result column="city_id" jdbcType="INTEGER" property="cityId"/>
        <result column="supplier_codes" jdbcType="VARCHAR" property="supplierCodes"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, `name`, strategy_no, `status`, risk_type, during, `description`, product_lines,
    strategy_word, control_type, hit_field, hit_action, control_time, expression, dispose_action,
    notice_type, create_time, update_time, create_user, update_user
    </sql>

    <resultMap id="SceneStrategyMap" type="com.ly.car.risk.process.repo.risk.mapper.entity.OfflineRiskSceneStrategyDTO">
        <result property="strategyId" column="strategy_id"/>
        <result property="productLines" column="product_lines"/>
        <result property="strategyNo" column="strategy_no"/>
        <result property="riskType" column="risk_type"/>
        <result property="expression" column="expression"/>
        <result property="strategyWord" column="strategy_word"/>
        <result property="controlType" column="control_type"/>
        <result property="controlTime" column="control_time"/>
        <result property="hitField" column="hit_field"/>
        <result property="hitAction" column="hit_action"/>
        <result property="disposeAction" column="dispose_action"/>
        <result property="status" column="status"/>
        <result property="during" column="during"/>
        <result property="script" column="script"/>
        <result property="channels" column="channels"  />
        <result property="cityId" column="city_id" />
        <result property="supplierCodes" column="supplier_codes" />
    </resultMap>

    <select id="findStrategy" resultMap="SceneStrategyMap">
        SELECT
               strategy.id as strategy_id,
               strategy.product_lines,
               strategy.strategy_no,
               strategy.risk_type,
               strategy.expression,
               strategy.strategy_word,
               strategy.control_type,
               strategy.control_time,
               strategy.hit_field,
               strategy.hit_action,
               strategy.dispose_action,
               strategy.during,
               strategy.script,
               strategy.status,
               strategy.supplier_codes,
               strategy.city_id,
               strategy.channels
        FROM offline_metric_strategy strategy
        where strategy.status not in (2)
    </select>
</mapper>