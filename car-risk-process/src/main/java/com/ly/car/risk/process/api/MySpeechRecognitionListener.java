package com.ly.car.risk.process.api;

import com.ly.car.utils.JsonUtils;
import com.tencent.asr.model.SpeechRecognitionResponse;
import com.tencent.asr.service.SpeechRecognitionListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MySpeechRecognitionListener extends SpeechRecognitionListener {
    @Override
    public void onRecognitionResultChange(SpeechRecognitionResponse speechRecognitionResponse) {
        //识别结果
        log.info("[][][][]onRecognitionResultChange:{}", JsonUtils.json(speechRecognitionResponse));
    }

    @Override
    public void onRecognitionStart(SpeechRecognitionResponse speechRecognitionResponse) {
        //开始识别
        log.info("[][][][]onRecognitionStart:{}", JsonUtils.json(speechRecognitionResponse));
    }

    @Override
    public void onSentenceBegin(SpeechRecognitionResponse speechRecognitionResponse) {
        //一句话开始
        log.info("[][][][]onSentenceBegin:{}", JsonUtils.json(speechRecognitionResponse));
    }

    @Override
    public void onSentenceEnd(SpeechRecognitionResponse speechRecognitionResponse) {
        //一句话结束
        log.info("[][][][]onSentenceEnd:{}", JsonUtils.json(speechRecognitionResponse));
    }

    @Override
    public void onRecognitionComplete(SpeechRecognitionResponse speechRecognitionResponse) {
        //识别结束
        log.info("[][][][]onRecognitionComplete:{}", JsonUtils.json(speechRecognitionResponse));
    }

    @Override
    public void onFail(SpeechRecognitionResponse speechRecognitionResponse) {
        log.info("[][][][]onFail:{}", JsonUtils.json(speechRecognitionResponse));
    }

    @Override
    public void onMessage(SpeechRecognitionResponse speechRecognitionResponse) {
        //onMessage
        log.info("[][][][]onMessage:{}", JsonUtils.json(speechRecognitionResponse));
    }
}
