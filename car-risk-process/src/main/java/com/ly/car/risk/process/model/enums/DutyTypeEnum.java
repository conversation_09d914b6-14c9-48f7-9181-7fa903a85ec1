package com.ly.car.risk.process.model.enums;

import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum DutyTypeEnum {

    DAMAGED(0, "有损"),

    NO_DAMAGED(1, "无损"),

    ;

    private int type;

    private String desc;

    private static final Map<Integer, DutyTypeEnum> ENUMS = new HashMap();


    DutyTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DutyTypeEnum getByType(int type) {
        return ENUMS.get(type);
    }


    static {
        DutyTypeEnum[] dutyTypes = values();
        for(int i = 0; i < dutyTypes.length; i++) {
            DutyTypeEnum de = dutyTypes[i];
            ENUMS.put(de.type, de);
        }
    }
}
