package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/*
* 2-6 查询当前订单是否命中敏感词
* */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRiskUserCancelService extends FilterSfcHandler{

    @Resource
    SensitiveRecordMapper sensitiveRecordMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public void doHandler(FilterSfcContext context) {
        List<SensitiveRecord> recordList = sensitiveRecordMapper.selectList(new QueryWrapper<SensitiveRecord>()
                .eq("order_id", context.getOrderId())
        );
        List<RiskResultDTO> dtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(recordList)){
            List<SensitiveRecord> collect = recordList.stream().filter(data -> data.getWordType() == 5).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                RiskResultDTO dto = new RiskResultDTO(1, "风控不通过-命中敏感词", "cancel_001", null);
                dtoList.add(dto);
            }
        }
        RBucket<String> bucket = redissonClient.getBucket("risk:driver:0913:plate");
        if(bucket.isExists() && StringUtils.isNotBlank(bucket.get()) && StringUtils.isNotBlank(context.getParams().getPlate())){
            if(bucket.get().contains(context.getParams().getPlate())){
                RiskResultDTO dto = new RiskResultDTO(1, "风控不通过-命中高危司机取消", "cancel_002", null);
                dtoList.add(dto);
            }
        }
        context.getUiResult().setData(dtoList);
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            List<String> ruleList = dtoList.stream().map(RiskResultDTO::getRuleNo).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ruleList)){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(StringUtils.join(ruleList,","),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }

        }
    }
}
