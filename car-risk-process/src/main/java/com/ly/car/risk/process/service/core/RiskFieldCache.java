package com.ly.car.risk.process.service.core;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskFieldMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskField;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskScene;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RiskFieldCache {

    private static final String KEY = "risk_field_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskFieldMapper riskFieldMapper;

    @Scheduled(fixedRate = 1000L * 60L,initialDelay = 0)
    public void init(){
        List<RiskField> list = this.riskFieldMapper.selectList(new QueryWrapper<RiskField>()
                .eq("is_deleted", 0)
        );
        for(RiskField field : list){
            redissonClient.getBucket(KEY+field.getFieldNo()).set(JSONObject.toJSONString(field),1,TimeUnit.DAYS);
        }
    }

    public RiskField loadField(String key){
        RBucket<String> riskFieldRBucket = redissonClient.getBucket(KEY + key);
        if(riskFieldRBucket == null){
            return null;
        }
        return JSONObject.parseObject(riskFieldRBucket.get(),RiskField.class);
    }
}
