package com.ly.car.risk.process.strategy;

import static com.ly.car.risk.process.constants.StrategySceneEnum.DRIVER_ACCEPT_ORDER;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.scene.SpecialSceneHandlerFactory;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.core.DisposeCenterService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.mtGroup.MqSendConvertService;
import com.ly.car.risk.process.strategy.blacklist.BlackListHandler;
import com.ly.car.risk.process.strategy.model.RiskRuleResult;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.strategy.model.RiskStrategyDetail;
import com.ly.car.risk.process.strategy.model.RiskStrategyResult;
import com.ly.car.risk.process.strategy.model.StrategyContext;
import com.ly.car.risk.process.strategy.model.detail.RiskFieldDetail;
import com.ly.car.risk.process.strategy.model.detail.RiskRuleDetail;
import com.ly.car.risk.process.utils.GroovyScriptUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.StrategyUtil;
import com.ly.car.utils.JsonUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Description of RiskStrategyHandler
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Service
@Slf4j
public class RiskStrategyHandler {
    
    public static final String PRODUCT_LINE = "productLine";
    public static final String CHANNEL      = "channel";
    public static final String SCENE        = "scene";
    
    public static final String PASSENGER_PHONE = "passengerPhone";
    
    public static final String USER_PHONE = "userPhone";
    
    public static final String CAR_NUM = "carNum";
    
    public static final String MEMBER_ID = "memberId";
    
    public static final String UNION_ID = "unionId";
    
    public static final String ORDER_ID = "orderId";
    
    public static final String SUPPLIER_CODE = "supplierCode";
    
    public static final String SUPPLIER_NAME = "supplierName";
    
    public static final String DEVICE_ID = "deviceId";
    
    public static final String ORDER = "order";
    
    public static final String IP = "ip";
    
    @Resource
    private RiskStrategyHelper strategyHelper;
    
    @Resource
    private DisposeCenterService disposeCenterService;
    
    @Resource
    private RiskHitService riskHitService;
    
    @Resource
    private SpecialSceneHandlerFactory specialSceneHandlerFactory;
    
    @Resource
    private MqSendConvertService mqSendConvertService;

    @Resource
    private BlackListHandler blackListHandler;
    
    @Resource
    private CarOrderService  carOrderService;
    
    private static final ThreadLocal<HashMap<String, List<CarRiskOrderDetail>>> localCache = ThreadLocal.withInitial(() -> new HashMap<>());
    private static final ThreadLocal<HashMap<String, Object>> localObjectCache = ThreadLocal.withInitial(() -> new HashMap<>());

    public RiskSceneResult unifyCheck(UnifyCheckRequest request) throws BizException {
        
        // 参数校验
        paramCheck(request);


        RiskSceneResult blackListCheckResult = blackListHandler.blackListCheck(request);
        if(null != blackListCheckResult){
            return blackListCheckResult;
        }

        if (request.isDistributionFlag()) {
            return RiskSceneResult.pass("分销单判定风险名单通过");
        }

        // 特殊校验
        RiskSceneResult specialSceneCheck = specialHandlerCheck(request);
        if (null != specialSceneCheck && specialSceneCheck.isRiskFlag()) {
            return specialSceneCheck;
        }

        // 策略校验前的一些动作
        beforeStrategyCheck(request);

        // 策略校验
        RiskSceneResult riskSceneResult = strategyCheck(request);

        // 策略校验后的一些动作
        afterStrategyCheck(request, riskSceneResult);

        return riskSceneResult;
    }
    
    
    private void afterStrategyCheck(UnifyCheckRequest request, RiskSceneResult riskSceneResult) {
        // MT 且 司机认证
        if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())
                && Objects.equals(request.getScene(), StrategySceneEnum.DRIVER_AUTHENTICATION.getScene())) {
            // 非风险则通知MT 司机认证成功
            if (null != riskSceneResult && !riskSceneResult.isRiskFlag()) {
                RiskResultNewDTO dto = new RiskResultNewDTO();
                Map<String, Object> data = new HashMap<>();
                data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
                data.put("idCard", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO));
                dto.setObj(data);
                mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            }
        }
    }
    
    private RiskSceneResult specialHandlerCheck(UnifyCheckRequest request) {
        RiskSceneResult riskSceneResult = specialSceneHandlerFactory.handlerCheck(request);
        return riskSceneResult;
    }
    
    private void beforeStrategyCheck(UnifyCheckRequest request) {
        
        // 如果是司机接单场景，并且是权益单，则将场景变更为 权益活动
        if (Objects.equals(request.getScene(), DRIVER_ACCEPT_ORDER.getScene()) && request.isRightsOrderFlag()) {
            request.setScene(StrategySceneEnum.RIGHTS_ORDER.getScene());
        }
    }
    
    /**
     * MetricStrategyProductLineEnum
     */
    private Map<String, Object> fillParam(UnifyCheckRequest request) {
        Map<String, Object> params = null == request.getExt() ? new HashMap<>() : request.getExt();
        params.put(SCENE, request.getScene());
        params.put(MEMBER_ID, request.getMemberId());
        params.put(UNION_ID, request.getUnionId());
        params.put(CHANNEL, request.getChannel());
        params.put(PRODUCT_LINE, request.getProductLine());
        params.put(ORDER_ID, request.getOrderId());
        params.put(USER_PHONE, request.getUserPhone());
        params.put(PASSENGER_PHONE, request.getPassengerPhone());
        params.put(CAR_NUM, request.getCarNum());
        params.put(IP, request.getIp());
        params.put(SUPPLIER_CODE, request.getSupplierCode());
        params.put(SUPPLIER_NAME, request.getSupplierName());
        params.put(DEVICE_ID, request.getDeviceId());
        return params;
    }
    
    private void paramCheck(UnifyCheckRequest request) throws BizException {
        if (null == request.getExt()) {
            request.setExt(new HashMap<>());
        }
        CheckUtil.notBlankCheck(request.getScene(), "场景值不可为空");
        CheckUtil.notBlankCheck(request.getProductLine(), "业务线不可为空");
        if (!Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
            CheckUtil.notBlankCheck(request.getChannel(), "渠道不可为空");
        }
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        CheckUtil.notNullCheck(sceneEnum, "场景值异常");
        switch (sceneEnum) {
            case DRIVER_REGISTER:
                CheckUtil.notBlankCheck(request.getDeviceId(), "deviceId不可为空");
                break;
            case USER_CREATE_ORDER:
            case USER_DISPATCHING_ORDER:
                CheckUtil.notBlankCheck(request.getMemberId(), "memberId不可为空");
                break;
            case ACT_COUPON_LQ:
                CheckUtil.notAllNullCheck("memberId和unionId不可同时为空", request.getMemberId(), request.getUnionId());
                break;
            case CANCEL_REMINDER:
                CheckUtil.notBlankCheck(request.getOrderId(),"orderId不可为空");
                break;
            case DRIVER_ACCEPT_ORDER:
            default:
                break;
        }

    }


    public RiskSceneResult strategyCheck(UnifyCheckRequest request) {
        
        Map<String, Object> paramMap = fillParam(request);
        
        try {
            // 1.上下文
            StrategyContext strategyContext = buildContext(paramMap, request);
            // 2.对入参一些字段，进行加密处理，
            encryptParam(strategyContext);
            // 3.找策略
            findStrategy(strategyContext);
            // 4.自检指标
            fillOrderParam(strategyContext);
            // 5.执行
            strategyHandle(strategyContext);
            // 6.针对策略的后置处理
            strategyPostProcessing(strategyContext);
            
            return strategyContext.getResult();
        } finally {
            localCache.remove();
            localObjectCache.remove();
        }
    }
    
    private void fillOrderParam(StrategyContext strategyContext) {
        Map<String, Object> params = strategyContext.getParams();
        
        // 指标需要对当前订单校验，补充订单详情。
        // 需要限制调用次数：目前仅用于8-1
        if (CollectionUtils.isNotEmpty(strategyContext.getStrategyList())
                && StringUtils.isNotBlank((String) params.get(ORDER_ID))
                && Objects.equals(StrategySceneEnum.FINISH_ORDER.getScene(), strategyContext.getRequest().getScene())) {
            
            boolean basedOnCurrent = strategyContext.getStrategyList().stream().anyMatch(s -> s.getRules().stream().anyMatch(r -> r.getRiskFields().stream().anyMatch(f -> Objects.equals(f.getBasedCurrent(), 1))));
            if (basedOnCurrent) {
                CarOrderDetail orderDetail = carOrderService.queryOrderDetail((String) params.get(ORDER_ID));
                params.put(ORDER, orderDetail);
            }
            
        }
    }
    
    /**
     * 1. 组装上下文
     */
    private StrategyContext buildContext(Map<String, Object> params, UnifyCheckRequest request) {
        StrategyContext strategyContext = new StrategyContext();
        strategyContext.setParams(params);
        strategyContext.setRequest(request);
        return strategyContext;
    }
    
    /**
     * 2. 对某些入参字段进行加密操作，这样才能和库里的数据一致
     *
     * @param strategyContext
     */
    private void encryptParam(StrategyContext strategyContext) {
        // 不需要加解密操作
    }
    
    /**
     * 3.获取对应策略
     */
    public void findStrategy(StrategyContext context) {
        Map<String, Object> params = context.getParams();
        // 1. 找到对应策略，策略里面包含了规则及指标
        String productLine = (String) params.get(PRODUCT_LINE);
        String channel = null == params.get(CHANNEL) ? StringUtils.EMPTY : (String) params.get(CHANNEL);
        String scene = (String) params.get(SCENE);
        String supplierCode = (String) params.get(SUPPLIER_CODE);
        List<RiskStrategyDetail> strategyList = strategyHelper.findStrategy(productLine, channel, scene, supplierCode);
        String strategyNos = strategyList.stream().map(RiskStrategyDetail::getStrategyNo).collect(Collectors.joining(","));
        LoggerUtils.info(log, "获取到对应策略:{} ", strategyNos);
        context.setStrategyList(strategyList);
    }
    
    /**
     * 4.策略执行
     *
     * @param context
     */
    private void strategyHandle(StrategyContext context) {
        List<RiskStrategyDetail> strategyList = context.getStrategyList();
        if (CollUtil.isEmpty(strategyList)) {
            return;
        }
        Map<String, RiskStrategyResult> strategyResultMap = new HashMap<>();
        for (RiskStrategyDetail strategyDetail : strategyList) {
            RiskStrategyResult simpleStrategyResult = checkStrategy(strategyDetail, context.getParams());
            strategyResultMap.put(strategyDetail.getStrategyNo(), simpleStrategyResult);
        }
        LoggerUtils.info(log, "策略执行结果:{}", JSON.toJSONString(strategyResultMap));
        context.setStrategyResultMap(strategyResultMap);
    }
    
    /**
     * 5.策略结果的判定
     *
     * @param strategyContext
     */
    private void strategyPostProcessing(StrategyContext strategyContext) {
        // 响应
        RiskSceneResult result = new RiskSceneResult();
        strategyContext.setResult(result);
        
        Map<String, Object> params = strategyContext.getParams();
        // 先释放掉
        params.remove(ORDER);
        Map<String, RiskStrategyResult> strategyResultMap = strategyContext.getStrategyResultMap();
        if (CollUtil.isEmpty(strategyResultMap)) {
            result.setRiskFlag(false);
            result.setRiskMsg("风控策略通过");
            return;
        }
        List<Long> matchStrategyIds = strategyResultMap.values().stream().filter(p -> p.getStrategyMatched()).map(p -> p.getStrategyId()).collect(Collectors.toList());
        List<RiskStrategyDetail> strategyList = strategyContext.getStrategyList().stream()
                .filter(p -> matchStrategyIds.contains(p.getStrategyId())).collect(Collectors.toList());
        
        // 过滤掉用于测试的策略
        strategyList = strategyList.stream().filter(p -> p.getStatus() != 0).collect(Collectors.toList());
        if (CollUtil.isEmpty(strategyList)) {
            result.setRiskFlag(false);
            result.setRiskMsg("命中测试策略，风控策略通过");
            return;
        }
        
        // 走到这必定有值
        RiskStrategyDetail disposeStrategy = strategyList.stream().sorted(Comparator.comparing(RiskStrategyDetail::getLevel).reversed()).findFirst().get();
        
        List<String> matchedStrategyList = strategyList.stream().map(p -> p.getStrategyNo()).collect(Collectors.toList());
        result.setMatchedStrategy(matchedStrategyList);
        
        // 如果有命中策略，计入risk_hit中
        RiskStrategyResult disposeStrategyResult = strategyResultMap.get(disposeStrategy.getStrategyNo());
        UnifyCheckRequest request = strategyContext.getRequest();
        if (null != request && null != disposeStrategyResult) {
            Integer controlType = disposeStrategy.getControlType();
            String hitField = disposeStrategy.getHitField();
            Integer disposeAction = disposeStrategy.getDisposeAction();
            
            request.setHitType(0);
            request.setHitRule(String.join(",", disposeStrategyResult.getMatchRules()));
            request.setStrategyNos(disposeStrategyResult.getStrategyNo());
            request.setHitField(StringUtils.defaultString(hitField));
            request.setControlTarget(null == controlType ? "" : String.valueOf(controlType));
            request.setDisposeAction(null == disposeAction ? "" : String.valueOf(disposeAction));
            riskHitService.initHitRisk(request, strategyContext.getResult());
        }
        
        // 过滤掉通过的策略，如果没有策略了，说明命中的策略都是通过的
        strategyList = strategyList.stream().filter(p -> p.getDisposeAction() != 2).collect(Collectors.toList());
        if (CollUtil.isEmpty(strategyList)) {
            result.setRiskFlag(false);
            result.setRiskMsg("命中策略无需拦截");
            return;
        }
        
        result.setRiskFlag(true);
        result.setRiskMsg(StringUtils.isBlank(disposeStrategy.getStrategyWord()) ? "风控拦截" : disposeStrategy.getStrategyWord());
        
        // 根据命中策略，进行处置
        for (RiskStrategyDetail strategy : strategyList) {
            doStrategyAction(params, strategy);
        }
        
    }
    
    private void doStrategyAction(Map<String, Object> params, RiskStrategyDetail disposeStrategy) {
        if (StringUtils.isBlank(disposeStrategy.getHitField()) || null == disposeStrategy.getControlTime() || disposeStrategy.getControlTime() == 0) {
            return;
        }
        Integer controlTime = disposeStrategy.getControlTime();
        Integer riskType = disposeStrategy.getHitAction();
        String hitField = disposeStrategy.getHitField();
        String customerValue = null;
        
        Integer customerType = 0;
        String passengerPhone = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
        String orderId = (String) params.getOrDefault(ORDER_ID, StringUtils.EMPTY);
        String productLine = (String) params.getOrDefault(PRODUCT_LINE, StringUtils.EMPTY);
        String supplierName = (String) params.getOrDefault(SUPPLIER_NAME, StringUtils.EMPTY);
        if (hitField.equals("driverCardNo")) {
            customerValue = (String) params.getOrDefault(CAR_NUM, StringUtils.EMPTY);
            customerType = RiskCustomerCustomerTypeEnum.car_number.getCode();
        } else if (hitField.equals("userId")) {
            customerValue = (String) params.getOrDefault(MEMBER_ID, StringUtils.EMPTY);
            customerType = RiskCustomerCustomerTypeEnum.user_id.getCode();
        } else if (hitField.equals("phone")) {
            customerValue = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
            customerType = RiskCustomerCustomerTypeEnum.user_phone.getCode();
        }
        //处理拉黑类型
        Integer resultDisposeType = 7;//默认是1v1
        if (riskType == 0) {
            resultDisposeType = 1;//如果命中动作是0全局拉黑的情况
        }
        
        // 萌艇不落名单
        if (!Objects.equals(StringUtils.upperCase(productLine), "MT")) {
            disposeCenterService.actionCustomer(customerValue, passengerPhone, controlTime
                    , orderId, disposeStrategy.getStrategyNo(), resultDisposeType, customerType, supplierName);
        }
    }
    
    private RiskStrategyResult checkStrategy(RiskStrategyDetail strategyDetail, Map<String, Object> params) {
        String strategyScript = strategyDetail.getScript();
        List<RiskRuleDetail> rules = strategyDetail.getRules();
        if (CollUtil.isEmpty(rules)) {
            return RiskStrategyResult.builder().strategyMatched(false).build();
        }
        Map<String, Object> ruleDataMap = new HashMap<>();
        List<String> matchRules = new ArrayList<>();
        for (RiskRuleDetail rule : rules) {
            // 1.校验规则
            RiskRuleResult simpleRuleResult = checkRule(rule, params);
            LoggerUtils.info(log, "规则:{} 执行结果为:{}", rule.getRuleNo(), simpleRuleResult.getRuleMatched());
            // 2.收集规则结果
            ruleDataMap.put("rule" + rule.getRuleId(), simpleRuleResult.getRuleMatched());
            if (simpleRuleResult.getRuleMatched()) {
                matchRules.add(simpleRuleResult.getRuleNo());
            }
        }
        // 3.校验策略
        boolean strategyMatched = checkStrategyScript(strategyScript, ruleDataMap, strategyDetail.getStrategyNo());
        
        return RiskStrategyResult.builder()
                .strategyId(strategyDetail.getStrategyId())
                .strategyNo(strategyDetail.getStrategyNo())
                .strategyMatched(strategyMatched)
                .matchRules(matchRules).build();
        
    }
    
    private RiskRuleResult checkRule(RiskRuleDetail rule, Map<String, Object> params) {
        String ruleScript = rule.getScript();
        List<RiskFieldDetail> riskFields = rule.getRiskFields();
        Map<String, Object> fieldDataMap = new HashMap<>();
        for (RiskFieldDetail riskField : riskFields) {
            String fieldScript = riskField.getScript();
            // 通过groovy脚本，从策略中获取值
            HashMap<String, Object> result = getFieldScriptResult(fieldScript, params, riskField.getFieldNo());
            String num = Optional.ofNullable(result.get("num")).map(String::valueOf).orElse("");
            double value;
            if (StringUtils.isBlank(num)) {
                value = 0.0;
            } else {
                value = new BigDecimal(num).setScale(2, RoundingMode.HALF_UP).doubleValue();
            }
            fieldDataMap.put("field" + riskField.getFieldId(), value);
        }
        boolean ruleResult = checkRuleScript(ruleScript, fieldDataMap, rule.getRuleNo());
        return RiskRuleResult.builder().ruleId(rule.getRuleId()).ruleNo(rule.getRuleNo()).ruleMatched(ruleResult).build();
    }
    
    private Boolean checkStrategyScript(String strategyScript, Map data, String strategyNo) {
        Object[] args = { data };
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(strategyScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,strategyNo:{}", e, strategyNo);
        }
        return ret;
    }
    
    private Boolean checkRuleScript(String ruleScript, Map data, String ruleNo) {
        Object[] args = { data };
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,ruleNo:{}", e, ruleNo);
        }
        return ret;
    }
    
    private HashMap<String, Object> getFieldScriptResult(String ruleScript, Map data, String fieldNo) {
        Object[] args = { data };
        HashMap<String, Object> ret = null;
        try {
            ret = (HashMap<String, Object>) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.error(log, "执行规则,groovy脚本失败,fieldNo:{}", e, fieldNo);
            ret = new HashMap<>();
        }
        return ret;
    }
    
    public List<CarRiskOrderDetail> getLocal(String methodName) {
        HashMap<String, List<CarRiskOrderDetail>> localMap = localCache.get();
        return localMap.get(methodName);
    }
    
    public void setLocal(String methodName, List<CarRiskOrderDetail> orderList) {
        HashMap<String, List<CarRiskOrderDetail>> localMap = localCache.get();
        localMap.put(methodName, orderList);
    }

    public Object getObjectLocal(String methodName) {
        HashMap<String, Object> localMap = localObjectCache.get();
        return localMap.get(methodName);
    }
    
    public void setObjectLocal(String methodName, Object orderList) {
        HashMap<String, Object> localMap = localObjectCache.get();
        localMap.put(methodName, orderList);
    }
}
