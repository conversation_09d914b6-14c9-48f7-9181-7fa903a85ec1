package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2-2  todo 重复代码后面去除
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk040Service extends FilterSfcHandler{

    private static final String ruleNo = "040";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRisk040Service][][]前置判断已通过，进入规则040判断{}", JsonUtils.json(context.getMemberCancelList()));

        try {
            if(!context.getSfcRiskRuleConfig().getOnOff040()){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }

            List<OrderStatusCancelDTO> orderStatusCancelDTOList = context.getMemberCancelList().stream()
                    .filter(data->data.getCancelTime().after(DateUtil.addMinute(new Date(),-context.getSfcRiskRuleConfig().getTime040())))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orderStatusCancelDTOList)){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }

            orderStatusCancelDTOList = orderStatusCancelDTOList.stream()
                    .filter(data->data.getPassengerCellphone().equals(context.getUserPhone()))
                    .filter(data->data.getType() == 1)
                    .collect(Collectors.toList());
            if(orderStatusCancelDTOList.size() > context.getSfcRiskRuleConfig().getOrderNum040()){
                List<String> orderIds = orderStatusCancelDTOList.stream().map(OrderStatusCancelDTO::getOrderId).collect(Collectors.toList());
                distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                log.info("[SfcRuleRisk040Service][doHandler][{}][{}]命中040规则{}",context.getMemberId(),context.getUnionId(),orderIds);
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过040",null,null);
                context.getUiResult().setData(dto);
                context.getUiResult().setMsg("风控不通过");
                distributionRiskManageService.addRiskCustomer(context.getMemberId(),1,1,1);
//                distributionRiskManageService.addRiskCustomer(context.getUserPhone(),1,2,30);

                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
            }

            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                if(StringUtils.isNotBlank(context.getRuleNo())){
                    riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                            RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
                }
            }
        }catch (Exception e) {
            log.error("[][][][]规则040错误",e);
        }

    }
}
