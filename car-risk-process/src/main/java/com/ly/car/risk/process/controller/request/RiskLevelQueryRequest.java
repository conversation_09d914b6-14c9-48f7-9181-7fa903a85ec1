package com.ly.car.risk.process.controller.request;

import com.ly.car.risk.process.utils.RandomUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class RiskLevelQueryRequest {

    private String traceId;
    private String memberId;
    private String unionId;
    private String userPhone;
    private String deviceId;
    private String productLine = "YNC";//YNC,SFC
    private Integer channel;

    // ******************** 内部使用 ****************************
    private String  sourceId;
    private Integer historySplitFlag;
    private String  requestId;
    // 兼容之前传值逻辑，实际未使用，赋默认值
    private String  orderId            = "";
    private Integer mainScene          = 5;
    private Integer childScene         = 1;
    private String  driverCardNo       = "";
    private String  passengerCellphone = "";

    public String getRequestId() {
        return StringUtils.isBlank(requestId) ? RandomUtil.getRandomString(16) + System.currentTimeMillis() : requestId;
    }
}
