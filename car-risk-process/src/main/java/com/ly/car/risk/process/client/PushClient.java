package com.ly.car.risk.process.client;

import com.alibaba.fastjson.JSON;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.client.model.ShortUrlRes;
import com.ly.car.risk.process.model.common.DSFRequest;
import com.ly.car.risk.process.utils.DSFUtils;
import com.ly.car.risk.process.utils.GsonHelper;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.toolset.web.HttpHelper;
import com.ly.travel.pushcore.facade.model.request.PushParam;
import com.ly.travel.pushcore.facade.model.request.PushVO;
import com.ly.travel.pushcore.facade.request.PushRequest;
import com.ly.travel.pushcore.facade.response.PushResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/13 19:25
 **/
@Slf4j
@Service
public class PushClient {

    @Resource
    private UrlsProperties urlProperties;

    public PushResponse pushSms(Integer channel, String phone,
                                String areaCode,
                                Map<String,String> smsContent) {
        try {
            PushRequest req = new PushRequest();
            PushVO pushVO = new PushVO();
            pushVO.setNotUsedScript(true);
            PushParam pushParam = new PushParam();
            pushParam.setSmsParam(smsContent);
            pushVO.setSenderParam(pushParam);
            pushVO.setMobile(phone);
            pushVO.setAreaCode(areaCode);
            pushVO.setScene(urlProperties.getSafetyYcScene());
            pushVO.setDomain(urlProperties.getSafetyYcDomain());
            pushVO.setChannel(channel);
            req.setPushVO(pushVO);
            req.setTraceId(UUID.randomUUID().toString());
            LoggerUtils.warn(log,"[短信推送],req:{}", JSON.toJSONString(req));
            PushResponse res = DSFUtils.sendAction(PushResponse.class, getDsfRequest("push", "sender", req));
            LoggerUtils.warn(log,"[短信推送],resp:{}",JSON.toJSONString(res));

            if (null == res || !res.isSuccess()) {
                return null;
            }

            return res;
        } catch (Exception e) {
            LoggerUtils.warn(log,"[短信推送],业务异常", e);
        }

        return null;
    }

    /**
     * 长短链转换
     * @param longUrls
     * @return
     */
    public String handleLongUrlsToShortUrls(String longUrls){
        String url = "http://sapi.17usoft.com/tcsa-api/services/wsc/create";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("longUrls", longUrls);
        String result = HttpHelper.request("post", url, paramMap);
        log.info("[]长链短链转换{}", result);
        ShortUrlRes shortUrlDto = GsonHelper.convert(result, ShortUrlRes.class);
        if ("ok".equals(shortUrlDto.getStatus()) && shortUrlDto.getDatas() != null) {
            return shortUrlDto.getDatas().get(0).getShortUrl();
        }
        return "";
    }

    public DSFRequest getDsfRequest(String serviceName, String actionName, Object request) {
        return new DSFRequest.Builder(
                urlProperties.getPushServiceDsfName(),
                serviceName,
                actionName,
                urlProperties.getPushServiceDsfVersion()
        ).postBody(request).build();
    }
}
