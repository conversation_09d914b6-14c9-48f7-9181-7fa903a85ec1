package com.ly.car.risk.process.controller.params;

import com.ly.car.common.bean.model.UiResult;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class FilterParams {

    private String memberId;
    private String unionId;
    private String userPhone;
    private String passengerCellphone;
    private String driverCardNo;
    private String payAccount;
    private String orderId;
    private String deviceId;
    //通过refId查询distribution_info中看channel_type 0-app 1-微信 其他
    private Integer channelType;
    private String sourceId;//每个需求给一个，后续持久化到表里
    private Integer mainScene;
    private Integer childScene;
    private Integer isNewUser;
    private BigDecimal esAmount;
    private String productLine;//YNC,SFC
    private String startLat;
    private String startLng;
    private String endLat;
    private String endLng;

    //额外加一个参数进行转换
    private UiResult uiResult;

    /**
     * 下面是汇川的司机认证
     * */
    private String driverId;
    private String bankCard;
    private String name;
    private String idCard;
    private String mobile;
    private String plate;//车牌号
    private String plateType;//车牌类型
    private String carName;//行驶证名称
    private String licenseNo;
    private String invitePhone;//被邀请人手机号
    private List<String> orderIds;//司机返现查询
    //im专用参数
    private String text;

    //顺风车专用
    private List<String> driverList;


    //本地请求id
    private String requestId;
    private String currentLat;
    private String currentLng;
    private String eventType;
    private Integer isMini;
    private String passengerOrderId;

    private String userId;//用户id,为了兼容所有的营销场景，所以这边用userId代替unionId

    //拆单标记
    private Integer splitFlag=0;
    private Integer historySplitFlag=0;

    //来源
    private String sourceTag;

    // 是否分销订单
    private boolean isDistributionOrder;

}
