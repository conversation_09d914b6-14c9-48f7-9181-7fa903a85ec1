package com.ly.car.risk.process.service.ability;

import com.ly.car.risk.process.constants.DriverWarnNotifyEnum;
import com.ly.car.risk.process.service.dto.DriverWarningContentDTO;
import com.ly.car.risk.process.service.rule.mtGroup.MqSendConvertService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SendDriverWarnService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private MqSendConvertService mqSendConvertService;

    public void sendDriverPush(String code,String orderId,Integer type){
        RBucket<String> bucket = redissonClient.getBucket("driver:notify:1024:" + orderId + ":" + type);
        if(bucket.isExists()){
            return;
        }
        DriverWarningContentDTO driverWarningContentDTO = new DriverWarningContentDTO();
        String productLine = "MT";
        driverWarningContentDTO.setNotifyContent(DriverWarnNotifyEnum.sjtx_im_mt.getVoice());
        if(orderId.startsWith("PHC")){
            productLine = "HC";
            driverWarningContentDTO.setNotifyContent(DriverWarnNotifyEnum.sjtx_im_hc.getVoice());
        }
        MqRiskProducer mqRiskProducer = mqSendConvertService.getByProductLine(productLine);
        driverWarningContentDTO.setOrderId(orderId);
        driverWarningContentDTO.setSensitiveType(type);
        mqRiskProducer.send(MqTagEnum.car_risk_driver_inner_content_notify, JsonUtils.json(driverWarningContentDTO),0);
        bucket.set("1",1, TimeUnit.DAYS);
    }
}
