package com.ly.car.risk.process.service.rule.common.driver;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.rule.common.CommonFilterContext;
import com.ly.car.risk.process.service.rule.common.CommonFilterHandler;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterContext;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterHandler;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class DriverIdCardService extends CommonFilterHandler {

    private static final String TAG_NAME = "_driver_notify";

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource(name = "commonRiskProducer")
    private MqRiskProducer mqRiskProducer;

    @Override
    public void doHandler(CommonFilterContext context) {
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String,Object> data = new HashMap<>();
        Map<String,Object> resultMap = new HashMap<>();
        data.put("driverId",context.getParam().getString("driverId"));
        data.put("idCard",context.getParam().getString("idCard"));
        dto.setObj(data);
        if(!validParam(context.getParam())){
            dto.setCode(1);
            dto.setMessage("风控不通过-身份证参数缺失验证失败");
            mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
            return;
        }
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setIdCard((String) context.getParam().get("idCard"));
        param.setName((String) context.getParam().get("name"));
        Integer result = tianChuangRiskClient.verifyIdCard(param,null);
        if(result != 0){
            //发送mq
            dto.setCode(1);
            dto.setMessage("风控不通过-身份证验证失败");
            resultMap.put("idCard",1);
            data.put("resultMap",resultMap);
            dto.setObj(data);
            mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
            return;
        }
        if(this.nextHandler == null && dto.getCode() == 0){
            mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
        } else {
            this.nextHandler.doHandler(context);
        }
    }

    public Boolean validParam(JSONObject context){
        if(context.get("driverId") == null){
            return false;
        }
        if(context.get("name") == null){
            return false;
        }
        if(context.get("idCard") == null){
            return false;
        }
        return true;
    }
}
