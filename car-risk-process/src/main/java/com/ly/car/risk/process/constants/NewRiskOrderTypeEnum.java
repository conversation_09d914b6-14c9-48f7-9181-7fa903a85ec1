package com.ly.car.risk.process.constants;

public enum NewRiskOrderTypeEnum {

    BRUSH_ORDER_EX(1, "刷单","频繁小额完单"),
    AMOUNT_EX(2,"金额异常","订单金额过小或过大;预估与实际金额差过大"),
    AMOUNT_BIG_EX(21,"金额异常-异常大额","订单金额过小或过大;预估与实际金额差过大"),
    AMOUNT_OTHER_EX(22,"金额异常-附加费异常","订单金额过小或过大;预估与实际金额差过大"),

    TRACK_EX(3,"轨迹异常","行驶里程过短:预估与实际里程差过大"),
    COUPON_EX(4, "套券","司乘联合刷券"),
    QUICK_ORDER_EX(5, "快速划单","短时间快速完单"),
    COST_EX(6, "费用异议","立生额外费用或额外退款"),
    ACCOUNT_EX(7, "账户异常","非正常接完单账户"),
            ;

    private Integer code;
    private String msg;
    private String desc;

    NewRiskOrderTypeEnum(int code, String msg,String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getMsgByCode(Integer code){
        for(NewRiskOrderTypeEnum type : values()){
            if(type.getCode().equals(code)){
                return type.getMsg();
            }
        }
        return null;
    }
}
