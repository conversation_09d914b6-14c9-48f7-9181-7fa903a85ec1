package com.ly.car.risk.process.repo.data;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SfcOrderRiskData {

    private String orderId;
    private Date finishTime;
    private Long memberId;
    private String unionId;
    private BigDecimal startLat;
    private BigDecimal startLng;
    private BigDecimal endLat;
    private BigDecimal endLng;
    private BigDecimal estimateKilo;
}
