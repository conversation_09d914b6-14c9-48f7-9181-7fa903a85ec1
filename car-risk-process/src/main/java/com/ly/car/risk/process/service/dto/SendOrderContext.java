package com.ly.car.risk.process.service.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SendOrderContext {

    private String orderId;
    private Date finishTime;
    private String payAccount;
    private String memberId;
    private String unionId;
    private String deviceId;
    private String driverCardNo;
    private String startLat;
    private String startLng;
    private String endLat;
    private String endLng;
    private String totalAmount;
    private String passengerCellphone;
    private BigDecimal actualKilo;
    private Integer actualDuration;

    //司机开始服务时间与完单时间的间隔
    private Integer intervalTime;


}
