package com.ly.car.risk.process.service;

import com.ly.car.risk.process.repo.risk.mapper.DriverHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DriverHistoryService {

    @Resource
    private DriverHistoryMapper driverHistoryMapper;

    public Integer queryDriver(String driverNo){
        return driverHistoryMapper.queryCount(driverNo);
    }
}
