package com.ly.car.risk.process.controller.quartz;

import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.handler.riskJob.RiskCheckHandlerFactory;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description of RiskCouponUseCheckController
 *
 * <AUTHOR>
 * @date 2024/8/27
 * @desc 用券异常风险单清洗
 */
@RestController
@RequestMapping("/riskCoupon")
@Slf4j
public class RiskCouponUseCheckController {

    @Resource
    private RiskCheckHandlerFactory riskCheckHandlerFactory;


    @RequestMapping(value = "/riskCouponCheck", method = RequestMethod.GET)
    public String riskCouponCheck() {
        LoggerUtils.initLogMap("riskCouponCheck", "", "", "");
        try {
            LoggerUtils.info(log, "营销券策略排查开始");
            long startTime = System.currentTimeMillis();
            List<RiskJobTypeEnum> mktJob = RiskJobTypeEnum.findMktJob();
            riskCheckHandlerFactory.check(mktJob);
            LoggerUtils.info(log, "营销券策略排查结束，耗时:%s ms", System.currentTimeMillis() - startTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log, "营销券策略排查，异常", e);
            return "fail";
        } finally {
            LoggerUtils.removeAll();
        }
    }
    
    @RequestMapping(value = "/customerComplaintRefundCheck", method = RequestMethod.GET)
    public String check() {
        LoggerUtils.initLogMap("customerComplaintRefundCheck", "", "", "");
        try {
            LoggerUtils.info(log, "追款策略排查开始");
            long startTime = System.currentTimeMillis();
            List<RiskJobTypeEnum> mktJob = RiskJobTypeEnum.findJobByApproveType(RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND);
            riskCheckHandlerFactory.check(mktJob);
            LoggerUtils.info(log, "追款策略排查结束，耗时:{} ms", System.currentTimeMillis() - startTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log, "追款策略排查，异常", e);
            return "fail";
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping(value = "/approveRemind",method = RequestMethod.GET)
    public void approveRemind(){
        LoggerUtils.initLogMap("approveRemind", "", "", "");
        try {
            LoggerUtils.info(log, "风险预警提示开始");
            long startTime = System.currentTimeMillis();
            riskCheckHandlerFactory.approveRemind();
            LoggerUtils.info(log, "风险预警提示结束，耗时:%s ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            LoggerUtils.error(log, "风险预警提示，异常", e);
            throw new RuntimeException(e);
        } finally {
            LoggerUtils.removeAll();
        }
    }


    @RequestMapping(value = "noticeAfterDeal",method = RequestMethod.GET)
    public void noticeAfterDeal(@Param("approveCode") String approveCode){
        LoggerUtils.initLogMap("noticeAfterDeal", "", "", "");
        try {
            LoggerUtils.info(log, "预警处理后通知开始");
            long startTime = System.currentTimeMillis();
            riskCheckHandlerFactory.noticeAfterDeal(approveCode);
            LoggerUtils.info(log, "预警处理后通知结束，耗时:%s ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            LoggerUtils.error(log, "预警处理后通知，异常", e);
            throw new RuntimeException(e);
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping(value = "/mtWithdrawRiskCheck", method = RequestMethod.GET)
    public String mtWithdrawRiskCheck() {
        LoggerUtils.initLogMap("mtWithdrawRiskCheck", "", "", "");
        try {
            LoggerUtils.info(log, "萌艇提现风险排查开始");
            long startTime = System.currentTimeMillis();
            List<RiskJobTypeEnum> mktJob = RiskJobTypeEnum.findJobByApproveType(RiskAlertApproveSceneEnum.MT);
            riskCheckHandlerFactory.check(mktJob);
            LoggerUtils.info(log, "萌艇提现风险排查结束，耗时:%s ms", System.currentTimeMillis() - startTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log, "萌艇提现风险排查，异常", e);
            return "fail";
        } finally {
            LoggerUtils.removeAll();
        }
    }
    
    @RequestMapping(value = "/dailyCheck", method = RequestMethod.GET)
    public String dailyCheck() {
        LoggerUtils.initLogMap("dailyCheck", "", "", "");
        try {
            LoggerUtils.info(log, "每日排查开始");
            long startTime = System.currentTimeMillis();
            List<RiskJobTypeEnum> mktJob = RiskJobTypeEnum.findJobByApproveType(RiskAlertApproveSceneEnum.CALL_STATISTICS);
            riskCheckHandlerFactory.check(mktJob);
            LoggerUtils.info(log, "每日排查开始结束，耗时:{} ms", System.currentTimeMillis() - startTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log, "每日排查开始，异常", e);
            return "fail";
        } finally {
            LoggerUtils.removeAll();
        }
    }
    
    @RequestMapping(value = "/sensitiveActionCheck", method = RequestMethod.GET)
    public String sensitiveActionCheck() {
        LoggerUtils.initLogMap("sensitiveActionCheck", "", "", "");
        try {
            LoggerUtils.info(log, "敏感行为排查开始");
            long startTime = System.currentTimeMillis();
            List<RiskJobTypeEnum> mktJob = RiskJobTypeEnum.findJobByApproveType(RiskAlertApproveSceneEnum.SENSITIVE_ACTION);
            riskCheckHandlerFactory.check(mktJob);
            LoggerUtils.info(log, "敏感行为排查结束，耗时:{} ms", System.currentTimeMillis() - startTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log, "敏感行为排查，异常", e);
            return "fail";
        } finally {
            LoggerUtils.removeAll();
        }
    }
}