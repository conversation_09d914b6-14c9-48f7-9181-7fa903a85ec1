package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.OrderAddress;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.order.entity.SupplierInfo;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.api.MemberApi;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.constants.ChildSceneEnum;
import com.ly.car.risk.process.constants.MainSceneEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderAddressMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SupplierInfoMapper;
import com.ly.car.risk.process.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.process.service.context.SaveRiskManageContext;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderExpand;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderExpandMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DistributionRiskManageService{

    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private SupplierInfoMapper supplierInfoMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcOrderAddressMapper sfcOrderAddressMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private RedissonClient redissonClient;
    @Resource(name = "executorService")
    private ExecutorService executorService;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private OrderExpandMapper orderExpandMapper;
    @Resource
    private RiskCustomerService riskCustomerService;

    /**
     *
     * */
    public void saveRiskManage(SaveRiskManageContext context){
        log.info("[][][][]存储命中管理记录:"+ JsonUtils.json(context));
        if(StringUtils.isNotBlank(context.getLinkOrder())){

        }
        if(context.getLevel() == null){
            context.setLevel(RiskLevelEnum.HIGH.getCode());
        }
        String lockName = "lock:" + context.getOrderId() + context.getMainScene()+context.getChildScene();
        RLock lock = null;
        try {
            lock = redissonClient.getLock(lockName);
            Boolean lockFlag = lock.tryLock(2, TimeUnit.SECONDS);
            if(lockFlag){
                RiskOrderManage orderManage = new RiskOrderManage();
                orderManage.setOrderId(context.getOrderId());
                orderManage.setIsRisk(1);
                orderManage.setRuleNo(context.getRuleNoList());
                riskOrderManageService.addRiskOrder(orderManage);
            }
        } catch (Exception e) {
            log.error("[][][][]存储命中信息管理失败",e);
        } finally {
            try {
                if(lock != null && lock.isLocked()){
                    lock.unlock();
                }
            }catch (Exception e){
                log.error("[][][][][]解锁异常", e);
            }
        }
    }



    public void saveRiskManageNoOrder(SaveRiskManageContext context){
        DistributionRiskManage manage = new DistributionRiskManage();
        manage.setRuleNoList(context.getRuleNoList());
        manage.setRiskMainScenario(MainSceneEnum.getMsgByCode(context.getMainScene()));
        manage.setRiskChildScenario(ChildSceneEnum.getMsgByCode(context.getMainScene()+"-"+context.getChildScene()));
        manage.setRiskLevel(context.getLevel());
        manage.setCreateTime(new Date());
        manage.setUpdateTime(new Date());
        manage.setHitTime(new Date());
        manage.setPayAccount(context.getPayAccount());
        manage.setMainScene(context.getMainScene());
        manage.setChildScene(context.getChildScene());
        manage.setMemberId(context.getMemberId());
        manage.setPhone(context.getPhone());
        manage.setUserPhone(context.getUserPhone());
        manage.setUnionId(context.getUnionId());
        manage.setProductLine(context.getProductLine());
        manage.setHitValue(context.getHitValue());
        manage.setIsCustomer(context.getFlag());
        this.distributionRiskManageMapper.insert(manage);
    }

    /**
     * 规则离线
     * */
    public void addByRule(String orderId,String ruleNo,Integer mainScene,Integer childScene,
                          String linkOrder,DistributionRiskManage manage,Integer level){
        SaveRiskManageContext context = new SaveRiskManageContext();
        context.setOrderId(orderId);
        context.setRuleNoList(ruleNo);
        context.setMainScene(mainScene);
        context.setChildScene(childScene);
        context.setLinkOrder(linkOrder);
        context.setFlag(0);
        context.setLevel(level);
        context.setDistributionRiskManage(manage);
        executorService.execute(()->{
            saveRiskManage(context);
        });
    }

    /**
     * 优化规则插入
     * */
    public void addManageCommon(List<String> orderIds,String ruleNo,Integer mainScene,Integer childScene,Integer flag,String hitValue,Integer level){
        for(String str : orderIds){
            List<String> linkOrderIds = orderIds.stream().filter(e-> !Objects.equals(e,str)).collect(Collectors.toList());
            SaveRiskManageContext context = new SaveRiskManageContext();
            context.setOrderId(str);
            context.setRuleNoList(ruleNo);
            context.setMainScene(mainScene);
            context.setChildScene(childScene);
            context.setLinkOrder(StringUtils.join(linkOrderIds,","));
            context.setFlag(flag);
            context.setHitValue(hitValue);
            context.setLevel(level);
            executorService.execute(()->{
                saveRiskManage(context);
            });
        }
    }

    /**
     * 规则实时
     * */
    public void addByRuleChain(List<OrderRiskContext> contextList, String ruleNo,Integer mainScene,Integer childScene,Integer flag,String hitValue,Integer level){
        for(OrderRiskContext riskContext : contextList){
            List<String> orderIds = contextList.stream().map(OrderRiskContext::getOrderId).filter(e->!riskContext.getOrderId().equals(e)).collect(Collectors.toList());
            SaveRiskManageContext context = new SaveRiskManageContext();
            context.setOrderId(riskContext.getOrderId());
            context.setRuleNoList(ruleNo);
            context.setMainScene(mainScene);
            context.setChildScene(childScene);
            context.setLinkOrder(StringUtils.join(orderIds,","));
            context.setFlag(flag);
            context.setHitValue(hitValue);
            context.setLevel(level);
            executorService.execute(()->{
                saveRiskManage(context);
            });
        }
    }

    /**
     * 名单实时
     * */
    public void addByCustomerChain(String orderId, String ruleNo,Integer mainScene,Integer childScene,Integer flag,String hitValue,Integer level){
        SaveRiskManageContext context = new SaveRiskManageContext();
        context.setOrderId(orderId);
        context.setRuleNoList(ruleNo);
        context.setMainScene(mainScene);
        context.setChildScene(childScene);
        context.setFlag(flag);
        context.setHitValue(hitValue);
        context.setLevel(level);
        executorService.execute(()->{
            saveRiskManage(context);
        });
    }

    /**
     * 名单实时,无订单
     * */
    public void addByCustomerNoOrder(String ruleNo,Integer mainScene,Integer childScene,Integer flag,String hitValue){
        SaveRiskManageContext context = new SaveRiskManageContext();
        context.setRuleNoList(ruleNo);
        context.setMainScene(mainScene);
        context.setChildScene(childScene);
        context.setFlag(flag);
        context.setHitValue(hitValue);
//        executorService.execute(()->{
//            saveRiskManageNoOrder(context);
//        });
    }

    public void addRiskCustomer(String customerValue,Integer riskType,Integer customerType,Integer ttl ){
        executorService.execute(()->{
            riskCustomerService.addRiskCustomer(customerValue,riskType,customerType,ttl);
        });
    }

    public String noRepeatStr(String sourceStr,String targetStr){
        Set<String> stringSet = new HashSet<>();
        if(StringUtils.isNotBlank(sourceStr)){
            stringSet.addAll(Arrays.asList(sourceStr.split(",")));
        }
        if(StringUtils.isNotBlank(targetStr)){
            stringSet.addAll(Arrays.asList(targetStr.split(",")));
        }
        List<String> stringList = new ArrayList<>(stringSet);
        return StringUtils.join(stringList,",");
    }
}
