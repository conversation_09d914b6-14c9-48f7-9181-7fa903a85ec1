package com.ly.car.risk.process.handler.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.copier.Copier;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.model.enums.OfflineStrategyWashJobType;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.travel.shared.mobility.supply.trade.core.model.job.JobTypeEnum;
import io.netty.util.concurrent.CompleteFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Description of WashJobFactory
 *
 * <AUTHOR>
 * @date 2024/12/17
 * @desc
 */
@Service
@Slf4j
public class WashJobFactory implements ApplicationContextAware, InitializingBean {
    @Resource
    protected RiskOrderManageService riskOrderManageService;
    private ApplicationContext applicationContext;

    private final ExecutorService executor = new ThreadPoolExecutor(5, 15, 30, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1024));

    private Map<OfflineStrategyWashJobType, WashJobHandler> handlerMap;


    public void wash(String startTime, String endTime) throws InterruptedException, ExecutionException {
        ArrayList<WashJobHandler> washJobHandlers = new ArrayList<>(handlerMap.values());
        StringBuilder okNotice = new StringBuilder();
        List<RiskOrderManage> allOrderManages = new ArrayList<>();
        for (int i = 0; i < washJobHandlers.size(); i++) {
            if(washJobHandlers.get(i).exeAble()){
                List<RiskOrderManage> manages = washJobHandlers.get(i).doJob(startTime, endTime);
                String msg = String.format("%s job检查结束，任务结果：%s 条", washJobHandlers.get(i).support().getType(), manages.size()) + "\n";
                okNotice.append(msg);
                allOrderManages.addAll(manages);
            }
        }
        if(CollUtil.isNotEmpty(allOrderManages)){
            Map<String, List<RiskOrderManage>> orderRiskMap = allOrderManages.stream().collect(Collectors.groupingBy(RiskOrderManage::getOrderId));
            for(Map.Entry<String,List<RiskOrderManage>> entry:orderRiskMap.entrySet()){
                List<RiskOrderManage> orderRiskInfo = entry.getValue();
                RiskOrderManage riskOrder = orderRiskInfo.get(0);
                riskOrder.setRuleNo(orderRiskInfo.stream().map(RiskOrderManage::getRuleNo).collect(Collectors.joining(",")));
                riskOrderManageService.addRiskOrder(riskOrder);
            }
        }
        LoggerUtils.info(log, "任务执行结束：\n" + okNotice.toString());
    }


//    public void wash(String startTime, String endTime) throws InterruptedException, ExecutionException {
//        ArrayList<WashJobHandler> washJobHandlers = new ArrayList<>(handlerMap.values());
//        StringBuilder okNotice = new StringBuilder();
//        StringBuilder failNotice = new StringBuilder();
//        List<Future<Integer>> jobResults = executor.invokeAll(washJobHandlers.stream().map(p ->
//                        (Callable<Integer>) () -> p.doJob(startTime, endTime)).collect(Collectors.toList()),
//                40, TimeUnit.SECONDS);
//        for (int i = 0; i < washJobHandlers.size(); i++) {
//            Future<Integer> jobFuture = jobResults.get(i);
//            if (jobFuture.isCancelled()) {
//                String msg = String.format("%s job检查超时，任务失败", washJobHandlers.get(i).support().getType()) + "\n";
//                failNotice.append(msg);
//            } else {
//                Integer dealCount = jobFuture.get();
//                String msg = String.format("%s job检查结束，任务结果：%s 条", washJobHandlers.get(i).support().getType(), dealCount) + "\n";
//                okNotice.append(msg);
//            }
//        }
//        okNotice.append(failNotice.toString());
//        LoggerUtils.info(log, "任务执行结束：\n" + okNotice.toString());
//    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Map<OfflineStrategyWashJobType, WashJobHandler> map = new HashMap<>();
        Map<String, WashJobHandler> beansOfType = applicationContext.getBeansOfType(WashJobHandler.class, false, true);
        for (WashJobHandler handler : beansOfType.values()) {
            map.put(handler.support(), handler);
        }
        this.handlerMap = map;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}