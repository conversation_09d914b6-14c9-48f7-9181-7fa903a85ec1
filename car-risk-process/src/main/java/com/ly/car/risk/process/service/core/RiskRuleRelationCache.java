package com.ly.car.risk.process.service.core;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskRuleRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskRuleRelation;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RiskRuleRelationCache {
    private static final String KEY = "rule_relation_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskRuleRelationMapper riskRuleRelationMapper;

    @Scheduled(fixedRate = 1000L * 60L,initialDelay = 0)
    public void init(){
        List<RiskRuleRelation> relationList = this.riskRuleRelationMapper.selectList(new QueryWrapper<RiskRuleRelation>()
                .gt("id", 0)
        );
        Map<String,List<RiskRuleRelation>> relationMap = relationList.stream()
                .collect(Collectors.groupingBy(RiskRuleRelation::getRuleNo));
//        RKeys rKeys = redissonClient.getKeys();
//        Iterable<String> keys = rKeys.getKeysByPattern(KEY + "*");
//        for (String key : keys) {
//            rKeys.delete(key);
//        }
        for(Map.Entry<String,List<RiskRuleRelation>> entry : relationMap.entrySet()){
            redissonClient.getBucket(KEY+entry.getKey()).set(JsonUtils.json(entry.getValue()),1,TimeUnit.DAYS);
        }
    }

    public List<RiskRuleRelation> loadRuleRelation(String key){
        RBucket<String> bucket = redissonClient.getBucket(KEY + key);
        if(bucket == null || !bucket.isExists()){
            return null;
        }
        return JSONArray.parseArray(bucket.get(),RiskRuleRelation.class);
    }

}
