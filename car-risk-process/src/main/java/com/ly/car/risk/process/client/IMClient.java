package com.ly.car.risk.process.client;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.http.HttpUtils;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.client.model.im.IMMsgQueryResp;
import com.ly.car.risk.process.client.model.im.IMMsgRecord;
import com.ly.car.risk.process.client.model.im.ImMsgQueryRequest;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Description of IMClient
 *
 * <AUTHOR>
 * @date 2024/3/18
 * @desc
 */
@Service
@Slf4j
public class IMClient {

    @Resource
    private UrlsProperties urlsProperties;

    public List<IMMsgRecord> queryIMMsg(String orderId, String supplierCode, String unionId, String memberId, String carNum) {
        ImMsgQueryRequest request = new ImMsgQueryRequest();
        String sessionKey = buildSessionKey(supplierCode, unionId, memberId, carNum);
        request.setOrderId(orderId);
        request.setSessionKey(sessionKey);
        request.setQueryNum(500);


        try {
            String resp = HttpUtils.post(urlsProperties.getImMsgQueryUrl(), JSON.toJSONString(request));
            LoggerUtils.info(log, "IM记录查询,req:{},resp:{}", JSON.toJSONString(request), resp);
            if (StringUtils.isBlank(resp)) {
                return new ArrayList<>();
            }

            JSONObject jsonObject = JSON.parseObject(resp);
            JSONObject responseJSON = jsonObject.getJSONObject("response");
            if (null == responseJSON) {
                return new ArrayList<>();
            }
            String body = responseJSON.getString("body");
            if (StringUtils.isBlank(body)) {
                return new ArrayList<>();
            }
            IMMsgQueryResp imMsgQueryResp = JSON.parseObject(body, new TypeReference<IMMsgQueryResp>() {
            });
            if (imMsgQueryResp.getStatus() != 200) {
                return new ArrayList<>();
            }
            return imMsgQueryResp.getBody();
        } catch (Exception e) {
            LoggerUtils.warn(log, "IM记录查询,req:{}", e, JSON.toJSONString(request));
        }
        return new ArrayList<>();
    }

    private String buildSessionKey(String supplierCode, String unionId, String memberId, String carNum) {
        return String.format("%s_%s_%s", supplierCode, StringUtils.defaultIfBlank(unionId, memberId), carNum);
    }

}