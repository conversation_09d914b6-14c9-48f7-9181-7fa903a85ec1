package com.ly.car.risk.process.handler.job.detail;

import com.ly.car.risk.process.handler.job.AbstractWashJobHandler;
import com.ly.car.risk.process.model.enums.OfflineStrategyWashJobType;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description of R025JobHandler
 *
 * <AUTHOR>
 * @date 2024/12/17
 * @desc
 */
@Service
public class R034JobHandler extends AbstractWashJobHandler {

    @Override
    public List<CarRiskOrderDetail> findRiskOrders(String startTime, String endTime) {
        return orderDetailMapper.findMatchR034Order(startTime,endTime);
    }

    @Override
    public OfflineStrategyWashJobType support() {
        return OfflineStrategyWashJobType.R_034;
    }
}