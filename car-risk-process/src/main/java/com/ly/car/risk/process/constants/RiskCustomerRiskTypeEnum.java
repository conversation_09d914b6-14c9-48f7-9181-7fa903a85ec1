package com.ly.car.risk.process.constants;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
public enum RiskCustomerRiskTypeEnum {
    
    black_list(1, "黑名单", RiskCustomerRiskTipTypeEnum.black),
    white_list(2, "白名单", RiskCustomerRiskTipTypeEnum.white),
    ban_coupon_list(3, "禁止领券名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_reward_list(4, "禁止奖励名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_send_list(5, "禁止派单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_receive_list(6, "禁止接单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_one_to_one_list(7, "一对一名单", RiskCustomerRiskTipTypeEnum.one2one),
    ban_register_list(8, "禁止认证名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_ync_receive_list(9, "禁止网约车接单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_bus_create_order(10, "禁止汽车票下单名单", RiskCustomerRiskTipTypeEnum.forbid),
    ban_credit_auth_list(11, "禁止信用授权名单", RiskCustomerRiskTipTypeEnum.forbid),
    tx_black_list(21,"腾讯黑名单", RiskCustomerRiskTipTypeEnum.black),
    tx_ban_one_to_one_list(22, "腾讯一对一名单", RiskCustomerRiskTipTypeEnum.one2one),
    ;
    private Integer                     code;
    private String                      msg;
    private RiskCustomerRiskTipTypeEnum tip;
    
    private static final Map<Integer, RiskCustomerRiskTypeEnum> MAP = new HashMap<>();
    
    static {
        for (RiskCustomerRiskTypeEnum value : RiskCustomerRiskTypeEnum.values()) {
            MAP.put(value.code, value);
        }
    }
    
    RiskCustomerRiskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    public RiskCustomerRiskTipTypeEnum getTip() {
        return tip;
    }
    
    public void setTip(RiskCustomerRiskTipTypeEnum tip) {
        this.tip = tip;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public static String getMsgByCode(Integer code) {
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    
    public static Integer getCodeByMsg(String msg) {
        for (RiskCustomerRiskTypeEnum enumItem : RiskCustomerRiskTypeEnum.values()) {
            if (enumItem.getMsg().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
    
    public static RiskCustomerRiskTypeEnum of(Integer riskType) {
        if (null == riskType) {
            return null;
        }
        return MAP.get(riskType);
    }

    /**
     * 校验类型是否是黑名单， 黑名单返回true, 否则返回false
     * @param riskType 名单类型code
     * @return
     */
    public static boolean isBlackList(Integer riskType) {
        if (null == riskType) {
            return false;
        }
        return riskType.equals(black_list.code);
    }
}
