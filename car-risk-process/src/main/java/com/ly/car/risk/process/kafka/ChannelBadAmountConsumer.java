package com.ly.car.risk.process.kafka;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.monitor.HealthCheckService;
import com.ly.car.risk.entity.ChannelBadDebtsRisk;
import com.ly.car.risk.process.bean.properties.KafKaProperties;
import com.ly.car.risk.process.repo.risk.mapper.ChannelBadDebtsRiskMapper;
import com.ly.car.risk.process.utils.SendMailUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;

@Slf4j
@Component
public class ChannelBadAmountConsumer implements ApplicationListener<ApplicationStartedEvent> {

    @Resource
    private SendMailUtils sendMailUtils;
    @Resource
    private ChannelBadDebtsRiskMapper channelBadDebtsRiskMapper;
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private KafKaProperties kafKaProperties;
    @Resource
    private KafkaConsumer<String, String> szKafkaConsumer;
    @Resource
    private KafkaProducer<String, String> szKafkaProducer;

    private static final String TOPIC = "car_channel_badDebts_risk_topic";

    public void start(String message) {
        log.info("[userRisk] [info] [] [] kafka消息：{}", message);
        List<String> strList = Arrays.asList(message.split(","));
        ChannelBadDebtsRisk channelBadDebtsRisk = channelBadDebtsRiskMapper.selectOne(
                new QueryWrapper<ChannelBadDebtsRisk>()
                        .eq("order_created", Integer.valueOf(strList.get(0).replace("-", "")))
                        .eq("distribute_name", strList.get(1))
                        .eq("city_name",strList.get(2))
                        .eq("supplier_code",strList.get(3))
        );

        if (channelBadDebtsRisk != null) {
            log.info("[userRisk] [info] [] [] 查询坏账已有数据：{}", JSONObject.toJSONString(channelBadDebtsRisk));
            //修改
            channelBadDebtsRisk.setBadOrderNum(Integer.valueOf(strList.get(4)));
            channelBadDebtsRisk.setBadOrderAmount(new BigDecimal(strList.get(5)));
            channelBadDebtsRisk.setAllOrderNum(Integer.valueOf(strList.get(6)));
            channelBadDebtsRisk.setAllOrderAmount(new BigDecimal(strList.get(7)));
            channelBadDebtsRisk.setBadOrderRate(new BigDecimal(strList.get(8)));
            channelBadDebtsRisk.setBadOrderAmountRate(new BigDecimal(strList.get(9)));
            channelBadDebtsRisk.setRevenueAmount(new BigDecimal(strList.get(10)));
            channelBadDebtsRisk.setUpdateTime(new Date());
            channelBadDebtsRiskMapper.updateById(channelBadDebtsRisk);
            return;
        }
        channelBadDebtsRisk = new ChannelBadDebtsRisk();
        channelBadDebtsRisk.setOrderCreated(Integer.valueOf(strList.get(0).replace("-", "")));
        channelBadDebtsRisk.setDistributeName(strList.get(1));
        channelBadDebtsRisk.setDistributeCode("");
        channelBadDebtsRisk.setCityName(strList.get(2));
        channelBadDebtsRisk.setSupplierCode(strList.get(3));
        channelBadDebtsRisk.setDayCreated(strList.get(0));
        channelBadDebtsRisk.setBadOrderNum(Integer.valueOf(strList.get(4)));
        channelBadDebtsRisk.setBadOrderAmount(new BigDecimal(strList.get(5)));
        channelBadDebtsRisk.setAllOrderNum(Integer.valueOf(strList.get(6)));
        channelBadDebtsRisk.setAllOrderAmount(new BigDecimal(strList.get(7)));
        channelBadDebtsRisk.setBadOrderRate(new BigDecimal(strList.get(8)));
        channelBadDebtsRisk.setBadOrderAmountRate(new BigDecimal(strList.get(9)));
        channelBadDebtsRisk.setRevenueAmount(new BigDecimal(strList.get(10)));
        channelBadDebtsRisk.setCreateTime(new Date());
        channelBadDebtsRisk.setUpdateTime(new Date());
        channelBadDebtsRiskMapper.insert(channelBadDebtsRisk);
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if (!kafKaProperties.isChannelBadDebtsConsumer()) {
            return;
        }
        Executors.newSingleThreadExecutor().execute(() -> {
            log.info("首汽坏账消费开始...");
            szKafkaConsumer.subscribe(Arrays.asList(kafKaProperties.getChannelBadDebtsTopic()));
            ConsumerRecords<String, String> records;
            while (healthCheckService.isHealth()) {
                log.info("首汽坏账开始拉取消息");
                records = szKafkaConsumer.poll(1000);
                if (records.count() > 0) {
                    log.info("首汽坏账拉取消息{}条", records.count());
                    for (ConsumerRecord<String, String> record : records) {
                        //处理业务逻辑
//                        consumerTodo(record.value());
                        start(record.value());
                    }
                }
            }
            log.info("首汽坏账消费结束...");
            szKafkaConsumer.close();
        });
    }
}
