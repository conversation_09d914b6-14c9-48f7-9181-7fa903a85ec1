package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
public class CategoryRelation extends Model<CategoryRelation> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String strategyNo;
    private Integer type;
    private String ruleSceneNo;
    private String ruleSceneName;
    private Date createTime;
}
