package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警审核管理
 * risk_alert_approve
 */
@Data
public class RiskAlertApprove implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 预警编号
     */
    private String code;
    
    /**
     * 预警级别 1:关注 2:高风险
     */
    private Integer level;
    
    /**
     * 预警主体 USER:用户 DRIVER:司机 SUPPLIER:供应商
     */
    private String target;
    
    /**
     * 预警值
     */
    private String targetValue;
    
    /**
     * 预警场景 MARKETING:营销
     */
    private String alertScene;

    /**
     * 订单类型
     */
    private int orderType;

    /**
     * 预警策略
     */
    private String alertStrategy;
    
    /**
     * 预警内容
     */
    private String alertContent;
    
    /**
     * 预警时间
     */
    private Date alertTime;
    
    /**
     * 处理状态 0:未处理 1:无风险 2:跟进中 
     */
    private Integer handleResult;
    
    /**
     * 处理时间
     */
    private Date handleTime;
    
    /**
     * 处理人
     */
    private String handleUser;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 操作人
     */
    private String updateUser;
    
    private String ext;
    
    @Transient
    private String noticeContent;
    
    @Data
    public static class Ext {
        
        private String supplierName;
    }
    
    private static final long serialVersionUID = 1L;
}