package com.ly.car.risk.process.repo.mtticket.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.mtticket.entity.DriverMtInfo;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DriverInfoMtMapper extends BaseMapper<DriverMtInfo> {

    @Select("<script>" +
            "   SELECT di.id FROM driver_info di inner join `account` a on di.vcode=a.vcode where di.status=1 and a.status=1 " +
            "</script>")
    List<Long> queryList();

    @Select("<script>" +
            "select t4.vehicleNO from " +
            "   (select  sum(t3.fine) as sumMoney ,t3.vehicle_no  as vehicleNO from " +
            "   (select (max(t.pay_price)-sum(t.refund_amount)) as fine ,t.order_no,t.vehicle_no as vehicle_no from " +
            "   (SELECT t1.order_no as order_no,t1.create_time,t2.pay_order_no,t1.pay_price as pay_price,t1.vehicle_no as vehicle_no,t2.refund_amount as refund_amount FROM ride_order_info t1 left join order_refund t2 on t1.order_no = t2.pay_order_no where t1.create_time &gt;= DATE_ADD(CURDATE(), INTERVAL -2 MONTH) and t1.vehicle_no !='' " +
            "   and t2.pay_order_no !='') t " +
            "   group by t.order_no,t.vehicle_no " +
            "   having max(t.pay_price)-sum(t.refund_amount) &gt; 0) t3 " +
            "   group by t3.vehicle_no" +
            "   having sum(t3.fine) &gt;= 2000" +
            "   order by sum(t3.fine) desc )  t4 left JOIN " +
            " ( " +
            "  select vehicle_no, count(distinct order_no) as finishNum FROM  ride_order_info " +
            "  where order_status=6  and create_time &gt;= DATE_ADD(CURDATE(), INTERVAL -2 MONTH) " +
            "  group by vehicle_no " +
            " ) t5  on t4.vehicleNo= t5.vehicle_no " +
            " where t5.finishNum &lt; 150 " +
            " order by t5.finishNum " +
            "</script>")
    List<String> queryRiskDriver();
    
    List<CarRiskOrderDetail> driver24HourRegisterCountOnSameDevice(@Param("deviceId") String deviceId);
}
