package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.handler.HandlerChooseFactory;
import com.ly.car.risk.process.handler.riskSecurity.RiskSecurityHandler;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class RiskSecurityConsumer implements MessageListenerConcurrently {

    private HandlerChooseFactory handlerChooseFactory;


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            LoggerUtils.initLogMap("RiskSecurityConsumer", messageExt.getTags(), messageExt.getMsgId(), "");
            String tag = messageExt.getTags();
            try {
                String body = new String(messageExt.getBody(), "utf-8");
                LoggerUtils.info(log, "接收到消息:{},tag:{}", body, tag);

                JSONObject jsonObject = JSONObject.parseObject(body);
                String orderId = jsonObject.getString("orderId");
                LoggerUtils.getLogMap().put("filter2",orderId);

                handlerChooseFactory = SpringContextUtil.getBean("handlerChooseFactory");
                // 新老订单处理分开
                RiskSecurityHandler riskSecurityHandler = handlerChooseFactory.chooseRiskSecurityHandler(OrderUtils.isNewOrder(orderId) ? "new" : "old");
                riskSecurityHandler.doHandler(body, tag);

                LoggerUtils.info(log, "消息处理结束,orderId:{},tag:{}", orderId, tag);
            } catch (Exception e){
                log.error("[][][][]安全消费能力报错",e);
            } finally {
                LoggerUtils.removeAll();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


}
