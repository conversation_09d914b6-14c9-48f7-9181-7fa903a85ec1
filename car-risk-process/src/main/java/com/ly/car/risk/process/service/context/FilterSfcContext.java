package com.ly.car.risk.process.service.context;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.dto.SfcRiskRuleConfig;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FilterSfcContext {

    private List<RiskCustomerManage> riskCustomerManageList;
    private UiResult uiResult = UiResult.ok();
    private Integer mainScene;
    private Integer childScene;
    private String orderId;
    private String unionId;
    private String memberId;
    private String userPhone;//用户手机号
    private String passengerCellphone;//乘车人手机号
    private String driverCardNo;//司机车牌号
    private List<OrderRiskContext> driverContextList;//司机纬度完单
    private List<OrderRiskContext> userContextList;//用户纬度完单
    private List<OrderStatusCancelDTO> memberCancelList;//用户所有取消订单
    private List<SfcRiskLimitData> orderLimitList;//恶意下单用
    private List<SfcOrderNumDTO> cancelOrderNumList;//恶意取消
    private SfcRiskRuleConfig sfcRiskRuleConfig;//顺风车配置
    private Map<String,Boolean> ruleOnOff;//规则开关
    private List<OrderPassengerCellPhone> phoneContextList;//手机号维度的

    private DistributionRiskManageService distributionRiskManageService;
    private List<String> driverList;//邀请车主列表

    private FilterParams params;
    private String ruleNo;

    public FilterSfcContext(){
        UiResult result = this.uiResult;
        result.setData(new RiskResultDTO());
    }

    public void setDistribution(DistributionRiskManageService distributionRiskManageService){
        this.distributionRiskManageService = distributionRiskManageService;
    }
}
