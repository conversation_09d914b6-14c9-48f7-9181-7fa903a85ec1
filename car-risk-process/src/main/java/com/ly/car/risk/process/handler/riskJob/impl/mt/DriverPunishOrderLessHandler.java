//package com.ly.car.risk.process.handler.riskJob.impl.mt;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSON;
//import com.ly.car.risk.common.enums.RiskAlertApproveHandleResultEnum;
//import com.ly.car.risk.common.enums.RiskAlertApproveLevelEnum;
//import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
//import com.ly.car.risk.common.enums.RiskAlertApproveTargetEnum;
//import com.ly.car.risk.common.enums.RiskJobTypeEnum;
//import com.ly.car.risk.process.handler.riskJob.MtAbstractRiskCheckHandler;
//import com.ly.car.risk.process.model.riskJob.DriverPunishOrderCheckResp;
//import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
//import com.ly.car.risk.process.utils.LoggerUtils;
//import com.ly.sof.utils.common.UUID;
//import java.time.LocalDateTime;
//import java.util.Date;
//import java.util.List;
//import java.util.stream.Collectors;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//@Service
//public class DriverPunishOrderLessHandler extends MtAbstractRiskCheckHandler<DriverPunishOrderCheckResp> {
//
//    @Override
//    public RiskJobTypeEnum support() {
//        return RiskJobTypeEnum.DRIVER_PUNISH_LESS_ORDER;
//    }
//
//    @Override
//    public List<RiskAlertApprove> doCheck() {
//        String dayBegin = getDayBegin();
//        String now = formatter.format(LocalDateTime.now());
//        String checkThresholdVal = getMTCheckThreshold(support());
//        if (StringUtils.isBlank(checkThresholdVal)) {
//            LoggerUtils.info(logger, "未配置预警阈值，check结束");
//            return null;
//        }
//
//        int right = Integer.parseInt(checkThresholdVal);
//
//        if (right <= 0) {
//            LoggerUtils.info(logger, "预警阈值不合理，check结束");
//            return null;
//        }
//
//        // 小于左区间不发预警只落库
//        List<DriverPunishOrderCheckResp> punishResp = carMtDriverPunishRecordMapper.checkPunishCount(null, right, dayBegin);
//
//        if (CollUtil.isEmpty(punishResp)) {
//            return null;
//        }
//
//        return punishResp.stream().peek(p -> {
//            p.setStartTime(dayBegin);
//            p.setEndTime(now);
//        }).filter(p -> !todayAlreadyAlert(p, dayBegin)).map(p -> convert(p, right)).collect(Collectors.toList());
//
//    }
//
//    private boolean todayAlreadyAlert(DriverPunishOrderCheckResp checkResp, String dayBegin) {
//        List<RiskAlertApprove> approveList = approveMapper.findRecentlyAlertRecord(RiskAlertApproveTargetEnum.DRIVER.getCode(), checkResp.getCarNum(),
//                RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND.getCode(), RiskJobTypeEnum.DRIVER_PUNISH_LESS_ORDER.name(), null,
//                dayBegin);
//        return CollUtil.isNotEmpty(approveList);
//    }
//
//    private RiskAlertApprove convert(DriverPunishOrderCheckResp checkResp, int left) {
//        Date now = new Date();
//        RiskAlertApprove alertApprove = new RiskAlertApprove();
//        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
//        alertApprove.setLevel(RiskAlertApproveLevelEnum.NONE.getCode());
//        alertApprove.setTarget(RiskAlertApproveTargetEnum.DRIVER.getCode());
//        alertApprove.setTargetValue(checkResp.getCarNum());
//        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.CUSTOMER_COMPLAINT_REFUND.getCode());
//        alertApprove.setAlertStrategy(RiskJobTypeEnum.DRIVER_PUNISH_LESS_ORDER.name());
//        alertApprove.setAlertContent(JSON.toJSONString(checkResp));
//        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
//        alertApprove.setAlertTime(now);
//        alertApprove.setCreateTime(now);
//        alertApprove.setUpdateTime(now);
//        alertApprove.setCreateUser(support().name() + "_CHECK");
//        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
//        approveMapper.insertSelective(alertApprove);
//        return alertApprove;
//    }
//
//    @Override
//    public String getContentFormApprove(RiskAlertApprove approve) {
//        return StringUtils.EMPTY;
//    }
//
//    @Override
//    public String getDesc(RiskAlertApprove approve, DriverPunishOrderCheckResp resp) {
//        return StringUtils.EMPTY;
//    }
//}