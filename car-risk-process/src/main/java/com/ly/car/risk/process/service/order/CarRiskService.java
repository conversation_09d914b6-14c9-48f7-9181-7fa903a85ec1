package com.ly.car.risk.process.service.order;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.request.*;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.model.risk.DutyResultDTO;

public interface CarRiskService {
    void driverCancel(DriverCancelRequest request);

    /**
     * 用户取消判责
     * @param request
     * @return
     */
    DutyResultDTO assignDuty(AssignDutyRequest request) throws BizException;

    UiResult orderAcceptCheck(OrderAcceptCheckRequest request) throws BizException;

    UiResult checkIsRiskUser(IsRiskUserRequest request) throws BizException;

    UiResult queryRiskLevel(RiskLevelQueryRequest request) throws Exception;

    UiResult queryCashRate(CashRateQueryRequest request) throws Exception;
}
