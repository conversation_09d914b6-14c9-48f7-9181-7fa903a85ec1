<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.risk.mapper.OfflineMetricFieldRelationMapper">
    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.risk.mapper.entity.OfflineMetricFieldRelation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="field_id" jdbcType="BIGINT" property="fieldId"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="right_type" jdbcType="INTEGER" property="rightType"/>
        <result column="right_value" jdbcType="VARCHAR" property="rightValue"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, strategy_id, field_id, `operator`, right_type, right_value, sort, create_time
    </sql>

</mapper>