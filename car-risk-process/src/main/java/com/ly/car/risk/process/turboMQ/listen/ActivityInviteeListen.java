package com.ly.car.risk.process.turboMQ.listen;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.ActivityInviteeConsumer;
import com.ly.car.risk.process.turboMQ.consumer.BingLogYncOrderInfoConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 获取中台乘推乘关系
 * */
@Service
@Slf4j
public class ActivityInviteeListen implements ApplicationListener<ApplicationStartedEvent> {

    private static final String GROUP = "risk_group_activity_invitee";
    private static final String TOPIC = "TECarMarketingPlatform_activity_invitee";

    @Resource
    private UrlsProperties urlsProperties;

    @SneakyThrows
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if(!urlsProperties.getActivityConsumer()){
            return;
        }
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(GROUP);
        consumer.setNamesrvAddr(urlsProperties.getBigDataMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(TOPIC,"");
        consumer.registerMessageListener(new ActivityInviteeConsumer());
        consumer.start();
        log.info("[MqStartupRunner][ActivityInviteeListen][ActivityInviteeListen][]启动tuborMQ消费者-活动关系");
    }
}
