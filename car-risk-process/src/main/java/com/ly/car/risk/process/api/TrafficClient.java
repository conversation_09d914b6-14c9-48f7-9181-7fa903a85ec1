package com.ly.car.risk.process.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.process.api.dto.DriverLocationTrackResp;
import com.ly.car.risk.process.api.dto.LocationTrackDTO;
import com.ly.car.risk.process.api.param.DriverBlackNumReq;
import com.ly.car.risk.process.api.param.DriverBlackReq;
import com.ly.car.risk.process.api.rsp.TrafficRsp;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.client.HttpUtils;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class TrafficClient {

    @Resource
    private UrlsProperties urlProperties;

    /**
     * 调用司机拉黑动作
     * */
    public String driverShielding(DriverBlackReq req){
        try {
            log.info("[][driverShielding][info][][] 风控拉黑司机：{}",JSONObject.toJSONString(req));
            String result = OkHttpClientUtil.getInstance()
                    .post(urlProperties.getDriverBlack(), JSONObject.toJSONString(req),null,500l);
            log.info("[][driverShielding][info][][] 风控拉黑司机：{}",result==null?"":JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("[][driverShielding][error][][] 风控拉黑司机异常：{}",e);
        }
        return "ok";
    }

    /**
     * 查询司机拉黑次数
     * 一天查一次，不需要超时
     * */
    public String getBlackDriverNum(DriverBlackNumReq req){
        try {
            String result = OkHttpClientUtil.getInstance()
                    .post(urlProperties.getDriverBlackNum(), JSONObject.toJSONString(req),null);
            TrafficRsp rsp = JSONObject.parseObject(result,TrafficRsp.class);
            log.info("[][getBlackDriverNum][info] [][] 获取拉黑司机次数返回：{}",rsp==null?"":JSONObject.toJSONString(rsp));
            if(rsp.getCode() == 200 && rsp.getData() != null){
                return rsp.getData().toJSONString();
            }
            return result;
        } catch (Exception e) {
            log.error("[][getBlackDriverNum][error] [][] 获取拉黑司机次数异常：{}",e);
        }
        return null;
    }

    /**
     * 判断当前司机是否黑名单
     * */
    public Integer isBlackDriver(String platNumber){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("driver_plate_number",platNumber);
        jsonObject.put("remake","渠道风控");
        try {
            String result = OkHttpClientUtil.getInstance()
                    .post(urlProperties.getDriverBlackJudge(), JSONObject.toJSONString(jsonObject),null);
            TrafficRsp rsp = JSONObject.parseObject(result,TrafficRsp.class);
            log.info("[][isBlackDriver][info] [][] 判断当前是否黑名单司机返回：{}",rsp==null?"":JSONObject.toJSONString(rsp));
            if(rsp.getCode() == 200 && rsp.getIs_black() != null){
                return rsp.getIs_black();
            }
            return null;
        } catch (Exception e) {
            log.error("[][isBlackDriver][error] [][] 判断当前是否黑名单司机异常：{}",e);
        }
        return null;
    }

    /**
     * 获取路线经纬度
     * */
    public String queryPoint(String startLocation,String endLocation){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("start_location",startLocation);
        jsonObject.put("end_location",endLocation);
        try {
            String result = OkHttpClientUtil.getInstance()
                    .post(urlProperties.getPointQueryUrl(), JSONObject.toJSONString(jsonObject),null);
            TrafficRsp rsp = JSONObject.parseObject(result,TrafficRsp.class);
            log.info("[][isBlackDriver][info] [][] 获取路径规划返回：{}",rsp==null?"":JSONObject.toJSONString(rsp));
            if(rsp.getCode() == 200 && rsp.getData() != null){
                String points = rsp.getData().getString("points");
                //对预估路径进行去重
                List<String> pointArray = new ArrayList<>(Arrays.asList(points.split(";")));
                List<String> noRepeatPointList = new ArrayList<>();

                int i =0;
//                String strBand = pointArray.get(0);
                for(;i<pointArray.size()-1;i++){
                    for(int j=i+1;j<pointArray.size()-1;j++){
                        double startLat = Double.parseDouble(pointArray.get(i).split(",")[1]);
                        double startLng = Double.parseDouble(pointArray.get(i).split(",")[0]);
                        double endLat = Double.parseDouble(pointArray.get(j).split(",")[1]);
                        double endLng = Double.parseDouble(pointArray.get(j).split(",")[0]);
                        double distance = CoordUtil.getDistance(startLng, startLat, endLng, endLat);
                        if(new BigDecimal(distance).compareTo(new BigDecimal("200")) < 0){

                        } else {
                            i = j;
                            noRepeatPointList.add(pointArray.get(j));
//                            strBand = strBand + ";"+;
                            break;
                        }
                    }
                }
                return StringUtils.join(noRepeatPointList,";");
            }
            return null;
        } catch (Exception e) {
            log.error("[][isBlackDriver][error] [][] 获取路径规划api异常：{}",e);
        }
        return null;
    }

    public String queryPoint(String orderId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderSerialNo", orderId);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Labrador-Token", urlProperties.getDriverLocationTrackToken());
        try {
            LoggerUtils.info(log, "查询订单行程轨迹，req:{} ", JSON.toJSONString(jsonObject));
            String resp = HttpUtils.postJson(urlProperties.getDriverLocationTrackUrl(), jsonObject, headerMap, 5, TimeUnit.SECONDS);
            LoggerUtils.info(log, "查询订单行程轨迹，resp:{} ", resp);
            JSONObject jsonResp = JSON.parseObject(resp);
            DriverLocationTrackResp driverLocationTrackResp = JSON.parseObject(jsonResp.getString("data"), new TypeReference<DriverLocationTrackResp>() {
            });
            if(null == driverLocationTrackResp || !BooleanUtils.isTrue(driverLocationTrackResp.getSuccess())
                    || CollUtil.isEmpty(driverLocationTrackResp.getList())){
                return StringUtils.EMPTY;
            }
            List<String> noRepeatPointList = new ArrayList<>();
            List<LocationTrackDTO> list = driverLocationTrackResp.getList();
            for (int i = 0; i < list.size() - 1; i++) {
                for (int j = i + 1; j < list.size() - 1; j++) {
                    double startLat = list.get(i).getLatitude();
                    double startLng = list.get(i).getLongitude();
                    double endLat = list.get(j).getLatitude();
                    double endLng = list.get(j).getLongitude();
                    double distance = CoordUtil.getDistance(startLng, startLat, endLng, endLat);
                    if (new BigDecimal(distance).compareTo(new BigDecimal("200")) >= 0) {
                        i = j;
                        noRepeatPointList.add(endLng+","+endLat);
                        break;
                    }
                }
            }
            return StringUtils.join(noRepeatPointList,";");

        } catch (Exception e) {
            LoggerUtils.error(log,"查询订单行程轨迹 异常",e);
            return StringUtils.EMPTY;
        }

    }

}
