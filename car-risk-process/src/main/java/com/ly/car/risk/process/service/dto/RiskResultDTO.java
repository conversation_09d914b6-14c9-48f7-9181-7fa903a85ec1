package com.ly.car.risk.process.service.dto;

import lombok.Data;

@Data
public class RiskResultDTO {

    private Integer code;
    private String message;
    private String ruleNo;
    private String customer;
    private Integer level;
    private String cashRate;
    private Object obj;
    private Integer riskFlag;

    public RiskResultDTO(){
        this.code = 0;
        this.message = "风控通过";
    }
    
    public RiskResultDTO(Integer code,String message){
        this.code = code;
        this.message = message;
    }

    public RiskResultDTO(Integer code,String message,String ruleNo,String customer){
        this.code = code;
        this.message = message;
        this.ruleNo = ruleNo;
        this.customer = customer;
    }

    /**
     * 新加了个等级
     * */
    public RiskResultDTO(Integer code,String message,String ruleNo,String customer,Integer level){
        this.code = code;
        this.message = message;
        this.ruleNo = ruleNo;
        this.customer = customer;
        this.level = level;
    }


}
