package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 当前司机近3天完单关联相同用户的订单大于等于7单
 * */
@Service
@Slf4j
public class RuleRisk021Service extends FilterRuleChain {

    private static final String ruleNo = "021";

    @Resource
    private RuleRisk022Service ruleRisk022Service;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk020Service][][]前置判断已通过，进入规则021判断{}",orderContext.getMemberId(),orderContext.getDriverCardNo());
        this.next(ruleRisk022Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }

        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }

        //执行此规则就给个执行标记
        orderContext.getNeedRuleMap().put(ruleNo,true);

        //计算开始时间戳
        List<OrderRiskContext> orderRiskContextList = orderContext.getDriverContextList().stream()
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.threeDay()))
                .collect(Collectors.toList());
        Map<String, List<OrderRiskContext>> collect = orderRiskContextList.stream()
                .filter(context -> StringUtils.isNotBlank(context.getMemberId()) && !context.getMemberId().equals("0"))
                .collect(Collectors.groupingBy(OrderRiskContext::getMemberId));
        for(Map.Entry<String,List<OrderRiskContext>> entry : collect.entrySet()){
            if(entry.getValue().size() >= orderContext.getSfcRiskRuleConfig().getOrderNum021()){
                String orderIds = StringUtils.join(entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList()),",");
                log.info("[RuleRisk018Service][doHandler][{}][{}]命中021规则，司机关联用户为{},关联订单为{}"
                        ,orderContext.getMemberId(),orderContext.getDriverCardNo(),entry.getKey(), JsonUtils.json(orderIds));
                distributionRiskManageService.addByRuleChain(entry.getValue(),ruleNo,2,1,0,null, RiskLevelEnum.HIGH.getCode());
                UiResult result = UiResult.ok();
                result.setMsg("风控不通过");
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过021",null,null);
                result.setData(dto);
                orderContext.setUiResult(result);
//                return result;
            }
        }
        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
//            UiResult result = UiResult.ok();
//            result.setData("0");
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
