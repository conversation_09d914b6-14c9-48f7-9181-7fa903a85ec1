package com.ly.car.risk.process.api.param;

import lombok.Data;

/**
 * {
 *     "origin": "120.739019,31.252341",
 *     "destination": "120.722995,31.252666",
 *     "platform": "",
 *     "originid": "B0FFLEKCCH",
 *     "destinationid": "BX10021499",
 *     "origintype": "商务住宅;楼宇;商务写字楼",
 *     "destinationtype": "交通设施服务;地铁站;出入口"
 * }
 * */
@Data
public class LbsDrivingParam {

    private String platform;//唯一标识
    private String origin;//出发经纬度
    private String destination;//目的地经纬度
}
