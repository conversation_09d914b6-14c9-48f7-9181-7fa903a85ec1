package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.service.SfcRiskService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sfcRule")
public class SfcRuleRiskController {

    @Resource
    private SfcRiskService sfcRiskService;

    @RequestMapping("/action")
    public String action(@RequestBody CommonParams commonParams){
        this.sfcRiskService.sfcRuleRisk(commonParams);
        return "success";
    }
}
