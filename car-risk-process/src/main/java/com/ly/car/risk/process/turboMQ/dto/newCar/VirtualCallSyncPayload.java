package com.ly.car.risk.process.turboMQ.dto.newCar;

import lombok.Data;

/**
 * Description of VituralCallSyncPayload
 *
 * <AUTHOR>
 * @date 2024/7/8
 * @desc 虚拟通话同步消息体
 */
@Data
public class VirtualCallSyncPayload {
    /** 智能状态码（阿里返回的智能状态码，不保证一定存在，枚举在文档下方） */
    private String callStatus;

    /** 外部业务合作id（一般业务传的都是订单号） */
    private String externalId;

    /** 号码池 */
    private String poolKey;

    /** 供应商（1:阿里、3华为等） */
    private Integer supplierCode;

    /** 供应商绑定关系 ID */
    private String supplierSubId;

    /** 通话对应的三元组的绑定关系ID */
    private Long subId;

    /** 供应商通话记录的ID */
    private String supplierCallId;

    /** 唯一标识一通通话记录的ID */
    private String callId;

    /** AXB中的X号码 */
    private String secretNo;

    /** 分机号码 */
    private String extension;

    /** AXB中的A号码 */
    private String noA;

    /** AXB中的B号码或者N号码 */
    private String noB;

    /** 录音下载URL，有效期是7天 */
    private String recordUrl;

    /** 放音录音URL，有效期是7天 */
    private String ringRecordUrl;

    /** 呼叫类型 */
    private Integer callType;

    /** 被叫显号 */
    private String calledDisplayNo;

    /** 主叫拨打时间 */
    private String callTime;

    /** 被叫接听时间 */
    private String startTime;

    /** 呼叫被叫侧发起的时间。如未发起，则等于call_time的时间；短信话单时，此值传短信接收时间。 */
    private String callOutTime;

    /** 被叫响铃时间 */
    private String ringTime;

    /** 被叫空闲振铃时间。free_ring_time大于call_out_time表示被叫真实发生了振铃事件。free_ring_time和call_out_time相等表示未振铃。如获取不到，则等于ring_time的时间。 */
    private String freeRingTime;

    /** 通话释放时间（通话计费结束时间）；短信话单时，此值传短信接收时间。release_time和start_time之差表示通话时长， 如果结果为0，说明呼叫未接通。 */
    private String releaseTime;

    /** 短信长度 */
    private Integer smsNumber;

    /** 通话释放方向，0代表主叫挂断 1被叫挂断 2平台释放 */
    private Integer releaseDir;

    /** 外部业务合作id */
    private String outId;

    /** 未接通通话的原因归类。取值：0：正常通话。1：黑名单拦截。2：无绑定关系。3：呼叫限制。4：其他。 */
    private String unconnectedCause;

    /** 释放原因，对应话单错误码表 */
    private Integer releaseCause;

    /** 无绑定关系时返回NO_SUBS_EXIST */
    private String controlMsg;

    /** 主叫号码 */
    private String callingNo;

    /** 被叫号码 */
    private String calledNo;

    /** 通话时长分钟 */
    private Integer callMinutes;

    /** 通话时长.秒 */
    private Integer callSeconds;

    /** 逻辑删除 */
    private Integer deleted;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String modifier;

    /** 创建时间 */
    private String createTime;

    /** 更新时间 */
    private String updateTime;
}