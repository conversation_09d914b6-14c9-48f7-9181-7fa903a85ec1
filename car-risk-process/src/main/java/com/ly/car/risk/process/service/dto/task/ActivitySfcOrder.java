package com.ly.car.risk.process.service.dto.task;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ActivitySfcOrder {

    private String orderId;
    private String supplierOrderId;
    private String plateNumber;
    private String passengerCellphone;
    private Integer totalAmount;
    private BigDecimal startLat;
    private BigDecimal startLng;
    private BigDecimal endLat;
    private BigDecimal endLng;
    private Date finishTime;
}
