package com.ly.car.risk.process.repo.riskmetrics.mapper;


import com.ly.car.risk.process.model.riskJob.*;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderCoupon;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface CarRiskOrderCouponMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CarRiskOrderCoupon record);

    int insertSelective(CarRiskOrderCoupon record);

    CarRiskOrderCoupon selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CarRiskOrderCoupon record);

    int updateByPrimaryKey(CarRiskOrderCoupon record);

    List<SupplierCouponCheckResp> checkDaySupplierCoupon(@Param("threshold") BigDecimal checkThreshold, @Param("dayBegin") String dayBegin);

    /**
     * 当日单个供应商核销优惠券张数大于等于n张
     * @param checkThreshold
     * @param dayBegin
     * @return
     */
    List<SupplierCouponCountCheckResp> checkDaySupplierCountCoupon(@Param("threshold") BigDecimal checkThreshold, @Param("dayBegin") String dayBegin);

    /**
     * 当日单个供应商核销单个批次号的优惠券张数大于等于n张
     * @param checkThreshold
     * @param dayBegin
     * @return
     */
    List<SupplierCouponBatchCountCheckResp> checkDaySupplierBatchCountCoupon(@Param("threshold") BigDecimal checkThreshold, @Param("dayBegin") String dayBegin);

    List<DriverCouponCheckResp> checkDayDriverCoupon(@Param("threshold") BigDecimal checkThreshold, @Param("dayBegin") String dayBegin);

    /**
     *
     当日单个司机核销优惠券张数大于等于n张
     * @param checkThreshold
     * @param dayBegin
     * @return
     */
    List<DriverCouponCountCheckResp> checkDayDriverCouponCount(@Param("threshold") BigDecimal checkThreshold, @Param("dayBegin") String dayBegin);

    /**
     * 当日单个司机核销单个批次号的优惠券张数大于等于n张
     * @param checkThreshold
     * @param dayBegin
     * @return
     */
    List<DriverCouponBatchCountCheckResp> checkDayDriverCouponBatchCount(@Param("threshold") BigDecimal checkThreshold, @Param("dayBegin") String dayBegin);


    List<UserCouponCheckResp> checkHourUserCoupon(@Param("threshold")int checkThreshold, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 当日单个用户单个批次号的优惠券核销张数大于等于n张的数据
     * @param checkThreshold
     * @param dayBegin
     * @return
     */
    List<UserCouponBatchCountCheckResp> checkDailyBatchNoUserCoupon(@Param("threshold")int checkThreshold, @Param("dayBegin") String dayBegin);

    List<UserDriverCouponCheckResp> checkUserDriverCoupon(@Param("couponCount") int couponCount, @Param("driverCount") int driverCount, @Param("startTime") String startTime, @Param("endTime") String endTime);

    void cleanRiskMetricOldCouponData();
}