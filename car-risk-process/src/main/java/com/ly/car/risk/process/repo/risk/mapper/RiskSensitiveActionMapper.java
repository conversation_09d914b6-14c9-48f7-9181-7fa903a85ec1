package com.ly.car.risk.process.repo.risk.mapper;

import com.ly.car.risk.process.model.riskJob.UserViewSensitiveCheckResp;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskSensitiveAction;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RiskSensitiveActionMapper {
    int deleteByPrimaryKey(Long id);
    
    int insert(RiskSensitiveAction record);
    
    int insertSelective(RiskSensitiveAction record);
    
    RiskSensitiveAction selectByPrimaryKey(Long id);
    
    int updateByPrimaryKeySelective(RiskSensitiveAction record);
    
    int updateByPrimaryKey(RiskSensitiveAction record);
    
    List<UserViewSensitiveCheckResp> checkCount(@Param("left") Integer left, @Param("right") Integer right, @Param("startTime") String startTime, @Param("endTime") String endTime);
}