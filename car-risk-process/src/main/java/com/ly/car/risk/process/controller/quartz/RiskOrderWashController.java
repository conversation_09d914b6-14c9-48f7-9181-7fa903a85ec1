package com.ly.car.risk.process.controller.quartz;

import com.ly.car.risk.process.service.task.WashTaskService;
import com.ly.car.risk.process.strategy.OfflineRiskStrategyHandler;
import com.ly.car.risk.process.strategy.OfflineRiskStrategyHelper;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Description of RiskOrderWashController
 *
 * <AUTHOR>
 * @date 2024/7/10
 * @desc
 */
@RestController
@RequestMapping("/riskOrderWash")
@Slf4j
public class RiskOrderWashController {

    @Resource
    private WashTaskService            washTaskService;
    @Resource
    private OfflineRiskStrategyHandler offlineRiskStrategyHandler;

    @RequestMapping("/generalRiskOrderWash")
    public String generalRiskOrderWash() {
        LoggerUtils.initLogMap("generalRiskOrderWash", "", "", "");
        try {
            // 每小时执行一次，
            String nowDate = DateUtil.date2String(DateUtil.addHour(new Date(), -1));
            String splitTime = nowDate.split(":")[0];
            String startTime = splitTime + ":00:00";
            String endTime = splitTime + ":59:59";
            washTaskService.generalRiskOrderWash(startTime, endTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log,"generalRiskOrderWash error",e);
            return "fail:" + e.getMessage();
        } finally {
            LoggerUtils.removeAll();
        }
    }


    @RequestMapping("/abnormalAmountRiskOrder")
    public String abnormalAmountRiskOrder() {
        LoggerUtils.initLogMap("abnormalAmountRiskOrder", "", "", "");
        try {
            // 每小时执行一次，
            String nowDate = DateUtil.date2String(DateUtil.addHour(new Date(), -1));
            String splitTime = nowDate.split(":")[0];
            String startTime = splitTime + ":00:00";
            String endTime = splitTime + ":59:59";
            washTaskService.abnormalAmountRiskOrder(startTime, endTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log,"abnormalAmountRiskOrder error",e);
            return "fail:"+e.getMessage();
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping("/offlineJobSummary")
    public String offlineJobSummary() {
        LoggerUtils.initLogMap("offlineJobSummary", "", "", "");
        try {
            // 每小时执行一次，
            String nowDate = DateUtil.date2String(DateUtil.addHour(new Date(), -1));
            String splitTime = nowDate.split(":")[0];
            String startTime = splitTime + ":00:00";
            String endTime = splitTime + ":59:59";
            washTaskService.offlineJobSummary(startTime, endTime);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log,"offlineJobSummary error",e);
            return "fail:"+e.getMessage();
        } finally {
            LoggerUtils.removeAll();
        }
    }
    
    /**
     * 离线策略
     * @param during OfflineStrategyDuringEnum
     * @return
     */
    @RequestMapping("/offlineStrategy")
    public String offlineStrategy(@RequestParam("during") String during) {
        LoggerUtils.initLogMap("offlineStrategy", "", "", "");
        try {
            offlineRiskStrategyHandler.wash(during);
            return "success";
        } catch (Exception e) {
            LoggerUtils.error(log,"offlineStrategy error",e);
            return "fail:"+e.getMessage();
        } finally {
            LoggerUtils.removeAll();
        }
    }
}