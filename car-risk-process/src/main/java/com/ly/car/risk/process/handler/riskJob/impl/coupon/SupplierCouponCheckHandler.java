package com.ly.car.risk.process.handler.riskJob.impl.coupon;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ly.car.order.entity.SupplierInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.common.enums.RiskAlertApproveHandleResultEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveTargetEnum;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.handler.riskJob.AbstractRiskCheckHandler;
import com.ly.car.risk.process.model.riskJob.SupplierCouponCheckResp;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;

import cn.hutool.core.collection.CollUtil;

/**
 * Description of SupplierCouponCheckHandler
 *
 * <AUTHOR>
 * @date 2024/8/28
 * @desc
 */
@Service
public class SupplierCouponCheckHandler extends AbstractRiskCheckHandler<SupplierCouponCheckResp> {

    private Map<String, SupplierInfo>supplierMap = new HashMap<>();

    @Override
    public RiskJobTypeEnum support() {
        return RiskJobTypeEnum.SUPPLIER_COUPON;
    }

    @Override
    public List<RiskAlertApprove> doCheck() {
        String dayBegin = getDayBegin();
        String now = formatter.format(LocalDateTime.now());
        String checkThresholdVal = getCouponCheckThreshold(support());
        if (StringUtils.isBlank(checkThresholdVal)) {
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }
        BigDecimal checkThreshold = new BigDecimal(checkThresholdVal);
        if (checkThreshold.compareTo(BigDecimal.ZERO) <= 0) {
            LoggerUtils.info(logger, "预警阈值不合理，check结束");
            return null;
        }
        List<SupplierCouponCheckResp> checkResp = couponMapper.checkDaySupplierCoupon(checkThreshold, dayBegin);

        if (CollectionUtils.isEmpty(checkResp)) {
            return null;
        }
        supplierMap =  supplierService.getSupplierMap();

        return checkResp.stream().peek(p -> {
            p.setStartTime(dayBegin);
            p.setEndTime(now);
        }).filter(p->!todayAlreadyAlert(p,dayBegin)).map(this::convert).collect(Collectors.toList());

    }

    private boolean todayAlreadyAlert(SupplierCouponCheckResp checkResp, String dayBegin) {
       List<RiskAlertApprove> approveList = approveMapper.findRecentlyAlertRecord(RiskAlertApproveTargetEnum.SUPPLIER.getCode(),checkResp.getSupplierCode(),
               RiskAlertApproveSceneEnum.MARKETING.getCode(), RiskJobTypeEnum.SUPPLIER_COUPON.name(),RiskAlertApproveHandleResultEnum.SAFE.getCode(),
               dayBegin);
       return CollUtil.isNotEmpty(approveList);
    }

    private RiskAlertApprove convert(SupplierCouponCheckResp supplierCouponCheckResp) {
        Date now = new Date();
        RiskAlertApprove alertApprove = new RiskAlertApprove();
        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
        alertApprove.setLevel(2);
        alertApprove.setTarget(RiskAlertApproveTargetEnum.SUPPLIER.getCode());
        alertApprove.setTargetValue(supplierCouponCheckResp.getFullSupplierCode());
        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.MARKETING.getCode());
        alertApprove.setAlertStrategy(RiskJobTypeEnum.SUPPLIER_COUPON.name());
        SupplierInfo supplierInfo = supplierMap.get(supplierCouponCheckResp.getFullSupplierCode());
        if (null != supplierInfo) {
            supplierCouponCheckResp.setSupplierName(supplierInfo.getCompanyName());
        }
        alertApprove.setAlertContent(JSON.toJSONString(supplierCouponCheckResp));
        alertApprove.setOrderType(supplierCouponCheckResp.getOrderType());
        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
        alertApprove.setAlertTime(now);
        alertApprove.setCreateTime(now);
        alertApprove.setUpdateTime(now);
        alertApprove.setCreateUser(support().name() + "_CHECK");
        approveMapper.insertSelective(alertApprove);

        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
        return alertApprove;
    }

    @Override
    public String getContentFormApprove(RiskAlertApprove approve) {
        String alertContent = approve.getAlertContent();
        if (StringUtils.isBlank(alertContent)) {
            return StringUtils.EMPTY;
        }
        SupplierCouponCheckResp resp = JSON.parseObject(alertContent, new TypeReference<SupplierCouponCheckResp>() {
        });
        StringBuilder sb = doGetContentFormApprove(approve,resp);
        return sb.toString();
    }

    @Override
    public String getDesc(RiskAlertApprove approve, SupplierCouponCheckResp resp) {
        StringBuilder sb = new StringBuilder();
        sb.append(RiskJobTypeEnum.getDescByCode(approve.getAlertStrategy()));
        sb.append(String.format("(供应商:%s,批次号:%s,用券金额:%s)",resp.getSupplierName(),resp.getBatchNo(),resp.getAmount()));
        return sb.toString();
    }


}