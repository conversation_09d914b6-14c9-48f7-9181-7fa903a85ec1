package com.ly.car.risk.process.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerTtlEnum;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveWordsMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveWords;
import com.ly.car.risk.process.service.dto.SensitiveWordExcel;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("word")
public class SensitiveWordController {

    @Resource
    private SensitiveWordsMapper sensitiveWordsMapper;

    @RequestMapping("init")
    public void init(MultipartFile file) throws IOException {
        List<SensitiveWordExcel> list = new ArrayList<>();
        ExcelReader excelReader = null;
        EasyExcel.read(file.getInputStream(), SensitiveWordExcel.class, new PageReadListener<SensitiveWordExcel>(dataList ->{
            for (SensitiveWordExcel data : dataList){
                list.add(data);
            }
        })).sheet().doRead();
        for(SensitiveWordExcel excel : list){
            SensitiveWords sensitiveWords = new SensitiveWords();
            sensitiveWords.setWord(excel.getWord());
            if(excel.getType().equals("线下交易")){
                sensitiveWords.setWordType(5);
            } else if(excel.getType().equals("暴恐")){
                sensitiveWords.setWordType(0);
            } else if(excel.getType().equals("不文明用语")){
                sensitiveWords.setWordType(1);
            } else if(excel.getType().equals("广告")){
                sensitiveWords.setWordType(2);
            } else if(excel.getType().equals("其他")){
                sensitiveWords.setWordType(3);
            }else if(excel.getType().equals("违法违禁")){
                sensitiveWords.setWordType(4);
            }else if(excel.getType().equals("政治")){
                sensitiveWords.setWordType(6);
            }
            sensitiveWords.setHitCount(0);
            sensitiveWords.setDeleted(0);
            sensitiveWords.setLevel(excel.getLevel().equals("高")?1:0);
            sensitiveWords.setCreateTime(new Date());
            sensitiveWords.setUpdateTime(new Date());
            sensitiveWords.setOperateUser("李文");
            this.sensitiveWordsMapper.insert(sensitiveWords);
        }
        SensitiveWords sensitiveWords = new SensitiveWords();
        sensitiveWords.setDeleted(1);
        sensitiveWords.setUpdateTime(new Date());
        this.sensitiveWordsMapper.update(sensitiveWords,
                new UpdateWrapper<SensitiveWords>().lt("create_time", DateUtil.addDay(new Date(),-1))
        );
    }
}
