<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverBillMapper">
    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverBill">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="withdraw_no" jdbcType="VARCHAR" property="withdrawNo"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="voucher_no" jdbcType="VARCHAR" property="voucherNo"/>
        <result column="voucher_type" jdbcType="INTEGER" property="voucherType"/>
        <result column="bill_type" jdbcType="INTEGER" property="billType"/>
        <result column="bill_sub_type" jdbcType="INTEGER" property="billSubType"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, bill_no, withdraw_no, driver_id, voucher_no, voucher_type, bill_type, bill_sub_type, 
    amount, remark, create_time, create_user, update_time, update_user
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from car_mt_driver_bill
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from car_mt_driver_bill
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverBill" useGeneratedKeys="true">
        insert into car_mt_driver_bill (bill_no, withdraw_no, driver_id,
                                        voucher_no, voucher_type, bill_type,
                                        bill_sub_type, amount, remark,
                                        create_time, create_user, update_time,
                                        update_user)
        values (#{billNo,jdbcType=VARCHAR}, #{withdrawNo,jdbcType=VARCHAR}, #{driverId,jdbcType=BIGINT},
                #{voucherNo,jdbcType=VARCHAR}, #{voucherType,jdbcType=INTEGER}, #{billType,jdbcType=INTEGER},
                #{billSubType,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{updateUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverBill" useGeneratedKeys="true">
        insert into car_mt_driver_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billNo != null">
                bill_no,
            </if>
            <if test="withdrawNo != null">
                withdraw_no,
            </if>
            <if test="driverId != null">
                driver_id,
            </if>
            <if test="voucherNo != null">
                voucher_no,
            </if>
            <if test="voucherType != null">
                voucher_type,
            </if>
            <if test="billType != null">
                bill_type,
            </if>
            <if test="billSubType != null">
                bill_sub_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billNo != null">
                #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="withdrawNo != null">
                #{withdrawNo,jdbcType=VARCHAR},
            </if>
            <if test="driverId != null">
                #{driverId,jdbcType=BIGINT},
            </if>
            <if test="voucherNo != null">
                #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherType != null">
                #{voucherType,jdbcType=INTEGER},
            </if>
            <if test="billType != null">
                #{billType,jdbcType=INTEGER},
            </if>
            <if test="billSubType != null">
                #{billSubType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverBill">
        update car_mt_driver_bill
        <set>
            <if test="billNo != null">
                bill_no = #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="withdrawNo != null">
                withdraw_no = #{withdrawNo,jdbcType=VARCHAR},
            </if>
            <if test="driverId != null">
                driver_id = #{driverId,jdbcType=BIGINT},
            </if>
            <if test="voucherNo != null">
                voucher_no = #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherType != null">
                voucher_type = #{voucherType,jdbcType=INTEGER},
            </if>
            <if test="billType != null">
                bill_type = #{billType,jdbcType=INTEGER},
            </if>
            <if test="billSubType != null">
                bill_sub_type = #{billSubType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverBill">
        update car_mt_driver_bill
        set bill_no       = #{billNo,jdbcType=VARCHAR},
            withdraw_no   = #{withdrawNo,jdbcType=VARCHAR},
            driver_id     = #{driverId,jdbcType=BIGINT},
            voucher_no    = #{voucherNo,jdbcType=VARCHAR},
            voucher_type  = #{voucherType,jdbcType=INTEGER},
            bill_type     = #{billType,jdbcType=INTEGER},
            bill_sub_type = #{billSubType,jdbcType=INTEGER},
            amount        = #{amount,jdbcType=DECIMAL},
            remark        = #{remark,jdbcType=VARCHAR},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            create_user   = #{createUser,jdbcType=VARCHAR},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            update_user   = #{updateUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <resultMap id="strategyResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail">
        <id property="id" column="id"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="memberId" column="member_id"/>
        <result property="unionId" column="union_id"/>
        <result property="productLine" column="product_line"/>
        <result property="orderType" column="order_type"/>
        <result property="orderState" column="order_state"/>
        <result property="amount" column="amount"/>
        <result property="payState" column="pay_state"/>
        <result property="orderChannel" column="order_channel"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtPaid" column="gmt_paid"/>
        <result property="gmtCanceled" column="gmt_canceled"/>
        <result property="gmtUsage" column="gmt_usage"/>
        <result property="gmtDeparture" column="gmt_departure"/>
        <result property="gmtArrive" column="gmt_arrive"/>
        <result property="gmtTripFinished" column="gmt_trip_finished"/>
        <result property="cancelType" column="cancel_type"/>
        <result property="cancelReason" column="cancel_reason"/>
        <result property="payCategory" column="pay_category"/>
        <result property="departureCityCode" column="departure_city_code"/>
        <result property="arrivalCityCode" column="arrival_city_code"/>
        <result property="departureAddress" column="departure_address"/>
        <result property="arrivalAddress" column="arrival_address"/>
        <result property="passengerPhone" column="passenger_phone"/>
        <result property="estimateDistance" column="estimate_distance"/>
        <result property="estimateTime" column="estimate_time"/>
        <result property="realDistance" column="real_distance"/>
        <result property="realTime" column="real_time"/>
        <result property="carNum" column="car_num"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierPrice" column="supplier_price"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="washExt" column="wash_ext"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="distributionFlag" column="distribution_flag"/>
        <result property="bookAmount" column="book_amount"/>
        <result property="surcharge" column="surcharge"/>
        <result property="supplementaryAmount" column="supplementary_amount"/>
        <result property="rightsOrderFlag" column="rights_order_flag"/>
    </resultMap>

    <select id="driver24HourBillOrderCount" resultMap="strategyResultMap">
        SELECT distinct db.voucher_no as orderSerialNo
        FROM car_mt_driver_bill db
                 join car_mt_driver_info di on di.driver_id = db.driver_id
        where db.create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
          and db.voucher_type = 11
          and db.remark like '%补贴奖励%'
          and di.car_num = #{carNum}
    </select>

    <select id="driver24hourBillOrderAmount" resultMap="strategyResultMap">
        SELECT amount
        FROM car_mt_driver_bill db
                 join car_mt_driver_info di on di.driver_id = db.driver_id
        where db.create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
          and db.voucher_type = 11
          and db.remark like '%补贴奖励%'
          and di.car_num = #{carNum}
    </select>
</mapper>