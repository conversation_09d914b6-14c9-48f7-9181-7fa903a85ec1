package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.MetricSceneStrategyRelation;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskRuleFieldDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskSceneStrategyDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategyRuleDTO;

import java.util.List;

public interface MetricSceneStrategyRelationMapper extends BaseMapper<MetricSceneStrategyRelation> {
    List<RiskSceneStrategyDTO> findSceneStrategy();

    List<RiskStrategyRuleDTO> findStrategyRule();

    List<RiskRuleFieldDTO> findRuleField();



}
