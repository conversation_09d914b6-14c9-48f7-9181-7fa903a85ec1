package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 当天用户完单次数大于2单，且订单关联单个司机占比大于80%
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk2004Service extends FilterSendOrderHandler{

    public static final String ruleNo = "2004";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        log.info("[][RuleRisk2004Service][][]2004规则参数:{}" + JsonUtils.json(context));
        if(!context.getSpecialCarRuleConfig().getRule2004onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            List<SendOrderContext> orderContextList = context.getMemberList().stream().
                    filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.currentDay())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(orderContextList) && orderContextList.size() > context.getSpecialCarRuleConfig().getRule2004OrderNum()){
                //判断这些订单的同一司机占比是否大于80%
                Map<String,List<SendOrderContext>> driverContextMap = orderContextList.stream().collect(Collectors.groupingBy(SendOrderContext::getDriverCardNo));
                if(StringUtils.isNotBlank(context.getDriverCardNo())){
                    if(driverContextMap.get(context.getDriverCardNo()) != null && driverContextMap.get(context.getDriverCardNo()).size() > context.getSpecialCarRuleConfig().getRule2004OrderNum()){
                        //该用户下面的完单有大于两单是该司机的且占比当天完单是》80%
                        BigDecimal rate = new BigDecimal(driverContextMap.get(context.getDriverCardNo()).size()).divide(new BigDecimal(orderContextList.size()),2,BigDecimal.ROUND_HALF_UP);
                        if(rate.compareTo(context.getSpecialCarRuleConfig().getRule2004Rate()) > 0){
                            log.info("[][RuleRisk2004Service][][]命中2004规则:{}" + JsonUtils.json(driverContextMap.get(context.getDriverCardNo())));
                            List<String> orderIds = driverContextMap.get(context.getDriverCardNo()).stream()
                                    .map(SendOrderContext::getOrderId)
                                    .collect(Collectors.toList());
                            orderIds.add(context.getOrderId());
                            context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
                            distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene(), context.getChildScene(),
                                    0, null, RiskLevelEnum.HIGH.getCode());

                            orderIds.remove(context.getOrderId());
                            riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                        }
                    }
                }

//                for(Map.Entry<String,List<SendOrderContext>> entry : driverContextMap.entrySet()){
//                    BigDecimal rate = new BigDecimal(entry.getValue().size()).divide(new BigDecimal(orderContextList.size()),2,BigDecimal.ROUND_HALF_UP);
//                    if (rate.compareTo(context.getSpecialCarRuleConfig().getRule2004Rate()) > 0) {
//                        log.info("[][RuleRisk2004Service][][]命中2004规则:{}" + JsonUtils.json(entry.getValue()));
//                        List<String> orderIds = entry.getValue().stream()
//                                .map(SendOrderContext::getOrderId)
//                                .collect(Collectors.toList());
//                        orderIds.add(context.getOrderId());
//                        context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
//                        distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene(), context.getChildScene(),
//                                0, null, RiskLevelEnum.HIGH.getCode());
//                        return;
//                    }
//                }
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
