package com.ly.car.risk.process.controller.request.safecenter;

import com.ly.car.risk.process.controller.request.BaseRequest;
import com.ly.car.risk.process.model.enums.WorkOrderEnum;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/13 18:57
 **/
@Data
public class AppealOrderRequest extends BaseRequest {
    /** 订单号 */
    private String orderId;

    /**
     * 用户memberId
     */
    private String memberId;

    /**
     * unionId（有就传）
     */
    private String unionId;

    /**
     * 用户实际乘车车牌
     */
    private String currentCarNo;

    /**
     * 描述问题
     */
    private String questionDesc;

    /**
     * 申诉问题合集
     */
    private List<String> appearQuestions;

    /**
     * 申诉联系人手机号
     */
    private String appearContactPhone;
    /**
     * 问题描述图片
     */
    private List<String> descImage;

    /** 1-人车不符工单 3 -物品遗失工单*/
    private int queryFeedBackType;

    private WorkOrderEnum workOrderType;
}
