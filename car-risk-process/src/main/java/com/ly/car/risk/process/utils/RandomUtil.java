package com.ly.car.risk.process.utils;

import java.util.Random;

public class RandomUtil {

    //length用户要求产生字符串的长度
    public static String getRandomString(int length){
        String str="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789^@#$*&";
        Random random=new Random();
        StringBuffer sb=new StringBuffer();
        for(int i=0;i<length;i++){
            int number=random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(getRandomString(32));
    }

    //CC8G0ZVJdOKfsUMP77DlZFzLXeeh3N9p 顺风车需求，1208迭代
    //yUW1CJmDY99Flrbp3KC1XAeT5w3nYVYE 系统使用，全部规则
    //lg1G8BrFgg2UB5ifSjoepSL4OwjDxpJy 炒鸡生前卡
}
