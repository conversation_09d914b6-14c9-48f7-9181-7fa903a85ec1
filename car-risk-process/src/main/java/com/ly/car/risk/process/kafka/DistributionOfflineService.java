package com.ly.car.risk.process.kafka;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.http.HttpUtils;
import com.ly.car.monitor.HealthCheckService;
import com.ly.car.order.entity.DistributionInfo;
import com.ly.car.risk.entity.DistributionOfflineRisk;
import com.ly.car.risk.process.bean.properties.KafKaProperties;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.kafka.param.MarketingRiskReq;
import com.ly.car.risk.process.kafka.rsp.MarketingRiskRsp;
import com.ly.car.risk.process.repo.order.mapper.DistributionInfoMapper;
import com.ly.car.risk.process.repo.risk.mapper.DistributionOfflineRiskMapper;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderExpand;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderExpandMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.sharding.order.params.OrderListKey;
import com.ly.car.utils.IdUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.spat.dsf.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class DistributionOfflineService implements ApplicationListener<ApplicationStartedEvent> {

    private static final String TOPIC= "car_distribution_offline_topic";

    private static final String RISK_TAG_URL = "/marketing/risk";

    @Resource
    private DistributionOfflineRiskMapper distributionOfflineRiskMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private OrderExpandMapper orderExpandMapper;
    @Resource
    private UrlsProperties urlProperties;
    @Resource
    private DistributionInfoMapper distributionInfoMapepr;
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private KafKaProperties kafKaProperties;
    @Resource
    private KafkaConsumer<String, String> szKafkaConsumer;
    @Resource
    private KafkaProducer<String, String> szKafkaProducer;

    /**
     * 接受数仓同步的数据
     * @param message
     */
    public void start(String message) {
        String jsonStr = message;
        log.info("[][DistributionOfflineConsumer][car_usecar_distribution_offline_topic][]kafka消息msg=" + jsonStr);
        List<String> strList = Arrays.asList(message.split("\\|"));
//        if(strList.size() != 13){
//            log.info("[][DistributionOfflineConsumer][car_usecar_distribution_offline_topic][]kafka消息有问题,msg=" + jsonStr);
//        }
        //订单号、分销员unionid、分销员姓名、分销商id、分销商名称、分销商-市、渠道名称、渠道id、分销员佣金、分销商佣金、总佣金、创单日期

        //查下是否是存在的，存在直接返回
        DistributionOfflineRisk oldRisk = distributionOfflineRiskMapper.selectOne(
                new QueryWrapper<DistributionOfflineRisk>().eq("order_id",strList.get(0))
        );
        if(oldRisk != null){
//            oldRisk.setRevenueAmount(new BigDecimal(strList.get(12)));
            if(strList.size() == 13){
                oldRisk.setRevenueAmount(new BigDecimal(strList.get(12)));
            }
            oldRisk.setCommissionAmount(new BigDecimal(strList.get(8)));
            distributionOfflineRiskMapper.updateById(oldRisk);
            return;
        }

        log.info("[][DistributionOfflineConsumer][car_usecar_distribution_offline_topic][]新增插入" + jsonStr);
        DistributionOfflineRisk risk = new DistributionOfflineRisk();
        risk.setCreateTime(new Date());
        risk.setUpdateTime(new Date());
        risk.setOrderId(strList.get(0));
        risk.setDistributeId(strList.get(1));
        risk.setDistributeUnionid(strList.get(1));
        risk.setDistributeName(strList.get(2).replaceAll("[^\\u0000-\\uFFFF]", ""));
        risk.setCityName(strList.get(5));
        risk.setCommissionAmount(new BigDecimal(strList.get(8)));
        risk.setDistributorsId(strList.get(3));
        risk.setDistributorsName(strList.get(4));
        risk.setChannelId(Integer.valueOf(strList.get(7)));
        risk.setChannelName(strList.get(6));
        risk.setDistributorsCommissionAmount(new BigDecimal(strList.get(9)));
        risk.setAllCommissionAmount(new BigDecimal(strList.get(10)));
        risk.setCreateDate(strList.get(11));
        if(strList.size() == 13){
            risk.setRevenueAmount(new BigDecimal(strList.get(12)));
        }


        if(StringUtils.isNotBlank(strList.get(0))){
            OrderInfo orderInfo = orderInfoMapper.findByOrderId(strList.get(0));
            if(StringUtils.isNotBlank(orderInfo.getSupplierCode()) && orderInfo.getSupplierCode().startsWith("DiDaTaxi")){
                //嘀嗒出租车过滤
                return;
            }
//            if(orderInfo.getStatus() != 300 && orderInfo.getStatus() != 400){
//                //只存储完单的
//                return;
//            }
            //这边搞一下大研发风控标签
            MarketingRiskReq req = new MarketingRiskReq();
            OrderExpand orderExpand = orderExpandMapper.findByOrderId(strList.get(0));
            if(orderExpand != null){
                risk.setDeviceId(orderExpand.getDeviceId());
                risk.setPayAccount(orderExpand.getPayAccount());
                req.setDeviceId(orderExpand.getDeviceId());
            }
            OrderDriver orderDriver = orderDriverMapper.findByOrderId(orderInfo.getOrderId());
            if(orderDriver != null){
                risk.setDriverCardNo(orderDriver.getPlateNumber());
            }


            long refId = orderInfo.getRefId();
            //通过refId查询distribution_info中看channel_type 0-app 1-微信 其他
            DistributionInfo distributionInfo = distributionInfoMapepr.selectByRefId(refId);
            if(distributionInfo != null){
                if(distributionInfo.getChannelType() == 0){
                    req.setChannel("201");
                } else if(distributionInfo.getChannelType() == 1){
                    req.setChannel("852");
                } else {
                    req.setChannel("301");
                }
            }
            req.setAccessId("1565604246704529408");
            req.setActionTime(DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            req.setOpenId(orderInfo.getOpenId());
            req.setUnionId(orderInfo.getUniId());
            req.setMemberId(orderInfo.getMemberId());
            req.setPhone(orderInfo.getPassengerCellphone());
            req.setIp("");
            req.setTraceId(IdUtils.getUUID());
            log.info("[][DistributionOfflineConsumer][http][]请求信息=" + JsonUtils.json(req));
            String rsp = HttpUtils.post(urlProperties.getMarketingRisk()+RISK_TAG_URL, JsonUtils.json(req));
            log.info("[][DistributionOfflineConsumer][http][]返回信息=" + JsonUtils.json(rsp));
            MarketingRiskRsp marketingRiskRsp = JSONObject.parseObject(rsp,MarketingRiskRsp.class);
            if(marketingRiskRsp.getCode().equals(200)){
                JSONObject data = marketingRiskRsp.getData();
                if(data != null){
                    JSONObject tagJson = data.getJSONObject("tag");
                    //机器行为
                    JSONObject machineBehaviorLevel = tagJson.getJSONObject("machineBehaviorLevel");
                    if(Integer.valueOf(machineBehaviorLevel.getString("riskLevel")) > 2){
                        //则是有风险
                        risk.setOutRiskTag(1);
                    }
                    JSONObject aggregationAccountLevel = tagJson.getJSONObject("aggregationAccountLevel");
                    if(Integer.valueOf(aggregationAccountLevel.getString("riskLevel")) > 2){
                        //则是有聚集小号风险
                        if(aggregationAccountLevel.getJSONArray("riskType").get(0).equals("2")){
                            risk.setOutRiskTag(2);
                        }
                    }
                }
            }
            //使用memberId查询当前用户所有的订单，看下是否是新客订单
            String memberId = orderInfo.getMemberId();
            String unionId = orderInfo.getUniId();
            List<Long> memberIds = new ArrayList<>();
            if(StringUtils.isNotBlank(memberId)){
                memberIds.add(Long.valueOf(memberId));
            }
            risk.setOpenId(orderInfo.getOpenId());
            risk.setUnionId(orderInfo.getUniId());
            risk.setMemberId(orderInfo.getMemberId());
            risk.setPhone(orderInfo.getPassengerCellphone());
//            if(StringUtils.isNotBlank(unionId)){
//                //unionId转memberId
//                String unionIdConvertMemberId = memberApi.getMemberInfoByWxUnionId(unionId);
//                memberIds.add(Long.valueOf(unionIdConvertMemberId));
//            }
            OrderListKey orderListKey = new OrderListKey();
            orderListKey.setMemberIds(memberIds);
            List<OrderInfo> orderInfos = orderInfoMapper.getOrderListByMemberId(memberId);
            if(orderInfos == null || orderInfos.size() == 0){
                log.info("[][DistributionOfflineConsumer][car_usecar_distribution_offline_topic][]kafka消息查询订单有问题,memberId=" + JsonUtils.json(memberIds));
                return;
            }
            if(orderInfos != null && orderInfos.size() == 1){
                risk.setIsNewOrder(1);
                distributionOfflineRiskMapper.insert(risk);
                return;
            }
            //进行时间的一个排序
            orderInfos.sort(new Comparator<OrderInfo>() {
                @Override
                public int compare(OrderInfo o1, OrderInfo o2) {
                    if(o1.getCreateTime().compareTo(o2.getCreateTime()) > 0){
                        return 1;
                    }
                    return -1;
                }
            });
            if(orderInfos.get(0).getOrderId().equals(strList.get(0))){
                //说明是新客订单
                risk.setIsNewOrder(1);
            }
        }
        log.info("[][DistributionOfflineConsumer][car_usecar_distribution_offline_topic][]司推乘插入结束{}" , JsonUtils.json(risk));
        distributionOfflineRiskMapper.insert(risk);
    }


    public static Date string2Date(String dateString, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        ParsePosition pos = new ParsePosition(0);
        return format.parse(dateString, pos);
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if (!kafKaProperties.isDisOfflineConsumer()) {
            return;
        }
        Executors.newSingleThreadExecutor().execute(() -> {
            log.info("司推乘消费开始...");
            szKafkaConsumer.subscribe(Arrays.asList(kafKaProperties.getDisOfflineTopic()));
            ConsumerRecords<String, String> records;
            while (healthCheckService.isHealth()) {
                log.info("司推乘开始拉取数据");
                records = szKafkaConsumer.poll(1000);
                if (records.count() > 0) {
                    log.info("司推乘拉取消息{}条", records.count());
                    for (ConsumerRecord<String, String> record : records) {
                        //处理业务逻辑
//                        consumerTodo(record.value());
                        start(record.value());
                    }
                }
            }
            log.info("司推乘消费结束...");
            szKafkaConsumer.close();
        });
    }
}
