package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.*;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2-2
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk039Service extends FilterSfcHandler{

    private static final String ruleNo = "039";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][SfcRuleRisk039Service][][]前置判断已通过，进入规则039判断");
        List<SfcOrderNumDTO> orderNumDTOList = context.getCancelOrderNumList().stream()
                .filter(order->order.getCancelTime().after(TimeUtil.thirtyMin(context.getSfcRiskRuleConfig().getTime039())))
                .filter(order-> StringUtils.isNotBlank(order.getOrderId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(orderNumDTOList) && orderNumDTOList.size() > context.getSfcRiskRuleConfig().getOrderNum039()){
            List<String> phoneList = orderNumDTOList.stream().map(SfcOrderNumDTO::getPassengerCellPhone).distinct().collect(Collectors.toList());
            if(phoneList.size() >= context.getSfcRiskRuleConfig().getPhoneNum039()){
                List<String> orderIds = orderNumDTOList.stream().map(SfcOrderNumDTO::getOrderId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(orderIds)){
                    distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                    log.info("[SfcRuleRisk039Service][doHandler][{}][{}]命中039规则",context.getMemberId(),context.getUnionId());
                    RiskResultDTO dto = new RiskResultDTO(405,"风控不通过039",null,null);
                    context.getUiResult().setData(dto);
                    context.getUiResult().setMsg("风控不通过");

                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
