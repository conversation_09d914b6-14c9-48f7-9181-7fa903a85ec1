package com.ly.car.risk.process.api.rsp;

import lombok.Data;

@Data
public class DriverReviewV2Rsp {

    private Integer code;
    private Boolean success;
    private String requestId;
    private String message;
    private RspData data;

    private Integer result;

    @Data
    public static class RspData{
        private RetData retData;
    }

    @Data
    public static class RetData{
        private String score_A_4;
        private String score_A_3;
        private String score_A_6;
        private String score_A_5;
        private String score_A_7;
        private String score_A_2;
        private String score_A_1;
        private String hit;
        private String score_F;
        private String score_E;
        private String score_G;
        private String score_B;
        private String score_D;
        private String score_C;
    }
}
