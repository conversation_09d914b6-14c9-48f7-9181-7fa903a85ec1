package com.ly.car.risk.process.model.riskJob;

import java.math.BigDecimal;

import lombok.Data;

/**
 * Description of SupplierCouponCheckResp
 *
 * <AUTHOR>
 * @date 2024/11/04
 * @desc
 */
@Data
public class MTDriverBalanceCheckResp {

    private String     driverId;

    private String     carNum;
    /**
     * 司机调度角色，1:司机 2:调度
     */
    private Integer    scheduleRole;

    private String     supplierCode;

    private BigDecimal balance;

    private String     startTime;

    private String     endTime;
}