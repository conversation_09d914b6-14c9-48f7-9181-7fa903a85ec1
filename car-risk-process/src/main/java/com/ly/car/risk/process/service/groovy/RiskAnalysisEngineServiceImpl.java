package com.ly.car.risk.process.service.groovy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.risk.mapper.*;
import com.ly.car.risk.process.repo.risk.mapper.entity.*;
import com.ly.car.risk.process.service.core.*;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskAnalysisEngineServiceImpl implements RiskAnalysisEngineService{

    @Resource
    private RiskEngineImpl riskEngine;
    @Resource
    private RiskSceneCache riskSceneCache;
    @Resource
    private CategoryRelationCache categoryRelationCache;
    @Resource
    private RiskStrategyCache riskStrategyCache;
    @Resource
    private RiskFieldCache riskFieldCache;
    @Resource
    private RiskRuleRelationCache riskRuleRelationCache;

    @Override
    public Map<String,List<HitStrategyDTO>> queryRisk(FilterParams params) {
        //这个map很重要，命中的东西都放在这边
        Map<String,List<HitStrategyDTO>> mapResult = new HashMap<>();
        //查询场景
        RiskScene riskScene = this.riskSceneCache.loadScene(params.getSourceId());
        if(riskScene == null){
            return null;
        }
        //验证名单是否通过
        Map<String, String> categoryMap = convertCategory(params);
        riskEngine.executeCustomer(riskScene,categoryMap,mapResult);
        log.info("[][][][]名单查询结果{}",JsonUtils.json(mapResult));
        if(mapResult.get("customer") != null ){
            //命中名单，区分下是否白名单
            if(mapResult.get("customer").get(0).getStrategyNo().equals("2")){
                mapResult.remove("customer");
                return null;
            }
        }
        //通过策略查询规则
        List<CategoryRelation> ruleCategoryRelations = new ArrayList<>();
        // 先拿场景，再根据场景拿策略
        List<RiskStrategy> strategyList = this.riskStrategyCache.loadStrategyList(params.getSourceId(),params.getProductLine());
        log.info("[][][][]根据场景获取策略:{}",JsonUtils.json(strategyList));
        // 根据策略拿规则
        for(RiskStrategy strategy : strategyList){
            ruleCategoryRelations.addAll(this.categoryRelationCache.loadRelation(strategy.getStrategyNo(),0));
        }
        //通过规则查询所有的特征指标
        List<String> ruleNoList = ruleCategoryRelations.stream().map(CategoryRelation::getRuleSceneNo).collect(Collectors.toList());
        log.info("[][][][]根据关系组获取规则:{}",JsonUtils.json(ruleNoList));
        List<RiskRuleRelation> relationList = new ArrayList<>();
        for (String ruleNo : ruleNoList) {
            // 规则的关联关系
            relationList.addAll(this.riskRuleRelationCache.loadRuleRelation(ruleNo));
        }
        log.info("[][][][]根据关系组获取规则表达式:{}",JsonUtils.json(relationList));
        //再看下当前规则是属于哪个类型的
        List<String> fieldNos = relationList.stream().map(RiskRuleRelation::getLeftField).collect(Collectors.toList());
        List<RiskField> riskFields = new ArrayList<>();
        for(String field : fieldNos){
            riskFields.add(this.riskFieldCache.loadField(field));
        }
        log.info("[][][][]根据规则获取指标:{}",JsonUtils.json(riskFields));
        Map<String,Object> valueMap = new HashMap<>();
        //这边获取规则整个策略所用到的字段指标. 这边的数据，就是数据中心清洗的指标数据了
        riskEngine.executeAbstraction(riskScene, categoryMap,valueMap,riskFields);
        //这边开始匹配规则
        riskEngine.executeRule(strategyList, valueMap,mapResult,riskFields);
        //执行特殊规则
        riskEngine.executeSpecialRule();
        log.info("[][][][]规则引擎执行结果:{}",JsonUtils.json(mapResult));
        //输出结果
        return mapResult;
    }

    public Map<String,String> convertCategory(FilterParams params){
        Map<String,String> map = new HashMap<>();
        if(params.getMainScene() == 2 && params.getChildScene() == 1){
            map.put("driverCardNo",params.getDriverCardNo());
        } else {
            map.put("userPhone",params.getUserPhone());
            map.put("passengerCellphone",params.getPassengerCellphone());
            map.put("deviceId",params.getDeviceId());
            map.put("memberId",params.getMemberId());
            map.put("driverCardNo",params.getDriverCardNo());
            map.put("payAccount",params.getPayAccount());
            map.put("unionId",params.getUnionId());
            map.put("driverId",params.getDriverId());
        }
        if(params.getDriverList() != null){
            map.put("driverList", StringUtils.join(params.getDriverList(),","));
        }
        return map;
    }

    public static void main(String[] args) {
        String strList = "[CategoryRelation(id=18, strategyNo=sfc_driver_accept, type=1, ruleSceneNo=IoITLz70HvAewf4tuPheV5G6kHqPKDbE, ruleSceneName=司机接单, createTime=Thu Jul 13 13:42:13 GMT+08:00 2023)]";
        List<CategoryRelation> list = JSONArray.parseArray(strList, CategoryRelation.class);
        System.out.println(JsonUtils.json(list));
    }

}
