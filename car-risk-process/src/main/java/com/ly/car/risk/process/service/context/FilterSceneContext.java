package com.ly.car.risk.process.service.context;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.SpecialCarRuleConfig;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class FilterSceneContext {

    private String orderId;
    private Integer mainScene;
    private Integer childScene;
    private List<RiskCustomerManage> riskCustomerManageList;
    private UiResult uiResult = UiResult.ok();
    private String payAccount;
    private String memberId;
    private String unionId;
    private Integer isNewUser;
    private BigDecimal esAmount;
    private Map<String, String> rateMap;
    private SpecialCarRuleConfig specialCarRuleConfig;

    public FilterSceneContext(){
        UiResult result = this.uiResult;
        result.setData(new RiskResultDTO());
    }
}
