package com.ly.car.risk.process.constants;

import java.util.Arrays;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ProductLineEnum {
    
    SFC("SFC", "顺风车"),
    YNC("YNC", "网约车"),
    BUS("BUS", "汽车票"),
    SJ("JSJ", "接送机"),
    GJC("GJC", "国际车"),
    ZY("ZY", "自营"),
    FLIGHT("FLIGHT", "机票"),
    TRAIN("TRAIN", "火车票"),
    MT("MT", "萌艇"),
    YCX("YCX", "上门接送"),
    XCAR("XCAR", "上门接送"),
    LINE("LINE", "巴士快线"),
    CZC("CZC", "出租车"),
    WB("WB", "外部"),
    MKT("MKT", "营销"),
    FT("FT", "飞艇"),
    ;
    
    private final String code;
    private final String desc;
    
    public static ProductLineEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return YNC;
        }
        
        return Arrays.stream(ProductLineEnum.values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }
}
