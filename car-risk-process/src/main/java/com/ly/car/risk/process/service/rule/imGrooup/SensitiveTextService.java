package com.ly.car.risk.process.service.rule.imGrooup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.AutoCallApi;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.constants.DriverWarnNotifyEnum;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveWordsMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord;
import com.ly.car.risk.process.service.AutoCallRecordService;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.ability.SendDriverWarnService;
import com.ly.car.risk.process.service.dto.DriverWarningContentDTO;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.dto.SensitiveWordsSimple;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.SensitiveWordUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SensitiveTextService extends ImFilterHandler {

    private final String ruleNo = "sf001";
    @Resource
    private SensitiveWordsMapper sensitiveWordsMapper;
    @Resource
    private SensitiveRecordMapper sensitiveRecordMapper;
    @Resource
    private ExecutorService executorService;
    @Resource
    private AutoCallApi autoCallApi;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private LabelClient labelClient;
    @Resource
    private AutoCallRecordService autoCallRecordService;
    @Resource(name = "binlogProducer")
    private MqRiskProducer mqRiskProducer;
    @Resource(name = "hitchRiskProducer")
    private MqRiskProducer hcRiskProducer;
    @Resource(name = "mtRiskProducer")
    private MqRiskProducer mtRiskProducer;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private AutoCallService autoCallService;
    @Resource
    private SendDriverWarnService sendDriverWarnService;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private OrderClient orderClient;

    @Override
    public void doHandler(ImFilterContext imContext) {
        log.info("[][][][]敏感词过滤{}", JsonUtils.json(imContext));
        if (imContext.getText().contains("希望") || imContext.getText().contains("方便准时") || imContext.getText().contains("尽早送达")) {
            log.info("[][][][]自定义词语不过滤");
            return;
        }
        Map<String, SensitiveWordsSimple> wordsNumMap = SensitiveWordUtil.matchWords(imContext.getText());
        if (wordsNumMap.isEmpty()) {
            //未命中直接返回
            return;
        }
        Map<String, String> resultMap = new HashMap<>();
        //封装系统调用返回值
        convertResult(imContext, wordsNumMap, resultMap);

        //命中敏感词后不同场景的处理
        executorService.execute(() -> {
            //获取配置是否开始通知
            //命中记录
            Set<Integer> wordType = new HashSet<>();
            List<SensitiveRecord> sensitiveRecords = new ArrayList<>();
            for (Map.Entry<String, SensitiveWordsSimple> entry : wordsNumMap.entrySet()) {
                SensitiveRecord sensitiveRecord = new SensitiveRecord();
                sensitiveRecord.setSensitiveContext(entry.getKey());
                sensitiveRecord.setType(0);
                sensitiveRecord.setOrderId(imContext.getOrderId());
                sensitiveRecord.setDriverCardNo(imContext.getDriverCardNo());
                sensitiveRecord.setUnionId(imContext.getUnionId());
                sensitiveRecord.setMemberId(imContext.getMemberId());
                sensitiveRecord.setMessageId("");
                sensitiveRecord.setCreateTime(new Date());
                sensitiveRecord.setOriginalText(imContext.getText());
                sensitiveRecord.setWordType(entry.getValue().getWordType());
                sensitiveRecord.setType(imContext.getSource());
                sensitiveRecords.add(sensitiveRecord);
                this.sensitiveWordsMapper.incrHitNum(entry.getValue().getId());
                this.sensitiveRecordMapper.insert(sensitiveRecord);
                wordType.add(entry.getValue().getWordType());
            }
            boolean ivrCallFlag = false;
            //说明命中了线下交易，所以需要外呼
            Map<String, Boolean> configMap = queryConfig();
            if (configMap.get("onOffRisk") != null && !configMap.get("onOffRisk")) {
                //开关不开
                log.info("[][][][]敏感词过滤查看敏感词类型{}", JsonUtils.json(wordType));
                return;
            }
            log.info("[][][][]敏感词过滤查看敏感词类型{}", JsonUtils.json(wordType));
            // 这里startWith YC是老逻辑,正好新订单都是YC开头
            boolean isNewFlag = false;
            if (StringUtils.isNotBlank(imContext.getOrderId()) && (imContext.getOrderId().startsWith("SFC") || imContext.getOrderId().startsWith("YC"))) {
                String supplierCode = "";
                Integer orderStatus = null;
                String supplierOrderId = "";
                String memberId = "";
                String unionId = "";
                Date useTime = null;
                String passengerCellphone = "";
                String startAddress = "";
                String endAddress = "";
                if (OrderUtils.isNewOrder(imContext.getOrderId())) {
                    isNewFlag = true;
                    // 新订单流程
                    CarOrderDetail sfcOrder = carOrderService.queryOrderDetail(imContext.getOrderId());
                    if(null == sfcOrder){
                        return;
                    }
                    supplierCode = sfcOrder.getCarInfo().getSupplierCode();
                    useTime = sfcOrder.getBaseInfo().getGmtUsage();
                    passengerCellphone = sfcOrder.getPassengerInfo().getPassengerCellPhone();
                    startAddress = sfcOrder.getOrderTrip().getDepartureAddress();
                    endAddress = sfcOrder.getOrderTrip().getArrivalAddress();
                    orderStatus = sfcOrder.getOrderState();
                    supplierOrderId = sfcOrder.getSupplierOrderId();
                    memberId = sfcOrder.getMemberId();
                    unionId = sfcOrder.getUnionId();

                } else {
                    // 老流程走sfcOrder
                    SfcOrder sfcOrder = null;
                    CarOrderDetail newSfcOrder = null;

                    if (imContext.getOrderId().startsWith("SFC")) {
                        sfcOrder = this.sfcOrderMapper.queryByOrderId(imContext.getOrderId());
                    } else {
                        // YC的是萌艇的，但是萌艇新订单，从表里是查不到的，得走供应链接口+交易接口
                        sfcOrder = this.sfcOrderMapper.queryBySupplierOrderId(imContext.getOrderId());
                        // 如果查不到，则说明是新订单
                        if(null == sfcOrder){
                            QueryDriverInfoResponse newYcOrder = orderClient.queryDriverInfo(imContext.getOrderId());
                            if(null != newYcOrder){
                                newSfcOrder = carOrderService.queryOrderDetail(newYcOrder.getOrderSerialNo());
                                isNewFlag = true;
                            }
                        }
                    }

                    if (null == sfcOrder && null == newSfcOrder) {
                        return;
                    }

                    if(sfcOrder != null){
                        supplierCode = sfcOrder.getSupplierCode();
                        orderStatus = sfcOrder.getStatus();
                        supplierOrderId = sfcOrder.getSupplierOrderId();
                        memberId = String.valueOf(sfcOrder.getMemberId());
                        unionId = sfcOrder.getUnionId();
                        useTime = sfcOrder.getUseTime();
                        passengerCellphone = sfcOrder.getPassengerCellphone();
                        OrderAddress orderAddress = orderAddressMapper.findByOrderId(sfcOrder.getOrderId());
                        startAddress = orderAddress.getStartAddress();
                        endAddress = orderAddress.getEndAddress();

                    }else{

                        supplierCode = newSfcOrder.getCarInfo().getSupplierCode();
                        useTime = newSfcOrder.getBaseInfo().getGmtUsage();
                        passengerCellphone = newSfcOrder.getPassengerInfo().getPassengerCellPhone();
                        startAddress = newSfcOrder.getOrderTrip().getDepartureAddress();
                        endAddress = newSfcOrder.getOrderTrip().getArrivalAddress();
                        orderStatus = newSfcOrder.getOrderState();
                        supplierOrderId = newSfcOrder.getSupplierOrderId();
                        memberId = newSfcOrder.getMemberId();
                        unionId = newSfcOrder.getUnionId();
                    }

                }
                if (wordType.contains(5)) {
                    log.info("[][][][]敏感词过滤线下交易发送外呼{}", imContext.getOrderId());
                    if (isNewFlag && Objects.equals(orderStatus, OrderState.CANCELED.getCode())) {
                        return;
                    }else if(!isNewFlag && orderStatus == 1000){
                        return;
                    }
                    autoCallService.sendCall(imContext.getOrderId(), "AO_117_1692337105096", passengerCellphone, null);
                    ivrCallFlag = true;
                    //如果是卡罗拉的话还要发站内信
                    //对卡罗拉进行处理
                    if (supplierCode.startsWith("zhizhuxia") || imContext.getOrderId().startsWith("YC")) {
                        sendDriverWarnService.sendDriverPush("AO_117_1692337105096", supplierOrderId, 5);
                    }
                }
                //如果存在不文明用语
                if (wordType.contains(1)) {
                    LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(null,memberId, unionId);
                    if (detailRsp != null && detailRsp.getGender() == 2) {
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("@startstation", startAddress);
                        paramMap.put("@endstation", endAddress);
                        paramMap.put("@trainno", DateUtil.date2String(useTime));
                        autoCallService.sendCall(imContext.getOrderId(), "AO_117_1692100840781", passengerCellphone, paramMap);
                        ivrCallFlag = true;
                        if (supplierCode.startsWith("zhizhuxia") || imContext.getOrderId().startsWith("YC")) {
                            sendDriverWarnService.sendDriverPush("AO_117_1692100840781", supplierOrderId, 1);
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(imContext.getOrderId()) && imContext.getOrderId().startsWith("PHC")) {
                //对卡罗拉进行处理
                DriverWarningContentDTO driverWarningContentDTO = new DriverWarningContentDTO();
                driverWarningContentDTO.setOrderId(imContext.getOrderId());
                driverWarningContentDTO.setNotifyContent(DriverWarnNotifyEnum.sjtx_im_hc.getVoice());
                hcRiskProducer.send(MqTagEnum.car_risk_driver_inner_content_notify, JsonUtils.json(driverWarningContentDTO), 0);
                redissonClient.getBucket("im:notify:repeat:" + imContext.getOrderId()).set("1", 1, TimeUnit.HOURS);
            }

            if(ivrCallFlag){
                for (SensitiveRecord sensitiveRecord : sensitiveRecords) {
                    sensitiveRecord.setIvrCallFlag(ivrCallFlag ? 1 : 0);
                    sensitiveRecordMapper.updateById(sensitiveRecord);
                }
            }

        });
    }

    public void convertResult(ImFilterContext context, Map<String, SensitiveWordsSimple> wordsNumMap, Map<String, String> resultMap) {
        //需要给页面端的就封装返回值
        RiskResultNewDTO dto = new RiskResultNewDTO();
        //到这边说明是命中的,获取所有关键字
        dto.setCode(1);
        dto.setMessage("命中关键字");

        Map<String, Boolean> configMap = queryConfig();
        for (Map.Entry<String, SensitiveWordsSimple> entry : wordsNumMap.entrySet()) {
            if (entry.getValue().getWordType() == 5) {
                resultMap.put("aq006", entry.getKey());
            } else if (entry.getValue().getWordType() == 7) {
                resultMap.put("aq007", entry.getKey());
            } else {
                resultMap.put("normal", entry.getKey());
            }
        }
        if (resultMap.get("aq006") != null && configMap.get("aq006") != null
                && !configMap.get("aq006")) {
            resultMap.put("normal", resultMap.get("aq006"));
            resultMap.remove("aq006");
        }
        if (resultMap.get("aq007") != null && configMap.get("aq007") != null
                && !configMap.get("aq007")) {
            resultMap.put("normal", resultMap.get("aq007"));
            resultMap.remove("aq007");
        }
        dto.setObj(resultMap);
        context.getUiResult().setData(dto);
    }

    private Map<String, Boolean> queryConfig() {
        try {
            String configJson = ConfigCenterClient.get("warn_rule_config");
            Map<String, Boolean> config = JSONObject.parseObject(configJson, Map.class);
            log.info("[][][][]获取预警规则:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取预警规则配置错误:", e);
        }
        return null;
    }

    public static void main(String[] args) {
        //调用下模型
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("sentence", "电话联系158，可以吗");

        try {
            String post = OkHttpClientUtil.getInstance().post("http://car-riskgategory.17usoft.com/riskManage/sensitiveWordsCheck", JsonUtils.json(paramMap), null, 3L);
            log.info("[][][][]调用模型结果{}", post);
            if (post != null) {
//                sensitiveRecord.setModelResult(Integer.valueOf(post));
            }
        } catch (Exception e) {
            log.error("[][][][]调用模型报错", e);
        }
    }
}
