package com.ly.car.risk.process.constants;

import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_bus_create_order;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_coupon_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_credit_auth_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_receive_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_send_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_ync_receive_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.black_list;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum StrategySceneEnum {

    DRIVER_REGISTER("1-1", "司机注册"),

    DRIVER_LOGIN("2-1", "司机登录"),
    DISPATCHER_LOGIN("2-2", "调度登录"),
    MERCHANT_LOGIN("2-3", "商户登录"),

    ACT_COUPON_LQ("3-3", "活动领券"),

    USER_DISPATCHING_ORDER("4-1", "用户派单"),
    DRIVER_LD("4-2", "司机录单"),

    DRIVER_ACCEPT_ORDER("5-1", "司机接单"),
    DISPATCHER_ACCEPT_ORDER("5-2", "调度接单"),

    IM_COMMUNICATE("6-1", "IM沟通"),
    
    FINISH_ORDER("8-1", "完单"),
    
    RIGHTS_ORDER("9-1", "权益活动"),

    DRIVER_WITHDRAWAL("11-1", "司机提现"),

    BANKCARD_CHANGE("12-3", "银行卡变更"),
    PHONE_CHANGE("12-5", "手机号变更"),

    DRIVER_AUTHENTICATION("14-1", "司机认证"),

    DRIVER_PUBLISH("15-1", "司机发布"),

    USER_CREATE_ORDER("16-1", "用户创单"),

    DRIVER_PWD_RESET("18-1", "司机重置密码"),
    BANKCARD_UNBIND("18-2", "司机解绑卡"),

    CANCEL_REMINDER("19-1","取消-安全提示"),

    USER_CREDIT_AUTH("20-1", "用户信用授权"),
    ;


    private String scene;

    private String desc;

    private static final Map<String, StrategySceneEnum> MAP = new HashMap<>();

    static {
        for (StrategySceneEnum value : StrategySceneEnum.values()) {
            MAP.put(value.scene, value);
        }
    }


    StrategySceneEnum(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    public static StrategySceneEnum of(String sceneValue) {
        if (StringUtils.isBlank(sceneValue)) {
            return null;
        }
        return MAP.get(sceneValue);
    }

    public static List<Integer> sceneRiskType(String scene, String productLine) {
        List<Integer> basicRiskTypes = Stream.of(black_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
        StrategySceneEnum sceneEnum = of(scene);
        if (null == sceneEnum) {
            return basicRiskTypes;
        }
        switch (sceneEnum) {
            case USER_CREATE_ORDER:
                // 对特殊产品线单独设置过滤参数
                if (Objects.equals(productLine, ProductLineEnum.BUS.getCode())
                        || Objects.equals(productLine, ProductLineEnum.LINE.getCode())) {
                    return Stream.of(black_list, ban_bus_create_order).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
                }
                return Stream.of(black_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case USER_DISPATCHING_ORDER:
                return Stream.of(black_list, ban_send_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case ACT_COUPON_LQ:
                return Stream.of(black_list, ban_coupon_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_ACCEPT_ORDER:
                if (Objects.equals(productLine, ProductLineEnum.YNC.getCode())
                    || Objects.equals(productLine, ProductLineEnum.FT.getCode())
                    || Objects.equals(productLine, ProductLineEnum.YCX.getCode())
                    || Objects.equals(productLine, ProductLineEnum.XCAR.getCode())) {
                    return Stream.of(black_list, ban_receive_list, ban_ync_receive_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
                } else {
                    return Stream.of(black_list, ban_receive_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
                }
            case USER_CREDIT_AUTH:
                return Stream.of(black_list, ban_credit_auth_list).map(RiskCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            default:
                return basicRiskTypes;
        }
    }


    public static List<Integer> hcSceneBlackType(String scene, String productLine) {
        List<Integer> basicRiskTypes = Stream.of(HcCustomerRiskTypeEnum.BLACK_LIST).map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
        StrategySceneEnum sceneEnum = of(scene);
        if (null == sceneEnum) {
            return basicRiskTypes;
        }
        switch (sceneEnum) {
            case IM_COMMUNICATE:
            case BANKCARD_CHANGE:
            case DRIVER_PWD_RESET:
            case BANKCARD_UNBIND:
                return new ArrayList<>();
            case DRIVER_REGISTER:
                return Stream.of(HcCustomerRiskTypeEnum.BLACK_LIST,HcCustomerRiskTypeEnum.DISABLE_REGISTER_BLACK_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_LOGIN:
            case DISPATCHER_LOGIN:
            case MERCHANT_LOGIN:
                return Stream.of(HcCustomerRiskTypeEnum.BLACK_LIST, HcCustomerRiskTypeEnum.DISABLE_LOGIN_BLACK_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_ACCEPT_ORDER:
            case DISPATCHER_ACCEPT_ORDER:
                return Stream.of(HcCustomerRiskTypeEnum.BLACK_LIST, HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_BLACK_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_WITHDRAWAL:
                return Stream.of(HcCustomerRiskTypeEnum.BLACK_LIST, HcCustomerRiskTypeEnum.DISABLE_WITHDRAW_BLACK_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_PUBLISH:
                return Stream.of(HcCustomerRiskTypeEnum.BLACK_LIST, HcCustomerRiskTypeEnum.DISABLE_PUBLISH_BLACK_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            default:
                return basicRiskTypes;
        }
    }

    public static List<Integer> hcSceneWhiteType(String scene, String productLine) {
        List<Integer> basicRiskTypes = Stream.of(HcCustomerRiskTypeEnum.WHITE_LIST).map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
        StrategySceneEnum sceneEnum = of(scene);
        if (null == sceneEnum) {
            return basicRiskTypes;
        }
        switch (sceneEnum) {
            case DRIVER_REGISTER:
                return Stream.of(HcCustomerRiskTypeEnum.WHITE_LIST,HcCustomerRiskTypeEnum.DISABLE_REGISTER_WHITE_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_LOGIN:
            case DISPATCHER_LOGIN:
            case MERCHANT_LOGIN:
                return Stream.of(HcCustomerRiskTypeEnum.WHITE_LIST, HcCustomerRiskTypeEnum.DISABLE_LOGIN_WHITE_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_ACCEPT_ORDER:
            case DISPATCHER_ACCEPT_ORDER:
                
                return Stream.of(HcCustomerRiskTypeEnum.WHITE_LIST, HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_WHITE_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_WITHDRAWAL:
                return Stream.of(HcCustomerRiskTypeEnum.WHITE_LIST, HcCustomerRiskTypeEnum.DISABLE_WITHDRAW_WHITE_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            case DRIVER_PUBLISH:
                return Stream.of(HcCustomerRiskTypeEnum.WHITE_LIST, HcCustomerRiskTypeEnum.DISABLE_PUBLISH_WHITE_LIST)
                        .map(HcCustomerRiskTypeEnum::getCode).collect(Collectors.toList());
            default:
                return basicRiskTypes;
        }
    }
}
