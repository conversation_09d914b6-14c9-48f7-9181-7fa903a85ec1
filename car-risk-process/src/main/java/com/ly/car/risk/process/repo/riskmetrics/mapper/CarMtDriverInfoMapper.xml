<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverInfoMapper">

    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="full_supplier_code" jdbcType="VARCHAR" property="fullSupplierCode"/>
        <result column="schedule_role" jdbcType="INTEGER" property="scheduleRole"/>
        <result column="available_amount" jdbcType="DECIMAL" property="availableAmount"/>
        <result column="gmt_settled" jdbcType="TIMESTAMP" property="gmtSettled"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate"/>
    </resultMap>

    <resultMap id="driverBalanceCheckResp" type="com.ly.car.risk.process.model.riskJob.MTDriverBalanceCheckResp">
        <result column="driver_id" jdbcType="VARCHAR" property="driverId"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="schedule_role" jdbcType="INTEGER" property="scheduleRole"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
    </resultMap>


    <sql id="Base_Column_List">
        id
        , driver_id, car_num, supplier_code, full_supplier_code, schedule_role, available_amount,
    gmt_settled, gmt_create, gmt_update
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from car_mt_driver_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from car_mt_driver_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverInfo" useGeneratedKeys="true">
        insert into car_mt_driver_info (driver_id, car_num, supplier_code,
                                        full_supplier_code, schedule_role, available_amount,
                                        gmt_settled, gmt_create, gmt_update)
        values (#{driverId,jdbcType=BIGINT}, #{carNum,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR},
                #{fullSupplierCode,jdbcType=VARCHAR}, #{scheduleRole,jdbcType=INTEGER},
                #{availableAmount,jdbcType=DECIMAL},
                #{gmtSettled,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverInfo" useGeneratedKeys="true">
        insert into car_mt_driver_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="driverId != null">
                driver_id,
            </if>
            <if test="carNum != null">
                car_num,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
            <if test="fullSupplierCode != null">
                full_supplier_code,
            </if>
            <if test="scheduleRole != null">
                schedule_role,
            </if>
            <if test="availableAmount != null">
                available_amount,
            </if>
            <if test="gmtSettled != null">
                gmt_settled,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="driverId != null">
                #{driverId,jdbcType=BIGINT},
            </if>
            <if test="carNum != null">
                #{carNum,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="fullSupplierCode != null">
                #{fullSupplierCode,jdbcType=VARCHAR},
            </if>
            <if test="scheduleRole != null">
                #{scheduleRole,jdbcType=INTEGER},
            </if>
            <if test="availableAmount != null">
                #{availableAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtSettled != null">
                #{gmtSettled,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverInfo">
        update car_mt_driver_info
        <set>
            <if test="driverId != null">
                driver_id = #{driverId,jdbcType=BIGINT},
            </if>
            <if test="carNum != null">
                car_num = #{carNum,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="fullSupplierCode != null">
                full_supplier_code = #{fullSupplierCode,jdbcType=VARCHAR},
            </if>
            <if test="scheduleRole != null">
                schedule_role = #{scheduleRole,jdbcType=INTEGER},
            </if>
            <if test="availableAmount != null">
                available_amount = #{availableAmount,jdbcType=DECIMAL},
            </if>
            <if test="gmtSettled != null">
                gmt_settled = #{gmtSettled,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ly.car.risk.process.repo.riskmetrics.entity.CarMtDriverInfo">
        update car_mt_driver_info
        set driver_id          = #{driverId,jdbcType=BIGINT},
            car_num            = #{carNum,jdbcType=VARCHAR},
            supplier_code      = #{supplierCode,jdbcType=VARCHAR},
            full_supplier_code = #{fullSupplierCode,jdbcType=VARCHAR},
            schedule_role      = #{scheduleRole,jdbcType=INTEGER},
            available_amount   = #{availableAmount,jdbcType=DECIMAL},
            gmt_settled        = #{gmtSettled,jdbcType=TIMESTAMP},
            gmt_create         = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_update         = #{gmtUpdate,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="checkDriverBalance" resultMap="driverBalanceCheckResp">
        select driver_id, car_num, supplier_code, schedule_role, available_amount as balance
        from car_mt_driver_info
        where available_amount &lt; #{checkThreshold}
    </select>

    <resultMap id="strategyResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail">
        <id property="id" column="id"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="memberId" column="member_id"/>
        <result property="unionId" column="union_id"/>
        <result property="productLine" column="product_line"/>
        <result property="orderType" column="order_type"/>
        <result property="orderState" column="order_state"/>
        <result property="amount" column="amount"/>
        <result property="payState" column="pay_state"/>
        <result property="orderChannel" column="order_channel"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtPaid" column="gmt_paid"/>
        <result property="gmtCanceled" column="gmt_canceled"/>
        <result property="gmtUsage" column="gmt_usage"/>
        <result property="gmtDeparture" column="gmt_departure"/>
        <result property="gmtArrive" column="gmt_arrive"/>
        <result property="gmtTripFinished" column="gmt_trip_finished"/>
        <result property="cancelType" column="cancel_type"/>
        <result property="cancelReason" column="cancel_reason"/>
        <result property="payCategory" column="pay_category"/>
        <result property="departureCityCode" column="departure_city_code"/>
        <result property="arrivalCityCode" column="arrival_city_code"/>
        <result property="departureAddress" column="departure_address"/>
        <result property="arrivalAddress" column="arrival_address"/>
        <result property="passengerPhone" column="passenger_phone"/>
        <result property="estimateDistance" column="estimate_distance"/>
        <result property="estimateTime" column="estimate_time"/>
        <result property="realDistance" column="real_distance"/>
        <result property="realTime" column="real_time"/>
        <result property="carNum" column="car_num"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierPrice" column="supplier_price"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="washExt" column="wash_ext"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="distributionFlag" column="distribution_flag"/>
        <result property="bookAmount" column="book_amount"/>
        <result property="surcharge" column="surcharge"/>
        <result property="supplementaryAmount" column="supplementary_amount"/>
        <result property="rightsOrderFlag" column="rights_order_flag"/>
    </resultMap>

    <select id="driver24HourRegisterCountOnSameDevice" resultMap="strategyResultMap">
        SELECT distinct di.driver_id as id
        FROM `car_mt_driver_info` di
                 join `car_mt_driver_account_record` dar on di.id = dar.driver_id
        where di.gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY)
          and dar.status = 0
          and dar.device_id = #{deviceId}
    </select>

    <select id="driver05RegisterSameDevice" resultMap="strategyResultMap">
        SELECT distinct di.driver_id as id
        FROM `car_mt_driver_info` di
                 join `car_mt_driver_account_record` dar on di.id = dar.driver_id
        where di.gmt_create >= DATE_ADD(CURDATE(), INTERVAL 0 hour)
          and DATE_ADD(CURDATE(), INTERVAL 5 hour) >= di.gmt_create
          and dar.status = 0
          and dar.device_id = #{deviceId}
    </select>

</mapper>