package com.ly.car.risk.process.turboMQ;

public enum MqTopicEnum {

    CAR_RISK_TOPIC_HITCH("car_risk_topic_hitch",
            "",
            "car_risk_topic_hitch_default"),

    CAR_RISK_TOPIC_WORK_ORDER("car_risk_topic_work_order",
            "car_risk_topic_consumer_work_order",
            "car_risk_topic_producer_work_order"),

    CAR_RISK_TOPIC_BINLOG("car_risk_topic_binlog",
            "car_risk_topic_consumer_binlog",
            "car_risk_topic_producer_binlog"),

   /**这个是存储预估路径用的*/
    CAR_RISK_TOPIC_SAFE_WARNING("car_risk_topic_binlog",
            "car_risk_topic_consumer_safe_warning",
            "car_risk_topic_producer_safe_warning"),

    /**flink的topic*/
    CAR_FLINK_TOPIC_RISK("car_flink_topic_risk",
            "",
            "car_flink_topic_producer_risk"),

    /**萌艇的topic*/
    CAR_RISK_TOPIC_MT("car_risk_topic_mt",
            "",
            "car_risk_topic_producer_mt"),

    /*公用的topic*/
    CAR_RISK_TOPIC_COMMON("car_risk_topic_common",
                              "",
                              "car_risk_topic_producer_common"),

    /**
     * 顺风车事件
     * */
    CAR_SFC_ORDER_STATUS_CHANGE("yc_topic_sfc_order_status_change",
            "yc_topic_sfc_order_status_change_risk"
            ,""),

    /**
     * 安全处理
     * */
    CAR_SFC_SECURITY_PROCESS("car_risk_topic_security_process",
            "car_risk_topic_consumer_security_process",
            "car_risk_topic_producer_security_process"),


    /**慧瞳的topic*/
    CAR_RISK_TOPIC_HT("car_risk_topic_ht",
                              "",
                              "car_risk_topic_producer_ht"),

    /**
     * 和业务线转文本的topic
     * */
    CAR_RISK_TOPIC_TEXT("car_risk_topic_text",
            "",
            "car_risk_topic_text_producer"),


    /**
     * 风险订单同步
     */
    CAR_RISK_TOPIC_ORDER_SYNC("car_risk_topic_order_sync",
            "",
            "car_risk_producer_order_sync")

    ;


    public String topic;
    public String consumer;
    public String producer;

    MqTopicEnum(String topic,String consumer,String producer){
        this.topic = topic;
        this.consumer = consumer;
        this.producer = producer;
    }
}
