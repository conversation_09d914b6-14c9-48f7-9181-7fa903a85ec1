<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper">

    <select id="querySfcRisk" resultType="com.ly.car.risk.process.repo.data.SfcOrderRiskData">
        SELECT
            so.order_id,
            so.finish_time,
            so.member_id,
            so.union_id,
            oa.start_lat,
            oa.start_lng,
            oa.end_lat,
            oa.end_lng,
            oa.estimate_kilo
        FROM
            sfc_order so
                left join order_address oa on so.order_id = oa.order_id
        where
            so.finish_time BETWEEN #{startTime}
                AND #{endTime}
          AND so.status = 300
    </select>

    <select id="querySfcRiskLimit" resultType="com.ly.car.risk.process.repo.data.SfcRiskLimitData">
        SELECT
            so.order_id,
            so.created,
            so.status,
            soe.cancel_reason
            FROM
                sfc_order so left join sfc_order_ext soe on so.order_id = soe.order_id
        where
            so.created BETWEEN #{startTime}
                AND #{endTime}
            <if test="unionId != null and unionId != '' ">
                and so.union_id = #{unionId}
            </if>
            <if test="memberId != null and memberId !='' ">
                and so.member_id = #{memberId}
            </if>

    </select>

</mapper>
