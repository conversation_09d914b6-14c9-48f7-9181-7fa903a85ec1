package com.ly.car.risk.process.component;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 用户维度滑动时间窗口
 * */

@Component
@Slf4j
public class UserSlidingWindowCounter {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    public List<OrderRiskContext> getUserWindow(String key, long startMs){
        key = RedisKeyConstants.USER_SLIDING_WINDOW + key;
        //当前时间
        long currentTime = System.currentTimeMillis();
        //窗口开始时间
//        long windowStartMs = currentTime - windowSecond * 1000L;
        //按score统计key中符合的订单

        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        //优化下节省内存
        long initMs = TimeUtil.initMs();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);

        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, currentTime, true);
        List<OrderRiskContext> objList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            OrderRiskContext context = JSONObject.parseObject(obj.getValue().toString(),OrderRiskContext.class);
            objList.add(context);
        }
        return objList;
    }

    public static void main(String[] args) {
        String str = "{\"actualDuration\":0,\"actualKilo\":0.00,\"driverCardNo\":\"粤ER7D48\",\"endLat\":23.3755380,\"endLng\":110.9146060,\"estimateDuration\":0,\"estimateKilo\":241.50,\"finishTime\":\"2022-11-23 15:43:49\",\"memberId\":\"1401837780\",\"startLat\":23.2743759,\"startLng\":112.8135681,\"totalAmount\":159.00,\"unionId\":\"ohmdTtyF3Dt-9r5wzpT_7ZFpp_3c\"}";
        OrderRiskContext context = JSONObject.parseObject(str.toString(),OrderRiskContext.class);
        System.out.println(context);
    }

}
