package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
public class RiskScene extends Model<RiskScene> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String name;
    private String guid;
    private Integer isChild;
    private String parentScene;
    private String childScene;
    private Integer linkCategory;
    private String linkCategoryNo;
    private Date createTime;
    private Date updateTime;
    private String createUser;
    private String updateUser;
    private Integer isDeleted;
    private String productLine;
    private String filterCustomerType;
    private String sceneNo;
}
