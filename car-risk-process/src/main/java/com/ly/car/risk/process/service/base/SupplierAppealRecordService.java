package com.ly.car.risk.process.service.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.SupplierAppealRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SupplierAppealRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SupplierAppealRecordService {

    @Resource
    private SupplierAppealRecordMapper supplierAppealRecordMapper;


    public List<SupplierAppealRecord> queryByCreateTime(Date startDate,Date endDate){
        List<SupplierAppealRecord> supplierAppealRecords = this.supplierAppealRecordMapper.selectList(new QueryWrapper<SupplierAppealRecord>()
                .between("create_time",startDate,endDate)
                .eq("audit_status",0)
        );
        return supplierAppealRecords;
    }

}
