<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail">
        <id property="id" column="id"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="memberId" column="member_id"/>
        <result property="unionId" column="union_id"/>
        <result property="productLine" column="product_line"/>
        <result property="orderType" column="order_type"/>
        <result property="orderState" column="order_state"/>
        <result property="amount" column="amount"/>
        <result property="payState" column="pay_state"/>
        <result property="orderChannel" column="order_channel"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtPaid" column="gmt_paid"/>
        <result property="gmtCanceled" column="gmt_canceled"/>
        <result property="gmtUsage" column="gmt_usage"/>
        <result property="gmtDeparture" column="gmt_departure"/>
        <result property="gmtArrive" column="gmt_arrive"/>
        <result property="gmtTripFinished" column="gmt_trip_finished"/>
        <result property="cancelType" column="cancel_type"/>
        <result property="cancelReason" column="cancel_reason"/>
        <result property="payCategory" column="pay_category"/>
        <result property="departureCityCode" column="departure_city_code"/>
        <result property="arrivalCityCode" column="arrival_city_code"/>
        <result property="departureAddress" column="departure_address"/>
        <result property="arrivalAddress" column="arrival_address"/>
        <result property="passengerPhone" column="passenger_phone"/>
        <result property="estimateDistance" column="estimate_distance"/>
        <result property="estimateTime" column="estimate_time"/>
        <result property="realDistance" column="real_distance"/>
        <result property="realTime" column="real_time"/>
        <result property="carNum" column="car_num"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierPrice" column="supplier_price"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="washExt" column="wash_ext"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="distributionFlag" column="distribution_flag"/>
        <result property="bookAmount" column="book_amount"/>
        <result property="surcharge" column="surcharge"/>
        <result property="supplementaryAmount" column="supplementary_amount"/>
        <result property="rightsOrderFlag" column="rights_order_flag"/>
        <result property="deviceId" column="device_id"/>
        <result property="bookUserAmount" column="book_user_amount"/>
        <result property="depLat" column="dep_lat"/>
        <result property="depLon" column="dep_lon"/>
        <result property="arrLat" column="arr_lat"/>
        <result property="arrLon" column="arr_lon"/>
    </resultMap>

    <resultMap id="BaseOfflineResultMap" type="com.ly.car.risk.process.repo.riskmetrics.entity.CarOfflineRiskOrderDetail">
        <result property="orderSerialNo" column="order_serial_no"/>
    </resultMap>

    <sql id="allColumn">
        id
        ,order_serial_no,member_id,union_id,product_line,order_type,order_state,amount,pay_state,order_channel,contact_phone,gmt_create,gmt_paid,
            gmt_canceled,gmt_usage,gmt_departure,gmt_arrive,gmt_trip_finished,cancel_type,cancel_reason,pay_category,departure_city_code,arrival_city_code,
            departure_address,arrival_address,passenger_phone,estimate_distance,estimate_time,real_distance,real_time,car_num,supplier_code,supplier_price,
            refund_money,wash_ext,create_time,update_time,distribution_flag,book_amount,surcharge,supplementary_amount,rights_order_flag,device_id,
            dep_lat,dep_lon,arr_lat,arr_lon
    </sql>


    <select id="executeSql" parameterType="map" resultType="java.util.Map">
        ${sql}
    </select>

    <select id="queryUser1HourFinishOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and order_state in (7,8)
        and gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 HOUR )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryUser24HourFinishOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and order_state in (7,8)
        and gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryCar1HourFinishOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where car_num = #{carNum}
        and order_state in (7,8)
        and gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryCar24HourFinishOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where car_num = #{carNum}
        and order_state in (7,8)
        and gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryUser24HourAllOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryCar24HourTotalOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where car_num = #{carNum}
        and gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryCar1HourTotalOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where car_num = #{carNum}
        and gmt_create >= DATE_SUB(NOW(), INTERVAL 1 HOUR )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryUser1HourAllOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and gmt_create >= DATE_SUB(NOW(), INTERVAL 1 HOUR )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <delete id="cleanRiskMetricOldData">
        delete
        from car_risk_order_detail
        where (order_state in (7, 8) and DATE_SUB(now(), INTERVAL 10 DAY) > gmt_trip_finished)
           or (order_state = 2 and DATE_SUB(now(), INTERVAL 10 DAY) > gmt_canceled)
           or (member_id = '' and DATE_SUB(now(), INTERVAL 30 DAY) > gmt_create)

    </delete>


    <select id="findGeneralRiskOrder" resultMap="BaseResultMap">
        select order_serial_no,
               member_id,
               order_type,
               order_state,
               amount,
               pay_state,
               order_channel,
               contact_phone,
               gmt_trip_finished,
               pay_category,
               departure_address,
               arrival_address,
               passenger_phone,
               estimate_distance,
               estimate_time,
               real_distance,
               real_time,
               car_num,
               supplier_code,
               supplier_price,
               refund_money,
               distribution_flag,
               book_amount,
               surcharge,
               supplementary_amount,
               '025' as wash_ext
        from car_risk_order_detail
        where gmt_trip_finished between #{startTime} and #{endTime}
          and order_state in (7, 8)
          and product_line = 'YCW'
          and real_distance !=0 and real_distance &lt; 500
        and rights_order_flag = 0

        union all

        select order_serial_no,
               member_id,
               order_type,
               order_state,
               amount,
               pay_state,
               order_channel,
               contact_phone,
               gmt_trip_finished,
               pay_category,
               departure_address,
               arrival_address,
               passenger_phone,
               estimate_distance,
               estimate_time,
               real_distance,
               real_time,
               car_num,
               supplier_code,
               supplier_price,
               refund_money,
               distribution_flag,
               book_amount,
               surcharge,
               supplementary_amount,
               '026' as wash_ext
        from car_risk_order_detail
        where gmt_trip_finished between #{startTime} and #{endTime}
          and order_state in (7, 8)
          and product_line = 'YCW'
          and real_time!=0 and  real_time &lt; 120
          and rights_order_flag = 0

        union all

        select order_serial_no,
               member_id,
               order_type,
               order_state,
               amount,
               pay_state,
               order_channel,
               contact_phone,
               gmt_trip_finished,
               pay_category,
               departure_address,
               arrival_address,
               passenger_phone,
               estimate_distance,
               estimate_time,
               real_distance,
               real_time,
               car_num,
               supplier_code,
               supplier_price,
               refund_money,
               distribution_flag,
               book_amount,
               surcharge,
               supplementary_amount,
               '027' as wash_ext
        from car_risk_order_detail
        where gmt_trip_finished between #{startTime} and #{endTime}
          and order_state in (7, 8)
          and product_line = 'YCW'
          and amount > 500
          and rights_order_flag = 0

    </select>


    <select id="findAbnormalAmountRiskOrder" resultMap="BaseResultMap">
        select order_serial_no,
               member_id,
               order_type,
               order_state,
               amount,
               pay_state,
               order_channel,
               contact_phone,
               gmt_trip_finished,
               pay_category,
               departure_address,
               arrival_address,
               passenger_phone,
               estimate_distance,
               estimate_time,
               real_distance,
               real_time,
               car_num,
               supplier_code,
               supplier_price,
               refund_money,
               distribution_flag,
               book_amount,
               surcharge,
               supplementary_amount,
               'zc-001' as wash_ext
        from car_risk_order_detail
        where gmt_trip_finished between #{startTime} and #{endTime}
          and order_state in (7, 8)
          and product_line = 'YCW'
          and pay_category = 1
          and supplementary_amount > 100
          and book_amount &lt; 20

        union all

        select order_serial_no,
               member_id,
               order_type,
               order_state,
               amount,
               pay_state,
               order_channel,
               contact_phone,
               gmt_trip_finished,
               pay_category,
               departure_address,
               arrival_address,
               passenger_phone,
               estimate_distance,
               estimate_time,
               real_distance,
               real_time,
               car_num,
               supplier_code,
               supplier_price,
               refund_money,
               distribution_flag,
               book_amount,
               surcharge,
               supplementary_amount,
               'zc-002' as wash_ext
        from car_risk_order_detail
        where gmt_trip_finished between #{startTime} and #{endTime}
          and order_state in (7, 8)
          and product_line = 'YCW'
          and real_distance != 0 and abs(real_distance-estimate_distance) &lt; 3000
        and real_time!=0 and abs(real_time-estimate_time) &lt; 300
        and abs(car_price - surcharge) > 20
        and (
           (pay_category = 1 and supplementary_amount > 20)
           or
           (pay_category !=1 and pay_state = 0 and amount > 20)
           )


        union all

        select order_serial_no,
               member_id,
               order_type,
               order_state,
               amount,
               pay_state,
               order_channel,
               contact_phone,
               gmt_trip_finished,
               pay_category,
               departure_address,
               arrival_address,
               passenger_phone,
               estimate_distance,
               estimate_time,
               real_distance,
               real_time,
               car_num,
               supplier_code,
               supplier_price,
               refund_money,
               distribution_flag,
               book_amount,
               surcharge,
               supplementary_amount,
               'zc-007' as wash_ext
        from car_risk_order_detail
        where gmt_trip_finished between #{startTime} and #{endTime}
          and order_state in (7, 8)
          and product_line = 'YCW'
          and pay_category = 1
          and book_amount >= 20
          and book_amount &lt; 30
          and supplementary_amount > 200

    </select>

    <select id="queryUser30MinTotalOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and gmt_create >= DATE_SUB(NOW(), INTERVAL 30 MINUTE )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryPhone24HourFinishOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where passenger_phone !='' and passenger_phone = #{phone}
        and order_state in (7,8)
        and gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryPhone24HourTotalOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where passenger_phone !='' and passenger_phone = #{phone}
        and gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY )
        and rights_order_flag = 0
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="queryUser30MinCancelOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and order_state = 2 and car_num != ''
        and gmt_canceled >= DATE_SUB(NOW(), INTERVAL 30 MINUTE )
        and rights_order_flag = 0
        <choose>
            <when test="productLine!=null and productLine == 'YCS'">
                and product_line = #{productLine} and cancel_type in (1,3)
            </when>
            <when test="productLine!=null">
                and product_line = #{productLine} and cancel_type = 1
            </when>
            <otherwise>
                and cancel_type = 1
            </otherwise>
        </choose>
    </select>

    <select id="queryUser1HourCancelOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and order_state = 2 and car_num != ''
        and gmt_canceled >= DATE_SUB(NOW(), INTERVAL 1 HOUR )
        and rights_order_flag = 0
        <choose>
            <when test="productLine!=null and productLine == 'YCS'">
                and product_line = #{productLine} and cancel_type in (1,3)
            </when>
            <when test="productLine!=null">
                and product_line = #{productLine} and cancel_type = 1
            </when>
            <otherwise>
                and cancel_type = 1
            </otherwise>
        </choose>
    </select>


    <select id="queryUser24HourCancelOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where member_id = #{memberId}
        and order_state = 2 and car_num != ''
        and gmt_canceled >= DATE_SUB(NOW(), INTERVAL 1 DAY )
        and rights_order_flag = 0
        <choose>
            <when test="productLine!=null and productLine == 'YCS'">
                and product_line = #{productLine} and cancel_type in (1,3)
            </when>
            <when test="productLine!=null">
                and product_line = #{productLine} and cancel_type = 1
            </when>
            <otherwise>
                and cancel_type = 1
            </otherwise>
        </choose>
    </select>

    <select id="queryPhone24HourCancelOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where passenger_phone !='' and passenger_phone = #{phone}
        and order_state = 2 and car_num != ''
        and gmt_canceled >= DATE_SUB(NOW(), INTERVAL 1 DAY )
        and rights_order_flag = 0
        <choose>
            <when test="productLine!=null and productLine == 'YCS'">
                and product_line = #{productLine} and cancel_type in (1,3)
            </when>
            <when test="productLine!=null">
                and product_line = #{productLine} and cancel_type = 1
            </when>
            <otherwise>
                and cancel_type = 1
            </otherwise>
        </choose>
    </select>


    <resultMap id="carCancelOrderCountCheckResultMap" type="com.ly.car.risk.process.model.riskJob.DriverOrderCancelCountResp">
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="order_count" jdbcType="INTEGER" property="count"/>
    </resultMap>
    <select id="carCancelOrderCount" resultMap="carCancelOrderCountCheckResultMap">
        select car_num, order_type, count(order_serial_no) AS order_count
        from car_risk_order_detail
        where create_time between #{startTime} and #{endTime}
          and (cancel_amount - cancel_lost_amount) > 0
          and order_type in (19, 11, 80, 85, 86)
        group by car_num
        having count(order_serial_no) >= #{threshold}
    </select>

    <select id="car24HourFinishRightsOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where car_num = #{carNum}
        and order_state in (7,8)
        and gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        and rights_order_flag = 1
        <if test="productLine!=null and productLine!=''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="findMatchZc001Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and  order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and pay_category = 1 and book_amount &lt; 30
        and supplementary_amount > 200
    </select>

    <select id="findMatchR025Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and real_distance !=0 and real_distance &lt; 2000 and abs(real_distance-estimate_distance) > 500
    </select>

    <select id="findMatchR026Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and real_time!=0 and estimate_time!=0
        and real_time &lt; 120 and estimate_time > 300
    </select>

    <select id="findMatchR027Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and amount > 500 and amount &lt; 1000 and book_user_amount &lt; 100
    </select>

    <select id="findMatchR028Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and amount > 1000
    </select>

    <select id="findMatchR029Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and departure_address = arrival_address
    </select>

    <select id="findMatchR032Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and estimate_distance = 0
    </select>

    <select id="findMatchR034Order" resultMap="BaseResultMap">
        select
        <include refid="allColumn"/>
        from car_risk_order_detail
        where product_line = 'YCW' and order_state in (7,8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and book_user_amount!=0 and amount/book_user_amount > 1.2 and amount-book_user_amount > 20
        and real_time!=0 and estimate_time!=0 and real_time/estimate_time > 3
        and (
            (pay_category = 1 and supplementary_amount > 0)
            or
            (pay_category !=1 and pay_state = 0 and amount > 0)
            )
    </select>


    <select id="queryUser24hourCancelSupplierOrderCount" resultMap="BaseResultMap">
        SELECT
            supplier_code
        FROM
            car_risk_order_detail
        where
            gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY)
          AND member_id = #{memberId}
          AND car_num != ''
          AND order_state = 2
          AND supplier_code != ''
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
    </select>

    <select id="queryUser24hourFinishNearOrderLess6MinCount" resultMap="BaseResultMap">
        SELECT
        gmt_trip_finished
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND member_id = #{memberId}
        AND order_state in (7, 8)
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
        ORDER BY gmt_trip_finished
    </select>

    <select id="queryUser24hourFinishOrderSameDeviceCount" resultMap="BaseResultMap">
        SELECT
        device_id
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND member_id = #{memberId}
        AND device_id != ''
        AND order_state in (7, 8)
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
    </select>

    <select id="queryUser24hourFinishNearOrderLess500mCount" resultMap="BaseResultMap">
        SELECT
        dep_lat,
        dep_lon,
        arr_lat,
        arr_lon,
        gmt_trip_finished
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND member_id = #{memberId}
        AND order_state in (7, 8)
        AND (
        dep_lat != 0
        AND dep_lon != 0
        AND arr_lat != 0
        AND arr_lon != 0
        )
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
        ORDER BY gmt_trip_finished
    </select>

    <select id="queryUser24hourFinishOrderLess2kmCount" resultMap="BaseResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND member_id = #{memberId}
        AND order_state in (7, 8)
        AND 2000 > real_distance
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
    </select>

    <select id="queryDriver24hourFinishOrderLess6minCount" resultMap="BaseResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND car_num = #{carNum}
        AND order_state in (7, 8)
        AND 360 > real_time
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
    </select>

    <select id="queryDriver24hourFinishOrderCount" resultMap="BaseResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        AND car_num = #{carNum}
        AND order_state in (7, 8)
        <if test="productLine!=null and productLine!=''">
            AND product_line = #{productLine}
        </if>
    </select>

    <select id="queryPhoneSupplementaryAmountOrderCount" resultMap="BaseResultMap">
        select order_serial_no
        from car_risk_order_detail
        where passenger_phone = #{passengerPhone}
        and product_line in
        <foreach collection="productLineList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and order_state in (7, 8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and supplementary_amount > 0
    </select>

    <select id="queryPhoneOrderSupplementaryAmount" resultType="java.math.BigDecimal">
        select sum(ifnull(supplementary_amount, 0)) as amount
        from car_risk_order_detail
        where passenger_phone = #{passengerPhone}
        and product_line in
        <foreach collection="productLineList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and order_state in (7, 8)
        and pay_category=1
        and pay_state=2
        and gmt_trip_finished between #{startTime} and #{endTime}
        and supplementary_amount > 0
    </select>
    <select id="queryPhonePendingDriverOrderCount" resultType="java.lang.Long">
        select  COUNT(DISTINCT car_num) AS carNumCount
        from car_risk_order_detail
        where passenger_phone = #{passengerPhone}
        and product_line in
        <foreach collection="productLineList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and order_state in (7, 8)
        and gmt_trip_finished between #{startTime} and #{endTime}
        and supplementary_amount > 0
    </select>

    <select id="driverRealMoneyExceedRate" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND amount > (book_user_amount * #{rate} / 100)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRealMoneyExceedAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND amount > (book_user_amount + #{amount})
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverSurchargeExceedRate" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND surcharge > (book_user_amount * #{rate} / 100)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRealDistanceExceedKm" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND abs(real_distance - estimate_distance) > (#{distance} * 1000)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRealTimeExceedMinute" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND abs(real_time - estimate_time) > (#{minute} * 60)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverSupplementaryExceedAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND (
            (pay_category = 1 and supplementary_amount > 20)
            or
            (pay_category !=1 and pay_state = 0 and amount > 20)
        )
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverSurchargeRealExceedAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND surcharge - book_user_amount > #{amount}
        AND surcharge > 0
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverDistanceLessKm" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND (#{distance} * 1000) >	real_distance
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverTimeLessMinute" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND (#{minute} * 60) >	real_time
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverTimeExceedMinute" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND estimate_time > (#{minute} * 60)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverStateExp" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND pay_state in (0, 2)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverSurchargeMonthNum" resultMap="BaseResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_surcharge_order
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND car_num = #{carNum}
        AND surcharge > 0
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getByOrderSerialNo" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        car_risk_order_detail
        where
            order_serial_no = #{orderSerialNo}
        order by id desc limit 1
    </select>

    <select id="driverScorePayLessAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND pay_category = 1 and book_amount &lt; #{amount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverScorePaySupplementaryAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND supplementary_amount > #{amount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRangeAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND amount > #{leftAmount} and amount &lt; #{rightAmount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverBookLessAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND book_user_amount &lt; #{amount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverMoreAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND amount > #{amount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverODSame" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        and departure_address = arrival_address
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverZeroEstimateDistance" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        and estimate_distance = 0
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRealAmountMoreRate" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND book_user_amount != 0
        AND amount > (book_user_amount * #{rate} / 100)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRealAmountMoreAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND amount - book_user_amount > #{amount}
        AND book_user_amount != 0
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverTimeMoreRate" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND real_time != 0
        AND estimate_time != 0
        AND real_time > (estimate_time * #{rate} / 100)
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverSecondPayMoreAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        and (
            (pay_category = 1 and supplementary_amount > #{amount})
            or
            (pay_category !=1 and pay_state = 0 and amount > #{amount})
        )
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverLessAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND book_amount &lt; #{amount}
        AND pay_category = 1
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverShouldPayMoreAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND pay_category = 1
        AND supplementary_amount > #{amount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="driverRealRangeAmount" resultMap="BaseOfflineResultMap">
        SELECT
        order_serial_no
        FROM
        car_risk_order_detail
        where
        gmt_trip_finished between #{startTime} and #{endTime}
        AND pay_category = 1
        AND book_amount >= #{leftAmount}
        AND book_amount &lt; #{rightAmount}
        AND order_state in (7, 8)
        AND product_line in
        <foreach collection="productLineList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="channels != null and channels.size > 0">
            and order_channel in
            <foreach collection="channels" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplierCodeList != null and supplierCodeList.size > 0">
            and supplier_code in
            <foreach collection="supplierCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
