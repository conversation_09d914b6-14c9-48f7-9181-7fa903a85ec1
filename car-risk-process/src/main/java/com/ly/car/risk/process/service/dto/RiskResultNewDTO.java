package com.ly.car.risk.process.service.dto;

import lombok.Data;

@Data
public class RiskResultNewDTO<T> {

    private Integer code;
    private String message;
    private Integer recheck=0;
    private T obj;

    public RiskResultNewDTO(){
        this.code = 0;
        this.message = "风控通过";
    }

    public RiskResultNewDTO(Integer code,String message,T t){
        this.code = code;
        this.message = message;
        this.obj = t;
    }
}
