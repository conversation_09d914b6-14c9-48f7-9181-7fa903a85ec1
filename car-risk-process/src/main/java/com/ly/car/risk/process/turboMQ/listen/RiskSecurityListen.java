package com.ly.car.risk.process.turboMQ.listen;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.RiskSecurityConsumer;
import com.ly.car.risk.process.turboMQ.consumer.SelfBinlogConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class RiskSecurityListen implements ApplicationListener<ApplicationStartedEvent> {

    private static final String GROUP = "car_risk_group_security";
    private static final String TOPIC = "car_risk_topic_security_process";

    @Resource
    private UrlsProperties urlsProperties;

    @SneakyThrows
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(GROUP);
        consumer.setNamesrvAddr(urlsProperties.getPublicMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(TOPIC,"");
        consumer.registerMessageListener(new RiskSecurityConsumer());
        consumer.start();
        log.info("[MqStartupRunner][initTurboConsumer][RiskSecurityListen][]启动tuborMQ消费者-安全消息消费");
    }
}
