package com.ly.car.risk.process.controller.ability;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.ability.params.BadActionParam;
import com.ly.car.risk.process.service.MqSendService;
import com.ly.car.risk.process.service.dto.DriverWarningDTO;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/driver/voice")
@Slf4j
public class DriverVoiceBroadcastController {

    @Resource
    private MqSendService mqSendService;

    @RequestMapping("notifyDriver")
    public UiResult notifyDriver(@RequestBody BadActionParam param){
        log.info("[][][][]司机不良行为通知{}", JsonUtils.json(param));
        DriverWarningDTO dto = new DriverWarningDTO(param.getSupplierOrderId(),param.getBadType().toString());
        mqSendService.driverWarnNotify(JsonUtils.json(dto),null,0L);
        return UiResult.ok();
    }

}
