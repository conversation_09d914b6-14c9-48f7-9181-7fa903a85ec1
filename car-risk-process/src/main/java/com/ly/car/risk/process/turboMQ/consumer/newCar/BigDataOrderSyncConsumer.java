package com.ly.car.risk.process.turboMQ.consumer.newCar;

import cn.hutool.core.codec.Base64Decoder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.event.OrderCreateEvent;
import com.ly.car.risk.process.event.RiskEventPublisher;
import com.ly.car.risk.process.handler.HandlerChooseFactory;
import com.ly.car.risk.process.handler.orderstate.AbstractOrderStateHandler;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.turboMQ.dto.newCar.CarOrderCreatePayload;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.sof.utils.security.DESUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BigDataOrderSyncConsumer implements MessageListenerConcurrently {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Resource
    private RiskEventPublisher riskEventPublisher;

    private static String ORDER_CREATE_LOCK = "bigdata:order:create:%s";

    private static final byte[] decryptKeys = "CAR_RISK".getBytes();

    private static final FastDateFormat fullDateFmt = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msgExt : list) {
            LoggerUtils.initLogMap("bigDataOrderSync", msgExt.getTags(), msgExt.getMsgId(), "");
            try {
                String body = new String(msgExt.getBody(), "utf-8");
                LoggerUtils.info(log, "数据组通知订单创单，收到消息,body={}", body);
                CarOrderCreatePayload createPayload = JSON.parseObject(body, new TypeReference<CarOrderCreatePayload>() {
                });
                LoggerUtils.getLogMap().put("filter2", createPayload.getOrderId());

                String lockKey = String.format(ORDER_CREATE_LOCK, createPayload.getOrderId());
                RLock lock = redissonClient.getLock(lockKey);
                // 一个竞争锁
                if (!lock.tryLock(0, 30, TimeUnit.SECONDS)) {
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }

                // 对部分字段进行解密
                try {
                    decryptField(createPayload);
                } catch (Exception e) {
                    LoggerUtils.info(log, "订单：{}，敏感字段解密失败，跳过处理", createPayload.getOrderId());
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }

                // 将该订单信息，存入到用户zset中，然后根据不同要求，走不同的处理器去清洗指标
                String userZsetKey = String.format(RedisKeyConstants.BIGDATA_USER_CREATE_ORDER_WINDOW, createPayload.getMemberId());
                RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(userZsetKey);
                //移除3天前的订，同时这个key，也要有一个3天的过期时间
                long initMs = TimeUtil.initMs();
                long threeDayMs = TimeUtil.threeDayMs();
                scoredSortedSet.removeRangeByScore(initMs, true, threeDayMs, true);
                // 如果已经同步过该订单，不再处理
                if (orderAlreadySync(scoredSortedSet, createPayload.getOrderId())) {
                    LoggerUtils.info(log, "订单:{} 已同步，跳过", createPayload.getOrderId());
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                // 将该订单，加入zset
                long score = fullDateFmt.parse(createPayload.getCreateTime()).getTime();
                scoredSortedSet.add(score, JSON.toJSONString(createPayload));
                // 推送事件
                riskEventPublisher.publishRiskEvent(new OrderCreateEvent(createPayload.getOrderId(), createPayload));
                // 给这个key去设置一个3天的ttl
                scoredSortedSet.expire(Duration.ofDays(3));

            } catch (Exception e) {
                LoggerUtils.error(log, "数据组通知订单创单,处理异常", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            } finally {
                LoggerUtils.removeAll();
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private boolean orderAlreadySync(RScoredSortedSet<String> scoredSortedSet, String orderId) {
        Collection<ScoredEntry<String>> allOrders = scoredSortedSet.entryRange(0, scoredSortedSet.size() - 1);
        return allOrders.stream().anyMatch(p -> {
            CarOrderCreatePayload syncOrder = JSON.parseObject(p.getValue(), new TypeReference<CarOrderCreatePayload>() {
            });
            return Objects.equals(syncOrder.getOrderId(), orderId);
        });

    }


    private void decryptField(CarOrderCreatePayload createPayload) {
        String amount = createPayload.getAmount();
        if (StringUtils.isNotBlank(amount)) {
            createPayload.setAmount(doDecrypt(amount));
        }
        String passengerPhoneNum = createPayload.getPassengerPhoneNum();
        if (StringUtils.isNotBlank(passengerPhoneNum)) {
            createPayload.setPassengerPhoneNum(doDecrypt(passengerPhoneNum));
        }
    }

    private String doDecrypt(String encryptVal) {
        if (StringUtils.isBlank(encryptVal)) {
            return encryptVal;
        }
        try {
            byte[] bytes = Base64Decoder.decode(encryptVal);
            byte[] decrypt = DESUtils.decrypt(bytes, decryptKeys);
            return new String(decrypt, "UTF-8");
        } catch (Exception e) {
            LoggerUtils.error(log, "doDecrypt fail, sourceStr:{}", e, encryptVal);
        }
        return encryptVal;
    }
}
