package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Scope("prototype")
public class RuleRiskYangZhaoCashService extends FilterSendOrderHandler{


    @Override
    public void doHandler(FilterSendOrderContext context) {

    }
}
