package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2-1
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk022Service extends FilterSfcHandler{

    private static final String ruleNo = "022";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][SfcRuleRisk022Service][][]前置判断已通过，进入规则022判断");
        //计算开始时间戳
        List<OrderRiskContext> orderRiskContextList = context.getDriverContextList().stream()
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.threeDay()))
                .collect(Collectors.toList());
        int orderNum = 0;
        List<String> orderIds = new ArrayList<>();
        List<OrderRiskContext> contextList = new ArrayList<>();
        for(OrderRiskContext orderRiskContext : orderRiskContextList){
            Double distance = CoordUtil.getDistance(orderRiskContext.getStartLng(),orderRiskContext.getStartLat(),orderRiskContext.getEndLng(),orderRiskContext.getEndLat());
            if(new BigDecimal(distance).compareTo(new BigDecimal(context.getSfcRiskRuleConfig().getDistance022())) < 0){
                orderNum = orderNum + 1;
                orderIds.add(orderRiskContext.getOrderId());
                contextList.add(orderRiskContext);
            }
        }
        if(orderNum >= context.getSfcRiskRuleConfig().getOrderNum022()){
            log.info("[SfcRuleRisk022Service][doHandler][][]命中022规则，司机为{},关联订单为{}",context.getDriverCardNo(), JsonUtils.json(orderIds));
            distributionRiskManageService.addByRuleChain(contextList,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
            RiskResultDTO dto = new RiskResultDTO(405,"风控不通过022",null,null);
            context.getUiResult().setData(dto);
            context.getUiResult().setMsg("风控不通过");

            if(StringUtils.isBlank(context.getRuleNo())){
                context.setRuleNo(ruleNo);
            } else {
                context.setRuleNo(context.getRuleNo() + "," + ruleNo);
            }
            riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
