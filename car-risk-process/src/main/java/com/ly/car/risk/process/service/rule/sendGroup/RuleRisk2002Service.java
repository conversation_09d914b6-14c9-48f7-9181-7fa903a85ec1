package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.rule.receiveGroup.FilterReceiveOrderContext;
import com.ly.car.risk.process.service.rule.receiveGroup.FilterReceiveOrderHandler;
import com.ly.car.risk.process.service.rule.receiveGroup.ReceiveOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Scope("prototype")
public class RuleRisk2002Service extends FilterSendOrderHandler {

    private static final String ruleNo = "2002";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule2002onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            List<SendOrderContext> orderContextList = context.getDriverList().stream().
                    filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.currentDay())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderContextList) && orderContextList.size() > context.getSpecialCarRuleConfig().getRule2002OrderNum()) {
                List<String> orderIds = orderContextList.stream()
                        .map(SendOrderContext::getOrderId)
                        .collect(Collectors.toList());
                orderIds.add(context.getOrderId());
                context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
                distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene(), context.getChildScene(),
                        0, null, RiskLevelEnum.HIGH.getCode());

                orderIds.remove(context.getOrderId());

                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
