package com.ly.car.risk.process.service.rule.safe;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RMapCache;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class YncSafeNoNormalStopService {

    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource(name = "riskExecutorService")
    private ExecutorService executorService;
    @Resource
    private EsQueryClient esQueryClient;
    @Resource
    private SaveScoredSortedSetService saveScoredSortedSetService;

    public void dealStop(){
        //获取配置
        Integer distanceNum = 100;
        Integer speedNum = 120;
        Integer movingNum = 2500;
        try {
            String result = ConfigCenterClient.get("safe_warning_config");
            log.info("[YncSafeWarningService][computeMoving][][]专车安全预警任务异常停留启动-获取配置信息{}",result);
            if(result != null){
                JSONObject jsonObject = JSON.parseObject(result);
                distanceNum = jsonObject.getInteger("distance");
                speedNum = jsonObject.getInteger("speed");
                movingNum = jsonObject.getInteger("moving");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //异常停留，专车剔除起点终点500米
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(RedisKeyConstants.YNC_NO_NORMAL_STOPPING);
        long initMs = TimeUtil.initMs();
        long threeDayMs = TimeUtil.threeDayMs();
        scoredSortedSet.removeRangeByScore(initMs,true,threeDayMs,true);
        //获取前一分钟的开始和结束时间
        String minute = DateUtil.date2String(DateUtil.addMinute(new Date(),-1),"yyyy-MM-dd HH:mm");
        log.info("[YncSafeWarningService][computeMoving][][]专车安全预警任务启动-异常停留-获取权重区间{}", minute);
        long startMs =  DateUtil.string2Date(minute+":00").getTime();
        String endMinute = DateUtil.date2String(new Date(),"yyyy-MM-dd HH:mm");
        long endMs =  DateUtil.string2Date(endMinute+":00").getTime();
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, endMs, true);
        List<String> orderIds = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            String orderId = (String) obj.getValue();
            orderIds.add(orderId);
        }
        if(CollectionUtils.isEmpty(orderIds)){
            log.info("[YncSafeWarningService][computeMoving][][]专车安全预警任务启动-异常停留-当前无订单");
            return;
        }
        for(String orderId : orderIds){
            Integer finalDistanceNum = distanceNum;
            CompletableFuture.runAsync(new Runnable() {
                @Override
                public void run() {
                    //当前订单是否完单或取消
                    OrderInfo orderInfo = orderInfoMapper.findByOrderId(orderId);
                    //查询当前状态是否是已经完单
                    if(orderInfo.getStatus() > 200){
                        scoredSortedSet.remove(orderId);
                        return;
                    }
                    //存储当前司机位置
                    String locationPoint = esQueryClient.queryCurrentLocation(orderId);
                    String lng = locationPoint.split(",")[0];
                    String lat = locationPoint.split(",")[1];
                    RMapCache<String, String> mapCache = redissonClient.getMapCache(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES);
                    //上次停留位置
                    String locationStr = mapCache.get(orderId);
                    if(StringUtils.isNotBlank(locationStr)){
                        //专车去除起点终点
                        RMap<String, String> map = redissonClient.getMap(RedisKeyConstants.ESTIMATE_START_END_INFO + orderId);
                        if(map != null && !map.isEmpty()){
                            String startAddress = map.get("startAddress");
                            double distance = CoordUtil.getDistance(lng, lat, startAddress.split(",")[1], startAddress.split(",")[0]);
                            if(new BigDecimal(distance).compareTo(new BigDecimal("500")) < 0){
                                return;
                            }
                            String endAddress = map.get("endAddress");
                            double endDistance = CoordUtil.getDistance(lng, lat, endAddress.split(",")[1], endAddress.split(",")[0]);
                            if(new BigDecimal(endDistance).compareTo(new BigDecimal("500")) < 0){
                                return;
                            }
                        }
                        //计算是否未移动,当前位置与五分钟之前的位置相差是不是小于100米,处理是否异常停留
                        double distance = CoordUtil.getDistance(lng, lat, locationStr.split(",")[0], locationStr.split(",")[1]);
                        if(new BigDecimal(distance).compareTo(new BigDecimal(finalDistanceNum)) < 0){
                            //这个时候塞入缓存，前端页面需要展示
                            redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING+orderId).put("aq003",DateUtil.date2String(new Date())+","+"车辆长时间停留");
                            redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING+orderId,6,TimeUnit.MINUTES);
                            log.info("[YncSafeWarningService][][{}][]命中安全预警策略aq003,当前经纬度{}，五分钟前经纬度{}",orderId,locationPoint,locationStr);
                        }
                        mapCache.put(orderId,locationPoint,10, TimeUnit.MINUTES);
                    } else {
                        //说明是第一次执行，暂时不管
                        mapCache.put(orderId,locationPoint,10, TimeUnit.MINUTES);
                    }
                    //更新行程中订单下次需要执行的时间
                    saveScoredSortedSetService.save(RedisKeyConstants.YNC_MOVING_ORDER,
                            3 * 24 * 60 * 60L,orderId, DateUtil.addMinute(new Date(),5).getTime());
                    redissonClient.getKeys().expire(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES, 10, TimeUnit.MINUTES);
                }
            },executorService).handle((result, e) -> {
                if (e != null) {
                    log.error("[YncSafeNoNormalStopService][][][]CompletableFuture处理异常{}",e);
                }
                return result;
            });
        }

    }

}
