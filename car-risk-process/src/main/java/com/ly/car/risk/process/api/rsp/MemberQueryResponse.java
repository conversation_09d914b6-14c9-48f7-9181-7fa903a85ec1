package com.ly.car.risk.process.api.rsp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/24 2:05 下午
 */
@Data
public class MemberQueryResponse {
    private String code;
    private String msg;
    private Integer rspcode;
    private MemberQueryDetail data;

    @Data
    public class MemberQueryDetail{
        private Long memberId;//会员Id
        private String loginName;//登陆名
        private String memberSystem;//会员体系
        private String mobile;//手机号
        private String email;//邮箱
        private String realname;//真实姓名
        private String nickname;//昵称
        private String userPhotoUrl;//用户头像
        private Integer sex;//性别（0：男，1：女；2：未知）
        private String showName;//展示名
        @JSONField(format = "yyyy-MM-dd HH:mm")
        private Date registerTime;//注册时间(yyyy-MM-dd HH:mm)
        private Integer valid;//状态 1:有效 0:无效、已经注销
    }
}
