package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当前用户的订单里程小于3公里，且近24小时里程小于3公里的订单数>=10单
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk016Service extends FilterSendOrderHandler{

    private static final String ruleNo = "016";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule016onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            if(context.getStartLat() != null){
                double distance = CoordUtil.getDistance(context.getStartLat(), context.getStartLng(), context.getEndLat(), context.getEndLng());
                if (new BigDecimal(distance).compareTo(new BigDecimal(context.getSpecialCarRuleConfig().getRule016Distance())) < 0) {
                    //获取近24h的完单
                    List<SendOrderContext> orderContextList = context.getMemberList().stream()
                            .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.oneDay()))
                            .filter(orderContext -> new BigDecimal(CoordUtil.getDistance(orderContext.getStartLat(), orderContext.getStartLng(),
                                    orderContext.getEndLat(), orderContext.getEndLng())).compareTo(new BigDecimal(context.getSpecialCarRuleConfig().getRule016Distance())) < 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orderContextList) && orderContextList.size() >= context.getSpecialCarRuleConfig().getRule016OrderNum()) {
                        context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.MEDIUM.getCode()));
                        List<String> orderIds = orderContextList.stream().map(SendOrderContext::getOrderId)
                                .distinct()
                                .collect(Collectors.toList());
                        orderIds.add(context.getOrderId());
                        distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene()
                                , context.getChildScene(), 0, null, RiskLevelEnum.MEDIUM.getCode());

                        orderIds.remove(context.getOrderId());
                        if(StringUtils.isBlank(context.getRuleNo())){
                            context.setRuleNo(ruleNo);
                        } else {
                            context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                        }
                        riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                    }
                }
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
