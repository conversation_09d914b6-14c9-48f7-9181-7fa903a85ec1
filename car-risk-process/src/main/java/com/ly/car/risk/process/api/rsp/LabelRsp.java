package com.ly.car.risk.process.api.rsp;

import lombok.Data;

@Data
public class LabelRsp {

    private Integer code;
    private String msg;
    private String detail;
    private DetailRsp data;

    @Data
    public static class DetailRsp{
        private Integer age;
        // 2-女性
        private Integer gender;
        private Integer ageGroup;
        private String birthday;
        private Integer blacklist;
        private Integer busNum;
        private Integer hotelNum;
        private String name;
        private Integer planeTicketNum;
        private Integer residentCityId;
        private Integer scenicSpotNum;
        // 1-学生
        private Integer student;
        private Integer trainTicketNum;
        private String userId;
        private Integer level;
        private Integer newSplitStatus;
    }
}
