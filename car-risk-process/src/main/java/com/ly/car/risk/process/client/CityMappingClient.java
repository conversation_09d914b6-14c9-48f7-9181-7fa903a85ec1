package com.ly.car.risk.process.client;

import com.ly.car.risk.process.bean.properties.UrlsProperties;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class CityMappingClient implements InitializingBean {

    @Resource
    private UrlsProperties urlsProperties;

    /**
     * 城市简称-城市三字码 映射map
     */
    @Getter
    private Map<String, String> cityMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
//        try {
//            LoggerUtils.info(log, "[获取基础资源城市信息]开始， url:{},", urlsProperties.getCityClientUrl());
//            Map<String, String> headerMap = new HashMap<>();
//            headerMap.put("Labrador-Token", urlsProperties.getCityClientToken());
//
//            FindCitiesRequest findCitiesRequest = FindCitiesRequest.builder().domainKey("travelsystem.java.marketing.advertiseservice").channelCode("852").build();
//            HttpUriRequest httpUriRequest = HttpUtils.buildPost(urlsProperties.getCityClientUrl(), JSON.toJSONString(findCitiesRequest), headerMap);
//            String resp = HttpUtils.execute(httpUriRequest);
//            JSONObject jsonObject = JSON.parseObject(resp);
//            FindCitiesResponse<List<CityDTO>> resultMsg = JSON.parseObject(jsonObject.getString("data"), new TypeReference<FindCitiesResponse<List<CityDTO>>>() {
//            });
//            if (null != resultMsg && resultMsg.getSuccess() && CollectionUtils.isNotEmpty(resultMsg.getValues())) {
//                cityMap = resultMsg.getValues().stream().collect(Collectors.toMap(CityDTO::getCityCode, CityDTO::getCityId, (k1, k2) -> k1));
//            }
//            LoggerUtils.info(log, "[获取基础资源城市信息]结束， cityMap:{}", cityMap.size());
//        } catch (Exception e) {
//            LoggerUtils.error(log, "[获取基础资源城市信息]发生异常 url:{}", e, urlsProperties.getCityClientUrl());
//            throw new IllegalStateException("获取基础资源城市信息接口失败");
//        }
    }

}
