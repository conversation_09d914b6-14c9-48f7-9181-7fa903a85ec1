package com.ly.car.risk.process.service.rule.safe;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class YncSafeNoNormalSpeedService {

    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource(name = "riskExecutorService")
    private ExecutorService executorService;
    @Resource
    private EsQueryClient esQueryClient;
    @Resource
    private SaveScoredSortedSetService saveScoredSortedSetService;

    public void dealSpeed(){
        //获取配置
        Integer distanceNum = 100;
        Integer speedNum = 120;
        Integer movingNum = 2500;
        try {
            String result = ConfigCenterClient.get("safe_warning_config");
            log.info("[YncSafeWarningService][computeMoving][][]顺风车安全预警任务异常停留启动-获取配置信息{}",result);
            if(result != null){
                JSONObject jsonObject = JSON.parseObject(result);
                distanceNum = jsonObject.getInteger("distance");
                speedNum = jsonObject.getInteger("speed");
                movingNum = jsonObject.getInteger("moving");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //车速过快
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(RedisKeyConstants.YNC_NO_NORMAL_SPEED);
        long initMs = TimeUtil.initMs();
        long threeDayMs = TimeUtil.threeDayMs();
        scoredSortedSet.removeRangeByScore(initMs,true,threeDayMs,true);
        //获取前一分钟的开始和结束时间
        String minute = DateUtil.date2String(DateUtil.addMinute(new Date(),-1),"yyyy-MM-dd HH:mm");
        log.info("[YncSafeWarningService][computeMoving][][]专车安全预警任务启动-车速过快-获取权重区间{}", minute);
        long startMs =  DateUtil.string2Date(minute+":00").getTime();
        String endMinute = DateUtil.date2String(new Date(),"yyyy-MM-dd HH:mm");
        long endMs =  DateUtil.string2Date(endMinute+":00").getTime();
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, endMs, true);
        List<String> orderIds = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            String orderId = (String) obj.getValue();
            orderIds.add(orderId);
        }
        if(CollectionUtils.isEmpty(orderIds)){
            log.info("[YncSafeWarningService][computeMoving][][]专车安全预警任务启动-车速过快-当前无订单");
            return;
        }
        for(String orderId : orderIds){
            Integer finalSpeedNum = speedNum;
            CompletableFuture.runAsync(new Runnable() {
                @Override
                public void run() {
                    String locationPoint = esQueryClient.queryCurrentLocation(orderId);
                    if(locationPoint == null){
                        scoredSortedSet.remove(orderId);
                        return;
                    }
                    String lng = locationPoint.split(",")[0];
                    String lat = locationPoint.split(",")[1];
                    RMapCache<String, String> mapCache = redissonClient.getMapCache(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES);
                    String locationStr = mapCache.get(orderId);
                    if(locationStr != null){
                        double distance = CoordUtil.getDistance(lng, lat, locationStr.split(",")[0], locationStr.split(",")[1]);
                        BigDecimal speed =new BigDecimal(distance).divide(new BigDecimal("1000"))
                                .divide(new BigDecimal("6")).multiply(new BigDecimal("60")).setScale(2,BigDecimal.ROUND_CEILING);
                        if(speed.compareTo(new BigDecimal(finalSpeedNum)) > 0){
                            //这个时候塞入缓存，前端页面需要展示
                            redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING+orderId).put("aq002",DateUtil.date2String(new Date())+","+"车辆行驶速度过快");
                            redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING+orderId,6, TimeUnit.MINUTES);
                            log.info("[YncSafeWarningService][][{}][]命中安全预警策略aq002车速过快,当前经纬度{}，五分钟前经纬度{}",orderId,locationPoint,locationStr);
                        }
                    } else {
                        mapCache.put(orderId,locationPoint,10, TimeUnit.MINUTES);
                    }
                }
            });
        }
    }
}
