package com.ly.car.risk.process.repo.risk.mapper.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * metric_strategy_rule_relation
 * <AUTHOR>
public class MetricStrategyRuleRelation implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}