package com.ly.car.risk.process.event;

import org.springframework.context.ApplicationEvent;

import java.time.Clock;

/**
 * Description of AbstractRiskEvent
 *
 * <AUTHOR>
 * @date 2024/4/23
 * @desc
 */
public class AbstractRiskEvent<T> extends ApplicationEvent {

    private T order;

    public AbstractRiskEvent(Object source, T order) {
        super(source);
        this.order = order;
    }

    public T getOrder() {
        return order;
    }
}