package com.ly.car.risk.process.repo.risk.mapper.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 离线风控策略配置-策略指标关联
 * offline_metric_field_relation
 */
@Data
public class OfflineMetricFieldRelation implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 离线策略id(offline_metric_strategy表id)
     */
    private Long strategyId;

    /**
     * 离线指标id(offline_metric_field表id)
     */
    private Long fieldId;

    /**
     * 运算符
     */
    private String operator;

    /**
     * 值类型 0-常量
     */
    private Integer rightType;

    /**
     * 对比值
     */
    private String rightValue;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}