package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.OfflineMetricField;
import com.ly.car.risk.process.repo.risk.mapper.entity.OfflineRiskFieldDTO;
import java.util.List;

public interface OfflineMetricFieldMapper extends BaseMapper<OfflineMetricField> {
    
    List<OfflineRiskFieldDTO> findField();
}
