package com.ly.car.risk.process.task;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SfcFinishOrCancelData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orderId;
    private String memberId;
    private String unionId;
    private Date finishTime;
    private Date createTime;
    private Date cancelTime;
    private Integer status;//300 / 1000
    private Integer cancelType;//0-未接单取消 1-接单后取消

    private Date acceptTime;
    private BigDecimal estimateKilo;
    private String driverCardNo;
    private Integer cityId;
    private BigDecimal startLat;
    private BigDecimal startLng;
    private BigDecimal endLat;
    private BigDecimal endLng;
    private String phone7;
    private String passengerCellphone;
    private Integer intervalTime;//完单间隔

    private Integer distributorFlag;//是否分销单


    private Integer eventType;
    private Date eventTime;
    private BigDecimal TotalAmount;

}
