package com.ly.car.risk.process.repo.riskmetrics.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 顺风车订单基础信息表
 * car_mt_ride_order_info
 */
@Data
public class CarMtRideOrderInfo implements Serializable {
    /**
     * 自增id
     */
    private Long id;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 订单金额（元）
     */
    private BigDecimal amount;
    
    /**
     * 付款金额（元）
     */
    private BigDecimal payPrice;
    
    /**
     * 小程序unionid
     */
    private String unionId;
    
    /**
     * 微信openid
     */
    private String openId;
    
    /**
     * 用户id
     */
    private Long memberId;
    
    /**
     * 司机id
     */
    private Long driverId;
    
    /**
     * 车牌
     */
    private String vehicleNo;
    
    /**
     * 订单状态
     1.已下单
     2.已取消
     3.已确认
     4.已派车
     5.已处理
     6.已完成
     7.供应商取消
     8.平台取消
     9.出票成功
     10.出票失败
     11.行程中
     12.派车中 13.可手工拼单 14.供应商可抢单
     */
    private Integer orderStatus;
    
    /**
     * 支付状态
     1.已支付
     2.未支付
     3.申请退款
     4.已退款
     5. 部分支付
     */
    private Integer payStatus;
    
    /**
     * 订单类型：1、顺风车；2、包车； 3、拼车；6、站到站手工派单；9、线下拉新活动；10.站到站包车 0、定时班线正常单 5、定时班线异常单；
     */
    private Integer orderType;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改人
     */
    private String updateUser;
    
    /**
     * 修改时间
     */
    private Date updateTime;
    
    private static final long serialVersionUID = 1L;
}