package com.ly.car.risk.process.repo.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.SfcOrderExt;
import org.apache.ibatis.annotations.Select;

public interface SfcOrderExtMapper extends BaseMapper<SfcOrderExt> {


    /**
     * 根据主订单id 查询价格明细
     *
     * @param orderId
     * @return
     */
    @Select("select * from  sfc_order_ext where order_id = #{orderId} limit 1")
    SfcOrderExt queryByOrderId(String orderId);
}
