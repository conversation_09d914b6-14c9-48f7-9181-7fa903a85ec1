package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.repo.risk.mapper.ActivityConfigMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityConfig;
import com.ly.car.risk.process.turboMQ.dto.ActivityConfigConvert;
import com.ly.car.risk.process.turboMQ.dto.ActivityInvitee;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.utils.BingLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class ActivityConfigConsumer implements MessageListenerConcurrently {

    private ActivityConfigMapper activityConfigMapper;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][ActivityConfigConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                BingLogData bingLogData = JSON.parseObject(body, BingLogData.class);
                String eventType = bingLogData.getEventType();
                ActivityConfigConvert beforeActivityConfigConvert = BingLogUtil.buildBefore(bingLogData, ActivityConfigConvert.class);
                ActivityConfigConvert afterActivityConfigConvert = BingLogUtil.buildSource(bingLogData, ActivityConfigConvert.class);
                if(eventType.equals("INSERT")){
                    activityConfigMapper = SpringContextUtil.getBean("activityConfigMapper");
                    ActivityConfig activityConfig = new ActivityConfig();
                    activityConfig.setActivityId(afterActivityConfigConvert.getId());
                    activityConfig.setStartTime(afterActivityConfigConvert.getStartTime());
                    activityConfig.setEndTime(afterActivityConfigConvert.getEndTime());
                    activityConfig.setStatus(afterActivityConfigConvert.getStatus());
                    activityConfig.setCreateTime(new Date());
                    activityConfigMapper.insert(activityConfig);
                } else{
                    activityConfigMapper = SpringContextUtil.getBean("activityConfigMapper");
                    ActivityConfig activityConfig = activityConfigMapper.selectOne(new QueryWrapper<ActivityConfig>()
                        .eq("activity_id",afterActivityConfigConvert.getId())
                    );
                    if(activityConfig == null){
                        ActivityConfig activityConfigInsert = new ActivityConfig();
                        activityConfigInsert.setActivityId(afterActivityConfigConvert.getId());
                        activityConfigInsert.setStartTime(afterActivityConfigConvert.getStartTime());
                        activityConfigInsert.setEndTime(afterActivityConfigConvert.getEndTime());
                        activityConfigInsert.setStatus(afterActivityConfigConvert.getStatus());
                        activityConfigInsert.setCreateTime(new Date());
                        activityConfigMapper.insert(activityConfigInsert);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    activityConfig.setStartTime(afterActivityConfigConvert.getStartTime());
                    activityConfig.setEndTime(afterActivityConfigConvert.getEndTime());
                    activityConfig.setStatus(afterActivityConfigConvert.getStatus());
                    activityConfigMapper.updateById(activityConfig);
                }
            } catch (Exception e){
                log.error("[][][][]同步活动配置错误",e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
