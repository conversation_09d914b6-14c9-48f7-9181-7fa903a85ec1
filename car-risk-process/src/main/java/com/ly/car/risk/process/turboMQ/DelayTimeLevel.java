package com.ly.car.risk.process.turboMQ;

public interface DelayTimeLevel {
    int SECOND_1 = 1;
    int SECOND_5 = 2;
    int SECOND_10 = 3;
    int SECOND_30 = 4;
    int MINUTE_1 = 5;
    int MINUTE_2 = 6;
    int MINUTE_3 = 7;
    int MINUTE_4 = 8;
    int MINUTE_5 = 9;
    int MINUTE_6 = 10;
    int MINUTE_7 = 11;
    int MINUTE_8 = 12;
    int MINUTE_9 = 13;
    int MINUTE_10 = 14;
    int MINUTE_20 = 15;
    int MINUTE_30 = 16;
    int HOUR_1 = 17;
    int HOUR_2 = 18;

    static int calculate(long executeDate) {
        //重新用当前时间可能会下降到上一档
        long surplusSecond = (executeDate - System.currentTimeMillis()) / 1000;
        if (surplusSecond > 0) {
            if (surplusSecond < 5) {
                return SECOND_1;
            } else if (surplusSecond < 10) {
                return SECOND_5;
            } else if (surplusSecond < 30) {
                return SECOND_10;
            } else if (surplusSecond < 60) {
                return SECOND_30;
            } else if (surplusSecond < 60 * 2) {
                return MINUTE_1;
            } else if (surplusSecond < 60 * 3) {
                return MINUTE_2;
            } else if (surplusSecond < 60 * 4) {
                return MINUTE_3;
            } else if (surplusSecond < 60 * 5) {
                return MINUTE_4;
            } else if (surplusSecond < 60 * 6) {
                return MINUTE_5;
            } else if (surplusSecond < 60 * 7) {
                return MINUTE_6;
            } else if (surplusSecond < 60 * 8) {
                return MINUTE_7;
            } else if (surplusSecond < 60 * 9) {
                return MINUTE_8;
            } else if (surplusSecond < 60 * 10) {
                return MINUTE_9;
            } else if (surplusSecond < 60 * 20) {
                return MINUTE_10;
            } else if (surplusSecond < 60 * 30) {
                return MINUTE_20;
            } else if (surplusSecond < 60 * 60) {
                return MINUTE_30;
            } else if (surplusSecond < 60 * 60 * 2) {
                return HOUR_1;
            } else {
                return HOUR_2;
            }
        }
        return 0;
    }

    static Long getTimeByLevel(int level) {
        Long time = 0L;
        switch (level) {
            case 1:
                time = 1 * 1000l;
                break;
            case 2:
                time = 5 * 1000l;
                break;
            case 3:
                time = 10 * 1000l;
                break;
            case 4:
                time = 30 * 1000l;
                break;
            case 5:
                time = 1 * 60 * 1000l;
                break;
            case 6:
                time = 2 * 60 * 1000l;
                break;
            case 7:
                time = 3 * 60 * 1000l;
                break;
            case 8:
                time = 4 * 60 * 1000l;
                break;
            case 9:
                time = 5 * 60 * 1000l;
                break;
            case 10:
                time = 6 * 60 * 1000l;
                break;
            case 11:
                time = 7 * 60 * 1000l;
                break;
            case 12:
                time = 8 * 60 * 1000l;
                break;
            case 13:
                time = 9 * 60 * 1000l;
                break;
            case 14:
                time = 10 * 60 * 1000l;
                break;
            case 15:
                time = 20 * 60 * 1000l;
                break;
            case 16:
                time = 30 * 60 * 1000l;
                break;
            case 17:
                time = 1 * 60 * 60 * 1000l;
                break;
            case 18:
                time = 2 * 60 * 60 * 1000l;
                break;
            default:
                break;
        }
        return time;
    }

}
