package com.ly.car.risk.process.api.dto;

import lombok.Data;

@Data
public class ShuMeiDeviceDTO {

    private String id;

    private String abtmac;

    private String adid;

    private String appId;

    private String apps;

    private String apputm;

    private String appver;

    private String axposed;

    private String availableSpace;

    private String band;

    private String battery;

    private String binderhook;

    private String boot;

    private String brand;

    private String brightness;

    private String bssid;

    private String cell;

    private String debuggable;

    private String debugger;

    private String emu;

    private String iccid;

    private String model;

    private String name;

    private String operator;

    private String os;

    private String riskapp;

    private String riskdir;

    private String sdk_flavor;

    private String smid;

    private String ssid;

    private String virtual;

    private String wifiip;

    private String simstate;

    private String serial;

    private String manufacturer;

    private String fingerprint;

    private String wifi_mac;

    private String gothk;

    private String hook;

    private String hookErr;

    private String is_vpn;

    private String resett;

    private String root;
}
