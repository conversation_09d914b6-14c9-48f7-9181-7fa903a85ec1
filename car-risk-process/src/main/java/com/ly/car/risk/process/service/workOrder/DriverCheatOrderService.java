package com.ly.car.risk.process.service.workOrder;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.bean.MqProducerSelect;
import com.ly.car.risk.process.controller.params.DriverCheatOrderParams;
import com.ly.car.risk.process.repo.risk.mapper.DriverCheatSyncOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheatSyncOrder;
import com.ly.car.risk.process.service.workOrder.dto.DriverCheatListDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DriverCheatOrderService {

    private static Map<Integer,String> statusMap;
    static {
        statusMap = new HashMap<>();
        statusMap.put(0,"可申诉");
        statusMap.put(1,"申诉中");
        statusMap.put(2,"申诉通过");
        statusMap.put(3,"申诉驳回");
    }

    @Resource
    private DriverCheatSyncOrderMapper driverCheatSyncOrderMapper;
    @Resource
    private MqProducerSelect mqProducerSelect;

    public List<DriverCheatListDTO> getList(DriverCheatOrderParams params){

        List<DriverCheatSyncOrder> driverCheatSyncOrders = driverCheatSyncOrderMapper.selectPage(
                params.getDriverId(), params.getCurrent(), params.getPageSize()
        );
        List<DriverCheatListDTO> dtoList = new ArrayList<>();
        for(DriverCheatSyncOrder order : driverCheatSyncOrders){
            DriverCheatListDTO dto = new DriverCheatListDTO();
            dto.setCreateTime(DateUtil.date2String(order.getCreateTime()));
            dto.setOrderId(order.getOrderId());
            dto.setRiskLabel(Arrays.asList(order.getRiskLabel().split(",")));
            dto.setAppealStatus(order.getAppealStatus());
            dto.setAppealStatusName(statusMap.get(order.getAppealStatus()));
            dto.setAppealTime(order.getAppealStatus()>0?DateUtil.date2String(order.getAppealTime()):"");
            dto.setCheckTime(order.getAppealStatus()>1?DateUtil.date2String(order.getCheckTime()):"");
            dto.setAppealText(order.getAppealText());
            dto.setAppealUrl(Arrays.asList(order.getAppealUrl().split(",")));
            dto.setSoundFile(StringUtils.isNotBlank(order.getSoundFile())?JsonUtils.json(order.getSoundFile(),List.class):new ArrayList());
            dto.setDriverMemberId(order.getDriverId());
            String text = "";
            if(order.getAppealStatus() == 1){
                text = "我们会尽快进行订单申诉处理，如您对已处理完成的订单有异议可联系客服咨询";
            } else if(order.getAppealStatus() == 3){
                text = "如您对已处理完成的订单有异议可联系客服咨询";
            }
            dto.setCheckText(order.getAppealStatus()==2?"您的申诉已通过":text);
            dto.setRefuseText(order.getRefuseText());
            dto.setRiskRemark(order.getRiskRemark());
            dtoList.add(dto);
        }
        return dtoList;
    }

    public DriverCheatListDTO getDetail(DriverCheatOrderParams params){
        DriverCheatSyncOrder order = this.driverCheatSyncOrderMapper.selectOne(new QueryWrapper<DriverCheatSyncOrder>()
                .eq("order_id",params.getOrderId()).last("limit 1")
        );
        DriverCheatListDTO dto = new DriverCheatListDTO();
        dto.setCreateTime(DateUtil.date2String(order.getCreateTime()));
        dto.setOrderId(order.getOrderId());
        dto.setRiskLabel(Arrays.asList(order.getRiskLabel().split(",")));
        dto.setAppealStatus(order.getAppealStatus());
        dto.setAppealStatusName(statusMap.get(order.getAppealStatus()));
        dto.setAppealTime(order.getAppealStatus()>0?DateUtil.date2String(order.getAppealTime()):"");
        dto.setCheckTime(order.getAppealStatus()>1?DateUtil.date2String(order.getCheckTime()):"");
        dto.setAppealText(order.getAppealText());
        dto.setAppealUrl(Arrays.asList(order.getAppealUrl().split(",")));
        dto.setSoundFile(StringUtils.isNotBlank(order.getSoundFile())?JsonUtils.json(order.getSoundFile(),List.class):new ArrayList());
        dto.setDriverMemberId(order.getDriverId());
        String text = "";
        if(order.getAppealStatus() == 1){
            text = "我们会尽快进行订单申诉处理，如您对已处理完成的订单有异议可联系客服咨询";
        } else if(order.getAppealStatus() == 3){
            text = "如您对已处理完成的订单有异议可联系客服咨询";
        }
        dto.setCheckText(order.getAppealStatus()==2?"您的申诉已通过":text);
        dto.setRefuseText(order.getRefuseText());
        dto.setRiskRemark(order.getRiskRemark());
        return dto;
    }

    public UiResult appeal(DriverCheatOrderParams params){
        DriverCheatSyncOrder syncOrder = this.driverCheatSyncOrderMapper.selectOne(new QueryWrapper<DriverCheatSyncOrder>()
            .eq("order_id",params.getOrderId()).last("limit 1")
        );
        if(syncOrder.getAppealStatus() > 0){
            return UiResult.fail();
        }
        syncOrder.setUpdateTime(new Date());
        syncOrder.setAppealTime(new Date());
        syncOrder.setAppealStatus(1);
        syncOrder.setAppealText(params.getAppealText());
        syncOrder.setAppealUrl(StringUtils.join(params.getAppealUrl(),","));
        syncOrder.setSoundFile(JsonUtils.json(params.getSoundFile()));
        this.driverCheatSyncOrderMapper.updateById(syncOrder);


        return UiResult.ok();
    }

    public UiResult queryOrder(DriverCheatOrderParams params){
        List<DriverCheatSyncOrder> syncOrderList = this.driverCheatSyncOrderMapper.selectList(new QueryWrapper<DriverCheatSyncOrder>()
            .in("order_id",params.getOrderIds())
            .in("appeal_status",0,1,3)
        );
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIds",syncOrderList.stream().map(DriverCheatSyncOrder::getOrderId).collect(Collectors.toList()));
        return UiResult.ok(jsonObject);
    }

    public UiResult insert(DriverCheatSyncOrder syncOrder){
        DriverCheatSyncOrder order = new DriverCheatSyncOrder();
        order.setOrderId(syncOrder.getOrderId());
        order.setDriverOrderId(syncOrder.getDriverOrderId());
        order.setIsSync(1);
        order.setAppealStatus(0);
        order.setRiskLabel(syncOrder.getRiskLabel());
        order.setDisposeResult(syncOrder.getDisposeResult());
        order.setFinalAppealTime(DateUtil.string2Date("2023-09-20 23:00:00"));
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setDriverId(syncOrder.getDriverId());
        order.setProductLine(syncOrder.getProductLine());
        order.setRiskRemark(syncOrder.getRiskRemark());
        this.driverCheatSyncOrderMapper.insert(order);

        //通知卡罗拉冻结
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("passengerOrderId",syncOrder.getOrderId());
        jsonObject.put("appealStatus",0);
        jsonObject.put("riskLabel",syncOrder.getRiskLabel());
        jsonObject.put("riskRemark",syncOrder.getRiskRemark());
        mqProducerSelect.getProducer(syncOrder.getProductLine()).send(MqTagEnum.risk_order_appeal_frozen,JsonUtils.json(jsonObject),0);
        return UiResult.ok();
    }

    public UiResult check(DriverCheatSyncOrder syncOrder){
        log.info("[][][][]管理员审核{}",JsonUtils.json(syncOrder));
        DriverCheatSyncOrder order = this.driverCheatSyncOrderMapper.selectOne(new QueryWrapper<DriverCheatSyncOrder>().eq("order_id", syncOrder.getOrderId()));
        order.setUpdateTime(new Date());
        order.setCheckUser("风控后台");
        order.setCheckTime(new Date());
        order.setRefuseText(syncOrder.getRefuseText());
        order.setAppealStatus(syncOrder.getAppealStatus());
        this.driverCheatSyncOrderMapper.updateById(order);
        //如果是解除，审核通过则发送mq通知解除
        if(syncOrder.getAppealStatus() == 2){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("passengerOrderId",syncOrder.getOrderId());
            jsonObject.put("appealStatus",syncOrder.getAppealStatus());
            mqProducerSelect.getProducer(syncOrder.getProductLine()).send(MqTagEnum.risk_order_appeal_approved,JsonUtils.json(jsonObject),0);
        }
        return UiResult.ok();
    }

    public void adminFrozen(JSONObject jsonObject){
        mqProducerSelect.getProducer(jsonObject.getString("productLine")).send(MqTagEnum.risk_order_appeal_frozen,JsonUtils.json(jsonObject),0);
    }

    public void adminCheck(JSONObject jsonObject){
        Integer status = jsonObject.getInteger("appealStatus");
        if(status == 2){
            mqProducerSelect.getProducer(jsonObject.getString("productLine")).send(MqTagEnum.risk_order_appeal_approved,JsonUtils.json(jsonObject),0);
        }
    }

}
