package com.ly.car.risk.process;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HttpAsyncClientConfiguration {

    @Bean(initMethod = "start", destroyMethod = "close")
    public CloseableHttpAsyncClient getCloseableHttpAsyncClient() throws IOReactorException {
        //反应堆
        ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor(IOReactorConfig.custom()
                .setIoThreadCount(32)
                .setSoKeepAlive(true)
                .setConnectTimeout(3000)
                .setSoTimeout(10000)
                .build(),
                new BasicThreadFactory.Builder().namingPattern("io-work-%d").build()
        );

        //链接池配置
        PoolingNHttpClientConnectionManager connManager = new PoolingNHttpClientConnectionManager(ioReactor);
        connManager.setDefaultMaxPerRoute(1000);
        connManager.setMaxTotal(3000);

        //请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(10000)
                .setConnectTimeout(3000)
                .setConnectionRequestTimeout(3000)
                .setRedirectsEnabled(false)
                .build();

        return HttpAsyncClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(requestConfig)
                .setThreadFactory(new BasicThreadFactory.Builder().namingPattern("io-reactor-%d").build())
                .build();
    }

}
