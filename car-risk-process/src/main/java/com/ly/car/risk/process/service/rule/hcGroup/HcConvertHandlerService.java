package com.ly.car.risk.process.service.rule.hcGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.component.SfcRuleServiceContextUtil;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.ChildSceneEnum;
import com.ly.car.risk.process.constants.CustomerConstants;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.SfcRiskRuleConfig;
import com.ly.car.risk.process.service.rule.SpecialCarRuleConfig;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class HcConvertHandlerService {

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private DriverCheckService driverCheckService;

    public List<FilterHcAroundHandler> getHcHandlerList(Integer mainScene, Integer childScene){
        List<FilterHcAroundHandler> serviceNameList = new ArrayList<>();
        List<String> childSceneStr = new ArrayList<>();
        if(childScene == null){
            //先取出主场景下所有子场景
            childSceneStr.addAll(ChildSceneEnum.getAllChildStr(mainScene));
        } else {
            childSceneStr.add(mainScene+"-"+childScene);
        }

        //名单类肯定放第一个
        for(String str : childSceneStr){
            List<String> mapService = HcRuleServiceContextUtil.serviceNameMap.get(str);
            for(String serviceStr : mapService){
                if(CustomerConstants.customerMap.get(serviceStr) != null){
                    serviceNameList.add(0, SpringContextUtil.getBean(serviceStr));
                } else {
                    serviceNameList.add(SpringContextUtil.getBean(serviceStr));
                }
            }
        }
        //串联链路
        if(!CollectionUtils.isEmpty(serviceNameList)){
            for(int i = 1;i< serviceNameList.size();i++){
                serviceNameList.get(i-1).next(serviceNameList.get(i));
            }
        }
        return serviceNameList;
    }

    public FilterHcContext convertParams(FilterParams params){
        //这边的params完全可以用规则配置平台生成json去匹配，定义好参数库
        FilterHcContext filterHcContext = new FilterHcContext();
        filterHcContext.setDriverId(params.getDriverId());
        filterHcContext.setBankCard(params.getBankCard());
        filterHcContext.setName(params.getName());
        filterHcContext.setIdCard(params.getIdCard());
        filterHcContext.setMobile(params.getMobile());
        filterHcContext.setPlate(params.getPlate());
        filterHcContext.setPlateType(params.getPlateType());
        filterHcContext.setCarName(params.getCarName());
        filterHcContext.setLicenseNo(params.getLicenseNo());
        filterHcContext.setCustomerManageList(queryBanRegister(params));
        filterHcContext.setSceneConfig(queryConfig());
        filterHcContext.setMainScene(params.getMainScene());
        filterHcContext.setChildScene(params.getChildScene());
        filterHcContext.setDeviceId(params.getDeviceId());
        filterHcContext.setOrderIds(params.getOrderIds());
        filterHcContext.setDriverCheckService(driverCheckService);
        return filterHcContext;
    }

    /**
     * 查下当前是否已经是禁止注册名单了
     * */
    public List<RiskCustomerManage> queryBanRegister(FilterParams params){
        if(StringUtils.isBlank(params.getDriverId()) && StringUtils.isBlank(params.getMobile()) && StringUtils.isBlank(params.getInvitePhone())){
            return new ArrayList<>();
        }
        CommonCustomerParam param = new CommonCustomerParam();
        param.setDriverId(params.getDriverId());
        param.setMobile(params.getMobile());
        param.setInvitePhone(params.getInvitePhone());
        List<RiskCustomerManage> customerManageList = this.riskCustomerService.getListByValueByGroup(param,new Date());
        return customerManageList;
    }

    /**
     * 获取配置信息
     * */
    private HcSceneConfig queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("hc_driver_risk");
            HcSceneConfig config = JSONObject.parseObject(configJson,HcSceneConfig.class);
            log.info("[][][][]获取汇川规则配置:{}",configJson);
            log.info("[][][][]获取汇川规则配置转换:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取汇川规则配置错误:",e);
        }
        return null;
    }

}
