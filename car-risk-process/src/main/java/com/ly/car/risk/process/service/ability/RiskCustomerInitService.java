package com.ly.car.risk.process.service.ability;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RiskCustomerInitService {

    private static final String KEY = "TC_CUSTOMER_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;

    //这个每天凌晨跑一次 或者只跑一次
    public void initCustomer(){
        List<RiskCustomerManage> riskCustomerManageList = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().gt("invalid_time",new Date())
                        .in("risk_type",1,2)
        );
        for(RiskCustomerManage manage : riskCustomerManageList){
            RBucket<String> bucket = redissonClient.getBucket(KEY + manage.getRiskType() + "_" + manage.getCustomerValue());
            if(manage.getTtl() == -1){
                bucket.set(DateUtil.date2String(manage.getInvalidTime()));
                continue;
            }
            //按天存储
            long daysBetween = getDaysBetween(new Date(), manage.getInvalidTime());
            if(daysBetween == 0){
                daysBetween = 1;
            }
            if(!bucket.isExists()){
                bucket.set(DateUtil.date2String(manage.getInvalidTime()),daysBetween, TimeUnit.DAYS);
            } else {
                Date redisDate = DateUtil.string2Date(bucket.get());
                if(redisDate.before(manage.getInvalidTime())){
                    bucket.set(DateUtil.date2String(manage.getInvalidTime()),daysBetween, TimeUnit.DAYS);
                }
            }
        }
    }

    //根据updateTime和invalidTime来判断是否新增或者删除
    public void incrementUpdate(){
        //查询过去一分钟  是否有过期的
        Date startDate = DateUtil.addMinute(new Date(),-1);
        Date endDate = new Date();
        List<RiskCustomerManage> riskCustomerManageList = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .between("invalid_time",startDate,endDate)
                        .in("risk_type",1,2)
        );
        for(RiskCustomerManage manage : riskCustomerManageList){
            //删除缓存
            log.info("[][][][]当前过期缓存{}", JsonUtils.json(manage));
            RBucket<String> bucket = redissonClient.getBucket(KEY + manage.getRiskType() + "_" + manage.getCustomerValue());
            bucket.delete();
        }
        List<RiskCustomerManage> insertCustomerList = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .between("update_time",startDate,endDate)
                        .gt("invalid_time",endDate)
                        .in("risk_type",1,2)
        );
        for(RiskCustomerManage manage : insertCustomerList){
            log.info("[][][][]当前新增缓存{}", JsonUtils.json(manage));
            RBucket<String> bucket = redissonClient.getBucket(KEY + manage.getRiskType() + "_" + manage.getCustomerValue());
            if(manage.getTtl() == -1){
                bucket.set(DateUtil.date2String(manage.getInvalidTime()));
                continue;
            }
            //按天存储
            long daysBetween = getDaysBetween(new Date(), manage.getInvalidTime());
            if(daysBetween == 0){
                daysBetween = 1;
            }
            if(!bucket.isExists()){
                bucket.set(DateUtil.date2String(manage.getInvalidTime()),daysBetween, TimeUnit.DAYS);
            } else {
                Date redisDate = DateUtil.string2Date(bucket.get());
                if(redisDate.before(manage.getInvalidTime())){
                    bucket.set(DateUtil.date2String(manage.getInvalidTime()),daysBetween, TimeUnit.DAYS);
                }
            }
        }


    }




    public static long getDaysBetween(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        long diff = cal2.getTimeInMillis() - cal1.getTimeInMillis();
        // 将相差毫秒数转换为绝对值
        diff = Math.abs(diff);

        return diff / (24 * 60 * 60 * 1000);
    }

    public static void main(String[] args) {
        Date date1 = new Date();
        Date date2 = DateUtil.addMinute(new Date(),100);
        long daysBetween = getDaysBetween(date1, date2);
        if(daysBetween == 0){
            daysBetween = 1;
        }
        System.out.println(daysBetween);
    }
}
