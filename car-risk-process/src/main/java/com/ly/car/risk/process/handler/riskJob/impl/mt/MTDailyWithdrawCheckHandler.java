package com.ly.car.risk.process.handler.riskJob.impl.mt;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.common.enums.RiskAlertApproveHandleResultEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveSceneEnum;
import com.ly.car.risk.common.enums.RiskAlertApproveTargetEnum;
import com.ly.car.risk.common.enums.RiskJobTypeEnum;
import com.ly.car.risk.process.handler.riskJob.MtAbstractRiskCheckHandler;
import com.ly.car.risk.process.model.riskJob.MTDailyWithdrawCheckResp;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskAlertApprove;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;

import cn.hutool.core.collection.CollUtil;

/**
 * Description of SupplierCouponCheckHandler
 *
 * <AUTHOR>
 * @date 2024/8/28
 * @desc
 */
@Service
public class MTDailyWithdrawCheckHandler extends MtAbstractRiskCheckHandler<MTDailyWithdrawCheckResp> {

    @Override
    public RiskJobTypeEnum support() {
        return RiskJobTypeEnum.MT_DAILY_WITHDRAW_LARGE;
    }

    @Override
    public List<RiskAlertApprove> doCheck() {
        String dayBegin = getDayBegin();
        String now = formatter.format(LocalDateTime.now());
        String checkThresholdVal = getMTCheckThreshold(support());
        if (StringUtils.isBlank(checkThresholdVal)) {
            LoggerUtils.info(logger, "未配置预警阈值，check结束");
            return null;
        }
        BigDecimal checkThreshold = new BigDecimal(checkThresholdVal);
        if (checkThreshold.compareTo(BigDecimal.ZERO) <= 0) {
            LoggerUtils.info(logger, "预警阈值不合理，check结束");
            return null;
        }

        List<MTDailyWithdrawCheckResp> checkResp = carMtDriverWithdrawalRecordMapper.checkDailyWithdrawLargeInfo(checkThreshold,dayBegin);

        if (CollUtil.isEmpty(checkResp)) {
            return null;
        }

        return checkResp.stream().peek(p -> {
            p.setStartTime(dayBegin);
            p.setEndTime(now);
        }).filter(p->!todayAlreadyAlert(p,dayBegin)).map(this::convert).collect(Collectors.toList());

    }


    private boolean todayAlreadyAlert(MTDailyWithdrawCheckResp checkResp, String dayBegin) {
        List<RiskAlertApprove> approveList = approveMapper.findRecentlyAlertRecord(RiskAlertApproveTargetEnum.DRIVER.getCode(),checkResp.getCarNum(),
                RiskAlertApproveSceneEnum.MT.getCode(), RiskJobTypeEnum.MT_DAILY_WITHDRAW_LARGE.name(),RiskAlertApproveHandleResultEnum.SAFE.getCode(),
                dayBegin);
        return CollUtil.isNotEmpty(approveList);
    }

    private RiskAlertApprove convert(MTDailyWithdrawCheckResp checkResp) {
        Date now = new Date();
        RiskAlertApprove alertApprove = new RiskAlertApprove();
        alertApprove.setCode(UUID.generateFormatedTimeBasedUUID());
        alertApprove.setLevel(1);
        alertApprove.setTarget(RiskAlertApproveTargetEnum.DRIVER.getCode());
        alertApprove.setTargetValue(checkResp.getCarNum());
        alertApprove.setAlertScene(RiskAlertApproveSceneEnum.MT.getCode());
        alertApprove.setAlertStrategy(RiskJobTypeEnum.MT_DAILY_WITHDRAW_LARGE.name());
        alertApprove.setAlertContent(JSON.toJSONString(checkResp));
        alertApprove.setHandleResult(RiskAlertApproveHandleResultEnum.TODO.getCode());
        alertApprove.setAlertTime(now);
        alertApprove.setCreateTime(now);
        alertApprove.setUpdateTime(now);
        alertApprove.setCreateUser(support().name() + "_CHECK");
        approveMapper.insertSelective(alertApprove);

        alertApprove.setNoticeContent(getContentFormApprove(alertApprove));
        return alertApprove;
    }

    @Override
    public String getContentFormApprove(RiskAlertApprove approve) {
        String alertContent = approve.getAlertContent();
        if (StringUtils.isBlank(alertContent)) {
            return StringUtils.EMPTY;
        }
        MTDailyWithdrawCheckResp resp = JSON.parseObject(alertContent, new TypeReference<MTDailyWithdrawCheckResp>() {
        });
        StringBuilder sb = doGetContentFormApprove(approve,resp);
        return sb.toString();
    }

    @Override
    public  String getDesc(RiskAlertApprove approve, MTDailyWithdrawCheckResp resp) {
        StringBuilder sb = new StringBuilder();
        sb.append(RiskJobTypeEnum.getDescByCode(approve.getAlertStrategy()));
        sb.append(String.format("(司机:%s,车牌:%s,提现金额:%s,时间:%s-%s)", resp.getDriverId(), resp.getCarNum(), resp.getAmount(),resp.getStartTime(), resp.getEndTime()));
        return sb.toString();
    }

}