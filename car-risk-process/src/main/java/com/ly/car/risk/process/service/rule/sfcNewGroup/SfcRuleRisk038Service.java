package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户预定
 * 2-2
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk038Service extends FilterSfcHandler{

    private static final String ruleNo = "038";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][SfcRuleRisk038Service][][]前置判断已通过，进入规则038判断");
        if(context.getOrderLimitList() != null && context.getOrderLimitList().size() > 0){
            //写判断逻辑,下查询该用户的单子
            List<SfcRiskLimitData> cancelList = context.getOrderLimitList().stream().filter(data->data.getStatus().equals(1000)
                    && StringUtils.isNotBlank(data.getCancelReason()) && data.getCancelReason().equals("车主取消")).collect(Collectors.toList());
            List<SfcRiskLimitData> acceptList = context.getOrderLimitList().stream().filter(data -> data.getStatus().equals(20)).collect(Collectors.toList());

            if((cancelList != null && cancelList.size() > context.getSfcRiskRuleConfig().getOrderNum038()) ||
                    (acceptList != null && acceptList.size() > context.getSfcRiskRuleConfig().getOrderNum038())){
                List<String> orderIds = context.getOrderLimitList().stream().map(SfcRiskLimitData::getOrderId).collect(Collectors.toList());
                distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                log.info("[SfcRuleRisk038Service][doHandler][{}][{}]命中038规则",context.getMemberId(),context.getDriverCardNo());
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过038",null,null);
                context.getUiResult().setData(dto);
                context.getUiResult().setMsg("风控不通过");

                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
