package com.ly.car.risk.process.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.ly.car.risk.process.handler.job.WashJobFactory;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.service.task.WashTaskService;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.LoggerUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Description of WashTaskServiceImpl
 *
 * <AUTHOR>
 * @date 2024/7/11
 * @desc
 */
@Service
@Slf4j
public class WashTaskServiceImpl implements WashTaskService {

    private static final String RISK_ORDER_SYNC_TAG = "riskOrder";

    @Resource
    private CarRiskOrderDetailMapper orderDetailMapper;

    @Resource
    private RiskOrderManageService riskOrderManageService;

    @Resource(name = "riskOrderSyncProducer")
    private MqRiskProducer riskOrderSyncProducer;

    @Resource
    private WashJobFactory washJobFactory;


    @Override
    public void generalRiskOrderWash(String startTime, String endTime) {

        try {
            List<CarRiskOrderDetail> riskOrders = orderDetailMapper.findGeneralRiskOrder(startTime, endTime);
            LoggerUtils.info(log, "清洗出风险订单:{}条", riskOrders.size());
            Map<String, RiskOrderManage> manageOrders = new HashMap<>();
            for (CarRiskOrderDetail riskOrder : riskOrders) {
                RiskOrderManage manage = manageOrders.getOrDefault(riskOrder.getOrderSerialNo(), new RiskOrderManage());
                manageOrders.put(riskOrder.getOrderSerialNo(), manage);
                manage.setOrderId(riskOrder.getOrderSerialNo());
                manage.setIsRisk(1);
                String riskType = riskOrder.getWashExt();
                if ("025".equals(riskType)) {
                    manage.setRiskType(3);
                    manage.setRuleNo("025");
                } else if ("026".equals(riskType)) {
                    manage.setRiskType(5);
                    manage.setRuleNo(StringUtils.isBlank(manage.getRuleNo()) ? "026" : manage.getRuleNo() + ",026");
                } else if ("027".equals(riskType)) {
                    manage.setRiskType(21);
                    manage.setRuleNo(StringUtils.isBlank(manage.getRuleNo()) ? "027" : manage.getRuleNo() + ",027");
                }
                riskOrderManageService.addRiskOrder(manage);
//                riskOrderSyncProducer.send(car_risk_order_sync_mng, JSON.toJSONString(manage),0L);
            }
            LoggerUtils.info(log, "清洗结束");
        } catch (Exception e) {
            LoggerUtils.error(log, "风控订单清洗过程中发生异常", e);
        }
    }


    @Override
    public void abnormalAmountRiskOrder(String startTime, String endTime) {
        try {
            List<CarRiskOrderDetail> riskOrders = orderDetailMapper.findAbnormalAmountRiskOrder(startTime, endTime);
            LoggerUtils.info(log, "清洗出风险订单:{}条", riskOrders.size());
            
            Boolean zc001Switch = exeAble("zc-001");
            Boolean zc002Switch = exeAble("zc-002");
            Boolean zc007Switch = exeAble("zc-007");
            
            for (CarRiskOrderDetail riskOrder : riskOrders) {
                RiskOrderManage manage = new RiskOrderManage();
                manage.setOrderId(riskOrder.getOrderSerialNo());
                manage.setIsRisk(1);
                String riskType = riskOrder.getWashExt();
                if ("zc-001".equals(riskType)) {
                    if (Objects.equals(zc001Switch, false)) {
                        continue;
                    }
                    manage.setRiskType(21);
                    manage.setRuleNo("zc-001");
                    riskOrderManageService.addRiskOrder(manage);
                } else if ("zc-002".equals(riskType)) {
                    if (Objects.equals(zc002Switch, false)) {
                        continue;
                    }
                    manage.setRiskType(22);
                    manage.setRuleNo("zc-002");
                    riskOrderManageService.addRiskOrder(manage);
                } else if ("zc-007".equals(riskType)) {
                    if (Objects.equals(zc007Switch, false)) {
                        continue;
                    }
                    manage.setRiskType(21);
                    manage.setRuleNo("zc-007");
                    riskOrderManageService.addRiskOrder(manage);
                }
//                riskOrderSyncProducer.send(car_risk_order_sync_mng, JSON.toJSONString(manage),0L);
            }
            LoggerUtils.info(log, "清洗结束");
        } catch (Exception e) {
            LoggerUtils.error(log, "风控订单清洗过程中发生异常", e);
        }
    }

    @Override
    public void offlineJobSummary(String startTime, String endTime) throws ExecutionException, InterruptedException {
        washJobFactory.wash(startTime,endTime);
    }
    
    public Boolean exeAble(String name) {
        String washJobConfig = ConfigCenterService.getDefault("WASH_JOB_CONFIG", "{}");
        Object object = JSON.parseObject(washJobConfig).get(name);
        if (object == null) {
            return true;
        }
        return (boolean) object;
    }
}