package com.ly.car.risk.process.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName WorkWeiXinRobotUtils
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 10:41
 * @Version 1.0
 */
@Slf4j
@Component
public class WorkWeiXinRobotUtils {

    /**
     * 发送文本消息
     *
     * @param message
     * @param needCueAll
     * @param userPhone
     */
    public static void sendTextMessage(String message, boolean needCueAll, String userPhone, String workId) {
        Map<String, Object> textMap = new HashMap<>();
        textMap.put("content", message);
        if(StringUtils.isNotBlank(workId)){
            textMap.put("mentioned_list", Arrays.asList(workId));
        }
        else if (needCueAll) {
            textMap.put("mentioned_list", Arrays.asList("@all"));
        } else {
            textMap.put("mentioned_mobile_list", Arrays.asList(userPhone));
        }

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("msgtype", "text");
        paramsMap.put("text", textMap);

        log.info("[][][][][]request:" + JSONObject.toJSONString(paramsMap));
        String responseJson = OkHttpClientUtil.getInstance().post(
                "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a120a939-f004-4dfa-b439-9dd621ad8ceb",
                JSONObject.toJSONString(paramsMap),null
        );
        log.info("response:" + responseJson);
    }

}
