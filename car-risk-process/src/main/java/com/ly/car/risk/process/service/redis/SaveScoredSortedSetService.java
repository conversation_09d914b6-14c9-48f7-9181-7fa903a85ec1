package com.ly.car.risk.process.service.redis;
import java.math.BigDecimal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.component.FastjsonCodec;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SaveScoredSortedSetService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    /**
     * @param key 不同维度的key
     * @param expireTime 过期时间
     * @param value 存入洗好数据的实体
     * @param finishTime 完单时间
     * */
    public void save(String key,long expireTime,Object value,long finishTime){
        RScoredSortedSet<String> set = redissonClient.getScoredSortedSet(key);
        // 序列化
        if (value instanceof String) {
            set.remove(value);
            set.add(finishTime, (String) value);
            LoggerUtils.info(log,"存入redis值字符串:{}，key:{}",value,key);
        } else {
            set.remove(JSON.toJSONString(value));
            LoggerUtils.info(log,"存入redis值字符串:{}，key:{}",JSON.toJSONString(value),key);
            set.add(finishTime, JSON.toJSONString(value));
        }
        //重新给个过期时间
        redissonClient.getKeys().expire(key, expireTime, TimeUnit.SECONDS);
    }

    public String get(String key){
        RScoredSortedSet<Object> set = redissonClient.getScoredSortedSet(key);

        for(ScoredEntry<Object> obj : set.entryRange(0,10)){
            log.info("[][][][]获取redis值:{}",obj.getValue());
            log.info("[][][][]序列化后的值：{}", JSONObject.parse(obj.getValue().toString()));
            OrderRiskContext context = JSONObject.parseObject(obj.getValue().toString(),OrderRiskContext.class);
            log.info("[][][][]序列化后的值：{}", JsonUtils.json(context));
        }
        return set.getName();
    }

    /**
     * @param key 不同维度的key
     * @param expireTime 过期时间
     * @param value 存入洗好数据的实体
     * @param actionTime 轮询时间,权重
     * */
    public void saveString(String key,long expireTime,Object value,long actionTime){
        RScoredSortedSet<String> set = redissonClient.getScoredSortedSet(key);
        // 序列化
        if (value instanceof String) {
            log.info("[SaveScoredSortedSetService][][][]存入redis订单值:{}",JSON.toJSONString(value));
            set.remove(value);
            set.add(actionTime, (String) value);
        } else {
            set.remove(JSON.toJSONString(value));
            log.info("[SaveScoredSortedSetService][][][]存入redis值:{}",JSON.toJSONString(value));
            set.add(actionTime, JSON.toJSONString(value));
        }
//        redissonClient.getKeys().expire(key, expireTime, TimeUnit.SECONDS);
    }

    public String set(String key){
        RScoredSortedSet<String> set = redissonClient.getScoredSortedSet(key);
        OrderRiskContext orderRiskContext = new OrderRiskContext();
        orderRiskContext.setOrderId("YNC");
        orderRiskContext.setFinishTime("");
        orderRiskContext.setMemberId("123456");
        orderRiskContext.setUnionId("DLAK");
        orderRiskContext.setDriverCardNo("Su123");
        orderRiskContext.setStartLat(new BigDecimal("0"));
        orderRiskContext.setStartLng(new BigDecimal("0"));
        orderRiskContext.setEndLat(new BigDecimal("0"));
        orderRiskContext.setEndLng(new BigDecimal("0"));
        orderRiskContext.setTotalAmount(new BigDecimal("0"));
        orderRiskContext.setEstimateKilo(new BigDecimal("0"));
        orderRiskContext.setActualKilo(new BigDecimal("0"));
        orderRiskContext.setEstimateDuration(0);
        orderRiskContext.setActualDuration(0);
        orderRiskContext.setIntervalTime(0);
        log.info("[][][][]存入数据:{}",JSON.toJSONString(orderRiskContext));


        // 序列化
//        if (orderRiskContext instanceof String) {
//            set.remove(orderRiskContext);
//            set.add(1995l, (String) orderRiskContext);
//        } else {
            set.remove(JSON.toJSONString(orderRiskContext));
            set.add(1995l, JSON.toJSONString(orderRiskContext));
//        }
        //重新给个过期时间
        redissonClient.getKeys().expire(key, 20, TimeUnit.SECONDS);
        return "success";
    }
}
