package com.ly.car.risk.process.controller.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskDataMapper;
import com.ly.car.risk.process.service.dto.task.DriverCancelOrder;
import com.ly.dal.util.DateUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("riskDriverLevel")
@RestController
@Slf4j
public class RiskDriverTask {

    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private RiskDataMapper           riskDataMapper;

    @RequestMapping("init")
    public void init(){

        List<DriverCancelOrder> driverCancelOrders = riskDataMapper.queryAllRiskDriver();

        List<String> midLevelDriverList = sfcOrderMapper.queryMidRiskDriver();
        midLevelDriverList.addAll(driverCancelOrders
                .stream()
                .filter(d -> Objects.equals(d.getRiskLevel(), 1)).map(DriverCancelOrder::getDriver)
                .collect(Collectors.toList()));
        midLevelDriverList = midLevelDriverList.stream().distinct().collect(Collectors.toList());
        log.info("[][][][]当前高危司机任务统计司机数{}",midLevelDriverList.size());
        for(String driver : midLevelDriverList){
            String KEY = "risk:level:driver:";
            redissonClient.getBucket(KEY+driver).set("mid",24, TimeUnit.HOURS);
        }

        //今天放过
        if(new Date().before(DateUtil.string2Date("2024-01-20 00:00:01"))){
            return;
        }
        List<String> highLevelDriverList = sfcOrderMapper.queryHighRiskDriver();
        highLevelDriverList.addAll(driverCancelOrders
                .stream()
                .filter(d -> Objects.equals(d.getRiskLevel(), 2)).map(DriverCancelOrder::getDriver)
                .collect(Collectors.toList()));
        highLevelDriverList = highLevelDriverList.stream().distinct().collect(Collectors.toList());

        if(CollectionUtils.isEmpty(highLevelDriverList)){
            return;
        }
        
        List<RiskCustomerManage> riskCustomerManageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .in("customer_value",highLevelDriverList)
                .gt("invalid_time",new Date())
        );
        Map<String,RiskCustomerManage> riskCustomerManageMap = new HashMap<>();
        if(!riskCustomerManageList.isEmpty()){
            riskCustomerManageMap = riskCustomerManageList.stream().collect(Collectors.toMap(RiskCustomerManage::getCustomerValue, v -> v, (old, cur) -> cur));
        }
        for(String driver : highLevelDriverList){
            RiskCustomerManage haveCustomer = riskCustomerManageMap.get(driver);
            if(haveCustomer != null && haveCustomer.getInvalidTime().after(DateUtil.addDay(new Date(),2))){
                continue;
            }
            RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
            riskCustomerManage.setRiskType(1);//直接用1v1和全局拉黑的模式，1-黑名单 7-1v1黑名单
            riskCustomerManage.setCustomerType(6);//司机车牌号等等信息
            riskCustomerManage.setCustomerValue(driver);
            riskCustomerManage.setStatus(1);
            riskCustomerManage.setTtl(365);
            riskCustomerManage.setOptionType(1);
            riskCustomerManage.setCreateUser("哈啰高危司机策略拉黑");
            riskCustomerManage.setOptionName("哈啰高危司机策略拉黑");
            riskCustomerManage.setRiskRemark("哈啰高危司机");
            riskCustomerManage.setCreateTime(new Date());
            riskCustomerManage.setUpdateTime(new Date());
            riskCustomerManage.setBindUser("");
            riskCustomerManage.setBindOrder("");
            riskCustomerManage.setInvalidTime(DateUtil.addDay(new Date(),365));
            riskCustomerManage.setSupplierName("哈啰顺风车");
            this.riskCustomerManageMapper.insert(riskCustomerManage);
        }
    }



}
