package com.ly.car.risk.process.repo.risk.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
public class HcOrderCheck extends Model<HcOrderCheck> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String orderId;
    private String tcOrderId;
    private String refId;
    private String refName;
    private Integer productId;
    private Integer cityId;
    private String cityName;
    private String distributorOrderId;
    private Integer isRisk;
    private String riskRule;
    private Integer riskType;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private Integer status;

}
