package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.param.TianMianRiskParam;
import com.ly.car.risk.process.api.rsp.TianMianCommonRsp;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class TianMianRiskClient {

    @Value("${config.urls.queryRiskUrl}")
    private String reqUrl;

    //高位风险查询
    public TianMianCommonRsp queryHighRisk(TianMianRiskParam param){
        log.info("[][TianMianRiskClient][][]天冕请求参数{}", JsonUtils.json(param));
        String response = OkHttpClientUtil.getInstance().post(reqUrl,JsonUtils.json(param),2l, TimeUnit.SECONDS);
        log.info("[][TianMianRiskClient][][]天冕返回结果{}", response);
        TianMianCommonRsp rsp = JSONObject.parseObject(response,TianMianCommonRsp.class);
        return rsp;
    }
}
