package com.ly.car.risk.process.service.dto;

import lombok.Data;

@Data
public class CommonRuleConfig {

    private Boolean aq006;
    private Boolean aq007;
    private Boolean aq001;
    private Boolean aq002;
    private Boolean aq003;
    private Boolean aq004;
    private Boolean aq008;
    private Boolean aq009;
    private Boolean aq011;
    private Boolean aq012;
    private Boolean aq013;
    private Boolean aq014;
    private Boolean aq015;
    private Boolean aq016;
    private Boolean aq017;
    private Boolean aq018;
    private Boolean aq019;
    private Boolean aq020;
    private Boolean aq010;
}


