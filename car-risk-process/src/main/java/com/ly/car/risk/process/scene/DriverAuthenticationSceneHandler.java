package com.ly.car.risk.process.scene;

import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.rule.mtGroup.MtDriverCarOwnerService;
import com.ly.car.risk.process.service.rule.mtGroup.MtDriverIdCardService;
import com.ly.car.risk.process.service.rule.mtGroup.MtDriverScoreService;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.utils.StrategyUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DriverAuthenticationSceneHandler implements SpecialSceneHandler {

    @Resource
    private MtDriverIdCardService mtDriverIdCardService;
    @Resource
    private MtDriverScoreService mtDriverScoreService;
    @Resource
    private MtDriverCarOwnerService mtDriverCarOwnerService;

    @Override
    public List<StrategySceneEnum> supportScene() {
        return Stream.of(StrategySceneEnum.DRIVER_AUTHENTICATION).collect(Collectors.toList());
    }

    @Override
    public RiskSceneResult check(UnifyCheckRequest request) {

        RiskSceneResult idCheckResult = mtDriverIdCardService.idCheck(request);
        if (null != idCheckResult && idCheckResult.isRiskFlag()) {
            return idCheckResult;
        }

        RiskSceneResult scoreCheckResult = mtDriverScoreService.scoreCheck(request);
        if (null != scoreCheckResult && scoreCheckResult.isRiskFlag()) {
            return scoreCheckResult;
        }

        RiskSceneResult carOwnerCheckResult = mtDriverCarOwnerService.carOwnerCheck(request);
        if (null != carOwnerCheckResult && carOwnerCheckResult.isRiskFlag()) {
            return carOwnerCheckResult;
        }

        return null;
    }
}
