package com.ly.car.risk.process.model.riskJob;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description of IvrCallCheckResp
 *
 * <AUTHOR>
 * @date 2025/3/25
 * @desc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IvrCallCheckResp {

    /** 调用次数 */
    private long callTimes;

    /** 配置阈值 */
    private long checkLimit;
}