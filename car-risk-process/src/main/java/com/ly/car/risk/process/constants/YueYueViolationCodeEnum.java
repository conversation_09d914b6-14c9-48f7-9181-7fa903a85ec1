package com.ly.car.risk.process.constants;

public enum YueYueViolationCodeEnum {

    SHUADAN_EX("7","刷单","司机刷单"),
    AMOUNT_EX("206","金额异常","费用异常"),
    GUIJI_EX("601","轨迹异常","行程轨迹异常"),
    QUICK_EX("215","快速划单","虚假完单或订单行为异常"),
    COST_EX("10","费用异议","多收取附加费用"),
    ACCOUNT_EX("621","账户异常","司机账号虚假或异常"),
    ;

    private String code;
    private String sceneName;
    private String desc;

    YueYueViolationCodeEnum(String code,String sceneName,String desc){
        this.code = code;
        this.sceneName = sceneName;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
