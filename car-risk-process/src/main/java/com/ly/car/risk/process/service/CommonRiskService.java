package com.ly.car.risk.process.service;

import com.ly.car.http.HttpUtils;
import com.ly.car.risk.process.api.CommonRiskApi;
import com.ly.car.risk.process.kafka.param.MarketingRiskReq;
import com.ly.car.risk.process.kafka.rsp.MarketingRiskRsp;
import com.ly.car.utils.IdUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class CommonRiskService {

    @Resource
    private CommonRiskApi commonRiskApi;

    /**
     * @Param channelType 渠道类型
     * */
    public MarketingRiskRsp queryRiskTag(MarketingRiskReq req,Integer channelType){
        if(channelType == 0){
            req.setChannel("201");
        } else if(channelType == 1){
            req.setChannel("852");
        } else {
            req.setChannel("301");
        }
        req.setAccessId("1565604246704529408");
        req.setActionTime(DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
        req.setIp("");
        req.setTraceId(IdUtils.getUUID());
        return commonRiskApi.getMarketingRiskRsp(req);
    }

}
