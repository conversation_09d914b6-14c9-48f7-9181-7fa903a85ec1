package com.ly.car.risk.process.controller.task;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.dal.util.DateUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@RequestMapping("aliTask")
@RestController
public class AliCouponTask {

    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;

    @RequestMapping("start")
    public UiResult aliCouponRisk(){
        List<String> driverList = this.sfcOrderMapper.queryRiskDriver();
        for (String str : driverList){
            RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
            riskCustomerManage.setRiskType(1);//直接用1v1和全局拉黑的模式，1-黑名单 7-1v1黑名单
            riskCustomerManage.setCustomerType(6);//司机车牌号等等信息
            riskCustomerManage.setCustomerValue(str);
            riskCustomerManage.setStatus(1);
            riskCustomerManage.setTtl(365);
            riskCustomerManage.setOptionType(1);
            riskCustomerManage.setCreateUser("支付宝刷券策略拉黑");
            riskCustomerManage.setOptionName("支付宝刷券策略拉黑");
            riskCustomerManage.setRiskRemark("支付宝刷券");
            riskCustomerManage.setCreateTime(new Date());
            riskCustomerManage.setUpdateTime(new Date());
            riskCustomerManage.setBindUser("");
            riskCustomerManage.setBindOrder("");
            riskCustomerManage.setInvalidTime(DateUtil.addDay(new Date(),365));
            this.riskCustomerManageMapper.insert(riskCustomerManage);
        }
        return UiResult.ok();
    }
}
