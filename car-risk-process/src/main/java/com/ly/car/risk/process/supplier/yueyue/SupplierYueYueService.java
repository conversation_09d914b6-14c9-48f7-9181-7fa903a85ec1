package com.ly.car.risk.process.supplier.yueyue;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;
import com.ly.car.risk.process.utils.GsonHelper;
import com.ly.car.risk.process.utils.RsaUtil;
import com.ly.car.toolset.web.HttpHelper;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class SupplierYueYueService {

    @Value("${spring.profiles.active}")
    private String env;

    public YueYueDisposeRsp judgeDriver(YueYueRiskSyncParam yueYueRiskSyncParam){
        try {
            String url = YueYueEnv.get(env).host + YueYueUrlConstant.DISPOSE_SYNC.url;
            yueYueRiskSyncParam.setApp_id(YueYueEnv.get(env).appId);
            yueYueRiskSyncParam.setTimestamp(String.valueOf(System.currentTimeMillis()/1000L));
            String result = postNew(url, yueYueRiskSyncParam, YueYueEnv.get(env).privateKey);
            return JSONObject.parseObject(result,YueYueDisposeRsp.class);
        } catch (Exception e){
            log.error("[][][][]约约处罚下发报错",e);
        }
        return null;

    }

    public YueYueDisposeRsp appealDriver(YueYueAppealParam param){
        String url = YueYueEnv.get(env).host + YueYueUrlConstant.APPEAL_SYNC.url;
        param.setApp_id(YueYueEnv.get(env).appId);
        param.setTimestamp(String.valueOf(System.currentTimeMillis()/1000L));
        String result = postNew(url, param, YueYueEnv.get(env).privateKey);
        return JSONObject.parseObject(result,YueYueDisposeRsp.class);
    }


    /**
     * @param url
     * @return
     */
    public String postNew(String url, Object obj, String privateKey) {
        Map<String, String> paramMap = new HashMap<>();
        String signData = TextConfoundUtil.confoundSignData(obj);
        try {
            String sign = RsaUtil.signByPrivateKey(signData, privateKey);
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            if(obj instanceof YueYueRiskSyncParam){
                YueYueRiskSyncParam param = (YueYueRiskSyncParam) obj;
                String eventInfoStr = GsonHelper.convertJson(param.getEvent_info()).replace("\\", "").replace("\\\\", "");
                String orderInfoStr = GsonHelper.convertJson(param.getOrder_info()).replace("\\", "").replace("\\\\", "");
                String violationInfoStr = GsonHelper.convertJson(param.getViolation_info()).replace("\\", "").replace("\\\\", "");
                String disposeInfoStr = GsonHelper.convertJson(param.getDispose_infos()).replace("\\", "").replace("\\\\", "");
                param.setEvent_info(null);
                param.setOrder_info(null);
                param.setViolation_info(null);
                param.setDispose_infos(null);

                paramMap = GsonHelper.convert(GsonHelper.convertJson(param), type);
                paramMap.put("event_info",eventInfoStr);
                paramMap.put("order_info",orderInfoStr);
                paramMap.put("violation_info",violationInfoStr);
                paramMap.put("dispose_infos",disposeInfoStr);
            } else if(obj instanceof YueYueAppealParam){
                YueYueAppealParam param = (YueYueAppealParam) obj;
                String orderInfoStr = GsonHelper.convertJson(param.getOrder_info()).replace("\\", "").replace("\\\\", "");
                String violationInfoStr = GsonHelper.convertJson(param.getViolation_info()).replace("\\", "").replace("\\\\", "");
                String appealInfoStr = GsonHelper.convertJson(param.getAppeal_info()).replace("\\", "").replace("\\\\", "");
                param.setOrder_info(null);
                param.setViolation_info(null);
                param.setAppeal_info(null);
                paramMap = GsonHelper.convert(GsonHelper.convertJson(param), type);
                paramMap.put("order_info",orderInfoStr);
                paramMap.put("violation_info",violationInfoStr);
                paramMap.put("appeal_info",appealInfoStr);
            }
            paramMap.put("sign", sign);
        } catch (Exception e) {
            log.error("[][][][]约约参数报错",e);
            return null;
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/x-www-form-urlencoded");
        try {
            log.info("[][][][]约约请求参数{}",JsonUtils.json(paramMap));
//            String result = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(paramMap),headerMap,1L);
            String result = HttpHelper.request(HttpHelper.HTTP_METHOD_POST, url, paramMap, headerMap, null);
            log.info("[][][][]约约返回{}",result);
            return result;
        }catch (Exception e){
            log.error("[][][][]约约请求报错",e);
        }
        return null;
    }

}
