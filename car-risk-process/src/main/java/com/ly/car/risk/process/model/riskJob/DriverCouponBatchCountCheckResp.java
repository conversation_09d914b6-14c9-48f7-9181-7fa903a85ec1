package com.ly.car.risk.process.model.riskJob;

import lombok.Data;

/**
 * Description of DriverCouponCountCheckResp
 * 当日单个司机核销单张优惠券张数较大
 * 当日单个司机核销单个批次号的优惠券张数大于等于n张
 *
 * <AUTHOR>
 * @date 2024/8/28
 * @desc
 */
@Data
public class DriverCouponBatchCountCheckResp {

    private int orderType;

    private String carNum;

    private String batchNo;

    private int count;

    private String startTime;

    private String endTime;
}