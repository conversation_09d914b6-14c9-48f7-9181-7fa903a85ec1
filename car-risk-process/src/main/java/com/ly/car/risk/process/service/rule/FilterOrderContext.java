package com.ly.car.risk.process.service.rule;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.dto.SfcRiskRuleConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FilterOrderContext {
    private List<OrderRiskContext> userContextList;
    private List<OrderRiskContext> driverContextList;
    private List<SfcOrderNumDTO> cancelOrderNumList;
    private List<SfcRiskLimitData> orderLimitList;
    private Map<String,Boolean> needRuleMap;
    private SfcRiskRuleConfig sfcRiskRuleConfig;
    private String memberId;
    private String driverCardNo;
    private String unionId;
    private UiResult uiResult = UiResult.ok();

    private FilterParams filterParams;
}