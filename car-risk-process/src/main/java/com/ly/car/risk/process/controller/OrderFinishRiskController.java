package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.controller.params.ChannelRiskReq;
import com.ly.car.risk.process.service.FinishOrderRiskService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/finish/risk")
public class OrderFinishRiskController {

    @Resource
    private FinishOrderRiskService finishOrderRiskService;

    @PostMapping("/action")
    public String findBadDebts(@RequestBody ChannelRiskReq req){
        return finishOrderRiskService.action();
    }

}
