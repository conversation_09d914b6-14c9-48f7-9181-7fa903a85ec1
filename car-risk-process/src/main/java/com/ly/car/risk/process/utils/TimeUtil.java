package com.ly.car.risk.process.utils;

import com.ly.dal.util.DateUtil;

import java.time.LocalTime;
import java.util.Date;

public class TimeUtil {

    public static Long currentDayMs(){
        String strDate = DateUtil.date2String(new Date()).split(" ")[0]+" 00:00:00";
        return DateUtil.string2Date(strDate).getTime();
    }

    public static Date currentDay(){
        String strDate = DateUtil.date2String(new Date()).split(" ")[0]+" 00:00:00";
        return DateUtil.string2Date(strDate);
    }

    public static Long threeDayMs(){
        return DateUtil.addDay(new Date(),-3).getTime();
    }

    public static Date threeDay(){
        return DateUtil.addDay(new Date(),-3);
    }

    /**
     * 24h
     * */
    public static Date oneDay(){
        return DateUtil.addDay(new Date(),-1);
    }

    public static Long initMs(){
        return DateUtil.string2Date("2000-01-01 00:00:00").getTime();
    }

    /**
     * 1h
     * */
    public static Date oneHour(){
        return DateUtil.addHour(new Date(),-1);
    }

    /**
     * 1h
     * */
    public static Date fortyHour(){
        return DateUtil.addHour(new Date(),-48);
    }

    /**
     *30分钟
     * */
    public static Date thirtyMin(Integer minute){
        return DateUtil.addMinute(new Date(),-minute);
    }

    public static Date thirtyDay(){
        return DateUtil.addDay(new Date(),-30);
    }

    /**
     * 时/分的判断
     * @param beginSfTime
     * @param endSfTime
     * @return
     */
    public static boolean checkTime(String beginSfTime,String endSfTime){
        // 起始时间和结束时间，可能跨天
        LocalTime startTime = LocalTime.parse(beginSfTime);
        LocalTime endTime = LocalTime.parse(endSfTime);
        // 需要判断的时间
        LocalTime now = LocalTime.now();

        // 判断当前时间是否在指定范围内
        boolean isInRange;
        if (startTime.isBefore(endTime)) {
            // 正常情况，如：9:00-18:00
            isInRange = !now.isBefore(startTime) && !now.isAfter(endTime);
        } else {
            // 跨天的情况，如：22:00-06:00
            isInRange = !now.isBefore(startTime) || !now.isAfter(endTime);
        }
        return isInRange;
    }
    public static void main(String[] args) {
        System.out.println(threeDayMs());
    }

}
