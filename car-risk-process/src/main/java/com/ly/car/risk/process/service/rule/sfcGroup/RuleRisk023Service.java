package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当天用户完单小于2公里的订单大于2单且完单司机车牌一致
 * 且用户近3天完单司机一致的订单大于等于7单
 * 2-2
 * */
@Service
@Slf4j
public class RuleRisk023Service extends FilterRuleChain {

    private static final String ruleNo = "023";

    @Resource
    private RuleRisk024Service ruleRisk024Service;

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk022Service][][]前置判断已通过，进入规则023判断{}",orderContext.getMemberId(),orderContext.getDriverCardNo());
        this.next(ruleRisk024Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }
        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }

        //执行此规则就给个执行标记
        orderContext.getNeedRuleMap().put(ruleNo,true);

        //获取用户当天完单
        List<OrderRiskContext> orderRiskContextList = orderContext.getUserContextList().stream()
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.currentDay()))
                .filter(context-> new BigDecimal(CoordUtil.getDistance(context.getStartLng(),context.getStartLat(),context.getEndLng(),context.getEndLat()))
                        .compareTo(new BigDecimal(orderContext.getSfcRiskRuleConfig().getDistance023())) < 0)
                .collect(Collectors.toList());
        //获取用户近3天完单
        List<OrderRiskContext> orderRiskContextListThreeDay = orderContext.getUserContextList().stream()
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.threeDay()))
                .collect(Collectors.toList());

        //判断完单是否大于2两单，小于等于两单直接下个规则
        if(!CollectionUtils.isEmpty(orderRiskContextList) && orderRiskContextList.size() > orderContext.getSfcRiskRuleConfig().getOrderNum023()){
            //判断这些订单的司机是否一致
            List<String> stringListBefore = orderRiskContextList.stream()
                    .map(OrderRiskContext::getDriverCardNo)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> stringListAfter = orderRiskContextListThreeDay.stream()
                    .map(OrderRiskContext::getDriverCardNo)
                    .distinct()
                    .collect(Collectors.toList());
            String orderIds = StringUtils.join(stringListAfter,",");
            if(stringListBefore.size() == 1 && stringListAfter.size() == 1 && orderRiskContextListThreeDay.size() >= orderContext.getSfcRiskRuleConfig().getOrderNumByDriver023()){
                log.info("[RuleRisk018Service][doHandler][{}][{}]命中023规则，关联用户为{},关联订单为{}"
                        ,orderContext.getMemberId(),orderContext.getDriverCardNo(),orderContext.getMemberId(), JsonUtils.json(orderIds));
                distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo,2,2,0,null, RiskLevelEnum.HIGH.getCode());
                UiResult result = UiResult.ok();
                result.setMsg("风控不通过");
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过023",null,null);
                result.setData(dto);
                orderContext.setUiResult(result);
//                return result;
            }
        }

        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
//            UiResult result = UiResult.ok();
//            result.setData("0");
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
