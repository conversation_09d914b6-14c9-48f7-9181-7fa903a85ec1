package com.ly.car.risk.process.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ly.car.risk.process.client.model.hellobike.HelloBikeSfcBlackDriverReqDTO;
import com.ly.car.risk.process.utils.HelloBikeUtils;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description of HelloBikeClient
 *
 * <AUTHOR>
 * @date 2024/5/20
 * @desc 哈啰客户端
 */
@Slf4j
@Service
public class HelloBikeClient {

    @Value("${config.urls.hellobikeBaseUrl}")
    private String helloBikeBaseUrl;

    @Value("${config.urls.hellobikeAppKey}")
    private String helloBikeAppKey;

    @Value("${config.urls.hellobikeSecret}")
    private String helloBikeSecret;


    public void doSyncBlack(int type, List<HelloBikeSfcBlackDriverReqDTO.BlackItem> subBlackList) {
        LoggerUtils.info(log, "哈啰黑名单同步开始,type:{}, blackListSize:{}", type, subBlackList.size());
        try {
            String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
            HelloBikeSfcBlackDriverReqDTO reqDTO = new HelloBikeSfcBlackDriverReqDTO();
            reqDTO.setMethod("sfc.black.driver");
            reqDTO.setAppKey(helloBikeAppKey);
            reqDTO.setTimestamp(timeStamp);
            reqDTO.setType(type);
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(reqDTO, SerializerFeature.SortField));
            List<TreeMap<String,String>> subBlackListStr = subBlackList.stream().map(p -> {
                TreeMap<String,String> treeMap = new TreeMap();
                treeMap.put("endTime",p.getEndTime());
                treeMap.put("partnerUserId",p.getPartnerUserId());
                treeMap.put("reason",p.getReason());
                treeMap.put("startTime",p.getStartTime());
                treeMap.put("vehiclePlateNum",p.getVehiclePlateNum());
                return treeMap;
            }).collect(Collectors.toList());
            jsonObject.put("blacklist",subBlackListStr);
            String sign = HelloBikeUtils.signTopRequest(jsonObject, helloBikeSecret);
            reqDTO.setBlacklist(subBlackList);
            reqDTO.setSign(sign);

            Map<String, String> header = new HashMap<>();
            header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
            LoggerUtils.info(log, "哈啰黑名单同步Url，req:{}", JSON.toJSONString(reqDTO));
            String resp = HttpUtils.postJson(helloBikeBaseUrl, reqDTO, header, 5, TimeUnit.SECONDS);
            LoggerUtils.info(log, "哈啰黑名单同步Url，resp:{}", resp);

        } catch (IOException e) {
            LoggerUtils.error(log, "哈啰黑名单同步，异常", e);
        }

    }
}