package com.ly.car.risk.process.utils;

import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.net.ssl.*;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/01/10.
 */
public class OkHttpClientUtil {
    public static OkHttpClientUtil instance = null;

    public OkHttpClient okHttpClient;

    OkHttpClientUtil() {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool())
                .build();
    }

    public static OkHttpClientUtil getInstance() {
        if (instance == null) {
            synchronized (OkHttpClientUtil.class) {
                if (instance == null) {
                    instance = new OkHttpClientUtil();
                }
            }

        }
        return instance;
    }

    public void asyncPost(String url, String requestBody, Map<String, String> headerMap, Consumer<String> success, Consumer<IOException> fail) {
        Request.Builder builder = new Request.Builder();
        if (!CollectionUtils.isEmpty(headerMap)) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                builder.header(entry.getKey(), entry.getValue());
            }
        }
        Request request = builder
                .url(url)
                .post(RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), requestBody))
                .build();

        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (fail != null) fail.accept(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (success != null) {
                    if (response.body() != null) {
                        success.accept(response.body().string());
                    } else {
                        success.accept(null);
                    }
                }
                response.close();
            }
        });
    }

    public static OkHttpClient getUnsafeOkHttpClient() {
        try {
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            // 过时方法，单构造参数方法过时，多传一个X509TrustManager就可以了
            //builder.sslSocketFactory(sslSocketFactory);
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);

            builder.hostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });
            builder.retryOnConnectionFailure(true)
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .writeTimeout(60, TimeUnit.SECONDS)
                    .connectionPool(new ConnectionPool());
            return builder.build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * post请求
     *
     * @param url
     * @param jsonData
     * @return
     */
    public static String post(OkHttpClient okHttpClient, String url, String jsonData) {
        Response response;
        try {
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), jsonData);
            Request request = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient.newCall(request).execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("请求失败");
        } finally {
            response = null;
        }
    }

    /**
     * post请求
     *
     * @param url
     * @param jsonData
     * @return
     */
    public String post(String url, String jsonData, Map<String, String> headerMap) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }

            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient.newCall(request).execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("请求失败");
        } finally {
            response = null;
        }
    }

    /**
     * post请求
     *
     * @param url
     * @param jsonData
     * @return
     */
    public String post(String url, String jsonData, Map<String, String> headerMap, Long timeOut) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }

            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient
                    .newBuilder()
                    .connectTimeout(timeOut, TimeUnit.SECONDS)
                    .readTimeout(timeOut, TimeUnit.SECONDS)
                    .writeTimeout(timeOut, TimeUnit.SECONDS)
                    .build()
                    .newCall(request)
                    .execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("请求失败");
        } finally {
            response = null;
        }
    }

    public String get(String url, Map<String, String> data, Map<String, String> headerMap) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }
            String params = "?";
            if(data != null && data.size() > 0){
                for(Map.Entry<String,String> entry : data.entrySet()){
                    if(StringUtils.isEmpty(entry.getValue())){
                        continue;
                    }
                    params = params+entry.getKey()+"="+entry.getValue()+"&";
                }
            }
            Request request = builder
                    .url(url + params)
                    .get()
                    .build();
            response = okHttpClient.newCall(request).execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("请求失败");
        } finally {
            response = null;
        }
    }

    public String get_ssl(String URL)
    {
        String result = null;
        try
        {
            Request request = new Request.Builder()
                    .url(URL)
                    .build();
            Call call = okHttpClient.newCall(request);

            Response response;

            response = call.execute();
            result = response.body().string();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * post请求
     *
     * @param url
     * @param jsonData
     * @return
     */
    public String post(String url, String jsonData, Long timeOut,TimeUnit timeUnit) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient
                    .newBuilder()
                    .connectTimeout(timeOut, timeUnit)
                    .readTimeout(timeOut, timeUnit)
                    .writeTimeout(timeOut, timeUnit)
                    .build()
                    .newCall(request)
                    .execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            response = null;
        }
    }
}
