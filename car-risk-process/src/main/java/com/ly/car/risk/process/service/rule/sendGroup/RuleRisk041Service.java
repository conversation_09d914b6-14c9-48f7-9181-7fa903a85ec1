package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.redis.OrderStatusDTO;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
* 当前用户近24小时取消次数大于3次，且历史近3天取消率大于90%
* */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk041Service extends FilterSendOrderHandler{

    private static final String ruleNo = "041";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        log.info("前置判断已通过，进入规则041判断{}", JsonUtils.json(context.getOrderStatusDTOList()));
        if(!context.getSpecialCarRuleConfig().getRule041onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            List<OrderStatusDTO> dtoList = context.getOrderStatusDTOList().stream()
                    .filter(dto -> dto.getJoinTime().after(TimeUtil.oneDay()))
                    .filter(dto -> dto.getStatus().equals(1000))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dtoList)) {
                if (dtoList.size() > context.getSpecialCarRuleConfig().getRule041CancelNum()) {
                    Integer cancelNum = context.getOrderStatusDTOList().stream()
                            .filter(dto -> dto.getJoinTime().after(TimeUtil.threeDay()))
                            .filter(dto -> dto.getStatus().equals(1000))
                            .collect(Collectors.toList()).size();
                    if (new BigDecimal(cancelNum)
                            .divide(new BigDecimal(context.getOrderStatusDTOList().size()),2,BigDecimal.ROUND_DOWN)
                            .compareTo(context.getSpecialCarRuleConfig().getRule041CancelRate()) > 0) {
                        context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
                        List<String> orderIds = context.getOrderStatusDTOList().stream().map(OrderStatusDTO::getOrderId)
                                .distinct()
                                .collect(Collectors.toList());
                        orderIds.add(context.getOrderId());
                        distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene()
                                , context.getChildScene(), 0, null, RiskLevelEnum.HIGH.getCode());

                        orderIds.remove(context.getOrderId());
                        if(StringUtils.isBlank(context.getRuleNo())){
                            context.setRuleNo(ruleNo);
                        } else {
                            context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                        }
                        riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                    }
                }
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
