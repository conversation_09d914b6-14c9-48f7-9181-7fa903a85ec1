package com.ly.car.risk.process.service.supplier.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.client.HelloBikeClient;
import com.ly.car.risk.process.client.model.hellobike.HelloBikeSfcBlackDriverReqDTO;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.supplier.HelloBikeService;
import com.ly.car.risk.process.utils.HelloBikeUtils;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description of HelloBikeServiceImpl
 *
 * <AUTHOR>
 * @date 2024/5/20
 * @desc
 */
@Service
@Slf4j
public class HelloBikeServiceImpl implements HelloBikeService {

    private static final FastDateFormat fullFmt = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    private static final String ALWAYS_BLACK_TIME = "2099-01-01 00:00:00";

    /** 供应商名称，用于查询风控名单表中Supplier_name 字段对应值 */
    private static final String HELLO_BIKE_SUPPLIER_NAME = "哈啰顺风车";

    /** 一次同步给哈啰的上限数量 */
    private static final int SPLIT_SIZE = 900;

    @Resource
    private RiskCustomerService riskCustomerService;

    @Resource
    private HelloBikeClient helloBikeClient;

    @Override
    public void syncBlack() {
        // 查询要同步的订单模式 1-增量 0-全量
        Integer syncType = Integer.parseInt(ConfigCenterService.getDefault("HELLO_BLACK_SYNC_TYPE", "1"));
        List<RiskCustomerManage> riskCustomerManages = null;
        if (syncType == 0) {
            riskCustomerManages = riskCustomerService.validAllRiskCarNoBySupplier(HELLO_BIKE_SUPPLIER_NAME);
        } else if (syncType == 1) {
            riskCustomerManages = riskCustomerService.validIncrRiskCarNoBySupplier(HELLO_BIKE_SUPPLIER_NAME);
        }

        if (CollUtil.isEmpty(riskCustomerManages)) {
            return;
        }
        // 这些订单，按照拉黑和解禁去分类. 如果失效时间早于当前时间，或者记录的状态是2，则是解禁
        Date now = new Date();
        Map<Integer, List<RiskCustomerManage>> groupedRecords = riskCustomerManages.stream()
                .collect(Collectors.groupingBy(record -> record.getInvalidTime().before(now) || record.getStatus() == 2 ? 2 : 1));

        List<RiskCustomerManage> whiteSyncList = groupedRecords.get(2);
        List<RiskCustomerManage> blackSyncList = groupedRecords.get(1);

        if (CollUtil.isNotEmpty(whiteSyncList)) {
            doSyncWhite(whiteSyncList);
        }

        if (CollUtil.isNotEmpty(blackSyncList)) {
            doSyncBlack(blackSyncList);
        }


    }

    private void doSyncWhite(List<RiskCustomerManage> whiteSyncList) {
        List<List<RiskCustomerManage>> riskListList = ListUtil.split(whiteSyncList, SPLIT_SIZE);

        for (List<RiskCustomerManage> riskList : riskListList) {
            List<HelloBikeSfcBlackDriverReqDTO.BlackItem> subBlackList = assembleWhiteList(riskList);
            doSync(2, subBlackList);
        }
    }

    private List<HelloBikeSfcBlackDriverReqDTO.BlackItem> assembleWhiteList(List<RiskCustomerManage> riskList) {
        List<HelloBikeSfcBlackDriverReqDTO.BlackItem> subBlackList = riskList.stream().map(riskCustomer -> {
            HelloBikeSfcBlackDriverReqDTO.BlackItem item = new HelloBikeSfcBlackDriverReqDTO.BlackItem();
            item.setVehiclePlateNum(riskCustomer.getCustomerValue());
            if (riskCustomer.getRiskType() == 7) {
                item.setPartnerUserId(HelloBikeUtils.getMd5Str(riskCustomer.getMemberId()));
            }
            return item;
        }).collect(Collectors.toList());
        return subBlackList;
    }

    private void doSyncBlack(List<RiskCustomerManage> blackSyncList) {
        List<List<RiskCustomerManage>> riskListList = ListUtil.split(blackSyncList, SPLIT_SIZE);

        for (List<RiskCustomerManage> riskList : riskListList) {
            List<HelloBikeSfcBlackDriverReqDTO.BlackItem> subBlackList = assembleBlackList(riskList);
            doSync(1, subBlackList);
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
            }
        }
    }

    private void doSync(int type, List<HelloBikeSfcBlackDriverReqDTO.BlackItem> subBlackList) {
        helloBikeClient.doSyncBlack(type, subBlackList);
    }

    private static List<HelloBikeSfcBlackDriverReqDTO.BlackItem> assembleBlackList(List<RiskCustomerManage> riskList) {
        List<HelloBikeSfcBlackDriverReqDTO.BlackItem> subBlackList = riskList.stream().map(riskCustomer -> {
            HelloBikeSfcBlackDriverReqDTO.BlackItem item = new HelloBikeSfcBlackDriverReqDTO.BlackItem();
            item.setVehiclePlateNum(riskCustomer.getCustomerValue());
            item.setStartTime(fullFmt.format(riskCustomer.getCreateTime()));
            item.setReason(getReason(riskCustomer));
            if (riskCustomer.getRiskType() == 7) {
                item.setPartnerUserId(HelloBikeUtils.getMd5Str(riskCustomer.getMemberId()));
                item.setEndTime(fullFmt.format(riskCustomer.getInvalidTime()));
            } else {
                // 根据我们自己的解禁时间去同步
                item.setEndTime(fullFmt.format(riskCustomer.getInvalidTime()));
            }
            return item;
        }).collect(Collectors.toList());
        return subBlackList;
    }

    private static String getReason(RiskCustomerManage riskCustomer) {
        if (StringUtils.isBlank(riskCustomer.getRiskRemark())) {
            return StringUtils.EMPTY;
        }
        int subLen = Math.min(riskCustomer.getRiskRemark().length(), 50);
        String reason = riskCustomer.getRiskRemark().substring(0, subLen);
        return reason;
    }


    @Override
    public void syncBlackAll() {
        int threshold = 5000;
        int index = 0;
        while (true) {
            int offset = index * threshold;
            LoggerUtils.info(log, "开始同步全量黑名单，当前批次：{}", index + 1);
            // 查询要同步的订单模式 1-增量 0-全量
            List<RiskCustomerManage> riskCustomerManages = riskCustomerService.queryAllValidRiskRecord(offset, threshold);
            if (CollUtil.isEmpty(riskCustomerManages)) {
                break;
            }
            doSyncBlack(riskCustomerManages);
            index++;
        }


    }
}