package com.ly.car.risk.process.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
@ColumnWidth(50)
public class SensitiveWordsBean {
//    id
//    word
//    word_type
//    hit_count
//    deleted     ',
//    level
//    create_time
//    update_time

    @ExcelProperty(value = "敏感词",index = 2)
    public String word;

    @ExcelProperty(value = "敏感词类型",index = 5)
    public String wordType;

    @ExcelProperty(value = "是否删除",index = 4)
    public String deleted;

    @ExcelProperty(value = "等级",index = 13)
    public String level;
}
