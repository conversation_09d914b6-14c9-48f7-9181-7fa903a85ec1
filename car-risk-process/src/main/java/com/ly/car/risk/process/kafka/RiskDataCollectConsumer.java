package com.ly.car.risk.process.kafka;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.car.bean.NameValue;
import com.ly.car.http.HttpUtils;
import com.ly.car.monitor.HealthCheckService;
import com.ly.car.risk.process.kafka.dto.RiskDataConvertDTO;
import com.ly.car.risk.process.kafka.rsp.ShortLinkRsp;
import com.ly.car.risk.process.repo.risk.mapper.RiskDataCollectMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskDataCollect;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RiskDataCollectConsumer implements ApplicationListener<ApplicationStartedEvent> {

    private static final String TOPIC = "car_data_collect_risk_topic";

    private static final String notifyUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4c035efb-dfa5-4a6e-b67c-2405edcb983d";

    @Resource
    private KafkaConsumer<String, String> szKafkaConsumer;
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private RiskDataCollectMapper riskDataCollectMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        Executors.newSingleThreadExecutor().execute(()->{
            szKafkaConsumer.subscribe(Arrays.asList(TOPIC));
            ConsumerRecords<String, String> records;
            while (healthCheckService.isHealth()) {
                records = szKafkaConsumer.poll(1000);
                if (records.count() > 0) {
                    for (ConsumerRecord<String, String> record : records) {
                        try {
                            start(record.value());
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        });
    }

    public void start(String message) throws UnsupportedEncodingException {
        log.info("[RiskDataCollectConsumer] [info] [] [] 风控统计数据kafka消息：{}", message);
        RiskDataConvertDTO dto = JSONObject.parseObject(message,RiskDataConvertDTO.class);
        if(dto == null){
            return;
        }
        String allMsg = "";
        RiskDataCollect collect = new RiskDataCollect();
        collect.setChanceLossMoneyAmount(new BigDecimal(dto.getChancelossmoneyamount()));
        collect.setControlDriverNum(Integer.valueOf(dto.getControldrivernum()));
        collect.setControlUserNum(Integer.valueOf(dto.getControlusernum()));
        collect.setDateType(dto.getDate_type().equals("小时")?0:1);
        collect.setCollectTime(dto.getDate_type().equals("小时")?
                Integer.valueOf(dto.getDate_time().replace("-","")+dto.getDate_hour())
                :Integer.valueOf(dto.getDate_time().replace("-","")));
        collect.setHcChanceLossMoneyAmount(new BigDecimal(dto.getHcchancelossmoneyamount()));
        collect.setHcControlDriverNum(Integer.valueOf(dto.getHccontroldrivernum()));
        collect.setHcControlUserNum(Integer.valueOf(dto.getHccontrolusernum()));
        collect.setHcRecoverLossMoneyAmount(new BigDecimal(dto.getHcrecoverlossmoneyamount()));
        collect.setHcTotalOffRiskOrderNum(Integer.valueOf(dto.getHctotaloffrsikordernum()));
        collect.setHcTotalRelateRiskOrderNum(Integer.valueOf(dto.getHctotalrelateriskordernum()));
        collect.setHcTotalRiskOrderNum(Integer.valueOf(dto.getHctotalriskordernum()));
        collect.setMtChanceLossMoneyAmount(new BigDecimal(dto.getMtchancelossmoneyamount()));
        collect.setMtControlDriverNum(Integer.valueOf(dto.getMtcontroldrivernum()));
        collect.setMtControlUserNum(Integer.valueOf(dto.getMtcontrolusernum()));
        collect.setMtRecoverLossMoneyAmount(new BigDecimal(dto.getMtrecoverlossmoneyamount()));
        collect.setMtTotalOffRiskOrderNum(Integer.valueOf(dto.getMttotaloffrsikordernum()));
        collect.setMtTotalRelateRiskOrderNum(Integer.valueOf(dto.getMttotalrelateriskordernum()));
        collect.setMtTotalRiskOrderNum(Integer.valueOf(dto.getMttotalriskordernum()));
        collect.setRecoverLossMoneyAmount(new BigDecimal(dto.getRecoverlossmoneyamount()));
        collect.setSfcChanceLossMoneyAmount(new BigDecimal(dto.getSfcchancelossmoneyamount()));
        collect.setSfcControlDriverNum(Integer.valueOf(dto.getSfccontroldrivernum()));
        collect.setSfcControlUserNum(Integer.valueOf(dto.getSfccontrolusernum()));
        collect.setSfcRecoverLossMoneyAmount(new BigDecimal(dto.getSfcrecoverlossmoneyamount()));
        collect.setSfcTotalOffRiskOrderNum(Integer.valueOf(dto.getSfctotaloffrsikordernum()));
        collect.setSfcTotalRelateRiskOrderNum(Integer.valueOf(dto.getSfctotalrelateriskordernum()));
        collect.setSfcTotalRiskOrderNum(Integer.valueOf(dto.getSfctotalriskordernum()));
        collect.setTotalOffRiskOrderNum(Integer.valueOf(dto.getTotaloffrsikordernum()));
        collect.setTotalRelateRiskOrderNum(Integer.valueOf(dto.getTotalrelateriskordernum()));
        collect.setTotalRiskOrderNum(Integer.valueOf(dto.getTotalriskordernum()));
        collect.setZcChanceLossMoneyAmount(new BigDecimal(dto.getZcchancelossmoneyamount()));
        collect.setZcControlDriverNum(Integer.valueOf(dto.getZccontroldrivernum()));
        collect.setZcControlUserNum(Integer.valueOf(dto.getZccontrolusernum()));
        collect.setZcRecoverLossMoneyAmount(new BigDecimal(dto.getZcrecoverlossmoneyamount()));
        collect.setZcTotalOffRiskOrderNum(Integer.valueOf(dto.getZctotaloffrsikordernum()));
        collect.setZcTotalRelateRiskOrderNum(Integer.valueOf(dto.getZctotalrelateriskordernum()));
        collect.setZcTotalRiskOrderNum(Integer.valueOf(dto.getZctotalriskordernum()));
        collect.setCreateTime(new Date());



//        riskDataCollectMapper.insert(collect);
        //插入后发个机器人通知
        String sendMsg = "风控策略核心数据通报\n";
        String content = "";
        String day = "";
        if(collect.getDateType() == 0){
            sendMsg = sendMsg + "截止时间=" +DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_HH_MM_SS) +"\n";
            content = content +"当日数据汇总";
            day = "当前";
        } else {
            sendMsg = sendMsg + "日期："+DateUtil.date2String(DateUtil.addDay(new Date(),-1),DateUtil.DATE_PATTERN_YYYY_MM_DD)+"\n";
            content = content +"昨日数据汇总";
            day = "当日";
        }

        allMsg = allMsg + day  +"总拦截风险订单数：" +dto.getTotalriskordernum()+"\n";
        allMsg = allMsg+ day +"关联召回的风险订单数：" +dto.getTotalrelateriskordernum()+"\n";
        allMsg = allMsg + day+"离线召回的风险订单数：" +dto.getTotaloffrsikordernum()+"\n";
        allMsg = allMsg + day+"机会挽损金额：" +dto.getChancelossmoneyamount()+"\n";
        allMsg = allMsg + day+"追损金额：" +dto.getRecoverlossmoneyamount()+"\n";
        allMsg = allMsg + day+"实际追损金额：" +dto.getRealrecoverloss()+"\n";
        allMsg = allMsg + day+"限制下单用户数：" +dto.getLimituserordernum()+"\n";
        allMsg = allMsg + day+"限制接单司机数：" +dto.getLimitdriverreceivenum()+"\n";
        allMsg = allMsg + day+"全局拉黑司机数：" +dto.getBlackdrivernum()+"\n";
        allMsg = allMsg + day+"一对一拉黑司机数：" +dto.getOnetooneblackdrivernum()+"\n";
        allMsg = allMsg + day+"拉黑用户数：" +dto.getBlackusernum()+"\n";
        allMsg = allMsg + day+"详细数据请查询:";

        // %0D
        content = content +"\n" + "顺风车拦截订单数:"+dto.getSfctotalriskordernum() +" "+
                "顺风车关联召回风险订单数:"+ dto.getSfctotalrelateriskordernum() + "\n" +
                "顺风车离线拦截订单数:"+ dto.getSfctotaloffrsikordernum() + " " +
                "顺风车限制下单用户数:" + dto.getSfclimituserordernum() + "\n"+
                "顺风车限制接单司机数:"+ dto.getSfclimitdriverreceivenum() + " "+
                "顺风车司机拉黑数:" + dto.getSfcblackdrivernum() + "\n" +
                "顺风车用户拉黑数:" + dto.getSfcblackusernum() + " " +
                "顺风车机会挽回金额:" + dto.getSfcchancelossmoneyamount() + "\n" +
                "顺风车潜在追损金额:" + dto.getSfcrecoverlossmoneyamount() + " " +
                "顺风车实际追损金额:" + dto.getSfcrealrecoverloss() + "\n" +
                "专车拦截订单数:" + dto.getZctotalriskordernum() +" " +
                "专车关联召回风险订单数:" + dto.getZctotalrelateriskordernum() +"\n" +
                "专车离线拦截订单数:" + dto.getZctotaloffrsikordernum() +" "+
                "专车限制下单用户数:" + dto.getZclimituserordernum() +"\n" +
                "专车限制接单司机数:" + dto.getZclimitdriverreceivenum() + " " +
                "专车司机拉黑数:" + dto.getZcblackdrivernum() + "\n" +
                "专车用户拉黑数:" + dto.getZcblackuserNum() + " " +
                "专车机会挽回金额:" + dto.getZcchancelossmoneyamount() +"\n" +
                "专车潜在追损金额:" +dto.getZcrecoverlossmoneyamount() + " "+
                "专车实际追损金额:" + dto.getZcrealrecoverloss() + "\n" +
                "卡罗拉拦截订单数:" + dto.getHctotalriskordernum() +"" +
                "卡罗拉关联召回风险订单数:" + dto.getHctotalrelateriskordernum() + "\n" +
                "卡罗拉离线拦截订单数:" + dto.getHctotaloffrsikordernum() + " " +
                "卡罗拉限制下单用户数:" + dto.getMdlimituserordernum() + "\n" +
                "卡罗拉限制接单司机数:" + dto.getMdlimitdriverreceivenum() + " " +
                "卡罗拉司机拉黑数:" + dto.getMdblackdrivernum() + "\n" +
                "卡罗拉用户拉黑数:" + dto.getMdblackUserNum() + " " +
                "卡罗拉机会挽回金额:" + dto.getHcchancelossmoneyamount() +"\n" +
                "卡罗拉潜在追损金额:" + dto.getHcrecoverlossmoneyamount() + " " +
                "卡罗拉实际追损金额:" + dto.getMdrealrecoverloss() + "\n" +
                "传祺拦截订单数:" + dto.getMttotalriskordernum() + " " +
                "传祺关联召回风险订单数:" + dto.getMttotalrelateriskordernum() + "\n" +
                "传祺离线拦截订单数:" + dto.getMttotaloffrsikordernum() + " " +
                "传祺限制下单用户数:" + dto.getMtlimituserordernum() + "\n" +
                "传祺限制接单司机数:" + dto.getMtlimitdriverreceivenum() + " " +
                "传祺司机拉黑数:" + dto.getMtblackdrivernum() + "\n" +
                "传祺用户拉黑数:" + dto.getMtblackusernum() + " " +
                "传祺机会挽回金额:" + dto.getMtchancelossmoneyamount() + "\n" +
                "传祺潜在追损金额:" + dto.getMtrecoverlossmoneyamount() + " " +
                "传祺实际追损金额:" + dto.getMtrealrecoverloss()
                ;

        String random = getStringRandom();
        redissonClient.getBucket(random).set(content,3, TimeUnit.DAYS);
        String excelUrl = "https://wx.t.17u.cn/ycpublic/riskAlarmInform?key="+random;
        JSONObject requestBody = new JSONObject();
        requestBody.put("msgtype","text");
        JSONObject contentMap = new JSONObject();

        contentMap.put("content",sendMsg + allMsg +"\n" + excelUrl);
        requestBody.put("text",contentMap);
        OkHttpClientUtil.getInstance().post(notifyUrl,requestBody.toJSONString(),null);
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String body = "{\"blackdrivernum\":\"71\",\"blackusernum\":\"3\",\"chancelossmoneyamount\":\"56753.68\",\"controldrivernum\":\"184\",\"controlusernum\":\"246\",\"date_hour\":\"10\",\"date_time\":\"2023-07-06\",\"date_type\":\"小时\",\"hcchancelossmoneyamount\":\"0.0\",\"hccontroldrivernum\":\"0\",\"hccontrolusernum\":\"0\",\"hcrecoverlossmoneyamount\":\"0.0\",\"hctotaloffrsikordernum\":\"0\",\"hctotalrelateriskordernum\":\"0\",\"hctotalriskordernum\":\"0\",\"limitdriverreceivenum\":\"184\",\"limituserordernum\":\"9\",\"mdblackdrivernum\":\"0\",\"mdlimitdriverreceivenum\":\"0\",\"mdlimituserordernum\":\"0\",\"mdrealrecoverloss\":\"0.0\",\"mtblackdrivernum\":\"9\",\"mtchancelossmoneyamount\":\"655.0\",\"mtcontroldrivernum\":\"4\",\"mtcontrolusernum\":\"4\",\"mtlimitdriverreceivenum\":\"4\",\"mtlimituserordernum\":\"0\",\"mtrealrecoverloss\":\"0.0\",\"mtrecoverlossmoneyamount\":\"0.0\",\"mttotaloffrsikordernum\":\"0\",\"mttotalrelateriskordernum\":\"0\",\"mttotalriskordernum\":\"4\",\"realrecoverloss\":\"0.0\",\"recoverlossmoneyamount\":\"4976.0\",\"sfcblackdrivernum\":\"48\",\"sfcchancelossmoneyamount\":\"54664.0\",\"sfccontroldrivernum\":\"128\",\"sfccontrolusernum\":\"186\",\"sfclimitdriverreceivenum\":\"128\",\"sfclimituserordernum\":\"9\",\"sfcrealrecoverloss\":\"0.0\",\"sfcrecoverlossmoneyamount\":\"40.0\",\"sfctotaloffrsikordernum\":\"6\",\"sfctotalrelateriskordernum\":\"22\",\"sfctotalriskordernum\":\"201\",\"totaloffrsikordernum\":\"126\",\"totalrelateriskordernum\":\"52\",\"totalriskordernum\":\"263\",\"zcblackdrivernum\":\"23\",\"zcchancelossmoneyamount\":\"2089.68\",\"zccontroldrivernum\":\"57\",\"zccontrolusernum\":\"60\",\"zclimitdriverreceivenum\":\"57\",\"zclimituserordernum\":\"0\",\"zcrealrecoverloss\":\"0.0\",\"zcrecoverlossmoneyamount\":\"0.0\",\"zctotaloffrsikordernum\":\"120\",\"zctotalrelateriskordernum\":\"0\",\"zctotalriskordernum\":\"62\"}";
        new RiskDataCollectConsumer().start(body);
    }

    public static String getStringRandom() {
        Random random = new Random();
        //把随机生成的数字转成字符串
        String str = String.valueOf(random.nextInt(9));
        for (int i = 0; i < 5; i++) {
            str += random.nextInt(9);
        }
        return str;
    }
}
