package com.ly.car.risk.process.repo.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.order.entity.OrderAddress;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 仅用于顺风车访问
 *
 * <AUTHOR> @since 2020-10-28
 */
public interface SfcOrderAddressMapper extends BaseMapper<OrderAddress> {

    @Select("select * from order_address where order_id = #{orderId} limit 1")
    OrderAddress findByOrderId(String orderId);

    List<OrderAddress> findByOrderIds(List<String> orderIds);
}
