package com.ly.car.risk.process;

import com.ly.car.risk.process.turboMQ.MqTopicEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class TurboMqConfiguration {

    @Bean(name = "hitchRiskProducer", initMethod = "start", destroyMethod = "shutdown")
    public MqRiskProducer hitchRiskProducer() {
        return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_HITCH);
    }

    @Bean(name = "workOrderProducer", initMethod = "start", destroyMethod = "shutdown")
    public MqRiskProducer workOrderProducer(){
        return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_WORK_ORDER);
    }

    @Bean(name = "binlogProducer", initMethod = "start", destroyMethod = "shutdown")
    public MqRiskProducer binlogProducer(){
        return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_BINLOG);
    }

    @Bean(name = "flinkFinalProducer",initMethod = "start",destroyMethod = "shutdown")
    public MqRiskProducer flinkProducer(){
        return new MqRiskProducer(MqTopicEnum.CAR_FLINK_TOPIC_RISK);
    }

    @Bean(name = "mtRiskProducer",initMethod = "start",destroyMethod = "shutdown")
    public MqRiskProducer mtRiskProducer(){return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_MT);}

    @Bean(name = "commonRiskProducer",initMethod = "start",destroyMethod = "shutdown")
    public MqRiskProducer commonRiskProducer(){return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_COMMON);}

    @Bean(name = "riskSecurityProducer",initMethod = "start",destroyMethod = "shutdown")
    public MqRiskProducer riskSecurityProducer(){return new MqRiskProducer(MqTopicEnum.CAR_SFC_SECURITY_PROCESS);}

    @Bean(name = "htRiskProducer", initMethod = "start", destroyMethod = "shutdown")
    public MqRiskProducer htRiskProducer() {
        return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_HT);
    }

    @Bean(name = "textRiskProducer", initMethod = "start", destroyMethod = "shutdown")
    public MqRiskProducer textRiskProducer() {
        return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_TEXT);
    }

    @Bean(name = "riskOrderSyncProducer", initMethod = "start", destroyMethod = "shutdown")
    public MqRiskProducer riskOrderSyncProducer() {
        return new MqRiskProducer(MqTopicEnum.CAR_RISK_TOPIC_ORDER_SYNC);
    }
}
