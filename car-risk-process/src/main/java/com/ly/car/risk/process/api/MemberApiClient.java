package com.ly.car.risk.process.api;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.dto.MemberData;
import com.ly.car.risk.process.api.rsp.MemberListRsp;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class MemberApiClient {

    private String url = "http://mkcloud.17usoft.com";
    private String appKey = "tcwireless.quick.car";
    private String appSecret = "c992d8041d23199d";

    @Resource
    private UrlsProperties urlProperties;

    /**
     * 通过会员id查询手机号
     * @param memberId
     * @return
     */
    public MemberQueryResponse getInfoByMemberId(String memberId){
        MemberQueryResponse memberInfoByMemberId = getMemberInfoByKey(memberId, 0,1);
        if(memberInfoByMemberId == null || StringUtils.isBlank(memberInfoByMemberId.getData().getMobile())){
            List<MemberData> listByMemberId = getListByMemberId(memberId);
            for(MemberData data : listByMemberId){
                if(data.getMemberId().equals(memberId)){
                    continue;
                }
                memberInfoByMemberId = getMemberInfoByKey(String.valueOf(data.getMemberId()), 0,1);
            }
        }
        return memberInfoByMemberId;
    }

    /**
     * 通过手机号获取用户信息
     * @param key 会员Id\登录名\手机号\邮箱
     * @param memberSystem 会员体系 0:同程 33:微信 默认为0
     * @param queryType 1-会员Id ;2-登录名\手机号\邮箱
     * @return
     */
    public MemberQueryResponse getMemberInfoByKey(String key, int memberSystem, int queryType){
        log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: queryType:{}",key,memberSystem,queryType);
        String reqUrl = urlProperties.getMemberUrl()+ "/main/query";
        if (StringUtils.isBlank(key)){
            log.warn("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: MemberId为空",key,memberSystem);
            return null;
        }
        JSONObject requestObject = new JSONObject();
        requestObject.put("queryKey",key);
        requestObject.put("queryType",queryType);//1-会员Id ;2-登录名\手机号\邮箱
        requestObject.put("memberSystem",memberSystem);
        HashMap<String,String> header = new HashMap<>();
        header.put("appKey","tcwireless.java.official.accounts");
        header.put("appSecret","3b3284cbd67b9b0d");
        log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: 请求内容:{}",key,memberSystem,requestObject.toJSONString());
        String response = OkHttpClientUtil.getInstance().post(reqUrl,requestObject.toJSONString(),header);
        log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: 请求内容:{},返回内容:{}",key,memberSystem,requestObject.toJSONString(),response);
        MemberQueryResponse memberQueryResponse = JSONObject.parseObject(response, MemberQueryResponse.class);
        if (memberQueryResponse == null || !"0".equals(memberQueryResponse.getCode())) {
            String errMsg = memberQueryResponse == null ? "未获取到返回内容！" : memberQueryResponse.getMsg();
            log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId:,错误信息:{}",key,memberSystem,errMsg);
            return null;
        }
        return memberQueryResponse;
    }

    List<MemberData> getListByMemberId(String memberId) {
        if(memberId.equals("0")){

        }
        String requestUrl = url + "/mthirdparty/wechat/getListByMemberId";
        HashMap<String,String> headMap = new HashMap<>();
        headMap.put("appKey",appKey);
        headMap.put("appSecret",appSecret);

        HashMap<String,String> dataMap = new HashMap<>();
        dataMap.put("memberId",memberId);
        String responseBody = OkHttpClientUtil.getInstance().get(requestUrl,dataMap,headMap);
        MemberListRsp rsp = JSONObject.parseObject(responseBody, MemberListRsp.class);
        if (StringUtils.equals(rsp.getCode(), "0")) {
            return rsp.getData();
        } else {
            throw new RuntimeException(memberId + ": " + rsp.getMsg());
        }
    }
}
