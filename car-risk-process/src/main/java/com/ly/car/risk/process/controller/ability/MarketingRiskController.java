package com.ly.car.risk.process.controller.ability;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.controller.ability.params.CustomerAbilityParam;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("marketingRisk")
public class MarketingRiskController {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;

    @RequestMapping("query")
    public UiResult marketingRiskQuery(@RequestBody CustomerAbilityParam param){
        RiskResultNewDTO riskResultNewDTO = new RiskResultNewDTO();
        //先查下用户端的名单
        List<RiskCustomerManage> customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .gt("invalid_time", new Date())
                .and(qw-> qw.eq(StringUtils.isNotBlank(param.getPassengerPhone()), "customer_value", param.getPassengerPhone())
                        .or().eq(StringUtils.isNotBlank(param.getMemberId()), "customer_value", param.getMemberId())
                        .or().eq(StringUtils.isNotBlank(param.getUnionId()), "customer_value", param.getUnionId())
                ));

        if(CollectionUtils.isEmpty(customerManageList)){
            return UiResult.ok(riskResultNewDTO);
        }
        RiskCustomerManage riskCustomerManage = customerManageList.stream().filter(data->data.getRiskType()==2).findFirst().orElse(null);
        if(riskCustomerManage == null){
            riskResultNewDTO.setCode(1);
            riskResultNewDTO.setMessage("命中黑名单");
        }
        return UiResult.ok(riskResultNewDTO);
    }
}
