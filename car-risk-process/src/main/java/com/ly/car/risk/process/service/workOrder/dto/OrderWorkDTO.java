package com.ly.car.risk.process.service.workOrder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderWorkDTO {
    private String authTocken;
    private String tempId;
    private String createBy;
    private String creatorId;
    private List<FormItems> formItems;
    private String poid;
    private String handlerJobNum;
    private String handlerName;
}
