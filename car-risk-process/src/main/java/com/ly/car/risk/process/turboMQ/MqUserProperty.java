package com.ly.car.risk.process.turboMQ;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public interface MqUserProperty {
    String MQ_DELAY_EXECUTE_DATE = "MQ_DELAY_EXECUTE_DATE";

    static long getDelayExecuteDate(Map<String, String> property) {
        String delayExecuteDate = property.get(MQ_DELAY_EXECUTE_DATE);
        if (StringUtils.isNotEmpty(delayExecuteDate)) {
            return Long.parseLong(delayExecuteDate);
        }
        return -1;
    }
}
