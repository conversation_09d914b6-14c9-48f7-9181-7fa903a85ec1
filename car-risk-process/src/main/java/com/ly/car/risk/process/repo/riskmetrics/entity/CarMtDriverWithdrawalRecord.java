package com.ly.car.risk.process.repo.riskmetrics.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * car_mt_driver_withdrawal_record
 * <AUTHOR>
@Data
public class CarMtDriverWithdrawalRecord implements Serializable {
    private Long id;

    /**
     * 提现单号
     */
    private String withdrawNo;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车牌号
     */
    private String carNum;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 供应商code(全)
     */
    private String fullSupplierCode;

    /**
     * 提现金额
     */
    private BigDecimal amount;

    /**
     * 申请时间
     */
    private Date gmtApply;

    /**
     * 申请通过时间
     */
    private Date gmtApproval;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;
}