package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ly.car.risk.common.constants.CommonConstants;
import com.ly.car.risk.process.api.rsp.SupplierSyncRsp;
import com.ly.car.risk.process.constants.NewRiskOrderTypeEnum;
import com.ly.car.risk.process.constants.RuleNoMapRiskTypeEnum;
import com.ly.car.risk.process.controller.params.WorkerOrderParams;
import com.ly.car.risk.process.repo.risk.mapper.OfflineMetricStrategyMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.SupplierAppealRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.TcSupplierWorkOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.OfflineMetricStrategy;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.risk.mapper.entity.SupplierAppealRecord;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.supplier.SupplierClient;
import com.ly.car.risk.process.supplier.SupplierClientStrategy;
import com.ly.car.risk.process.supplier.Tsan.T3WorkOrderCallBackParam;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.entity.OrderPrice;
import com.ly.car.sharding.order.entity.OrderSupplierBill;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.sharding.order.mapper.OrderPriceMapper;
import com.ly.car.sharding.order.mapper.OrderSupplierBillMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SupplierWorkOrderService {

    @Resource
    private TcSupplierWorkOrderMapper tcSupplierWorkOrderMapper;
    @Resource
    private OrderPriceMapper orderPriceMapper;
    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;
    @Resource
    SupplierClientStrategy supplierClientStrategy;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private OrderSupplierBillMapper orderSupplierBillMapper;
    @Resource
    private SupplierAppealRecordMapper  supplierAppealRecordMapper;
    @Resource
    private OfflineMetricStrategyMapper offlineMetricStrategyMapper;
    
    private List<String> syncSupplierCode = Lists.newArrayList("T3", "T3-preferential", "YueYueTHFixedPrice", "YueYue");
    
    //专车
    public void saveWorkOrder(OrderInfo orderInfo, OrderDriver orderDriver, String ruleNo, BigDecimal newOrderRealSupplierPrice){
        log.info("[][][][]开始生成工单");
        if(StringUtils.isBlank(ruleNo)){
            return;
        }
        List<String> ruleNoList = Arrays.asList(ruleNo.split(","));
        if(ruleNoList.size() > 0){
            ruleNo = ruleNoList.get(0);
        }
        RuleNoMapRiskTypeEnum descByRuleNo = RuleNoMapRiskTypeEnum.getDescByRuleNo(ruleNo);
        if((descByRuleNo == null && !StringUtils.lowerCase(ruleNo).startsWith(CommonConstants.OFFLINE_STRATEGY_PREFIX)) || !syncSupplierCode.contains(orderInfo.getSupplierCode())){
            return;
        }
        OfflineMetricStrategy strategy = null;
        if(StringUtils.lowerCase(ruleNo).startsWith(CommonConstants.OFFLINE_STRATEGY_PREFIX)){
            List<OfflineMetricStrategy> strategyList = offlineMetricStrategyMapper.selectList(new QueryWrapper<OfflineMetricStrategy>().eq("strategy_no", ruleNo));
            strategy = strategyList.stream().sorted(Comparator.comparing(OfflineMetricStrategy::getCreateTime).reversed()).limit(1).findFirst().orElse(null);
            if(strategy == null){
                return;
            }
        }
        //查询，有的就不插入了
        TcSupplierWorkOrder workOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq("order_id", orderInfo.getOrderId())
        );
        if(workOrder != null){
            return;
        }
        Integer sourceRiskType = descByRuleNo == null? strategy.getRiskType() : descByRuleNo.riskType;
        Integer mainRiskType = sourceRiskType;
        Integer childRiskType = 0;
        String riskType = String.valueOf(sourceRiskType);
        if(sourceRiskType > 10){
            mainRiskType = Integer.valueOf(riskType.substring(0,1));
            childRiskType = Integer.valueOf(riskType.substring(1,2));
        }

        BigDecimal changeFee;
        if(OrderUtils.isNewOrder(orderInfo.getOrderId())){
            changeFee = newOrderRealSupplierPrice;
        }else {
            OrderPrice orderPrice = orderPriceMapper.findByOrderId(orderInfo.getOrderId());
            changeFee = orderPrice.getSupplierBalanceAmount();
        }

        TcSupplierWorkOrder tcSupplierWorkOrder = new TcSupplierWorkOrder();
        tcSupplierWorkOrder.setOrderId(orderInfo.getOrderId());
        tcSupplierWorkOrder.setSupplierOrderId(orderInfo.getSupplierOrderId());
        tcSupplierWorkOrder.setSupplierCode(orderInfo.getSupplierCode());
        tcSupplierWorkOrder.setOperateType(0);
        tcSupplierWorkOrder.setPlatformWorkOrderNo("");//供应商单号我们暂时不知道
        tcSupplierWorkOrder.setCpWorkOrderNo("RISK_"+orderInfo.getOrderId());
        tcSupplierWorkOrder.setWoCateCode(mainRiskType);
        tcSupplierWorkOrder.setChildCateCode(childRiskType);
        tcSupplierWorkOrder.setMsg(descByRuleNo == null ? NewRiskOrderTypeEnum.getMsgByCode(strategy.getRiskType()) : descByRuleNo.riskDesc);
        tcSupplierWorkOrder.setEventTime(orderInfo.getFinishTime());
        tcSupplierWorkOrder.setDriverCardNo(orderDriver.getPlateNumber());
        tcSupplierWorkOrder.setAttachments("");
        tcSupplierWorkOrder.setUnionId(orderInfo.getOrderId());
        tcSupplierWorkOrder.setJudgeType(6);
        tcSupplierWorkOrder.setJudgeReason("");
        tcSupplierWorkOrder.setChangeFee(changeFee);
        tcSupplierWorkOrder.setCreateTime(new Date());
        tcSupplierWorkOrder.setUpdateTime(new Date());
        tcSupplierWorkOrder.setIsDistributor(StringUtils.isNotBlank(orderInfo.getDistributorOrderId())?1:0);
        this.tcSupplierWorkOrderMapper.insert(tcSupplierWorkOrder);
//        this.yueYueWorkOrderService.syncDispose(orderInfo.getOrderId(), descByRuleNo.riskType,orderPrice.getSupplierBalanceAmount());
    }

    public TcSupplierWorkOrder saveWorkerByRiskType(OrderInfo orderInfo, OrderDriver orderDriver,Integer riskType){
        RuleNoMapRiskTypeEnum descByRuleNo = RuleNoMapRiskTypeEnum.getDescByRiskType(riskType);
//        if(descByRuleNo == null || !orderInfo.getSupplierCode().equals("T3")){
//            return null;
//        }
        //查询，有的就不插入了
        TcSupplierWorkOrder workOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq("order_id", orderInfo.getOrderId())
        );
        if(workOrder != null){
            return null;
        }

        OrderPrice orderPrice = orderPriceMapper.findByOrderId(orderInfo.getOrderId());
        TcSupplierWorkOrder tcSupplierWorkOrder = new TcSupplierWorkOrder();
        tcSupplierWorkOrder.setOrderId(orderInfo.getOrderId());
        tcSupplierWorkOrder.setSupplierOrderId(orderInfo.getSupplierOrderId());
        tcSupplierWorkOrder.setSupplierCode(orderInfo.getSupplierCode());
        tcSupplierWorkOrder.setOperateType(0);
        tcSupplierWorkOrder.setPlatformWorkOrderNo("");//供应商单号我们暂时不知道
        tcSupplierWorkOrder.setCpWorkOrderNo("RISK_"+orderInfo.getOrderId());
        Integer mainRiskType = descByRuleNo.riskType;
        Integer childRiskType = 0;
        String riskTypeConvert = String.valueOf(descByRuleNo.riskType);
        if(descByRuleNo.riskType > 10){
            mainRiskType = Integer.valueOf(riskTypeConvert.substring(0,1));
            childRiskType = Integer.valueOf(riskTypeConvert.substring(1,2));
        }
        tcSupplierWorkOrder.setWoCateCode(mainRiskType);
        tcSupplierWorkOrder.setChildCateCode(childRiskType);
        tcSupplierWorkOrder.setMsg(descByRuleNo.riskDesc);
        tcSupplierWorkOrder.setEventTime(orderInfo.getFinishTime());
        tcSupplierWorkOrder.setDriverCardNo(orderDriver.getPlateNumber());
        tcSupplierWorkOrder.setAttachments("");
        tcSupplierWorkOrder.setUnionId(orderInfo.getOrderId());
        tcSupplierWorkOrder.setJudgeType(6);
        tcSupplierWorkOrder.setJudgeReason("");
        tcSupplierWorkOrder.setChangeFee(orderPrice.getSupplierBalanceAmount());
        //如果是附加费异常的话要单独扣掉附加费
        if(descByRuleNo.riskType == 22){
            OrderSupplierBill orderSupplierBill = this.orderSupplierBillMapper.findByOrderId(orderInfo.getOrderId());
            tcSupplierWorkOrder.setChangeFee(orderSupplierBill.getBridgeFee().add(orderSupplierBill.getOtherFee()).add(orderSupplierBill.getWaitFee()).add(orderSupplierBill.getParkFee()));
        }
        tcSupplierWorkOrder.setCreateTime(new Date());
        tcSupplierWorkOrder.setUpdateTime(new Date());
        tcSupplierWorkOrder.setIsDistributor(StringUtils.isNotBlank(orderInfo.getDistributorOrderId())?1:0);
        this.tcSupplierWorkOrderMapper.insert(tcSupplierWorkOrder);
        return tcSupplierWorkOrder;
    }

    public void sync(String orderId,Boolean allSync){
        //查询过去一小时产生的工单
        String nowDate = DateUtil.date2String(DateUtil.addHour(new Date(),-1));
        String splitTime = nowDate.split(":")[0];
        String startTime = splitTime+":00:00";
        String endTime = splitTime+":59:59";
        //查询这些工单
        List<TcSupplierWorkOrder> supplierWorkOrders = tcSupplierWorkOrderMapper.selectList(new QueryWrapper<TcSupplierWorkOrder>()
                .gt("create_time", startTime)
                .lt("create_time", endTime)
        );
        if(StringUtils.isNotBlank(orderId)){
            TcSupplierWorkOrder supplierWorkOrder = tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>().eq("order_id", orderId));
            supplierWorkOrders = new ArrayList<>();
            supplierWorkOrders.add(supplierWorkOrder);
        }

        //读取配置是全部传还是传渠道或者站内
        Map<String, Object> syncConfig = getSyncConfig();
        Integer number = (Integer) syncConfig.get("syncChannel");
        String supplierCode = (String) syncConfig.get("syncSupplier");
        Integer yueyueNum = (Integer) syncConfig.get("yueyueNum");
        List<String> supplierCodeList = Arrays.asList(supplierCode.split(","));
        supplierWorkOrders = supplierWorkOrders.stream().filter(data->supplierCodeList.contains(data.getSupplierCode())).collect(Collectors.toList());
        List<TcSupplierWorkOrder> syncList = supplierWorkOrders;
        LoggerUtils.info(log, "查询到待处理工单数:" + syncList.size());

        //对供应商进行分组
        Map<String,List<TcSupplierWorkOrder>> workOrderGroup = syncList.stream().collect(Collectors.groupingBy(TcSupplierWorkOrder::getSupplierCode));

        for(Map.Entry<String,List<TcSupplierWorkOrder>> entry : workOrderGroup.entrySet()){
            
            if("T3".equals(entry.getKey()) || "T3-preferential".equals(entry.getKey())){
                
                //查下今天已经传过去的单子
                Long count = this.tcSupplierWorkOrderMapper.selectCount(new QueryWrapper<TcSupplierWorkOrder>()
                        .between("create_time", TimeUtil.currentDay(), new Date())
                        .in("supplier_code", Lists.newArrayList("T3", "T3-preferential"))
                        .eq("is_sync", 1)
                );
                
                if(count > number){
                    continue;
                }
            }
            if("YueYue".equals(entry.getKey()) || "YueYueTHFixedPrice".equals(entry.getKey())){
                
                //查下今天已经传过去的单子
                Long count = this.tcSupplierWorkOrderMapper.selectCount(new QueryWrapper<TcSupplierWorkOrder>()
                        .between("create_time", TimeUtil.currentDay(), new Date())
                        .in("supplier_code", Lists.newArrayList("YueYue", "YueYueTHFixedPrice"))
                        .eq("is_sync", 1)
                );
                
                if(count > yueyueNum || yueyueNum == 0){
                    continue;
                }
            }
            //传递工单
            for(TcSupplierWorkOrder order : entry.getValue()){
                LoggerUtils.info(log,"订单:{} 开始处理",order.getCpWorkOrderNo());
                SupplierSyncRsp rsp = supplierClientStrategy.get(order.getSupplierCode()).syncWorkerOrder(order);
                LoggerUtils.info(log,"订单:{} 处理结束, 处理结果:{} ",order.getCpWorkOrderNo(), JSON.toJSONString(rsp));
                if(rsp == null || !rsp.getSuccess()){
                    continue;
                }
                order.setPlatformWorkOrderNo((String) rsp.getData());
                order.setIsSync(1);
                order.setUpdateTime(new Date());
                order.setWorkStatus(1);//待供应商反馈
                this.tcSupplierWorkOrderMapper.updateById(order);
                //并且要把风险订单也要更新下是否已同步
                RiskOrderManage manage = riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>()
                        .eq("order_id", order.getOrderId())
                );
                if(manage != null){
                    manage.setStatus(1);//待供应商反馈
                    this.riskOrderManageMapper.updateById(manage);
                }
            }

        }
    }

    public void syncWorkOrderBack(T3WorkOrderCallBackParam param){
        log.info("[syncWorkOrderBack][][][]工单回调参数:{}", JsonUtils.json(param));
        TcSupplierWorkOrder supplierWorkOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq("cp_work_order_no", param.getCpWorkOrderNo())
        );
        if(param.getJudgeInfo() != null){
            supplierWorkOrder.setJudgeReason(param.getJudgeInfo().getReason());
            supplierWorkOrder.setDisposeActType(param.getJudgeInfo().getDisposeActType());
            supplierWorkOrder.setDisposeResult(param.getJudgeInfo().getDisposeResult());
        }
        supplierWorkOrder.setAttachments(param.getMsg());//这个没啥用
        supplierWorkOrder.setPlatformWorkOrderNo(param.getPlatformWorkOrderNo());
        supplierWorkOrder.setJudgeResult(param.getJudgeResult());
        supplierWorkOrder.setUpdateTime(new Date());
        supplierWorkOrder.setWorkStatus(4);//T3的是默认结束，有问题的后面再说，但是还是先转到待审核
        this.tcSupplierWorkOrderMapper.updateById(supplierWorkOrder);
        //这边还要更新风险单状态
        RiskOrderManage manage = this.riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>()
                    .eq("order_id",supplierWorkOrder.getOrderId())
        );
        //这种直接回调的就在申诉信息里面加一条
        SupplierAppealRecord record = new SupplierAppealRecord();
        record.setOrderId(supplierWorkOrder.getOrderId());
        record.setTcAppealId("99999");
        record.setSupplierAppealId(param.getPlatformWorkOrderNo());
        record.setAppealText(param.getMsg());
        record.setAppealPic(null);
        record.setAppealSound(null);
        record.setAuditStatus(1);
        record.setAuditRemark("");
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        this.supplierAppealRecordMapper.insert(record);

        if(manage != null){
            manage.setStatus(2);
            this.riskOrderManageMapper.updateById(manage);
        }
    }

    //实时传递
    public void syncAdminWorker(WorkerOrderParams params){
        OrderInfo orderInfo = orderInfoMapper.findByOrderId(params.getOrderId());
        OrderDriver orderDriver = orderDriverMapper.findByOrderId(params.getOrderId());
        //再看看当前是什么规则
        TcSupplierWorkOrder tcSupplierWorkOrder = saveWorkerByRiskType(orderInfo, orderDriver, params.getRiskType());
        //当前线程查下
        SupplierClient supplierClient = supplierClientStrategy.get(orderInfo.getSupplierCode());
        if(supplierClient == null){
            return;
        }
        SupplierSyncRsp rsp = supplierClient.syncWorkerOrder(tcSupplierWorkOrder);
        if(rsp == null || !rsp.getSuccess()){
            return;
        }
        tcSupplierWorkOrder.setPlatformWorkOrderNo((String) rsp.getData());
        tcSupplierWorkOrder.setIsSync(1);
        tcSupplierWorkOrder.setUpdateTime(new Date());
        tcSupplierWorkOrder.setWorkStatus(1);
        this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);
        //并且要把风险订单也要更新下是否已同步
        RiskOrderManage manage = riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>()
                .eq("order_id", tcSupplierWorkOrder.getOrderId())
        );
        if(manage != null){
            manage.setStatus(1);//待供应商反馈
            this.riskOrderManageMapper.updateById(manage);
        }
    }

    //纯生成虚拟工单结果
    public void generateWorkOrderResult(WorkerOrderParams params){
        List<String> orderIds = Arrays.asList(params.getOrderId().split(","));
        for(String str : orderIds){
            OrderInfo orderInfo = this.orderInfoMapper.findByOrderId(str);
            TcSupplierWorkOrder tcSupplierWorkOrder = new TcSupplierWorkOrder();
            tcSupplierWorkOrder.setOrderId(orderInfo.getOrderId());
            tcSupplierWorkOrder.setSupplierOrderId(orderInfo.getSupplierOrderId());
            tcSupplierWorkOrder.setSupplierCode(orderInfo.getSupplierCode());
            tcSupplierWorkOrder.setOperateType(3);
            tcSupplierWorkOrder.setPlatformWorkOrderNo("");//供应商单号我们暂时不知道
            tcSupplierWorkOrder.setCpWorkOrderNo("RISK_"+orderInfo.getOrderId());
            RuleNoMapRiskTypeEnum riskDesc = RuleNoMapRiskTypeEnum.getDescByRiskType(params.getRiskType());
            tcSupplierWorkOrder.setWoCateCode(riskDesc.riskType);
            tcSupplierWorkOrder.setMsg(riskDesc.riskDesc);
            tcSupplierWorkOrder.setEventTime(orderInfo.getFinishTime());
            OrderDriver orderDriver = this.orderDriverMapper.findByOrderId(str);
            tcSupplierWorkOrder.setDriverCardNo(orderDriver.getPlateNumber());
            tcSupplierWorkOrder.setAttachments("线下判责司机有责");
            tcSupplierWorkOrder.setUnionId(orderInfo.getOrderId());
            tcSupplierWorkOrder.setJudgeType(6);
            tcSupplierWorkOrder.setJudgeResult(1);
            tcSupplierWorkOrder.setJudgeReason("");
            OrderPrice orderPrice = this.orderPriceMapper.findByOrderId(str);
            tcSupplierWorkOrder.setChangeFee(orderPrice.getSupplierBalanceAmount());
            tcSupplierWorkOrder.setCreateTime(new Date());
            tcSupplierWorkOrder.setUpdateTime(new Date());
            tcSupplierWorkOrder.setIsDistributor(StringUtils.isNotBlank(orderInfo.getDistributorOrderId())?1:0);
            tcSupplierWorkOrder.setIsSync(1);
            this.tcSupplierWorkOrderMapper.insert(tcSupplierWorkOrder);
        }

    }


    public Map<String,Object> getSyncConfig(){
        Map<String,Object> returnMap = new HashMap<>();
        returnMap.put("syncChannel",0);
        returnMap.put("syncSupplier","T3");
        try {
            String config = ConfigCenterClient.get("work_order_sync");
            log.info("工单同步规则:"+config);
            if(StringUtils.isNotBlank(config)){
                return JSONObject.parseObject(config);
            }
            return returnMap;
        } catch (Exception e) {
            log.error("获取工单同步规则错误:",e);
        }
        return returnMap;
    }
}
