package com.ly.car.risk.process.controller.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.controller.task.config.ZcRiskOfflineConfig;
import com.ly.car.risk.process.repo.dcdborder.mapper.DcdbOrderAmountData;
import com.ly.car.risk.process.repo.dcdborder.mapper.OrderDataMapper;
import com.ly.car.risk.process.repo.dcdbresource.entity.OrderTrack;
import com.ly.car.risk.process.repo.dcdbresource.mapper.OrderTrackMapper;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.service.rule.SpecialCarRuleConfig;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("zcAmountEx")
@RestController
@Slf4j
public class ZcAmountExTask {

    @Resource
    private OrderDataMapper orderDataMapper;
    @Resource
    private OrderTrackMapper orderTrackMapper;
    @Resource
    private RiskOrderManageService riskOrderManageService;

    @RequestMapping("action")
    public void action(){
        //通过完单查询，每小时跑一次
        String beforeDate = DateUtil.date2String(DateUtil.addHour(new Date(),-2),"yyyy-MM-dd HH");
        Date startTime = DateUtil.string2Date(beforeDate +":00:00");
        Date endTime = DateUtil.string2Date(beforeDate +":59:59");
        List<DcdbOrderAmountData> dcdbOrderAmountDataList = orderDataMapper.queryByFinishTime(startTime, endTime);
        //获取配置
        ZcRiskOfflineConfig config = queryConfig();
        if(config == null){
            return;
        }
        //跑专车规则1
        List<DcdbOrderAmountData> filterZc001 = dcdbOrderAmountDataList.stream()
                .filter(data->data.getAmountType() == 2)
                .filter(data->data.getRealityAmount().compareTo(config.getRealityAmount001()) < 0)
                .filter(data->data.getTotalAmount().compareTo(config.getTotalAmount001()) > 0)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterZc001)){
            //查询resource库 orderTrack
            for(DcdbOrderAmountData dcdbOrderAmountData : filterZc001){
                List<OrderTrack> orderTracks = orderTrackMapper.queryRecordList(dcdbOrderAmountData.getOrderId());
                OrderTrack dispatchTrack = orderTracks.stream().filter(data->data.getContent().startsWith("当前可派单车型:")).findFirst().orElse(null);
                OrderTrack createTrack = orderTracks.stream().filter(data->data.getContent().startsWith("用户下单选择车型:")).findFirst().orElse(null);
                if(dispatchTrack != null && createTrack != null){
                    String dispatchContent = dispatchTrack.getContent().replace("当前可派单车型:", "");
                    String createContent = createTrack.getContent().replace("用户下单选择车型:", "");
                    JSONArray arr1 = JSON.parseArray(dispatchContent);
                    JSONArray arr2 = JSON.parseArray(createContent);
                    if(arr1.size() > config.getDispatchNum001() && arr2.size() < config.getSelectNum001()){
                        RiskOrderManage req = new RiskOrderManage();
                        req.setOrderId(dcdbOrderAmountData.getOrderId());
                        req.setRuleNo("zc-001");
                        req.setRiskType(21);
                        riskOrderManageService.addRiskOrder(req);
                    }
                }
            }
        }

        //跑专车规则2
        List<DcdbOrderAmountData> filterZc002 = dcdbOrderAmountDataList.stream()
                .filter(data->data.getActualKilo().subtract(data.getEstimateKilo()).abs().compareTo(config.getKiloLeft002()) < 0)
                .filter(data->Math.abs(data.getActualMinute()-data.getEstimateMinute()) < config.getMinuteLeft002())
                .filter(data->data.getTwiceNeedAmount().compareTo(config.getTwiceNeedAmount002()) > 0)
                .filter(data->(data.getParkFee().add(data.getBridgeFee()).add(data.getOtherFee()).add(data.getWaitFee()).add(data.getHighWayFee()).subtract(data.getActualKilo()))
                        .compareTo(config.getOtherFee002()) >= 0)
                .collect(Collectors.toList());
        for(DcdbOrderAmountData dcdbOrderAmountData : filterZc002){
            RiskOrderManage req = new RiskOrderManage();
            req.setOrderId(dcdbOrderAmountData.getOrderId());
            req.setRuleNo("zc-002");
            req.setRiskType(22);
            riskOrderManageService.addRiskOrder(req);
        }

        //跑专车规则007
        List<DcdbOrderAmountData> filterZc007 = dcdbOrderAmountDataList.stream()
                .filter(data->data.getAmountType() == 2)
                .filter(data->data.getRealityAmount().compareTo(config.getRealityAmountRight007()) < 0)
                .filter(data->data.getRealityAmount().compareTo(config.getRealityAmountLeft007()) >= 0)
                .filter(data->data.getTotalAmount().compareTo(config.getTotalAmount007()) > 0)
                .collect(Collectors.toList());
        for(DcdbOrderAmountData dcdbOrderAmountData : filterZc007){
            List<OrderTrack> orderTracks = orderTrackMapper.queryRecordList(dcdbOrderAmountData.getOrderId());
            OrderTrack dispatchTrack = orderTracks.stream().filter(data->data.getContent().startsWith("当前可派单车型:")).findFirst().orElse(null);
            OrderTrack createTrack = orderTracks.stream().filter(data->data.getContent().startsWith("用户下单选择车型:")).findFirst().orElse(null);
            if(dispatchTrack != null && createTrack != null){
                String dispatchContent = dispatchTrack.getContent().replace("当前可派单车型:", "");
                String createContent = createTrack.getContent().replace("用户下单选择车型:", "");
                JSONArray arr1 = JSON.parseArray(dispatchContent);
                JSONArray arr2 = JSON.parseArray(createContent);
                if(arr1.size() > 3 && arr2.size() < 3){
                    RiskOrderManage req = new RiskOrderManage();
                    req.setOrderId(dcdbOrderAmountData.getOrderId());
                    req.setRuleNo("zc-007");
                    req.setRiskType(21);
                    riskOrderManageService.addRiskOrder(req);
                }
            }
        }

    }

    public ZcRiskOfflineConfig queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("zc_risk_offline");
            ZcRiskOfflineConfig config = JSONObject.parseObject(configJson,ZcRiskOfflineConfig.class);
            log.info("[][][][]获取专车离线规则配置:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取专车离线规则配置错误:",e);
        }
        return null;
    }

}
