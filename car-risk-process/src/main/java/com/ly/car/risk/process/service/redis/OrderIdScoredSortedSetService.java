package com.ly.car.risk.process.service.redis;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class OrderIdScoredSortedSetService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    public List<String> get(String key){
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        long initMs = TimeUtil.initMs();
        long startMs = DateUtil.addMinute(new Date(),-10).getTime();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, new Date().getTime(), true);
        List<String> orderIdList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            String context = obj.getValue().toString();
            orderIdList.add(context);
        }
        return orderIdList;
    }
}
