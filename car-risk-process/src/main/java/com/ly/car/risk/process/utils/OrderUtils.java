package com.ly.car.risk.process.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.common.enums.MetricStrategyProductLineEnum;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.SfcOrderStatus;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.dto.order.BaseOrderInfo;
import com.ly.car.risk.process.service.dto.order.CarInfo;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.CarPassenger;
import com.ly.car.risk.process.service.dto.order.FeeItemInfo;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.dto.order.UsedDiscountInfo;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.common.model.enums.CarTypeEnum;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.orderservice.facade.model.opsi.CarOrder;
import com.ly.travel.car.orderservice.facade.model.opsi.ItemDetail;
import com.ly.travel.car.orderservice.facade.model.opsi.OpsiRelation;
import com.ly.travel.car.orderservice.facade.model.opsi.PassengerInfo;
import com.ly.travel.car.orderservice.facade.model.opsi.ResourceInfo;
import com.ly.travel.car.orderservice.facade.model.opsi.SegmentInfo;
import com.ly.travel.car.tradecore.model.enums.BusinessType;
import com.ly.travel.car.tradecore.model.enums.CarFeeType;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.car.tradecore.model.enums.OrderTag;
import com.ly.travel.car.tradecore.model.enums.OrderType;
import com.ly.travel.car.tradecore.model.ext.OrderExtVO;
import com.ly.travel.car.tradecore.model.ext.TicketExtVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;

/**
 * Description of OrderUtils
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc 用车订单工具类
 */
@Slf4j
public class OrderUtils {

    // 分销标识
    public static final String DISTRIBUTION_TAG = "DISTRIBUTION_ORDER";
    // 权益订单标识
    public static final String RIGHTS_ORDER_TAG = "RIGHTS_ORDER";
    // 权益公里免单标识
    public static final String RIGHTS_KILOS_TAG = "RIGHTS_KILOS";

    private static final FastDateFormat fullFmt = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    public static Date limitTime;

    static {
        try {
            limitTime = fullFmt.parse("2000-01-01 00:00:00");
        } catch (ParseException e) {
            limitTime = new Date();
        }
    }

    public static boolean isNewOrder(String orderId) {
        return StringUtils.isNotBlank(orderId) && (orderId.startsWith("YCS") || orderId.startsWith("YCW"));
    }

    public static boolean isSFC(String orderId) {
        return orderId.startsWith("YCS");
    }


    public static String getProductLineByOrderPrefix(String orderId){
        if(StringUtils.isBlank(orderId)){
            return StringUtils.EMPTY;
        }
        String prefix = orderId.substring(0,3);
        switch (prefix){
            case "YCS":
                return MetricStrategyProductLineEnum.SFC.code;
            case "YCW":
                return MetricStrategyProductLineEnum.YNC.code;
            case "YCX":
                return MetricStrategyProductLineEnum.YCX.code;
            default:
                return MetricStrategyProductLineEnum.YNC.code;
        }
    }

    /**
     * 将交易给的订单转换成业务中使用的结构
     */
    public static CarOrderDetail assembleOrderDetail(CarOrder carOrder) {

        CarOrderDetail orderDetail = new CarOrderDetail();

        List<OpsiRelation> psis = carOrder.getPsis();

        Optional<OpsiRelation> tripOpsiOpt = psis.stream().filter(p -> p.getIsDelete() == 0 && null != p.getPassenger() && null != p.getSegment()).findFirst();
        if (!tripOpsiOpt.isPresent()) {
            return orderDetail;
        }
        OpsiRelation tripOpsi = tripOpsiOpt.get();
        // 乘客信息
        PassengerInfo respPassengerInfo = tripOpsi.getPassenger();
        CarPassenger passenger = new CarPassenger();
        passenger.setName(respPassengerInfo.getName());
        passenger.setPassengerCellPhone(respPassengerInfo.getLinkPhone());
        passenger.setAreaCode(respPassengerInfo.getAreaCode());
        passenger.setPassengerCount(respPassengerInfo.getPassengerCount());

        // 行程信息
        SegmentInfo segment = tripOpsi.getSegment();
        OrderTripInfo tripInfo = new OrderTripInfo();
        tripInfo.setDepartureCityCode(StringUtils.isBlank(segment.getDepartureCityCode()) ? null : Integer.parseInt(segment.getDepartureCityCode()));
        tripInfo.setDepartureCityName(segment.getDepartureCity());
        tripInfo.setDepartureAddress(segment.getDepartureAddress());
        tripInfo.setDepartureAddressDetail(segment.getDepartureFullAddress());
        tripInfo.setDepartureLat(StrToDecimal(segment.getDepLat()));
        tripInfo.setDepartureLng(StrToDecimal(segment.getDepLon()));
        tripInfo.setArrivalCityCode(StringUtils.isBlank(segment.getArrivalCityCode()) ? null : Integer.parseInt(segment.getArrivalCityCode()));
        tripInfo.setArrivalCityName(segment.getArrivalCity());
        tripInfo.setArrivalAddress(segment.getArrivalAddress());
        tripInfo.setArrivalAddressDetail(segment.getArrivalFullAddress());
        tripInfo.setArrivalLat(StrToDecimal(segment.getArrLat()));
        tripInfo.setArrivalLng(StrToDecimal(segment.getArrLon()));

        // 基础信息
        BaseOrderInfo baseOrderInfo = new BaseOrderInfo();
        baseOrderInfo.setPayState(carOrder.getPayState());
        baseOrderInfo.setUserState(carOrder.getUserState());
        baseOrderInfo.setCarPooling(CollUtil.isNotEmpty(carOrder.getOrderTags()) && carOrder.getOrderTags().contains(OrderTag.CARPOOL_ORDER.name()));
        baseOrderInfo.setHasAccepted(CollUtil.isNotEmpty(carOrder.getOrderTags()) && carOrder.getOrderTags().contains(OrderTag.ORDER_RECEIVED.name()));
        baseOrderInfo.setContactName(carOrder.getContactName());
        baseOrderInfo.setContactPhone(carOrder.getContactPhone());
        baseOrderInfo.setAreaCode(carOrder.getAreaCode());
        baseOrderInfo.setDeviceId(carOrder.getDeviceId());
        baseOrderInfo.setGmtPaid(parseOrderDate(carOrder.getGmtPaid()));
        baseOrderInfo.setGmtPayScoreDeduct(parseOrderDate(carOrder.getGmtPayScoreDeduct()));
        baseOrderInfo.setGmtCreate(parseOrderDate(carOrder.getCreateTime()));
        baseOrderInfo.setGmtUsage(parseOrderDate(carOrder.getGmtUsage()));
        baseOrderInfo.setUsageDelay(carOrder.getUsageDelay());
        baseOrderInfo.setGmtArrive(parseOrderDate(carOrder.getGmtArrive()));
        baseOrderInfo.setGmtDeparture(parseOrderDate(carOrder.getGmtDeparture()));
        baseOrderInfo.setGmtDispatched(parseOrderDate(carOrder.getGmtDispatched()));
        baseOrderInfo.setGmtReassigned(parseOrderDate(carOrder.getGmtReassigned()));
        baseOrderInfo.setGmtDriverArrived(parseOrderDate(carOrder.getGmtDriverArrived()));
        baseOrderInfo.setGmtPassengerBoard(parseOrderDate(carOrder.getGmtPassengerBoard()));
        baseOrderInfo.setGmtCanceled(parseOrderDate(carOrder.getGmtCanceled()));
        baseOrderInfo.setGmtTripFinished(parseOrderDate(carOrder.getGmtTripFinished()));
        baseOrderInfo.setGmtAccept(parseOrderDate(carOrder.getGmtDispatched()));
        baseOrderInfo.setCancelType(carOrder.getCancelType());
        baseOrderInfo.setCancelReason(carOrder.getCancelReason());
        baseOrderInfo.setAmount(carOrder.getAmount());
        baseOrderInfo.setPayAmount(carOrder.getAmount());
        baseOrderInfo.setPayCategory(carOrder.getPayCategory());
        baseOrderInfo.setReassignNum(carOrder.getReassignNum());
        baseOrderInfo.setRefId(carOrder.getRefId());
        baseOrderInfo.setThanksMoney(StringUtils.EMPTY);
        baseOrderInfo.setSurcharge(BigDecimal.ZERO);

        String carOrderExtStr = carOrder.getExt();
        if (StringUtils.isNotBlank(carOrderExtStr)) {
            OrderExtVO orderExtVO = JSON.parseObject(carOrderExtStr, new TypeReference<OrderExtVO>() {
            });
            baseOrderInfo.setRemark(orderExtVO.getRemark());
        }

        CarInfo carInfo = new CarInfo();
        // 从中找出车票类型
        Optional<OpsiRelation> ticketOpt = psis.stream().filter(p -> p.getIsDelete() == 0 && null != p.getItem() && Objects.equals(p.getItem().getBusinessType(), BusinessType.TICKET.getCode()))
                .findFirst();
        if (ticketOpt.isPresent()) {
            ItemDetail ticketItem = ticketOpt.get().getItem();

            orderDetail.setSupplierOrderId(ticketItem.getThirdOrderNo());
            orderDetail.setBusinessOrderNo(ticketItem.getBusinessOrderNo());
            carInfo.setSupplierCode(StringUtils.isBlank(ticketItem.getSupplierCode()) ? StringUtils.EMPTY : ticketItem.getSupplierCode().split("_")[0]);
            carInfo.setSupplierFullCode(ticketItem.getSupplierCode());
            carInfo.setCarNo(StringUtils.EMPTY);


            // 车辆信息
            String ticketExt = ticketItem.getExt();
            if (StringUtils.isNotBlank(ticketExt)) {
                TicketExtVO ticketExtVO = JSON.parseObject(ticketExt, new TypeReference<TicketExtVO>() {
                });
                
                baseOrderInfo.setRealSupplierPrice(StringUtils.isBlank(ticketExtVO.getRealSupplierPrice())?null :new BigDecimal(ticketExtVO.getRealSupplierPrice()));

                carInfo.setCarNum(ticketExtVO.getCarNum());
                carInfo.setCarType(null == ticketExtVO.getCarType()? null :CarTypeEnum.of(ticketExtVO.getCarType()));
                carInfo.setBrand(ticketExtVO.getBrand());
                carInfo.setColor(ticketExtVO.getColor());
                carInfo.setCarTags(ticketExtVO.getCarTags());
                carInfo.setDriverCode(ticketExtVO.getDriverCode());
                carInfo.setDriverName(ticketExtVO.getDriverName());
                carInfo.setDriverPhone(ticketExtVO.getDriverPhone());
                carInfo.setDriverVirtualPhone(ticketExtVO.getDriverVirtualPhone());
                carInfo.setSupplierName(StringUtils.EMPTY);


                tripInfo.setServiceType(null == ticketExtVO.getServiceType() ? null : ServiceType.of(ticketExtVO.getServiceType()));
                tripInfo.setEstimateRunDistance(new BigDecimal(ticketExtVO.getRunDistance()));
                tripInfo.setEstimateRunTime(ticketExtVO.getRunTime());
                tripInfo.setOldEstimateKilo(new BigDecimal(String.valueOf(ticketExtVO.getRunDistance())).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
                tripInfo.setOldEstimateMinute(0 >= ticketExtVO.getRunTime() ? 0 : (ticketExtVO.getRunTime() / 60 + 1));

                tripInfo.setRealRunDistance(new BigDecimal(ticketExtVO.getRealRunDistance()));
                tripInfo.setRealRunTime(ticketExtVO.getRealRunTime());
                tripInfo.setOldRealKilo(new BigDecimal(String.valueOf(ticketExtVO.getRealRunDistance())).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
                tripInfo.setOldRealMinute(0 >= ticketExtVO.getRealRunTime() ? 0 : (ticketExtVO.getRealRunTime() / 60 + 1));

                if (CollUtil.isNotEmpty(ticketExtVO.getAttachFeeItems())) {
                    List<FeeItemInfo> attachFeeItems = ticketExtVO.getAttachFeeItems().stream().map(fee -> {
                        FeeItemInfo feeItemInfo = new FeeItemInfo();
                        feeItemInfo.setType(fee.getType());
                        feeItemInfo.setName(Optional.ofNullable(CarFeeType.getByCode(fee.getType())).map(p->p.getDesc()).orElse(StringUtils.EMPTY));
                        feeItemInfo.setPrice(fee.getPrice());
                        return feeItemInfo;
                    }).collect(Collectors.toList());
                    baseOrderInfo.setAttachFeeItems(attachFeeItems);
                    
                    BigDecimal surcharge = baseOrderInfo.getAttachFeeItems()
                            .stream()
                            .filter(fee ->
                                    StringUtils.isNotBlank(fee.getPrice())
                                    && StringUtils.isNotBlank(fee.getType())
                                    && (Objects.equals(fee.getType(), CarFeeType.BRIDGE_FEE.name())
                                        || Objects.equals(fee.getType(), CarFeeType.PARK_FEE.name())
                                        || Objects.equals(fee.getType(), CarFeeType.CLEAR_FEE.name())
                                        || Objects.equals(fee.getType(), CarFeeType.HIGH_WAY_FEE.name())))
                            .map(FeeItemInfo::getPrice)
                            .map(BigDecimal::new)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    baseOrderInfo.setSurcharge(surcharge);
                }

                String resourceId = ticketExtVO.getResourceId();
                if (StringUtils.isNotBlank(resourceId)) {
                    Optional<ResourceInfo> resourceOpt = carOrder.getResourceInfos().stream().filter(p -> p.getResourceId().equals(resourceId)).findFirst();
                    if (resourceOpt.isPresent()) {
                        carInfo.setSupplierName(resourceOpt.get().getSupplierName());
                    }
                }
            }
        }

        List<UsedDiscountInfo> usedDiscountInfos = psis.stream()
                .filter(p -> p.getIsDelete() == 0 && null != p.getItem() && Objects.equals(p.getItem().getBusinessType(), BusinessType.COUPON.getCode()))
                .map(p -> p.getItem())
                .map(item -> {
                    UsedDiscountInfo discountInfo = new UsedDiscountInfo();
                    discountInfo.setBusinessType(item.getBusinessType());
                    discountInfo.setSalePrice(getAbsPrice(item.getSalePrice()));
                    discountInfo.setRealPrice(getAbsPrice(item.getRealPrice()));
                    discountInfo.setProductName(item.getProductName());
                    return discountInfo;
                })
                .collect(Collectors.toList());

        orderDetail.setOrderId(carOrder.getOrderSerialNo());
        orderDetail.setOrderChannel(carOrder.getOrderChannel());
        orderDetail.setMemberId(carOrder.getMemberId());
        orderDetail.setUnionId(carOrder.getUnionId());
        orderDetail.setProductLine(getProductLine(carOrder));
        orderDetail.setCrmType(carOrder.getOrderType());
        orderDetail.setOrderState(carOrder.getOrderState());
        orderDetail.setEnv(carOrder.getEnv());

        orderDetail.setCarInfo(carInfo);
        orderDetail.setPassengerInfo(passenger);
        orderDetail.setOrderTrip(tripInfo);
        orderDetail.setUsedCoupons(usedDiscountInfos);
        orderDetail.setBaseInfo(baseOrderInfo);
        return orderDetail;
    }

    private static String getProductLine(CarOrder carOrder) {
        OrderType orderType = OrderType.getByCrmType(carOrder.getOrderType());
        if(null!=orderType){
            return orderType.getRiskType();
        }
        String orderId = carOrder.getOrderSerialNo();
        if(orderId.startsWith("YCS")){
            return ProductLineEnum.SFC.getCode();
        }else if(orderId.startsWith("YCX")){
            return ProductLineEnum.XCAR.getCode();
        }else if(orderId.startsWith("YCW")){
            return ProductLineEnum.YNC.getCode();
        }
        return ProductLineEnum.YNC.getCode();
    }

    private static String getAbsPrice(String price) {
        if(StringUtils.isBlank(price)){
            return price;
        }
        try {
            BigDecimal priceDecimal = new BigDecimal(price);
            return priceDecimal.abs().toString();
        } catch (Exception e) {
            return price;
        }
    }


    private static Date parseOrderDate(String orderDate) {
        if (StringUtils.isBlank(orderDate)) {
            return null;
        }
        try {
            return fullFmt.parse(orderDate);
        } catch (ParseException e) {
            return null;
        }
    }

    public static SendOrderContext convertYNCTripFinishOrderCache(CarOrderDetail orderInfo) {
        BaseOrderInfo baseInfo = orderInfo.getBaseInfo();
        CarPassenger passengerInfo = orderInfo.getPassengerInfo();
        OrderTripInfo orderTrip = orderInfo.getOrderTrip();
        CarInfo carInfo = orderInfo.getCarInfo();

        SendOrderContext sendOrderContext = new SendOrderContext();
        sendOrderContext.setOrderId(orderInfo.getOrderId());
        sendOrderContext.setFinishTime(baseInfo.getGmtTripFinished());
        sendOrderContext.setTotalAmount(String.valueOf(baseInfo.getAmount()));
        sendOrderContext.setPassengerCellphone(passengerInfo.getPassengerCellPhone());
        sendOrderContext.setMemberId(orderInfo.getMemberId());
        sendOrderContext.setUnionId(orderInfo.getUnionId());
        if(null == baseInfo.getGmtTripFinished() || null == baseInfo.getGmtPassengerBoard()){
            sendOrderContext.setIntervalTime(0);
        }else{
            sendOrderContext.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(baseInfo.getGmtTripFinished().getTime() - baseInfo.getGmtPassengerBoard().getTime()));
        }

        sendOrderContext.setDeviceId(baseInfo.getDeviceId());
        sendOrderContext.setPayAccount(StringUtils.EMPTY);
        sendOrderContext.setDriverCardNo(carInfo.getCarNum());
        sendOrderContext.setStartLat(orderTrip.getDepartureLat().toString());
        sendOrderContext.setStartLng(orderTrip.getDepartureLng().toString());
        ;
        sendOrderContext.setEndLat(orderTrip.getArrivalLat().toString());
        sendOrderContext.setEndLng(orderTrip.getArrivalLng().toString());
        return sendOrderContext;
    }

    public static OrderRiskContext convertSFCTripFinishOrderCache(CarOrderDetail orderDetail) {
        log.info("[][][][]组装顺风车参数:{}", JsonUtils.json(orderDetail));

        BigDecimal amount = BigDecimal.ZERO;
        try {
            amount = orderDetail.getUsedCoupons().stream().map(p -> new BigDecimal(p.getRealPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("计算优惠券金额错误");
        }
        CarInfo carInfo = orderDetail.getCarInfo();
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        BaseOrderInfo baseInfo = orderDetail.getBaseInfo();
        CarPassenger passengerInfo = orderDetail.getPassengerInfo();

        OrderRiskContext orderRiskContext = new OrderRiskContext();
        orderRiskContext.setOrderId(orderDetail.getOrderId());
        orderRiskContext.setFinishTime(DateUtil.date2String(baseInfo.getGmtTripFinished()));
        orderRiskContext.setMemberId(orderDetail.getMemberId());
        orderRiskContext.setUnionId(orderDetail.getUnionId());
        orderRiskContext.setDriverCardNo(carInfo.getCarNum());
        orderRiskContext.setStartLat(orderTrip.getDepartureLat());
        orderRiskContext.setStartLng(orderTrip.getDepartureLng());
        orderRiskContext.setEndLat(orderTrip.getArrivalLat());
        orderRiskContext.setEndLng(orderTrip.getArrivalLng());
        orderRiskContext.setTotalAmount(new BigDecimal(baseInfo.getAmount()));
        orderRiskContext.setEstimateKilo(orderTrip.getOldEstimateKilo());
        orderRiskContext.setActualKilo(orderTrip.getOldRealKilo());
        orderRiskContext.setEstimateDuration(orderTrip.getOldEstimateMinute());
        orderRiskContext.setActualDuration(orderTrip.getOldRealMinute());
        orderRiskContext.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(baseInfo.getGmtTripFinished().getTime() - baseInfo.getGmtAccept().getTime()));
        orderRiskContext.setPassengerCellphone(passengerInfo.getPassengerCellPhone());
        orderRiskContext.setCouponAmount(amount);
        return orderRiskContext;
    }

    public static Integer convertStatus(int orderState) {
        if (orderState == OrderState.DRAFT.getCode()) {
            return SfcOrderStatus.CREATE_SUCCESS.code;
        } else if (orderState == OrderState.RECEIVING_ORDER.getCode()) {
            return SfcOrderStatus.DECISION_SUCCESS.code;
        } else if (orderState == OrderState.CANCELED.getCode()) {
            return SfcOrderStatus.CANCEL.code;
        } else if (orderState == OrderState.ORDER_CLOSED.getCode()) {
            return SfcOrderStatus.FINISH.code;
        }
        return SfcOrderStatus.SERVICE.code;
    }

    public static BigDecimal StrToDecimal(String val) {
        if (StringUtils.isBlank(val)) {
            return null;
        }
        return new BigDecimal(val);
    }

    public static int strToInt(String val){
        try {
            if (StringUtils.isBlank(val)) {
                return 0;
            }
            return Integer.parseInt(val);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public static boolean isRightsOrder(List<String> orderTags) {
        return CollUtil.isNotEmpty(orderTags) && (orderTags.contains(RIGHTS_ORDER_TAG) || orderTags.contains(RIGHTS_KILOS_TAG));
    }

    public static boolean isDistributionOrder(List<String> orderTags) {
        String distributionTagVal = ConfigCenterService.getDefault("DISTRIBUTION_TAGS", "DISTRIBUTION_ORDER,PAGE_DISTRIBUTION_ORDER");
        // 如果订单tag是空的，则肯定不是分销单
        if(CollUtil.isEmpty(orderTags)){
            return false;
        }
        List<String> distributionTags = StrUtil.split(distributionTagVal, ',', true, true);
        for(String tag : distributionTags){
            if(orderTags.contains(tag)){
                return true;
            }
        }
        return false;
    }

}