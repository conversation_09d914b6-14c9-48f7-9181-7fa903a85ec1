package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface RiskOrderManageMapper extends BaseMapper<RiskOrderManage> {
    
    /**
     * 被命中规则
     * @param carNum
     * @param ruleNo
     * @param startDate 命中查询时间范围-开始时间
     * @param endDate 命中查询时间范围-结束时间
     * @return
     */
    long queryDriverFitRuleCount(@Param("carNum")String carNum ,  String ruleNo, @Param("startTime") Date startDate, @Param("endTime") Date endDate);

    /**
     * 司机有责订单数
     * @param startDate 命中查询时间范围-开始时间
     * @param endDate 命中查询时间范围-结束时间
     */
    long queryDriverResponsibleOrderCount(@Param("carNum")String carNum ,@Param("startTime") Date startDate, @Param("endTime") Date endDate);
}
