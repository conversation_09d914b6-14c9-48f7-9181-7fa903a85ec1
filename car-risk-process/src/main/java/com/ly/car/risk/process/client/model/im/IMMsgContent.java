package com.ly.car.risk.process.client.model.im;

import lombok.Data;

/**
 * Description of IMMsgContent
 *
 * <AUTHOR>
 * @date 2024/3/18
 * @desc
 */
@Data
public class IMMsgContent {
    /**
     * 消息 GUID
     */
    private String msgId;

    /**
     * 消息类型 0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告 8-已读 9-时间卡片 10-协商卡片 12-邀请车主 13-邀请车主回复 14-邀请车主文本
     */
    private Integer msgType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 提示文案（不支持时展示）
     */
    private String showText;

    /**
     * 扩展字段 json 格式（预留）
     */
    private String extension;

    /**
     * msgType=4：图片路径 msgType=3：语音资源路径
     */
    private String url;

    /**
     * msgType=4：图片名称 msgType=3：语音文件名
     */
    private String path;

    /**
     * msgType=2：纬度
     */
    private String lat;

    /**
     * msgType=2：经度
     */
    private String lon;

    /**
     * msgType=2：长地址
     */
    private String longAddres;

    /**
     * msgType=2：短地址
     */
    private String shortAddress;

    /**
     * msgType=1
     */
    private String tips;

    /**
     * msgType=3
     */
    private Integer beganPlayTime;

    /**
     * msgType=3：音频时长
     */
    private Integer audioDuration;

    /**
     * msgType=3
     */
    private Boolean hasPlay;
}