package com.ly.car.risk.process.repo.riskmetrics.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 司机登陆记录表
 * car_mt_driver_account_record
 */
@Data
public class CarMtDriverAccountRecord implements Serializable {
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 司机唯一编码
     */
    private Long driverId;
    
    /**
     * 设备id
     */
    private String deviceId;
    
    /**
     * 状态 0有效  -1无效 
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    private static final long serialVersionUID = 1L;
}