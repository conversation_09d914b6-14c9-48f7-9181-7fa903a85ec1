package com.ly.car.risk.process.controller.workOrder;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.DriverCheatOrderParams;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheatSyncOrder;
import com.ly.car.risk.process.service.workOrder.DriverCheatOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/driver/risk")
public class DriverCheatOrderController {

    @Resource
    private DriverCheatOrderService driverCheatOrderService;

    @RequestMapping("/getList")
    public UiResult getList(@RequestBody DriverCheatOrderParams params){
        return UiResult.ok(driverCheatOrderService.getList(params));
    }

    @RequestMapping("/getDetail")
    public UiResult getDetail(@RequestBody DriverCheatOrderParams params){
        return UiResult.ok(driverCheatOrderService.getDetail(params));
    }

    @RequestMapping("/appeal")
    public UiResult appeal(@RequestBody DriverCheatOrderParams params){
        return this.driverCheatOrderService.appeal(params);
    }

    @RequestMapping("/queryOrder")
    public UiResult queryOrder(@RequestBody DriverCheatOrderParams params){
        return this.driverCheatOrderService.queryOrder(params);
    }

    @RequestMapping("/insert")
    public UiResult insert(@RequestBody DriverCheatSyncOrder driverCheatSyncOrder){
        return this.driverCheatOrderService.insert(driverCheatSyncOrder);
    }

    @RequestMapping("/check")
    public UiResult check(@RequestBody DriverCheatSyncOrder driverCheatSyncOrder){
        return this.driverCheatOrderService.check(driverCheatSyncOrder);
    }

    @RequestMapping("/adminFrozen")
    public UiResult adminFrozen(@RequestBody JSONObject jsonObject){
        this.driverCheatOrderService.adminFrozen(jsonObject);
        return UiResult.ok();
    }

    @RequestMapping("/adminCheck")
    public UiResult adminCheck(@RequestBody JSONObject jsonObject){
        this.driverCheatOrderService.adminCheck(jsonObject);
        return UiResult.ok();
    }

}
