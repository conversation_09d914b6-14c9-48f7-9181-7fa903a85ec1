package com.ly.car.risk.process.api;

import com.ly.car.fantasy.TargetUrl;
import com.ly.car.fantasy.WebRequest;
import com.ly.car.risk.process.api.param.RiskCheckOrderParam;
import com.ly.car.risk.process.api.rsp.TrafficCheckOrderRsp;

public interface TrafficApi {

    /**
     * http://tourwirelessapi.t.17usoft.com/traffic/api/risk/check/order/
     * {
     *     "unionId": "ohmdTt_WgsPpkRhXwGvsPs4kkkSE",
     *     "memberId": "",
     *     "payAccount": ""
     * }
     * */
    @WebRequest(timeout = 3000)
    @TargetUrl(env = "product",url = "http://tourwirelessapi.17usoft.com/traffic/api/risk/check/order/")
    @TargetUrl(env = "test,qa,uat,stage,stage_test2",url = "http://tourwirelessapi.t.17usoft.com/traffic/api/risk/check/order/")
    TrafficCheckOrderRsp riskCheckOrder(RiskCheckOrderParam param);
}
