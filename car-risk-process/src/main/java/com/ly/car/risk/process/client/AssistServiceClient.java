package com.ly.car.risk.process.client;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.model.common.DSFRequest;
import com.ly.car.risk.process.utils.DSFUtils;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.flight.marketing.assistservice.facade.request.CarNewJudgeReqDTO;
import com.ly.flight.marketing.assistservice.facade.request.MemberByMemberIdReqDTO;
import com.ly.flight.marketing.assistservice.facade.request.UnifyCustomerJudgeReqDTO;
import com.ly.flight.marketing.assistservice.facade.response.CarNewJudgeResDTO;
import com.ly.flight.marketing.assistservice.facade.response.GetListByWxUnionIdDataDTO;
import com.ly.flight.marketing.assistservice.facade.response.MemberByMemberIdResDTO;
import com.ly.flight.marketing.assistservice.facade.response.UnifyCustomerJudgeResDTO;
import com.ly.sof.utils.mapping.FastJsonUtils;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AssistServiceClient {
    
    @Resource
    private UrlsProperties urlProperties;
    
    public String carNewJudge(String memberId, String unionId) {
        String carNewFlag = "0";
        try {
            CarNewJudgeReqDTO dto = new CarNewJudgeReqDTO();
            dto.setMemberId(memberId);
            dto.setUnionId(unionId);
            dto.setTraceId(UUID.randomUUID().toString());
            CarNewJudgeResDTO res = DSFUtils.sendAction(CarNewJudgeResDTO.class, getDsfRequest("customer", "carNewJudge", dto));
            LoggerUtils.info(log, "用车新老用户查询,req:{},resp:{}", JSON.toJSONString(dto), JSON.toJSONString(res));
            
            if (null == res || !res.isSuccess()) {
                return carNewFlag;
            }
            return res.getCarNewFlag();
        } catch (Exception e) {
            LoggerUtils.warn(log, "[判断用车新老用户] 查询异常 mid: {}, uid: {}", e, memberId, unionId);
        }
        
        return carNewFlag;
    }
    
    public String unifyNewJudge(String memberId, String unionId, Integer channel) {
        String carNewFlag = "0";
        try {
            UnifyCustomerJudgeReqDTO dto = new UnifyCustomerJudgeReqDTO();
            dto.setChannel(null == channel ? 852 : channel);
            dto.setMemberId(memberId);
            dto.setUnionId(unionId);
            dto.setProductIds(Collections.singletonList("CAR"));
            dto.setTraceId(UUID.randomUUID().toString());
            UnifyCustomerJudgeResDTO res = DSFUtils.sendAction(UnifyCustomerJudgeResDTO.class, getDsfRequest("customer", "unifyNewJudge", dto));
            LoggerUtils.info(log, "用车新老用户查询,req:{},resp:{}", JSON.toJSONString(dto), JSON.toJSONString(res));
            
            if (null == res || !res.isSuccess() || null == res.getData()) {
                return carNewFlag;
            }
            return res.getData().getCar();
        } catch (Exception e) {
            LoggerUtils.warn(log, "[判断用车新老用户] 查询异常 mid: {}, uid: {}", e, memberId, unionId);
        }
        
        return carNewFlag;
    }
    
    public List<String> queryMembers(String memberId, String traceId) {
        MemberByMemberIdReqDTO request = new MemberByMemberIdReqDTO();
        request.setMemberIds(Lists.newArrayList(memberId));
        request.setTraceId(traceId);
        
        LoggerUtils.info(log, "queryMembers.request: {}", JSON.toJSONString(request));
        MemberByMemberIdResDTO response = DSFUtils.sendAction(MemberByMemberIdResDTO.class, getDsfRequest("customer", "memberbymemberid", request));
        LoggerUtils.info(log, "queryMembers.response: {}", JSON.toJSONString(response));
        
        List<String> memberList = Lists.newArrayList(memberId);
        
        if (response == null) {
            return memberList;
        }
        
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            memberList.addAll(response.getData().stream()
                    .map(GetListByWxUnionIdDataDTO::getMemberId)
                    .filter(v -> null != v && !Objects.equals(v, 0L))
                    .map(String::valueOf)
                    .collect(Collectors.toList()));
        }
        
        return CollUtil.distinct(memberList);
    }
    
    public DSFRequest getDsfRequest(String serviceName, String actionName, Object request) {
        return new DSFRequest.Builder(
                urlProperties.getAssistServiceDsfName(),
                serviceName,
                actionName,
                urlProperties.getAssistServiceVersion()
        ).postBody(request).build();
    }
}
