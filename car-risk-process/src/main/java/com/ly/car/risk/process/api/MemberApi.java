package com.ly.car.risk.process.api;

import com.ly.car.fantasy.HttpMethod;
import com.ly.car.fantasy.TargetHeader;
import com.ly.car.fantasy.TargetUrl;
import com.ly.car.fantasy.WebRequest;
import com.ly.car.risk.process.api.dto.MemberData;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.service.dto.MemberDataDTO;

import java.util.List;

/**
 * 会员信息查询
 * 日期  2020/1/14.
 */

public interface MemberApi {

    /**
     * 通过手机号获取用户信息
     *
     * @return
     */

    @TargetHeader(name = "appKey", value = "tcwireless.java.official.accounts")
    @TargetHeader(name = "appSecret", value = "3b3284cbd67b9b0d")
    @WebRequest(timeout = 3000)
    @TargetUrl("http://mkcloud.17usoft.com/membermain/main/query")
    MemberQueryResponse getMemberInfoByKey(MemberQueryParam memberQueryParam);
    /*{
        log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: queryType:{}",key,memberSystem,queryType);
        String reqUrl = urlProperties.getMemberUrl()+ "/main/query";
        if (StringUtils.isBlank(key)){
            log.warn("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: MemberId为空",key,memberSystem);
            return null;
        }
        JSONObject requestObject = new JSONObject();
        requestObject.put("queryKey",key);
        requestObject.put("queryType",queryType);//1-会员Id ;2-登录名\手机号\邮箱
        requestObject.put("memberSystem",memberSystem);
        HashMap<String,String> header = new HashMap<>();
        header.put("appKey","tcwireless.java.official.accounts");
        header.put("appSecret","3b3284cbd67b9b0d");
        log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: 请求内容:{}",key,memberSystem,requestObject.toJSONString());
        String response = OkHttpClientUtil.getInstance().post(reqUrl,requestObject.toJSONString(),header);
        log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId: 请求内容:{},返回内容:{}",key,memberSystem,requestObject.toJSONString(),response);
        MemberQueryResponse memberQueryResponse = JSONObject.parseObject(response, MemberQueryResponse.class);
        if (memberQueryResponse == null || !"0".equals(memberQueryResponse.getCode())) {
            String errMsg = memberQueryResponse == null ? "未获取到返回内容！" : memberQueryResponse.getMsg();
            log.info("[MemberApi][getMemberInfoByMobile][{}][{}] 手机号获取会员memberId:,错误信息:{}",key,memberSystem,errMsg);
            return null;
        }
        return memberQueryResponse;
    }*/

   /* private String url = "http://mkcloud.17usoft.com";
    private String appKey = "tcwireless.quick.car";
    private String appSecret = "c992d8041d23199d";*/

    @TargetHeader(name = "appKey", value = "tcwireless.quick.car")
    @TargetHeader(name = "appSecret", value = "c992d8041d23199d")
    @WebRequest(path = "/mthirdparty/wechat/getListByMemberId", timeout = 3000,method = HttpMethod.GET)
    @TargetUrl("http://mkcloud.17usoft.com")
    MemberDataDTO getListByMemberId(ListByMemberIdParam param);




}

