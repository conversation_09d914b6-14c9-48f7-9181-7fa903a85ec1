package com.ly.car.risk.process.utils;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.MailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class SendMailUtils {

    @Value("${send.mail}")
    private String sendMailUrl;

    private static final Map<String,String> headerMap = new HashMap<>();

    static {
        headerMap.put("Service-Name","interior-email");
        headerMap.put("Method-Name","sendByWorkId");
        headerMap.put("Content-Type","application/json");
        headerMap.put("Generalplatform-Token","247aa5234fb54426a1739907c4ec8251");
    }

    public void sendMail(MailDTO mailDTO){
        //发送
        log.info("[RiskCommissionService] [action] [][]邮件发送信息:"+ JSONObject.toJSONString(mailDTO));
        String post = OkHttpClientUtil.getInstance().post(sendMailUrl, JSONObject.toJSONString(mailDTO), headerMap, 10l);
        log.info("[RiskCommissionService] [action] [][]邮件发送完成:"+post);
    }

    public static String getBase64Str(ByteArrayOutputStream byteArrayOutputStream){
        byte[] bytes = byteArrayOutputStream.toByteArray();
        // 进行Base64转码
        String base64Str = Base64.getEncoder().encodeToString(bytes);
        return base64Str;
    }
}
