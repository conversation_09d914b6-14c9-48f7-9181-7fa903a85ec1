package com.ly.car.risk.process.service.rule.hcGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskResultEnum;
import com.ly.car.risk.process.constants.SceneToCustomerType;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.HitchBlackSendData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 这边查下禁止认证名单
 * */
@Service
@Slf4j
public class HcDriverBlackService extends FilterHcAroundHandler{

    private final String THIRD_TYPE = "customer";

    @Resource(name = "hitchRiskProducer")
    private MqRiskProducer mqRiskProducer;

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Override
    public void doHandler(FilterHcContext context) {
        RiskResultNewDTO resultDTO = (RiskResultNewDTO) context.getUiResult().getData();
        HitchBlackSendData data = new HitchBlackSendData();
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        long seconds = ChronoUnit.SECONDS.between(LocalDateTime.now(), midnight);
        Duration LOCK_DURATION = Duration.ofSeconds(seconds);
        //是白名单就直接下一步了，不严重次数了，直接流程
        List<String> whiteList = queryConfig();
        if(whiteList.contains(context.getPlate())){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                doAfter(resultDTO);
            }
            return;
        }
        //是否达到当天注册次数
        RAtomicLong atomicLong = redissonClient.getAtomicLong(context.getIdCard());
        if(atomicLong.isExists()){
            if(atomicLong.get() >= 3){
                //发送mq
                resultDTO.setCode(1);
                resultDTO.setMessage("风控不通过,当日已达到认证次数");
                data.setDriverId(context.getDriverId());
                data.getResultMap().put("limit",1);
                resultDTO.setObj(data);
                mqRiskProducer.send(MqTagEnum.car_risk_hitch_notify, JsonUtils.json(resultDTO),0L);
                return;
            } else {
                atomicLong.incrementAndGet();
            }
        } else {
            atomicLong.set(1);
            atomicLong.expire(LOCK_DURATION);
        }
        //先看下当前场景下判断几种名单类型
        List<Integer> customerTypeList = SceneToCustomerType.hcCustomerTypeMap.get(context.getMainScene()+"-"+context.getChildScene());
        //查询是否是禁止注册名单
        RiskCustomerManage manage = context.getCustomerManageList().stream().filter(entity->customerTypeList.contains(entity.getRiskType())).findFirst().orElse(null);
        data.setDriverId(context.getDriverId());
        resultDTO.setObj(data);
        if(manage != null || context.getDriverId().equals("123456")){
            //发送mq
            resultDTO.setCode(1);
            resultDTO.setMessage("风控不通过");
            data.setDriverId(context.getDriverId());
            data.getResultMap().put(THIRD_TYPE,1);
            resultDTO.setObj(data);
            mqRiskProducer.send(MqTagEnum.car_risk_hitch_notify, JsonUtils.json(resultDTO),0L);
            return;
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            doAfter(resultDTO);
        }
    }

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {
        if(this.nextHandler == null && resultDTO.getCode() == 0){
            mqRiskProducer.send(MqTagEnum.car_risk_hitch_notify, JsonUtils.json(resultDTO),0L);
        }
    }

    /**
     * 获取配置信息
     * */
    private List<String> queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("hc_register_white");
            log.info("[][][][]获取汇川白名单规则配置:{}",configJson);
            if(StringUtils.isNotBlank(configJson)){
                return Arrays.asList(configJson.split(","));
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取汇川规则配置错误:",e);
        }
        return null;
    }
}
