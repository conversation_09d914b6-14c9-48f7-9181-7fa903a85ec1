package com.ly.car.risk.process.service.rule.common.driver;

import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.service.rule.common.CommonFilterContext;
import com.ly.car.risk.process.service.rule.common.CommonFilterHandler;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterContext;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class DriverBankCardService extends CommonFilterHandler {

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;

    @Override
    public void doHandler(CommonFilterContext context) {
        if(!validParam(context.getParam())){
            context.getDto().setCode(1);
            context.getDto().setMessage("银行卡参数缺失，验证失败");
            return;
        }
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName((String) context.getParam().get("name"));
        param.setIdCard((String) context.getParam().get("idCard"));
        param.setBankCard((String) context.getParam().get("bankCard"));
        param.setMobile((String) context.getParam().get("mobile"));
        Integer result = this.tianChuangRiskClient.verifyBankCard(param);
        if(result != 0){
            context.getDto().setCode(1);
            context.getDto().setMessage("银行卡调用三方返回失败");
        }
        return;
    }

    public Boolean validParam(Map<String,Object> param){
        if(param.get("name") == null){
            return false;
        }
        if(param.get("idCard") == null){
            return false;
        }
        if(param.get("bankCard") == null){
            return false;
        }
        if(param.get("mobile") == null){
            return false;
        }
        return true;
    }
}
