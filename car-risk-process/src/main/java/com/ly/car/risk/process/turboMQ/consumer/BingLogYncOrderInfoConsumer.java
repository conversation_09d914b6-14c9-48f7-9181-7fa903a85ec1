package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.OrderCoupon;
import com.ly.car.order.entity.OrderWxCard;
import com.ly.car.risk.entity.OrderComplete;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.repo.order.mapper.OrderCouponMapper;
import com.ly.car.risk.process.repo.order.mapper.OrderWxCardMapper;
import com.ly.car.risk.process.repo.risk.mapper.OrderCompleteMapper;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.redis.OrderStatusDTO;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.BingLogUtil;
import com.ly.car.sharding.order.entity.*;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderExpandMapper;
import com.ly.car.sharding.order.mapper.OrderSupplierBillMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BingLogYncOrderInfoConsumer implements MessageListenerConcurrently {

    private SaveScoredSortedSetService saveScoredSortedSetService;
    private OrderSupplierBillMapper orderSupplierBillMapper;
    private OrderAddressMapper orderAddressMapper;
    private OrderExpandMapper orderExpandMapper;
    private OrderDriverMapper orderDriverMapper;
    private ExecutorService executorService;
    private OrderWxCardMapper orderWxCardMapper;
    private OrderCouponMapper orderCouponMapper;
    private OrderCompleteMapper orderCompleteMapper;
    private MqRiskProducer mqRiskProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][BingLogYncOrderInfoConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                BingLogData bingLogData = JSON.parseObject(body, BingLogData.class);
                String eventType = bingLogData.getEventType();
                OrderInfo afterOrderInfo = BingLogUtil.buildSource(bingLogData,OrderInfo.class);
                OrderInfo beforeOrderInfo = BingLogUtil.buildBefore(bingLogData,OrderInfo.class);

                log.info("[binlog][BingLogYncOrderInfoConsumer][][][]消息类型:{}",eventType);
                //几个维度参数存储 用户取消数，账户维度，设备号维度，用户维度、司机维度、union_id维度
                if(eventType.equals("UPDATE")){
                    saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
                    if(beforeOrderInfo.getStatus() != null && beforeOrderInfo.getStatus() != 200 && afterOrderInfo.getStatus() == 200){
                        //发送个mq,进行异步处理缓存预估路径
                        mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
                        mqRiskProducer.send(MqTagEnum.car_risk_safe_warning,afterOrderInfo.getOrderId(), 0L);
                    }
                    if(beforeOrderInfo.getStatus() != null && beforeOrderInfo.getStatus() != 300 && afterOrderInfo.getStatus() == 300){
                        //存入redis
                        OrderStatusDTO dto = new OrderStatusDTO(afterOrderInfo.getOrderId(),400,new Date());
                        SendOrderContext orderContext = convertContext(afterOrderInfo);
                        if(StringUtils.isNotBlank(orderContext.getPayAccount())){
                            saveScoredSortedSetService.save(RedisKeyConstants.PAY_ACCOUNT_WINDOW+orderContext.getPayAccount(),
                                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
                        }
                        if(StringUtils.isNotBlank(orderContext.getDeviceId())){
                            saveScoredSortedSetService.save(RedisKeyConstants.DEVICE_ID_WINDOW+orderContext.getDeviceId(),
                                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
                        }
                        if(StringUtils.isNotBlank(orderContext.getMemberId())){
                            saveScoredSortedSetService.save(RedisKeyConstants.USER_MEMBER_WINDOW+orderContext.getMemberId(),
                                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
                            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER+afterOrderInfo.getMemberId(),
                                    3 * 24 * 60 * 60L,dto,new Date().getTime());
                        }
                        if(StringUtils.isNotBlank(orderContext.getUnionId())){
                            saveScoredSortedSetService.save(RedisKeyConstants.USER_UNION_ID_WINDOW+orderContext.getUnionId(),
                                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
                            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_UNION+afterOrderInfo.getUniId(),
                                    3 * 24 * 60 * 60L,dto,new Date().getTime());
                        }
                        if(StringUtils.isNotBlank(orderContext.getDriverCardNo())){
                            saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_CARD_WINDOW+orderContext.getDriverCardNo(),
                                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
                        }
                    }
                    if(beforeOrderInfo.getStatus() != null && beforeOrderInfo.getStatus() != 1000 && afterOrderInfo.getStatus() == 1000){
                        OrderStatusDTO dto = new OrderStatusDTO(afterOrderInfo.getOrderId(),1000,new Date());
                        if(StringUtils.isNotBlank(afterOrderInfo.getMemberId())){
                            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER+afterOrderInfo.getMemberId(),
                                    3 * 24 * 60 * 60L,dto,new Date().getTime());
                        }
                        if(StringUtils.isNotBlank(afterOrderInfo.getUniId())){
                            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_UNION+afterOrderInfo.getUniId(),
                                    3 * 24 * 60 * 60L,dto,new Date().getTime());
                        }
                    }
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[][][][]专车消费报错:{}",e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    public SendOrderContext convertContext(OrderInfo orderInfo){
        SendOrderContext sendOrderContext = new SendOrderContext();
        sendOrderContext.setOrderId(orderInfo.getOrderId());
        sendOrderContext.setFinishTime(orderInfo.getFinishTime());
        sendOrderContext.setTotalAmount(String.valueOf(orderInfo.getTotalAmount()));
        sendOrderContext.setPassengerCellphone(orderInfo.getPassengerCellphone());
        sendOrderContext.setMemberId(orderInfo.getMemberId());
        sendOrderContext.setUnionId(orderInfo.getUniId());
        sendOrderContext.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(orderInfo.getFinishTime().getTime()-orderInfo.getServiceTime().getTime()));

        orderExpandMapper = SpringContextUtil.getBean("orderExpandMapper");
        OrderExpand orderExpand = orderExpandMapper.findByOrderId(orderInfo.getOrderId());
        if(orderExpand != null){
            sendOrderContext.setPayAccount(orderExpand.getPayAccount());
            sendOrderContext.setDeviceId(orderExpand.getDeviceId());
        }
        orderDriverMapper = SpringContextUtil.getBean("orderDriverMapper");
        OrderDriver orderDriver = orderDriverMapper.findByOrderId(orderInfo.getOrderId());
        if(orderDriver != null){
            sendOrderContext.setDriverCardNo(orderDriver.getPlateNumber());
        }
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderInfo.getOrderId());
        sendOrderContext.setStartLat(String.valueOf(orderAddress.getStartLat()));
        sendOrderContext.setStartLng(String.valueOf(orderAddress.getStartLng()));
        sendOrderContext.setEndLat(String.valueOf(orderAddress.getEndLat()));
        sendOrderContext.setEndLng(String.valueOf(orderAddress.getEndLng()));

//        orderSupplierBillMapper = SpringContextUtil.getBean("orderSupplierBillMapper");
//        OrderSupplierBill orderSupplierBill = orderSupplierBillMapper.findByOrderId(orderInfo.getOrderId());
//        try {
//            if(orderSupplierBill != null){
//                sendOrderContext.setActualKilo(orderSupplierBill.getDistance());
//                sendOrderContext.setActualDuration(orderSupplierBill.getDuration());
//            }
//        } catch (Exception e) {
//            log.error("[binlog][BingLogYncOrderInfoConsumer][][][]组装缓存错误:{}",e);
//        }

//        try {
//            //异步插入数据库
//            executorService = SpringContextUtil.getBean("executorService");
//            OrderComplete orderComplete = new OrderComplete();
//            orderComplete.setOrderId(orderInfo.getOrderId());
//            orderComplete.setSupplierOrderId(orderInfo.getSupplierOrderId());
//            orderComplete.setStartCityId(orderAddress.getStartCityId());
//            orderComplete.setStartCityName(orderAddress.getStartCityName());
//            orderComplete.setCreateOrderTime(orderInfo.getCreateTime());
//            orderComplete.setPayTime(orderInfo.getPaidTime());
//            orderComplete.setFinishTime(orderInfo.getFinishTime());
//            orderComplete.setMemberId(orderInfo.getMemberId());
//            orderComplete.setUnionId(orderInfo.getUniId());
//            orderComplete.setTotalAmount(orderInfo.getTotalAmount());
//            orderComplete.setPhone(orderInfo.getPassengerCellphone());
//            orderComplete.setStartAddress(orderAddress.getStartAddress());
//            orderComplete.setEndAddress(orderAddress.getEndAddress());
//
//            //先查微信优惠券
//            orderWxCardMapper = SpringContextUtil.getBean("orderWxCardMapper");
//            List<OrderWxCard> orderWxCards = orderWxCardMapper.selectList(
//                    new QueryWrapper<OrderWxCard>().eq("order_id",orderInfo.getOrderId())
//            );
//            if(!CollectionUtils.isEmpty(orderWxCards)){
//                List<String> couponList = orderWxCards.stream().map(OrderWxCard::getCardId).collect(Collectors.toList());
//                orderComplete.setCouponBatchNo(StringUtils.join(couponList,","));
//                orderComplete.setCouponAmount(String.valueOf(orderWxCards.stream().map(OrderWxCard::getReduceCost).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add)));
//            } else {
//                //查app优惠券
//                orderCouponMapper = SpringContextUtil.getBean("orderCouponMapper");
//                List<OrderCoupon> orderCoupons = orderCouponMapper.selectList(
//                        new QueryWrapper<OrderCoupon>().eq("order_id",orderInfo.getOrderId())
//                );
//                List<String> couponList = orderCoupons.stream().map(OrderCoupon::getBatchNo).collect(Collectors.toList());
//                orderComplete.setCouponBatchNo(StringUtils.join(couponList,","));
//                orderComplete.setCouponAmount(String.valueOf(orderCoupons.stream().map(OrderCoupon::getCouponAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add)));
//            }
//            orderComplete.setCreateTime(new Date());
//            orderComplete.setUpdateTime(new Date());
//            orderComplete.setEstimateKilo(orderAddress.getEstimateKilo());
//            orderComplete.setEstimateTime(orderAddress.getEstimateMinute());
////            if(orderSupplierBill != null){
////                orderComplete.setActualKilo(orderSupplierBill.getDistance());
////                orderComplete.setActualTime(orderSupplierBill.getDuration());
////            }
//            orderComplete.setDriverCardNo(orderDriver.getPlateNumber());
//            orderComplete.setSupplierCode(orderInfo.getSupplierCode());
//            orderComplete.setSupplierName("");
//            orderComplete.setStartLat(orderAddress.getStartLat());
//            orderComplete.setStartLng(orderAddress.getStartLng());
//            orderComplete.setEndLat(orderAddress.getEndLat());
//            orderComplete.setEndLng(orderAddress.getEndLng());
//            orderCompleteMapper = SpringContextUtil.getBean("orderCompleteMapper");
//            executorService.execute(()->{orderCompleteMapper.insert(orderComplete);});
//        } catch (Exception e){
//            log.error("[binlog][BingLogYncOrderInfoConsumer][][][]完单插入数据库错误:{}",e);
//        }

        return sendOrderContext;
    }
}