package com.ly.car.risk.process.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Description of SFCCancelReasonEnum
 *
 * <AUTHOR>
 * @date 2024/3/25
 * @desc
 */
public enum SFCCancelReasonEnum {

    TRIP_CHANGE("1","临时行程有变，无法出行"),

    CAR_NOT_MATCH("2","车主的人车与订单不符"),

    INFO_ERROR("3","信息填写有误，重新叫车"),

    OTHER("4","其他"),

    CONTACT_FAIL("7","联系不上车主"),

    TIME_DELAY("14","车主不能按时接乘客"),

    CHANGE_TIME("16","车主要求其他出发时间"),

    ADD_PASSENGER("18","独享订单车主额外拼人"),

    MARK_UP("19","车主要求加价"),

    UNDER_LINE("19","车主要求线下交易"),

    ;

    private String type;

    private String reason;

    SFCCancelReasonEnum(String type, String reason) {
        this.type = type;
        this.reason = reason;
    }

    public static String getTypeByReason(String reason){
        if(StringUtils.isBlank(reason)){
            return OTHER.type;
        }
        for(SFCCancelReasonEnum reasonEnum : values()){
            if(Objects.equals(reasonEnum.reason,reason)){
                return reasonEnum.type;
            }
        }
        return OTHER.type;
    }
}