package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Scope("prototype")
public class RuleRisk042Service extends FilterSendOrderHandler{

    private static final String ruleNo = "042";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        log.info("前置判断已通过，进入规则041判断{}", JsonUtils.json(context.getMemberList()));
        if(!context.getSpecialCarRuleConfig().getRule042onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            List<SendOrderContext> orderContextList = context.getMemberList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.oneDay())).collect(Collectors.toList());
            log.info("[][][][]进入042规则:{}", JsonUtils.json(orderContextList));
            if (CollectionUtils.isNotEmpty(orderContextList) && context.getMemberList().size()
                    > context.getSpecialCarRuleConfig().getRule042OrderNum()) {
                log.info("[][][][]命中042规则:{}", JsonUtils.json(orderContextList));
                context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
                List<String> orderIds = orderContextList.stream().map(SendOrderContext::getOrderId)
                        .distinct()
                        .collect(Collectors.toList());
                orderIds.add(context.getOrderId());
                distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene()
                        , context.getChildScene(), 0, null, RiskLevelEnum.HIGH.getCode());

                orderIds.remove(context.getOrderId());
                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
