package com.ly.car.risk.process.supplier.Tsan;

import com.ly.car.risk.process.utils.Md5Util;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class T3SignAdapter {

    public static String signAdapter(T3BaseReq paramJson, String token) {
        log.info("[][][][]T3工单生成签名原始数据:{}", JsonUtils.json(paramJson));
        List<Field> fields = new LinkedList<>();
        fields.addAll(Arrays.asList(paramJson.getClass().getDeclaredFields()));
        if (paramJson.getClass().getSuperclass() != null) {
            fields.addAll(Arrays.asList(paramJson.getClass().getSuperclass().getDeclaredFields()));
        }
        List<String> keyValueList = new ArrayList<>();
        fields.stream().filter(field -> baseType.contains(field.getType()))
                .forEach(field -> {
                    field.setAccessible(true);
                    try {
                        String key = field.getName();
                        Object value = field.get(paramJson);
                        if (value != null) {
                            keyValueList.add(key + "=" + value);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("生成T3签名错误", e);
                    }
                });

        String strParams = keyValueList.stream().sorted().collect(Collectors.joining("&"));
        strParams = strParams+ "&token="+token;
//        keyValueList.add("token=" + token);
        log.info("[][][][]T3工单生成签名串:{}",strParams);
        String sign = Md5Util.md5Hex(strParams);
        return sign;
    }

    private static Set<Class<?>> baseType = Stream.of(
            String.class,
            Integer.class,
            Long.class
    ).collect(Collectors.toSet());
}
