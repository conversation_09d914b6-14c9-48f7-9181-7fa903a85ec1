<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <property name="FILE_PATH" value="/data/logs/<EMAIL>@/app/"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %level %thread %marker [%c] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${FILE_PATH}/spring.log</File>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${FILE_PATH}/spring.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <maxFileSize>256MB</maxFileSize>
            <totalSizeCap>1GB</totalSizeCap>
            <maxHistory>1</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %level %thread %X{apmTrace} %marker [%c{0}] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="CONSOLE-ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1000000</queueSize>
        <appender-ref ref="CONSOLE"/>
    </appender>

    <appender name="FILE-ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1000000</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <springProfile name="product,stage,qa,uat,stage_test2">
        <root level="INFO">
            <appender-ref ref="FILE-ASYNC"/>
        </root>
    </springProfile>

    <springProfile name="test,qa,uat">
        <logger name="org.springframework" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE-ASYNC"/>
        </root>
    </springProfile>

    <!--开发环境:打印控制台-->
    <springProfile name="test,qa,uat,stage,stage_test2">
        <logger name="com.ly.car.risk.process.repo" level="debug"/>
    </springProfile>


    <logger name="com.ly.dal" level="WARN"/>
    <logger name="org.apache.kafka" level="WARN"/>
    <logger name="com.ly.spat" level="ERROR"/>
    <logger name="RocketmqClient" level="ERROR"/>

</configuration>