package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.math.RoundingMode

/**
 * 网约车24小时内用户下单取消率
 * wyc_memberid_order_cancel_ratio
 */
class WycMemberidOrderCancelRatio {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String memberId = (String) params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourTotalOrder(memberId, "YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int) orders.stream().filter({ p -> p.getOrderState() == 2}).count();
        Double rate = new BigDecimal(count).divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP).doubleValue();

        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
