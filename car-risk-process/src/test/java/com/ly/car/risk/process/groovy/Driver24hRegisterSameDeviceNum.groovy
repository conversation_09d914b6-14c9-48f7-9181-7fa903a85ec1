package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

/**
 * 当前设备近24小时注册司机账户数量
 * driver_24h_register_same_device_num
 */
class Driver24hRegisterSameDeviceNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String deviceId = (String) params.get("deviceId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.driver24HourRegisterCountOnSameDevice(deviceId);
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        resultMap.put("num", orders.size());
        return resultMap;
    }

}
