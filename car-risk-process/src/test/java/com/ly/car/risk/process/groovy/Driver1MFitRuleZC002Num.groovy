package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.time.*

//package com.ly.car.risk.process.groovy

/**
 * 司机一个自然月命中zc-002规则次数
 * driver_1m_fit_rule_zc002_num
 */
class Driver1MFitRuleZC002Num {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum");
        if (StringUtils.isBlank(carNum)) {
            return resultMap;
        }

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取上一个自然月
        YearMonth previousMonth = YearMonth.from(currentDate).minusMonths(1);

        // 上一个自然月的开始时间
        LocalDateTime startOfPreviousMonth = previousMonth.atDay(1).atStartOfDay();

        // 上一个自然月的结束时间
        LocalDateTime endOfPreviousMonth = previousMonth.atEndOfMonth().atTime(LocalTime.MAX);

        // 将 LocalDateTime 转换为 Date
        Date startTime = Date.from(startOfPreviousMonth.atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(endOfPreviousMonth.atZone(ZoneId.systemDefault()).toInstant());

        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        long count = bean.driverFitRuleNum(carNum, "zc-002", startTime, endTime);
        resultMap.put("num", count);
        return resultMap;
    }

}
