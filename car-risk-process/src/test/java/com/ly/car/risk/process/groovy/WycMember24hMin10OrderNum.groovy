package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail

//package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 24小时内用户完单金额少于10元的订单数
 * wyc_memberid_24h_min10_order_num
 */
class WycMember24hMin10OrderNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourFinishOrder(memberId,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({ p -> p.getAmount().compareTo(new BigDecimal(10)) < 0 }).count();
        resultMap.put("num",count);
        return resultMap;
    }

}
