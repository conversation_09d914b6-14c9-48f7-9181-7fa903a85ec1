package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.RoundingMode

/**
 * 网约车司机24小时大于300元订单完单占比
 * driver_24h_wyc_gte300_order_ratio
 */
class Driver24hWycGte300OrderRatio {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum");
        if(StringUtils.isBlank(carNum)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourFinishOrder(carNum,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({ p -> p.getAmount().compareTo(new BigDecimal(300) > 0) }).count();
        Double rate = new BigDecimal(count).divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP).doubleValue();

        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
