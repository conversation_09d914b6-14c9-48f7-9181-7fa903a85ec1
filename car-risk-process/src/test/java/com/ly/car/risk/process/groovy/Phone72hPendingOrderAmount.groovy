package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.time.DateUtils
import org.assertj.core.util.Lists
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 *
 /**
 * 当前订单手机号近3天存在待补款订单关联未付金额数
 *
 * Phone72hPendingOrderAmount
 * ycw/ycs_phone_72h_pending_order_amount
 */
class Phone72hPendingOrderAmount {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();

        String passengerPhone = (String) params.get("passengerPhone")

        Date endTime = new Date();
        Date startTime = DateUtils.addHours(endTime, -72);
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<String> productLineList = Lists.newArrayList("YCW","YCS")
        BigDecimal amount = bean.phonePendingOrderAmount(passengerPhone, productLineList, startTime, endTime);

        if (null==amount) {
            return resultMap;
        }

        resultMap.put("num", amount);
        return resultMap;
    }

}
