package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.math.RoundingMode

//package com.ly.car.risk.process.groovy

/**
 * 当前用户24小时内预估里程< 5公里订单占比行程结束、订单完成 顺风车
 * member_24h_ycs_order_prop
 */
class Member24hYcsOrderProp {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String memberId = (String) params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourFinishOrder(memberId,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int) orders.stream().filter({ p -> p.getEstimateDistance() > 0 && p.getEstimateDistance()<5000 }).count();
        Double rate = new BigDecimal(count).divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP).doubleValue();

        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
