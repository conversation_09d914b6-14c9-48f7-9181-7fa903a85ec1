package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 司机单月添加附加费订单数
 * driver_surcharge_month_num
 */
class DriverSurchargeMonthNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();

        String carNum = (String) params.get("carNum")
        List<String> productLineList = new ArrayList<>();
        productLineList.add("YCS");
        Integer orderNum = 8;

        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.driverSurchargeMonthNum(carNum, orderNum, productLineList);

        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        resultMap.put("num", orders.size());
        return resultMap;
    }

}
