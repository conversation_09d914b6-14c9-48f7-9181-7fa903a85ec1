package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory


/**
 * 当前用户近30分钟内用户取消订单数 顺风车
 * member_30min_ycs_cancel_num
 */
class Member30minYcsCancelNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user30MinCancelOrder(memberId,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = orders.size();
        resultMap.put("num",count);
        return resultMap;
    }

}
