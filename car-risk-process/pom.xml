<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.3</version>
        <relativePath/>
    </parent>
    <groupId>com.ly.car</groupId>
    <artifactId>car-risk-process</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <app.name>car.java.risk.process</app.name>
        <tcbase.env>${profile.name}</tcbase.env>
        <kafka.version>0.11.0.3</kafka.version>
        <mysql.version>5.1.38</mysql.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-common-bean</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-sharding-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-order-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-resource-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>usecar-toolset</artifactId>
            <version>1.3.1-202308181103</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.dal</groupId>
            <artifactId>dal-new</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.24.3</version>
        </dependency>

        <dependency>
            <groupId>com.ly.turbomq</groupId>
            <artifactId>turbomq-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.tcbase</groupId>
            <artifactId>configcenterclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.dsf</groupId>
            <artifactId>dsf-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.base</groupId>
            <artifactId>tcwireless-component-abtest</artifactId>
            <version>2.1.0.2</version>
        </dependency>

        <!-- easyexcel 依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.ly.turbomq</groupId>
            <artifactId>turbomq-client</artifactId>
            <version>4.2.5</version>
        </dependency>

        <!-- clickhouse -->
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.3.1</version>
        </dependency>

        <!-- md5加密 -->
<!--        <dependency>-->
<!--            <groupId>commons-codec</groupId>-->
<!--            <artifactId>commons-codec</artifactId>-->
<!--            <version>1.11</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.4.13</version>
        </dependency>

        <dependency>
            <groupId>com.ishumei.fp</groupId>
            <artifactId>fp-crypto</artifactId>
            <version>1.7.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/fp-crypto-1.7.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ishumei.fp</groupId>
            <artifactId>fp-boxid-tool</artifactId>
            <version>1.2.5</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/fp-boxid-tool-1.2.5.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
            <!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询所有版本，最新版本如下 -->
            <version>3.1.849</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.tencentcloudapi/tencentcloud-speech-sdk-java -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-speech-sdk-java</artifactId>
            <version>1.0.33</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>

        <dependency>
            <groupId>com.ly.flight</groupId>
            <artifactId>marketing.assistservice-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.pushcore</groupId>
            <artifactId>travel-push-platform-core-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>configcore-client</artifactId>
                    <groupId>com.ly.flight</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-trade-core-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.shared.mobility.supply</groupId>
            <artifactId>shared-mobility-supply-order-core-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car.base.service</groupId>
            <artifactId>car-virtualphone-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-trade-core-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-core-facade</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.ly.car</groupId>
                <artifactId>car-risk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>car_risk_process</finalName>

        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>

            <resource>
                <filtering>true</filtering>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/application*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>

        <plugins>

            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>

        </plugins>

    </build>

    <profiles>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.name>test</profile.name>
                <tcbase.env>test</tcbase.env>
                <dsf.host>qa.dsf2.17usoft.com</dsf.host>
                <dsf.env>test</dsf.env>
                <dsf.virtualphone.env>test</dsf.virtualphone.env>
            </properties>
        </profile>

        <profile>
            <id>qa</id>
            <properties>
                <profile.name>qa</profile.name>
                <tcbase.env>qa</tcbase.env>
                <dsf.host>qa.dsf2.17usoft.com</dsf.host>
                <dsf.env>qa</dsf.env>
                <dsf.virtualphone.env>qa</dsf.virtualphone.env>
            </properties>
        </profile>

        <profile>
            <id>uat</id>
            <properties>
                <profile.name>uat</profile.name>
                <tcbase.env>uat</tcbase.env>
                <dsf.host>qa.dsf2.17usoft.com</dsf.host>
                <dsf.env>uat</dsf.env>
                <dsf.virtualphone.env>uat</dsf.virtualphone.env>
            </properties>
        </profile>

        <profile>
            <id>stage</id>
            <properties>
                <profile.name>stage</profile.name>
                <dsf.host>t.dsf2.17usoft.com</dsf.host>
                <dsf.env>stage</dsf.env>
                <dsf.virtualphone.env>stage</dsf.virtualphone.env>
            </properties>
        </profile>

        <profile>
            <id>stage_test2</id>
            <properties>
                <profile.name>stage_test2</profile.name>
                <dsf.host>t.dsf2.17usoft.com</dsf.host>
                <dsf.env>stage_test2</dsf.env>
                <dsf.virtualphone.env>stage</dsf.virtualphone.env>
            </properties>
        </profile>

        <profile>
            <id>product</id>
            <properties>
                <profile.name>product</profile.name>
                <dsf.host>dsf2.17usoft.com</dsf.host>
                <dsf.env>product</dsf.env>
                <dsf.virtualphone.env>product</dsf.virtualphone.env>
            </properties>
        </profile>

    </profiles>

    <repositories>
        <repository>
            <id>17usoft</id>
            <name>LY Share Repository</name>
            <url>http://nexus.17usoft.com/repository/mvn-all/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>releases</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>https://nexus.17usoft.com/repository/mvn-flight-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>https://nexus.17usoft.com/repository/mvn-flight-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

</project>