# risk_customer_manage表业务流程分析 - Mapper层数据访问

## 1. Mapper接口定义

### 1.1 car-risk-manage模块的RiskCustomerManageMapper

```java
package com.ly.car.risk.manage.repo.risk.mapper;

public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {
    
    // 分页查询相关方法
    long getListTotal(Object query);
    List<RiskCustomerManage> getList(Object query);
    List<RiskCustomerManage> getListExport(Object query);
    
    // 条件查询方法
    RiskCustomerManage getByTypeAndValueAndRiskType(
        @Param("customerType") Integer customerType,
        @Param("customerValue") String customerValue,
        @Param("riskType") Integer riskType,
        @Param("time") Date time
    );
    
    RiskCustomerManage getByTypeAndValueAndRiskTypeAndUser(
        @Param("customerType") Integer customerType,
        @Param("customerValue") String customerValue,
        @Param("riskType") Integer riskType,
        @Param("bindUser") String bindUser,
        @Param("time") Date time
    );
    
    List<RiskCustomerManage> getByCondition(Object query);
    
    // 数据清理方法
    int clearInvalid();
    int clearInvalidById(@Param("id") Long id);
    
    // 重复数据查询
    List<RiskCustomerManage> getDupList();
    List<RiskCustomerManage> getDup1v1List();
}
```

### 1.2 car-risk-process模块的RiskCustomerManageMapper

```java
package com.ly.car.risk.process.repo.risk.mapper;

public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {
    
    // 查询失效数据
    @Select("select * from risk_customer_manage where status = 1 " +
            "and ((ttl != -1 and ttl != 0 and NOW() >= DATE_ADD(create_time, INTERVAL ttl day)) " +
            "or (ttl = 0 and NOW() >= invalid_time))")
    List<RiskCustomerManage> selectInvalidData();
    
    // 按条件查询风控名单
    List<RiskCustomerManage> getListByValue(
        @Param("params") FilterParams params, 
        @Param("invalidTime") Date dateTime
    );
    
    List<RiskCustomerManage> getListByValueByGroup(
        @Param("params") CommonCustomerParam params, 
        @Param("invalidTime") Date dateTime
    );
    
    // 统计有效数据
    long getValidCount(
        @Param("customerType") int customerType,
        @Param("customerValue") String customerValue,
        @Param("riskType") int riskType
    );
    
    // 查询所有有效风控记录
    List<RiskCustomerManage> queryAllValidRiskRecord(
        @Param("offset") int offset, 
        @Param("limit") int limit
    );
}
```

## 2. XML映射文件详细分析

### 2.1 car-risk-manage模块的XML配置

#### 2.1.1 分页查询配置
```xml
<select id="getList" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage
    <include refid="getListCondition"/>
    order by id desc
    limit #{offset}, #{size}
</select>

<select id="getListTotal" resultType="long">
    select count(*) from risk_customer_manage
    <include refid="getListCondition"/>
</select>
```

#### 2.1.2 查询条件配置
```xml
<sql id="getListCondition">
    <where>
        <if test="customerType != null">
            and customer_type = #{customerType}
        </if>
        <if test="customerValue != null and customerValue != ''">
            and customer_value like concat('%', #{customerValue}, '%')
        </if>
        <if test="riskType != null">
            and risk_type = #{riskType}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="invalidTime != null and invalidTime != ''">
            and invalid_time > #{invalidTime}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            and create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            and create_time &lt;= #{createTimeEnd}
        </if>
    </where>
</sql>
```

#### 2.1.3 特定条件查询
```xml
<select id="getByTypeAndValueAndRiskType" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage 
    where customer_type = #{customerType} 
    and customer_value = #{customerValue} 
    and risk_type = #{riskType} 
    and invalid_time > #{time}
    and status = 1
    order by create_time desc 
    limit 1
</select>

<select id="getByTypeAndValueAndRiskTypeAndUser" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage 
    where customer_type = #{customerType} 
    and customer_value = #{customerValue} 
    and risk_type = #{riskType} 
    and bind_user = #{bindUser} 
    and invalid_time > #{time}
    and status = 1
    order by create_time desc 
    limit 1
</select>
```

#### 2.1.4 重复数据查询
```xml
<select id="getDupList" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select risk_type, customer_type, customer_value
    from risk_customer_manage
    WHERE status = 1 and risk_type != 7
    GROUP BY risk_type, customer_type, customer_value
    HAVING COUNT(*) > 1
</select>

<select id="getDup1v1List" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select risk_type, customer_type, customer_value, bind_user
    from risk_customer_manage
    WHERE status = 1 and risk_type = 7
    GROUP BY risk_type, customer_type, customer_value, bind_user
    HAVING COUNT(*) > 1
</select>
```

#### 2.1.5 数据清理配置
```xml
<update id="clearInvalid">
    update risk_customer_manage
    set update_time = CURRENT_TIMESTAMP,
        status = 3,
        option_type = 1,
        option_name = '系统操作'
    where status = 2 or invalid_time < NOW()
</update>

<update id="clearInvalidById">
    update risk_customer_manage
    set update_time = CURRENT_TIMESTAMP,
        status = 3,
        option_type = 1,
        option_name = '系统操作'
    where id = #{id}
</update>
```

### 2.2 car-risk-process模块的XML配置

#### 2.2.1 多条件查询配置
```xml
<select id="getListByValue" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage 
    where invalid_time > #{invalidTime} 
    and (
        <trim prefixOverrides="or">
            <if test="params.memberId != null and params.memberId != ''">
                or customer_value = #{params.memberId}
            </if>
            <if test="params.unionId != null and params.unionId != ''">
                or customer_value = #{params.unionId}
            </if>
            <if test="params.userPhone != null and params.userPhone != ''">
                or customer_value = #{params.userPhone}
            </if>
            <if test="params.driverCardNo != null and params.driverCardNo != ''">
                or customer_value = #{params.driverCardNo}
            </if>
            <if test="params.payAccount != null and params.payAccount != ''">
                or customer_value = #{params.payAccount}
            </if>
            <if test="params.deviceId != null and params.deviceId != ''">
                or customer_value = #{params.deviceId}
            </if>
            <if test="params.idCardNos != null and params.idCardNos.size > 0">
                or customer_value in
                <foreach collection="params.idCardNos" item="item" index="index" 
                         open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    )
</select>
```

#### 2.2.2 统计查询配置
```xml
<select id="getValidCount" resultType="long">
    select count(*)
    from risk_customer_manage
    where customer_type = #{customerType}
    and customer_value = #{customerValue}
    and risk_type = #{riskType}
    and invalid_time > now()
    and status = 1
</select>
```

#### 2.2.3 批量查询配置
```xml
<select id="queryAllValidRiskRecord" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage
    where customer_type = 6
    and invalid_time > now()
    and risk_type in (1,6,7)
    and status = 1
    and customer_value != ''
    order by id
    limit #{limit} offset #{offset}
</select>
```

## 3. 数据访问方法分类

### 3.1 基础CRUD操作（继承自BaseMapper）

```java
// 插入操作
int insert(RiskCustomerManage entity);
int insertBatch(List<RiskCustomerManage> entityList);

// 查询操作
RiskCustomerManage selectById(Serializable id);
List<RiskCustomerManage> selectList(Wrapper<RiskCustomerManage> queryWrapper);
RiskCustomerManage selectOne(Wrapper<RiskCustomerManage> queryWrapper);
Long selectCount(Wrapper<RiskCustomerManage> queryWrapper);

// 更新操作
int updateById(RiskCustomerManage entity);
int update(RiskCustomerManage entity, Wrapper<RiskCustomerManage> updateWrapper);

// 删除操作
int deleteById(Serializable id);
int delete(Wrapper<RiskCustomerManage> queryWrapper);
```

### 3.2 自定义查询方法

#### 3.2.1 条件查询方法
- `getByTypeAndValueAndRiskType`: 根据客户类型、客户值、风险类型查询
- `getByTypeAndValueAndRiskTypeAndUser`: 一对一名单查询（包含绑定用户）
- `getListByValue`: 多条件模糊查询
- `getListByValueByGroup`: 分组条件查询

#### 3.2.2 分页查询方法
- `getList`: 分页查询名单列表
- `getListTotal`: 查询总数
- `getListExport`: 导出查询

#### 3.2.3 统计查询方法
- `getValidCount`: 统计有效数据数量
- `getDupList`: 查询重复数据
- `getDup1v1List`: 查询一对一重复数据

#### 3.2.4 特殊查询方法
- `selectInvalidData`: 查询失效数据
- `queryAllValidRiskRecord`: 查询所有有效风控记录

### 3.3 数据维护方法

#### 3.3.1 清理方法
- `clearInvalid`: 批量清理失效数据
- `clearInvalidById`: 根据ID清理数据

## 4. 查询性能优化

### 4.1 索引设计建议

```sql
-- 主键索引
PRIMARY KEY (id)

-- 业务查询索引
INDEX idx_customer_type_value_risk (customer_type, customer_value, risk_type)
INDEX idx_invalid_time_status (invalid_time, status)
INDEX idx_create_time (create_time)
INDEX idx_bind_user (bind_user)

-- 复合索引
INDEX idx_type_value_risk_time (customer_type, customer_value, risk_type, invalid_time)
INDEX idx_status_invalid_time (status, invalid_time)
```

### 4.2 查询优化策略

1. **分页查询优化**
   - 使用limit进行分页
   - 避免大offset的深度分页
   - 考虑使用游标分页

2. **条件查询优化**
   - 合理使用索引
   - 避免在where条件中使用函数
   - 优先使用等值查询

3. **批量操作优化**
   - 使用批量插入减少数据库交互
   - 控制批量大小避免内存溢出
   - 使用事务保证数据一致性

## 5. 数据访问层调用链路

### 5.1 管理端调用链路
```
Controller -> Service -> Mapper -> Database
    ↓           ↓         ↓         ↓
RiskCustomerController -> RiskCustomerService -> RiskCustomerManageMapper -> risk_customer_manage表
```

### 5.2 业务处理调用链路
```
规则引擎 -> Service -> Mapper -> Database
    ↓        ↓         ↓         ↓
RiskEngine -> RiskCustomerService -> RiskCustomerManageMapper -> risk_customer_manage表
```

### 5.3 定时任务调用链路
```
定时任务 -> Service -> Mapper -> Database
    ↓        ↓         ↓         ↓
@Scheduled -> RiskCustomerService -> RiskCustomerManageMapper -> risk_customer_manage表
```

## 6. 数据一致性保证

### 6.1 事务管理
- 使用@Transactional注解保证事务一致性
- 批量操作使用事务避免部分成功
- 关键业务操作使用分布式事务

### 6.2 并发控制
- 使用乐观锁避免并发更新冲突
- 关键操作使用分布式锁
- 合理设置事务隔离级别

### 6.3 数据校验
- 在Mapper层进行基础数据校验
- 使用数据库约束保证数据完整性
- 定期进行数据一致性检查

## 7. 监控和日志

### 7.1 SQL监控
- 监控慢查询SQL
- 统计查询频率和性能
- 监控数据库连接池状态

### 7.2 操作日志
- 记录所有数据变更操作
- 包含操作人、操作时间、操作内容
- 便于问题排查和审计

### 7.3 异常处理
- 捕获和处理数据库异常
- 记录详细的错误日志
- 提供友好的错误提示
