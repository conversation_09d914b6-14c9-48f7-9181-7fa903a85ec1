# `/riskCheck/queryRiskLevel` 接口技术实现分析

## 1. 接口技术概述

`/riskCheck/queryRiskLevel` 接口是一个基于 Spring MVC 框架实现的 RESTful API，用于查询用户的风险等级。该接口采用责任链模式进行风险检查，通过查询风险名单数据库表来判断用户是否存在风险。

## 2. 代码结构分析

### 2.1 控制层 (Controller)

```java
@RestController
@RequestMapping("/riskCheck")
@Slf4j
public class RiskCheckController {
    
    @Resource
    private CarRiskService carRiskService;
    
    @PostMapping("/queryRiskLevel")
    public UiResult queryRiskLevel(@RequestBody RiskLevelQueryRequest request) {
        log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级入参:{}", JsonUtils.json(request));
        if (StringUtils.isBlank(request.getProductLine())) {
            request.setProductLine(ProductLineEnum.YNC.getCode());
        }
        UiResult<RiskResultDTO> result = carRiskService.queryRiskLevel(request);
        log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级出参:{}", JsonUtils.json(result));
        return result;
    }
}
```

### 2.2 服务层 (Service)

#### 2.2.1 CarRiskService 接口

```java
public interface CarRiskService {
    UiResult<RiskResultDTO> queryRiskLevel(RiskLevelQueryRequest request);
    // 其他方法...
}
```

#### 2.2.2 CarRiskServiceImpl 实现类

```java
@Service
@Slf4j
public class CarRiskServiceImpl implements CarRiskService {
    
    @Resource
    private YncConvertHandlerService yncConvertHandlerService;
    
    @Override
    public UiResult<RiskResultDTO> queryRiskLevel(RiskLevelQueryRequest request) {
        try {
            if (request.getProductLine().equals(ProductLineEnum.YNC.getCode())) {
                FilterCheckPriceHandler handler = yncConvertHandlerService.getCheckRiskLevelHandler();
                FilterSceneContext context = yncConvertHandlerService.convertCheckRiskLevelCtx(request);
                UiResult<RiskResultDTO> result = handler.doHandler(context);
                return yncConvertHandlerService.convertRiskFlag(result);
            }
            return UiResult.ok(new RiskResultDTO());
        } catch (Exception e) {
            log.error("[CarRiskServiceImpl][queryRiskLevel][][]查询风险等级异常", e);
            return UiResult.ok(new RiskResultDTO());
        }
    }
}
```

### 2.3 处理器 (Handler)

#### 2.3.1 FilterCheckPriceHandler 抽象类

```java
public abstract class FilterCheckPriceHandler {
    public abstract UiResult doHandler(FilterSceneContext context);
    protected FilterCheckPriceHandler nextHandler;
    public void next(FilterCheckPriceHandler nextHandler){
        this.nextHandler = nextHandler;
    }
}
```

#### 2.3.2 RuleRisk1001Service 实现类

```java
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk1001Service extends FilterCheckPriceHandler {
    
    public static final String ruleNo = "1001";
    
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    
    @Override
    public UiResult doHandler(FilterSceneContext context) {
        UiResult result = context.getUiResult();
        result.setData(new RiskResultDTO());
        // 是否白名单，有一个直接返回，什么逻辑都不走
        RiskCustomerManage whiteManage = context.getRiskCustomerManageList().stream()
            .filter(e->e.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode()))
            .findFirst().orElse(null);
        if(whiteManage != null){
            result = UiResult.ok();
            RiskResultDTO dto = new RiskResultDTO(0,"风控通过","", whiteManage.getCustomerValue(),RiskLevelEnum.NO.getCode());
            dto.setCashRate("1.0");
            result.setData(dto);
            return result;
        }
        // 判断是否黑名单
        RiskCustomerManage blackManage = context.getRiskCustomerManageList().stream()
            .filter(e-> e.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode()))
            .findFirst().orElse(null);
        log.info("[RuleRisk1001Service][][][]1001查询黑名单:{}", JsonUtils.json(blackManage));
        if(blackManage != null){
            result = UiResult.ok();
            result.setMsg("风控不通过");
            RiskResultDTO dto = new RiskResultDTO(405,"风控不通过",ruleNo,blackManage.getCustomerValue(), RiskLevelEnum.HIGH.getCode());
            dto.setCashRate(context.getRateMap().get(ruleNo));
            result.setData(dto);
            return result;
        }
        if(this.nextHandler != null){
            return this.nextHandler.doHandler(context);
        }
        return result;
    }
}
```

### 2.4 转换服务 (Convert Service)

```java
@Service
@Slf4j
public class YncConvertHandlerService {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    public FilterCheckPriceHandler getCheckRiskLevelHandler() {
        return SpringContextUtil.getBean("ruleRisk1001Service");
    }
    
    public FilterSceneContext convertCheckRiskLevelCtx(RiskLevelQueryRequest request) throws Exception {
        FilterSceneContext context = new FilterSceneContext();
        CommonCustomerParam cusParams = new CommonCustomerParam();
        cusParams.setDeviceId(request.getDeviceId());
        cusParams.setMemberId(request.getMemberId());
        cusParams.setUnionId(request.getUnionId());
        cusParams.setUserPhone(request.getUserPhone());
        cusParams.setPassengerCellphone(request.getPassengerCellphone());
        Date date = new Date();
        List<RiskCustomerManage> listByValueByGroup = riskCustomerService.getListByValueByGroup(cusParams, date);
        context.setRiskCustomerManageList(listByValueByGroup);
        context.setMemberId(request.getMemberId());
        context.setUnionId(request.getUnionId());
        context.setRateMap(getRateConfig());//获取配置
        return context;
    }
    
    public Map<String, String> getRateConfig(){
        try {
            String configJson = ConfigCenterClient.get("price_rule_rate");
            log.info("获取询价价格系数:"+configJson);
            Map<String,String> map = JSONObject.parseObject(configJson,Map.class);
            return map;
        } catch (Exception e) {
            log.error("获取价格系数错误:",e);
        }
        return new HashMap<>();
    }
    
    public UiResult convertRiskFlag(UiResult<RiskResultDTO> result) {
        RiskResultDTO dto = result.getData();
        dto.setRiskFlag(dto.getCode() != 0 ? 1 : 0);
        result.setData(dto);
        return result;
    }
}
```

### 2.5 数据访问层 (DAO)

```java
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {
    List<RiskCustomerManage> getListByValueByGroup(@Param("params") CommonCustomerParam params, @Param("invalidTime")Date dateTime);
    // 其他方法...
}
```

### 2.6 实体类 (Entity)

```java
@Data
public class RiskCustomerManage {
    private Long id;
    private Integer customerType;
    private String customerValue;
    private Integer riskType;
    private Integer status;
    private Integer ttl;
    private Date createTime;
    private Date updateTime;
    private Date invalidTime;
    private Integer optionType;
    private String createUser;
    private String optionName;
    private String riskRemark;
    private String bindUser;
    private String bindOrder;
    private String supplierName;
    // 其他字段...
}
```

### 2.7 请求/响应对象 (DTO)

```java
@Data
public class RiskLevelQueryRequest {
    private String memberId;
    private String unionId;
    private String deviceId;
    private String userPhone;
    private String passengerCellphone;
    private String productLine;
    private String channel;
    // 其他字段...
}

@Data
public class RiskResultDTO {
    private Integer code;
    private String message;
    private String ruleNo;
    private String customer;
    private Integer level;
    private String cashRate;
    private Object obj;
    private Integer riskFlag;
    
    public RiskResultDTO(){
        this.code = 0;
        this.message = "风控通过";
    }
    
    public RiskResultDTO(Integer code,String message){
        this.code = code;
        this.message = message;
    }
    
    public RiskResultDTO(Integer code,String message,String ruleNo,String customer){
        this.code = code;
        this.message = message;
        this.ruleNo = ruleNo;
        this.customer = customer;
    }
    
    public RiskResultDTO(Integer code,String message,String ruleNo,String customer,Integer level){
        this.code = code;
        this.message = message;
        this.ruleNo = ruleNo;
        this.customer = customer;
        this.level = level;
    }
}
```

### 2.8 上下文对象 (Context)

```java
@Data
public class FilterSceneContext {
    private String orderId;
    private Integer mainScene;
    private Integer childScene;
    private List<RiskCustomerManage> riskCustomerManageList;
    private UiResult uiResult = UiResult.ok();
    private String payAccount;
    private String memberId;
    private String unionId;
    private Integer isNewUser;
    private BigDecimal esAmount;
    private Map<String, String> rateMap;
    private SpecialCarRuleConfig specialCarRuleConfig;
    
    public FilterSceneContext(){
        UiResult result = this.uiResult;
        result.setData(new RiskResultDTO());
    }
}
```

## 3. 技术实现细节

### 3.1 设计模式

#### 3.1.1 责任链模式 (Chain of Responsibility Pattern)

接口实现采用责任链模式，通过 `FilterCheckPriceHandler` 抽象类及其实现类构建责任链，每个处理器负责特定的风险检查逻辑，如果当前处理器无法处理，则传递给下一个处理器。

```java
public abstract class FilterCheckPriceHandler {
    public abstract UiResult doHandler(FilterSceneContext context);
    protected FilterCheckPriceHandler nextHandler;
    public void next(FilterCheckPriceHandler nextHandler){
        this.nextHandler = nextHandler;
    }
}
```

#### 3.1.2 工厂模式 (Factory Pattern)

通过 `YncConvertHandlerService.getCheckRiskLevelHandler()` 方法获取处理器实例，实现了简单工厂模式。

```java
public FilterCheckPriceHandler getCheckRiskLevelHandler() {
    return SpringContextUtil.getBean("ruleRisk1001Service");
}
```

#### 3.1.3 策略模式 (Strategy Pattern)

不同的产品线可以使用不同的风险检查策略，通过 `ProductLineEnum` 枚举类判断产品线，选择相应的处理策略。

```java
if (request.getProductLine().equals(ProductLineEnum.YNC.getCode())) {
    // 网约车产品线的处理策略
    FilterCheckPriceHandler handler = yncConvertHandlerService.getCheckRiskLevelHandler();
    FilterSceneContext context = yncConvertHandlerService.convertCheckRiskLevelCtx(request);
    UiResult<RiskResultDTO> result = handler.doHandler(context);
    return yncConvertHandlerService.convertRiskFlag(result);
}
// 其他产品线的处理策略
return UiResult.ok(new RiskResultDTO());
```

### 3.2 数据库操作

#### 3.2.1 MyBatis 映射

使用 MyBatis 框架进行数据库操作，通过 XML 映射文件定义 SQL 语句。

```xml
<select id="getListByValueByGroup" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage where invalid_time &gt; #{invalidTime} and (
    <trim prefixOverrides="or">
        <if test=" params.memberId != null and params.memberId != '' ">
            or customer_value = #{params.memberId}
        </if>
        <if test=" params.unionId != null and params.unionId != '' ">
            or customer_value = #{params.unionId}
        </if>
        <if test=" params.userPhone != null and params.userPhone != '' ">
            or customer_value = #{params.userPhone}
        </if>
        <if test=" params.passengerCellphone != null and params.passengerCellphone != '' ">
            or customer_value = #{params.passengerCellphone}
        </if>
        <if test=" params.driverCardNo != null and params.driverCardNo != '' ">
            or customer_value = #{params.driverCardNo}
        </if>
        <if test=" params.payAccount != null and params.payAccount != '' ">
            or customer_value = #{params.payAccount}
        </if>
        <if test=" params.deviceId != null and params.deviceId != '' ">
            or customer_value = #{params.deviceId}
        </if>
        <if test=" params.driverId != null and params.driverId != '' ">
            or customer_value = #{params.driverId}
        </if>
        <if test=" params.mobile != null and params.mobile != '' ">
            or customer_value = #{params.mobile}
        </if>
        <if test=" params.invitePhone != null and params.invitePhone != '' ">
            or customer_value = #{params.invitePhone}
        </if>
        <if test=" params.idCardNos != null and params.idCardNos.size >0 ">
            or ( customer_type = 10 and customer_value in
            <foreach collection="params.idCardNos" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
               )
        </if>
    </trim>
    )
</select>
```

#### 3.2.2 MyBatis-Plus 框架

项目使用 MyBatis-Plus 框架简化数据库操作，`RiskCustomerManageMapper` 接口继承自 `BaseMapper<RiskCustomerManage>`，自动获得基本的 CRUD 操作方法。

```java
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {
    // 自定义方法...
}
```

### 3.3 配置管理

使用配置中心管理配置信息，通过 `ConfigCenterClient.get("price_rule_rate")` 获取价格系数配置。

```java
public Map<String, String> getRateConfig(){
    try {
        String configJson = ConfigCenterClient.get("price_rule_rate");
        log.info("获取询价价格系数:"+configJson);
        Map<String,String> map = JSONObject.parseObject(configJson,Map.class);
        return map;
    } catch (Exception e) {
        log.error("获取价格系数错误:",e);
    }
    return new HashMap<>();
}
```

### 3.4 异常处理

采用 try-catch 机制处理异常，并返回默认结果，确保接口的稳定性。

```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("[CarRiskServiceImpl][queryRiskLevel][][]查询风险等级异常", e);
    return UiResult.ok(new RiskResultDTO());
}
```

### 3.5 日志记录

使用 Slf4j 框架记录日志，包括请求参数、响应结果和异常信息。

```java
log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级入参:{}", JsonUtils.json(request));
// 业务逻辑
log.info("[RiskCheckController][queryRiskLevel][][]查询风险等级出参:{}", JsonUtils.json(result));
```

## 4. 数据库表结构详解

### 4.1 risk_customer_manage 表

| 字段名 | 类型 | 是否为空 | 默认值 | 描述 |
| --- | --- | --- | --- | --- |
| id | bigint(20) | NO | AUTO_INCREMENT | 主键ID |
| customer_type | int(11) | NO | | 客户类型 |
| customer_value | varchar(255) | NO | | 客户值 |
| risk_type | int(11) | NO | | 风险类型 |
| status | int(11) | NO | 1 | 状态 |
| ttl | int(11) | NO | | 有效期 |
| create_time | datetime | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | NO | CURRENT_TIMESTAMP | 更新时间 |
| invalid_time | datetime | NO | | 失效时间 |
| option_type | int(11) | NO | | 操作类型 |
| create_user | varchar(255) | YES | | 创建人 |
| option_name | varchar(255) | YES | | 操作人 |
| risk_remark | varchar(255) | YES | | 风险备注 |
| bind_user | varchar(255) | YES | | 绑定用户 |
| bind_order | varchar(255) | YES | | 绑定订单 |
| supplier_name | varchar(255) | YES | | 供应商名称 |
| black_type | int(11) | YES | | 黑名单类型 |
| member_id | varchar(255) | YES | | 会员ID |

### 4.2 索引设计

| 索引名 | 类型 | 字段 |
| --- | --- | --- |
| PRIMARY | PRIMARY KEY | id |
| idx_customer_value | INDEX | customer_value |
| idx_invalid_time | INDEX | invalid_time |
| idx_status | INDEX | status |
| idx_risk_type | INDEX | risk_type |
| idx_customer_type | INDEX | customer_type |

## 5. 接口调用示例

### 5.1 请求示例

```json
{
  "memberId": "12345678",
  "unionId": "u12345678",
  "deviceId": "d12345678",
  "userPhone": "13800138000",
  "passengerCellphone": "13900139000",
  "productLine": "YNC",
  "channel": "APP"
}
```

### 5.2 响应示例

#### 5.2.1 无风险响应

```json
{
  "success": true,
  "errCode": 0,
  "msg": "请求成功",
  "data": {
    "code": 0,
    "message": "风控通过",
    "ruleNo": "",
    "customer": "",
    "level": 0,
    "cashRate": "1.0",
    "riskFlag": 0
  }
}
```

#### 5.2.2 有风险响应

```json
{
  "success": true,
  "errCode": 0,
  "msg": "风控不通过",
  "data": {
    "code": 405,
    "message": "风控不通过",
    "ruleNo": "1001",
    "customer": "13800138000",
    "level": 5,
    "cashRate": "0.8",
    "riskFlag": 1
  }
}
```

## 6. 性能优化

### 6.1 数据库查询优化

1. **索引优化**：为 `customer_value`、`invalid_time`、`status`、`risk_type`、`customer_type` 等字段创建索引，提高查询效率。

2. **SQL 优化**：使用 OR 条件查询多个字段，可能导致索引失效，可以考虑使用 UNION ALL 优化。

### 6.2 代码优化

1. **缓存优化**：可以考虑对风险名单数据进行缓存，减少数据库查询次数。

2. **并发优化**：使用 `@Scope("prototype")` 注解确保每个请求获取新的处理器实例，避免并发问题。

3. **异常处理优化**：细化异常类型，针对不同异常采取不同的处理策略。

## 7. 安全性考虑

### 7.1 数据安全

1. **敏感数据加密**：对用户手机号、设备ID等敏感信息进行加密存储。

2. **访问控制**：限制对风险名单数据的访问权限，只允许授权用户查询和修改。

### 7.2 接口安全

1. **参数校验**：对请求参数进行校验，防止恶意请求。

2. **接口鉴权**：使用 Token 或其他鉴权机制，确保只有授权系统可以调用接口。

3. **日志审计**：记录接口调用日志，便于安全审计和问题排查。

## 8. 可扩展性设计

### 8.1 产品线扩展

通过 `ProductLineEnum` 枚举类定义产品线，可以方便地添加新的产品线。

```java
@Getter
@AllArgsConstructor
public enum ProductLineEnum {
    SFC("SFC", "顺风车"),
    YNC("YNC", "网约车"),
    BUS("BUS", "汽车票"),
    // 可以添加新的产品线...
}
```

### 8.2 风险规则扩展

通过责任链模式，可以方便地添加新的风险检查规则，只需实现 `FilterCheckPriceHandler` 抽象类，并将其添加到责任链中。

```java
@Component
@Slf4j
@Scope("prototype")
public class NewRiskRuleService extends FilterCheckPriceHandler {
    @Override
    public UiResult doHandler(FilterSceneContext context) {
        // 新的风险检查逻辑
        if(this.nextHandler != null){
            return this.nextHandler.doHandler(context);
        }
        return context.getUiResult();
    }
}
```

## 9. 总结

`/riskCheck/queryRiskLevel` 接口采用责任链模式实现风险等级查询，通过查询风险名单数据库表判断用户是否存在风险。接口设计考虑了性能、安全性和可扩展性，能够满足不同产品线的风险检查需求。

该接口是风控系统的重要组成部分，为其他业务系统提供风险等级查询服务，帮助业务系统识别风险用户，保障业务安全。
