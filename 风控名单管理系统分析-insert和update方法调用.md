# 风控名单管理系统分析 - insert 和 update 方法调用

本文档分析了 `RiskCustomerManageMapper` 中 `insert` 和 `update` 方法的调用情况，包括 Controller 入口、URL 和调用链。

## insert 方法调用

### car-risk-manage 模块

#### 1. RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/riskCustomer/add` | 添加风控名单 | Controller.add -> Service.add -> Mapper.insert |
| excelImport | `/riskListManage/riskCustomer/import` | Excel批量导入风控名单 | Controller.excelImport -> Service.excelImport -> Service.add -> Mapper.insert |

#### 2. HcCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/hcCustomer/add` | 添加汇川司机风控名单 | Controller.add -> Service.add -> Mapper.insert |

### car-risk-process 模块

#### 1. BlackDriverShieldController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| userShield | `/blackDriver/userShield` | 用户拉黑司机 | Controller.userShield -> Service.userShield -> Mapper.insert |

#### 2. BlackListApiController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| blackDriver | `/blackList/driver/black` | 用户拉黑司机 | Controller.blackDriver -> Service.blackDriver -> Mapper.insert |
| syncBlackList | `/blackList/sync` | 同步黑名单 | Controller.syncBlackList -> Service.syncBlackList -> Service.change -> Mapper.insert |

#### 3. BlackListController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| blackDriver | `/manage/blackList/driver/black` | 客服拉黑司机 | Controller.blackDriver -> Service.blackDriverFromManage -> Mapper.insert |

#### 4. RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| syncBlackDriver | `/risk/customer/syncBlackDriver` | 同步司机黑名单 | Controller.syncBlackDriver -> Service.syncDriver -> Mapper.insert |

#### 5. DataSyncController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| syncDriver | `/sync/driverBlack` | 同步司机黑名单 | Controller.syncDriver -> Service.syncDriver -> Mapper.insert |
| syncDriverByErp | `/sync/syncDriverByErp` | 通过ERP同步司机黑名单 | Controller.syncDriverByErp -> Service.syncDriverByErp -> Mapper.insert |

## update 方法调用

### car-risk-manage 模块

#### 1. RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/riskCustomer/add` | 添加风控名单(更新已存在的) | Controller.add -> Service.add -> Mapper.updateById |
| delete | `/riskListManage/riskCustomer/delete` | 删除风控名单 | Controller.delete -> Service.delete -> Mapper.updateById |
| excelImport | `/riskListManage/riskCustomer/import` | Excel批量导入风控名单(更新已存在的) | Controller.excelImport -> Service.excelImport -> Service.add -> Mapper.updateById |
| excelDeleteImport | `/riskListManage/riskCustomer/deleteImport` | Excel批量删除风控名单 | Controller.excelDeleteImport -> Service.excelDeleteImport -> Service.delete -> Mapper.updateById |

#### 2. HcCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| add | `/riskListManage/hcCustomer/add` | 添加汇川司机风控名单(更新已存在的) | Controller.add -> Service.add -> Mapper.updateById |
| delete | `/riskListManage/hcCustomer/delete` | 删除汇川司机风控名单 | Controller.delete -> Service.delete -> Mapper.updateById |

### car-risk-process 模块

#### 1. BlackDriverShieldController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| userShield | `/blackDriver/userShield` | 用户拉黑司机(更新已存在的) | Controller.userShield -> Service.userShield -> Mapper.updateById |
| userRemove | `/blackDriver/userRemove` | 用户取消拉黑司机 | Controller.userRemove -> Service.userRemove -> Mapper.updateById |

#### 2. BlackListApiController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| blackDriver | `/blackList/driver/black` | 用户拉黑司机(更新已存在的) | Controller.blackDriver -> Service.blackDriver -> Mapper.updateById |
| removeDriver | `/blackList/driver/remove` | 用户取消拉黑司机 | Controller.removeDriver -> Service.removeDriver -> Mapper.updateById |
| syncBlackList | `/blackList/sync` | 同步黑名单 | Controller.syncBlackList -> Service.syncBlackList -> Service.change/delete -> Mapper.updateById |

#### 3. BlackListController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| blackDriver | `/manage/blackList/driver/black` | 客服拉黑司机(更新已存在的) | Controller.blackDriver -> Service.blackDriverFromManage -> Mapper.updateById |

#### 4. RiskCustomerController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| invalid | `/risk/customer/invalid` | 风控名单定时失效 | Controller.invalid -> Service.invalid -> Mapper.updateById |
| initCustomer | `/risk/customer/initCustomer` | 名单初始化失效时间 | Controller.initCustomer -> Service.initCustomer -> Mapper.updateById |
| syncBlackDriver | `/risk/customer/syncBlackDriver` | 同步司机黑名单(更新已存在的) | Controller.syncBlackDriver -> Service.syncDriver -> Mapper.updateById |

#### 5. DataSyncController

| 方法 | URL | 功能描述 | 调用链 |
|-----|-----|---------|-------|
| syncDriver | `/sync/driverBlack` | 同步司机黑名单(更新已存在的) | Controller.syncDriver -> Service.syncDriver -> Mapper.updateById |
| syncDriverByErp | `/sync/syncDriverByErp` | 通过ERP同步司机黑名单(更新已存在的) | Controller.syncDriverByErp -> Service.syncDriverByErp -> Mapper.updateById |
