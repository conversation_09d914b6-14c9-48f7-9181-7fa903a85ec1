package com.ly.car.risk.impl;

import com.ly.car.configcenter.ConfigCenterConfiguration;
import com.ly.car.mq.properties.TurboMqProperties;
import com.ly.car.risk.impl.bean.properties.UrlProperties;
import com.ly.car.env.CarEnvConfiguration;
import com.ly.car.monitor.EnableMonitor;
import com.ly.car.mq.TurboMqConfiguration;
import com.ly.car.redis.EnableRedis;
import com.ly.car.trend.TrendClientConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.AdviceMode;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.EnableLoadTimeWeaving;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@Import({TrendClientConfiguration.class,
        ConfigCenterConfiguration.class,
        CarEnvConfiguration.class,
        TurboMqConfiguration.class})
@SpringBootApplication(scanBasePackages = "com.ly.car.risk.impl",
        exclude = {RedisAutoConfiguration.class, DataSourceAutoConfiguration.class})
@EnableRetry
//@EnableDataSource
@EnableRedis
@EnableMonitor
@EnableLoadTimeWeaving
@EnableAspectJAutoProxy
@EnableTransactionManagement(mode = AdviceMode.ASPECTJ)
@EnableConfigurationProperties({UrlProperties.class, TurboMqProperties.class})
public class RiskImplApplication {
    public static void main(String[] args) {
        SpringApplication.run(RiskImplApplication.class);
    }
}
