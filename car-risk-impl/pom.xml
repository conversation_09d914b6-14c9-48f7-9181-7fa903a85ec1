<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.3</version>
        <relativePath/>
    </parent>
    <groupId>com.ly.car</groupId>
    <artifactId>car-risk-impl</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <app.name>car.java.risk.impl</app.name>
        <tcbase.env>${profile.name}</tcbase.env>
        <mysql.version>5.1.38</mysql.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-risk-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-common-bean</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-sharding-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-order-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-resource-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.29.1-GA</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.dal</groupId>
            <artifactId>dal-new</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.tcbase</groupId>
            <artifactId>configcenterclient</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.ly.car</groupId>
                <artifactId>car-risk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>car_risk_impl</finalName>

        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/application*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>

        <plugins>

            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>

        </plugins>

    </build>

    <profiles>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.name>test</profile.name>
                <tcbase.env>test</tcbase.env>
            </properties>
        </profile>

        <profile>
            <id>qa</id>
            <properties>
                <profile.name>qa</profile.name>
                <tcbase.env>test</tcbase.env>
            </properties>
        </profile>

        <profile>
            <id>stage</id>
            <properties>
                <profile.name>stage</profile.name>
            </properties>
        </profile>

        <profile>
            <id>product</id>
            <properties>
                <profile.name>product</profile.name>
            </properties>
        </profile>

    </profiles>

    <repositories>
        <repository>
            <id>releases</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>https://nexus.17usoft.com/repository/mvn-flight-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>https://nexus.17usoft.com/repository/mvn-flight-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

</project>